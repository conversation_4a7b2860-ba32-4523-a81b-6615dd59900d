package com.ltgj.web.job.handler;

import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelInfoChailvgjService;
import com.ltgj.ivw.service.IHotelInfoHsjlService;
import com.ltgj.ivw.service.IHotelInfoHsjlxyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "hsjlXyHotelInitDataHandler")
public class HsjlXyHotelInitDataHandler extends IJobHandler {
    @Autowired
    private IHotelInfoHsjlxyService hotelInfoHsjlxyService;
    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try{
            hotelInfoHsjlxyService.resetStatus();
            hotelInfoHsjlxyService.updateHsjlxyAll( "job", "job");
        }catch (Exception e) {
            e.printStackTrace();
            log.error("红色加力协议-酒店增量同步任务异常：{}", e);
        }
        log.info("红色加力协议-全量数据执行完成");
        return ReturnT.SUCCESS;
    }
}
