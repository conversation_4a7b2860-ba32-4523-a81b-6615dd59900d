package com.ltgj.supplier.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用是否状态枚举
 * 适用于所有二值状态场景：是否有无、是否收费、是否允许等
 * 
 * <AUTHOR>
 * @Date 2025/6/5
 */
@Getter
@AllArgsConstructor
public enum YesNoStatus {
    
    /**
     * 是/有/允许/收费
     */
    YES("1", "是"),
    
    /**
     * 否/无/不允许/免费
     */
    NO("0", "否");
    
    private final String code;
    private final String name;
    
    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 对应的枚举，未找到返回null
     */
    public static YesNoStatus fromCode(String code) {
        for (YesNoStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     * @param name 名称
     * @return 对应的枚举，未找到返回null
     */
    public static YesNoStatus fromName(String name) {
        for (YesNoStatus status : values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据布尔值获取枚举
     * @param value 布尔值
     * @return true返回YES，false返回NO，null返回null
     */
    public static YesNoStatus fromBoolean(Boolean value) {
        if (value == null) {
            return null;
        }
        return value ? YES : NO;
    }
    
    /**
     * 转换为布尔值
     * @return YES返回true，NO返回false
     */
    public Boolean toBoolean() {
        return this == YES;
    }
    
    /**
     * 是否为肯定状态
     * @return YES返回true
     */
    public boolean isTrue() {
        return this == YES;
    }
    
    /**
     * 是否为否定状态
     * @return NO返回true
     */
    public boolean isFalse() {
        return this == NO;
    }
    
    /**
     * 获取相反状态
     * @return YES返回NO，NO返回YES
     */
    public YesNoStatus opposite() {
        return this == YES ? NO : YES;
    }
} 