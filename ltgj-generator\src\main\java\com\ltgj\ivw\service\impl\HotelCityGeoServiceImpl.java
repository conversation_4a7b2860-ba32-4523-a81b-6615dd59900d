package com.ltgj.ivw.service.impl;

import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.ivw.domain.HotelCityGeo;
import com.ltgj.ivw.mapper.HotelCityGeoMapper;
import com.ltgj.ivw.service.IHotelCityGeoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.builder.GeoHierarchyBuilder;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.GeoStructureQueryReq;
import com.ltgj.ivw.service.IGeoHierarchyService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import com.ltgj.common.core.redis.RedisCache;
import com.alibaba.fastjson.JSON;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 酒店城市商圈Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
@Slf4j
public class HotelCityGeoServiceImpl implements IHotelCityGeoService
{
    @Autowired
    private HotelCityGeoMapper hotelCityGeoMapper;
    
    @Autowired
    private GeoHierarchyBuilder geoHierarchyBuilder;
    
    @Autowired
    private IGeoHierarchyService geoHierarchyService;
    
    @Autowired
    private RedisCache redisCache;
    
    /**
     * 缓存失效时间（小时），默认6小时
     */
    @Value("${geo.hierarchy.cache.expire.hours:6}")
    private int cacheExpireHours;
    
    /**
     * Redis缓存键前缀
     */
    private static final String CACHE_KEY_PREFIX = "geo:hierarchy:full:";
    
    /**
     * 完整地理层级数据的缓存键
     */
    private static final String FULL_HIERARCHY_CACHE_KEY = CACHE_KEY_PREFIX + "data";
    
    /**
     * 查询酒店城市商圈
     *
     * @param id 酒店城市商圈主键
     * @return 酒店城市商圈
     */
    @Override
    public HotelCityGeo selectHotelCityGeoById(String id)
    {
        return hotelCityGeoMapper.selectByPrimaryKey(id);
    }

    /**
     * 查询酒店城市商圈列表
     *
     * @param hotelCityGeo 酒店城市商圈
     * @return 酒店城市商圈
     */
    @Override
    public List<HotelCityGeo> selectHotelCityGeoList(HotelCityGeo hotelCityGeo)
    {
        return hotelCityGeoMapper.selectByExample(null);
    }

    /**
     * 新增酒店城市商圈
     *
     * @param hotelCityGeo 酒店城市商圈
     * @return 结果
     */
    @Override
    public int insertHotelCityGeo(HotelCityGeo hotelCityGeo)
    {
        Date now = new Date();
        hotelCityGeo.setCreateTime(now);
        hotelCityGeo.setUpdateTime(now);
        if (hotelCityGeo.getCreatedBy() == null) {
            hotelCityGeo.setCreatedBy(SecurityUtils.getUsername());
        }
        return hotelCityGeoMapper.insertSelective(hotelCityGeo);
    }

    /**
     * 修改酒店城市商圈
     *
     * @param hotelCityGeo 酒店城市商圈
     * @return 结果
     */
    @Override
    public int updateHotelCityGeo(HotelCityGeo hotelCityGeo)
    {
        hotelCityGeo.setUpdateTime(new Date());
        return hotelCityGeoMapper.updateByPrimaryKeySelective(hotelCityGeo);
    }

    /**
     * 批量删除酒店城市商圈
     *
     * @param ids 需要删除的酒店城市商圈主键
     * @return 结果
     */
    @Override
    public int deleteHotelCityGeoByIds(String[] ids)
    {
        int count = 0;
        for (String id : ids) {
            count += hotelCityGeoMapper.deleteByPrimaryKey(id);
        }
        return count;
    }

    /**
     * 删除酒店城市商圈信息
     *
     * @param id 酒店城市商圈主键
     * @return 结果
     */
    @Override
    public int deleteHotelCityGeoById(String id)
    {
        return hotelCityGeoMapper.deleteByPrimaryKey(id);
    }

    /**
     * 批量插入酒店城市商圈数据
     *
     * @param records 商圈数据列表
     * @return 插入成功的条数
     */
    @Override
    public int batchInsert(List<HotelCityGeo> records)
    {
        if (CollectionUtils.isEmpty(records)) {
            log.warn("批量插入商圈数据为空，跳过插入操作");
            return 0;
        }

        Date now = new Date();

        // 设置创建时间和创建人
        for (HotelCityGeo record : records) {
            record.setCreateTime(now);
            record.setUpdateTime(now);
            if (record.getCreatedBy() == null) {
                record.setCreatedBy("system");
            }
        }

        log.info("开始批量插入商圈数据，数量：{}", records.size());
        int result = hotelCityGeoMapper.batchInsert(records);
        log.info("批量插入商圈数据完成，成功插入：{} 条", result);
        
        return result;
    }

    /**
     * 批量插入酒店城市商圈数据 非id自增
     *
     * @param records 商圈数据列表
     * @return 插入成功的条数
     */
    @Override
    public int batchInsertForContidion(List<HotelCityGeo> records)
    {
        if (CollectionUtils.isEmpty(records)) {
            log.warn("批量插入商圈数据为空，跳过插入操作");
            return 0;
        }

        Date now = new Date();

        // 设置创建时间和创建人
        for (HotelCityGeo record : records) {
            record.setCreateTime(now);
            record.setUpdateTime(now);
            if (record.getCreatedBy() == null) {
                record.setCreatedBy("system");
            }
        }

        log.info("开始批量插入商圈数据，数量：{}", records.size());
        int result = hotelCityGeoMapper.batchInsertForContidion(records);
        log.info("批量插入商圈数据完成，成功插入：{} 条", result);

        return result;
    }

    /**
     * 批量插入或更新酒店城市商圈数据
     *
     * @param records 商圈数据列表
     * @return 插入/更新成功的条数
     */
    @Override
    public int batchInsertOrUpdate(List<HotelCityGeo> records)
    {
        if (CollectionUtils.isEmpty(records)) {
            log.warn("批量插入或更新商圈数据为空，跳过操作");
            return 0;
        }

        Date now = new Date();

        // 设置创建时间和创建人
        for (HotelCityGeo record : records) {
            record.setCreateTime(now);
            record.setUpdateTime(now);
            if (record.getCreatedBy() == null) {
                record.setCreatedBy("system");
            }
        }

        log.info("开始批量插入或更新商圈数据，数量：{}", records.size());
        int result = hotelCityGeoMapper.batchInsertOrUpdateSmart(records);
        log.info("批量插入或更新商圈数据完成，成功处理：{} 条", result);
        
        return result;
    }
    
    /**
     * Spring容器启动时自动加载地理层级数据到缓存
     */
    @PostConstruct
    public void init() {
        log.info("开始初始化地理层级数据缓存");
        preloadGeoHierarchyCache();
    }
    
    /**
     * 预加载地理层级数据到缓存
     * 只缓存到市级（国家-省-市），不包含区县
     */
    @Override
    public void preloadGeoHierarchyCache() {
        // 检查缓存是否需要加载
        if (!isCacheExpired()) {
            log.info("地理层级数据缓存仍然有效，跳过加载");
            return;
        }
        
        synchronized (this) {
            // 双重检查
            if (!isCacheExpired()) {
                return;
            }
            
            try {
                log.info("开始加载地理层级数据到缓存（只加载到市级）");
                long startTime = System.currentTimeMillis();
                
                // 使用 GeoHierarchyBuilder 构建三级数据（国家-省-市）
                List<GeoHierarchyNode> threeLevelHierarchy = geoHierarchyBuilder.buildThreeLevelHierarchy("00002");
                
                // 更新缓存
                redisCache.setCacheObject(FULL_HIERARCHY_CACHE_KEY, JSON.toJSONString(threeLevelHierarchy));
                
                long elapsed = System.currentTimeMillis() - startTime;
                log.info("地理层级数据加载完成（三级），耗时：{}ms，国家数量：{}", 
                    elapsed, threeLevelHierarchy.size());
                
            } catch (Exception e) {
                log.error("加载地理层级数据失败", e);
                throw new RuntimeException("预加载地理层级缓存失败：" + e.getMessage());
            }
        }
    }
    
    /**
     * 从缓存中获取地理位置层次结构
     * 只支持查询国家、省、市三级，不查询区县
     */
    @Override
    public AjaxResult<GeoHierarchyNode> getGeoStructureByIdFromCache(GeoStructureQueryReq request) {
        log.info("从缓存获取地理位置层次结构，请求：{}", request);
        
        try {
            // 参数校验
            if (request == null || StringUtils.isEmpty(request.getId())) {
                return AjaxResult.errorFor("ID参数不能为空");
            }
            
            // 确保缓存已加载
            if (isCacheExpired()) {
                preloadGeoHierarchyCache();
            }
            
            // 在缓存中查找目标节点
            String json = redisCache.getCacheObject(FULL_HIERARCHY_CACHE_KEY);
            if (json == null) {
                return AjaxResult.errorFor("缓存数据不存在，请稍后重试");
            }
            
            List<GeoHierarchyNode> fullHierarchy = JSON.parseArray(json, GeoHierarchyNode.class);
            GeoHierarchyNode targetNode = findNodeInCache(request.getId(), fullHierarchy);
            if (targetNode == null) {
                return AjaxResult.errorFor("未找到对应的地理位置信息：" + request.getId());
            }
            
            // 检查节点类型，只支持国家、省、市
            if ("district".equals(targetNode.getType())) {
                return AjaxResult.errorFor("该接口不支持查询区县级别数据");
            }
            
            // 根据请求参数构建返回结构
            GeoHierarchyNode result = buildResultStructure(targetNode, request);
            
            log.info("从缓存获取地理位置层次结构成功，ID：{}，类型：{}", request.getId(), targetNode.getType());
            return AjaxResult.successWith("查询成功", result);
            
        } catch (Exception e) {
            log.error("从缓存获取地理位置层次结构异常", e);
            return AjaxResult.errorFor("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 在缓存中递归查找指定ID的节点
     */
    private GeoHierarchyNode findNodeInCache(String id, List<GeoHierarchyNode> nodes) {
        for (GeoHierarchyNode node : nodes) {
            GeoHierarchyNode found = findNodeRecursive(node, id);
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    /**
     * 递归查找节点
     */
    private GeoHierarchyNode findNodeRecursive(GeoHierarchyNode node, String id) {
        if (node == null) {
            return null;
        }
        
        // 检查当前节点
        if (id.equals(node.getId())) {
            return node;
        }
        
        // 递归查找子节点
        if (node.getChildren() != null) {
            for (GeoHierarchyNode child : node.getChildren()) {
                GeoHierarchyNode found = findNodeRecursive(child, id);
                if (found != null) {
                    return found;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 根据请求参数构建返回结构
     */
    private GeoHierarchyNode buildResultStructure(GeoHierarchyNode targetNode, GeoStructureQueryReq request) {
        // 处理上级数据
        if (Boolean.TRUE.equals(request.getIncludeParents())) {
            // 需要构建从国家到目标节点的完整路径，这时要返回从根节点开始的完整结构
            return buildPathFromRoot(targetNode.getId(), request.getIncludeFullChildren(), request.getGeoType());
        }
        
        // 如果不需要包含上级，则只返回目标节点及其子节点
        // 深拷贝目标节点，避免修改缓存数据
        GeoHierarchyNode result = cloneNode(targetNode);
        
        // 处理子级数据
        if (!Boolean.TRUE.equals(request.getIncludeFullChildren())) {
            // 如果不需要完整子级，清空子节点
            result.setChildren(new ArrayList<>());
        }
        // 注意：由于缓存中已经不包含区县数据，不需要处理 geoType 过滤
        
        return result;
    }
    
    /**
     * 深拷贝节点
     */
    private GeoHierarchyNode cloneNode(GeoHierarchyNode source) {
        if (source == null) {
            return null;
        }
        
        GeoHierarchyNode clone = new GeoHierarchyNode();
        clone.setId(source.getId());
        clone.setName(source.getName());
        clone.setEnName(source.getEnName());
        clone.setFullName(source.getFullName());
        clone.setType(source.getType());
        clone.setLevel(source.getLevel());
        clone.setParentId(source.getParentId());
        clone.setParentName(source.getParentName());
        clone.setGeoType(source.getGeoType());
        clone.setLongitude(source.getLongitude());
        clone.setLatitude(source.getLatitude());
        clone.setExtra(source.getExtra());
        
        // 递归拷贝子节点
        if (source.getChildren() != null) {
            List<GeoHierarchyNode> clonedChildren = new ArrayList<>();
            for (GeoHierarchyNode child : source.getChildren()) {
                clonedChildren.add(cloneNode(child));
            }
            clone.setChildren(clonedChildren);
        }
        
        return clone;
    }
    
    /**
     * 根据geoType过滤节点
     * 只过滤 district 类型的子节点
     */
    private GeoHierarchyNode filterByGeoType(GeoHierarchyNode node, String geoType) {
        if (node == null || node.getChildren() == null || node.getChildren().isEmpty()) {
            return node;
        }
        
        Long geoTypeLong = Long.parseLong(geoType);
        
        // 如果当前节点是城市，过滤其下的区县子节点
        if ("city".equals(node.getType())) {
            List<GeoHierarchyNode> filteredChildren = node.getChildren().stream()
                .filter(child -> "district".equals(child.getType()) && 
                    (child.getGeoType() == null || child.getGeoType().equals(geoTypeLong)))
                .collect(Collectors.toList());
            node.setChildren(filteredChildren);
        } else {
            // 递归处理非城市节点的子节点
            for (GeoHierarchyNode child : node.getChildren()) {
                filterByGeoType(child, geoType);
            }
        }
        
        return node;
    }
    
    /**
     * 构建从根节点到目标节点的路径
     */
    private GeoHierarchyNode buildPathFromRoot(String targetId, Boolean includeFullChildren, String geoType) {
        // 从Redis获取完整的层级数据
        String json = redisCache.getCacheObject(FULL_HIERARCHY_CACHE_KEY);
        if (json == null) {
            return null;
        }
        
        List<GeoHierarchyNode> fullHierarchy = JSON.parseArray(json, GeoHierarchyNode.class);
        
        // 找到从根到目标节点的路径
        List<GeoHierarchyNode> path = new ArrayList<>();
        findPathToNode(fullHierarchy, targetId, path);
        
        if (path.isEmpty()) {
            log.warn("未找到从根节点到目标节点的路径，目标ID：{}", targetId);
            return null;
        }
        
        // 记录找到的路径
        log.debug("找到路径，长度：{}，路径：{}", path.size(), 
            path.stream().map(n -> n.getName() + "(" + n.getId() + ")").collect(Collectors.joining(" -> ")));
        
        // 从根节点开始构建结果
        GeoHierarchyNode root = null;
        GeoHierarchyNode current = null;
        
        // 正向遍历路径，从根节点（国家）开始
        for (int i = 0; i < path.size(); i++) {
            GeoHierarchyNode nodeInPath = path.get(i);
            GeoHierarchyNode cloned = cloneNode(nodeInPath);
            
            // 清空子节点，只保留路径上的节点
            cloned.setChildren(new ArrayList<>());
            
            if (root == null) {
                root = cloned;
                current = root;
            } else {
                current.addChild(cloned);
                current = cloned;
            }
            
            // 如果是目标节点（最后一个节点）且需要包含完整子级
            if (i == path.size() - 1 && Boolean.TRUE.equals(includeFullChildren)) {
                // 由于缓存中已经不包含区县数据，这里不需要再处理子节点
                // 城市级别已经是最底层
                log.debug("到达目标节点：{}，类型：{}，不再添加子节点（缓存只包含三级）", 
                    nodeInPath.getName(), nodeInPath.getType());
            }
        }
        
        return root;
    }
    
    /**
     * 查找到目标节点的路径
     */
    private boolean findPathToNode(List<GeoHierarchyNode> nodes, String targetId, List<GeoHierarchyNode> path) {
        for (GeoHierarchyNode node : nodes) {
            if (findPathRecursive(node, targetId, path)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 递归查找路径
     */
    private boolean findPathRecursive(GeoHierarchyNode node, String targetId, List<GeoHierarchyNode> path) {
        if (node == null) {
            return false;
        }
        
        // 将当前节点加入路径
        path.add(node);
        
        // 检查是否找到目标
        if (targetId.equals(node.getId())) {
            return true;
        }
        
        // 递归查找子节点
        if (node.getChildren() != null) {
            for (GeoHierarchyNode child : node.getChildren()) {
                if (findPathRecursive(child, targetId, path)) {
                    return true;
                }
            }
        }
        
        // 没找到，从路径中移除当前节点
        path.remove(path.size() - 1);
        return false;
    }
    
    /**
     * 检查缓存是否过期
     */
    private boolean isCacheExpired() {
        Boolean hasKey = redisCache.hasKey(FULL_HIERARCHY_CACHE_KEY);
        return hasKey == null || !hasKey;
    }
} 