# 背景
文件名：2024-12-19_1_project-framework-analysis
创建于：2024-12-19_14:30:00
创建者：gy135
主分支：feature-basic-data-v1.21.0
任务分支：feature-basic-data-v1.21.0
Yolo模式：Off

# 任务描述
分析当前项目框架，输出为md文档，放到用户桌面

# 项目概览
项目名称：cn-ltgj-travel-hotel（酒店数据管理系统）
项目版本：3.8.5
项目描述：数据管理系统
工作空间：D:\tianjiugongxiang_gaoyu\hotel\hotel-data-system-new\cn-ltgj-travel-hotel

⚠️ 警告：永远不要修改此部分 ⚠️
遵循RIPER-5协议规则：
- 在RESEARCH模式下只进行信息收集和深入理解
- 禁止在RESEARCH模式下进行建议、实施、规划
- 必须创建任务文件记录分析过程
- 按照模式声明要求在每个响应开头声明当前模式
⚠️ 警告：永远不要修改此部分 ⚠️

# 分析

## 项目整体架构
这是一个基于Spring Cloud Alibaba的微服务架构酒店数据管理系统，采用前后端分离设计。

### 技术栈
**后端技术栈：**
- Spring Boot 2.7.18
- Spring Cloud 2021.0.8  
- Spring Cloud Alibaba 2021.0.5.0
- Nacos（服务发现与配置管理）
- MyBatis（ORM框架）
- Druid 1.2.16（数据库连接池）
- Quartz（定时任务）
- ElasticSearch（搜索引擎）
- JWT（身份认证）
- Swagger 3.0.0（API文档）
- Maven（构建工具）

**前端技术栈：**
- Vue.js 2.6.12
- Element UI 2.15.12
- Vue Router 3.4.9
- Vuex 3.6.0
- Axios 0.24.0
- ECharts 5.4.0（图表）

### 多环境配置
项目支持4个环境：
- dev（开发环境，默认激活）
- uat（用户验收测试）
- pre（预发布环境）
- prod（生产环境）

## 模块结构分析

### 后端模块
1. **ltgj-admin**：主应用模块
   - 包含Spring Boot启动类`RuoYiApplication`
   - 启用了缓存（@EnableCaching）
   - 集成ElasticSearch工具（@EnableESTools）
   - 应用配置文件

2. **ltgj-common**：公共模块
   - ltgj-common-core：核心工具类
   - ltgj-common-es：ElasticSearch相关功能

3. **ltgj-framework**：框架核心模块
   - 包含框架基础配置
   - 安全、拦截器等框架组件

4. **ltgj-generator**：代码生成器模块
   - MyBatis代码生成
   - 包含大量mapper文件（48个ivw相关mapper）
   - 支持Java、JS、Vue、SQL等模板生成

5. **ltgj-quartz**：定时任务模块
   - 基于Quartz的任务调度

6. **ltgj-system**：系统管理模块
   - 用户、角色、菜单、部门等系统功能
   - 包含16个系统相关mapper

### 前端模块
**ruoyi-ui**：Vue.js前端项目
- 基于RuoYi框架定制的酒店数据管理系统
- 模块化组件设计
- 支持多种平台数据：艺龙、美团、航信、锦江、华住、千淘、萤旅、东呈、红色加力、付迅

## 核心业务功能

### 系统管理功能
- 用户管理（用户CRUD、权限分配）
- 角色管理（角色权限配置）
- 菜单管理（动态菜单配置）
- 部门管理（组织架构）
- 岗位管理
- 字典管理（数据字典）
- 参数配置（系统参数）
- 通知公告

### 监控中心
- 在线用户监控
- 定时任务管理
- 数据监控（Druid）
- 服务器监控
- 缓存监控
- 操作日志
- 登录日志

### 酒店数据管理（IVW模块）
基于项目结构分析，包含以下子模块：
- bcity：城市管理
- brand：品牌管理  
- cityHotel：城市酒店管理
- corp：公司管理
- daolv：导游相关
- dongcheng：东呈酒店数据
- elong：艺龙数据对接
- fuxun：付迅数据
- gjcity：国际城市
- hbe：HBE相关
- hsjl：航信加力
- huazhu：华住数据
- infoHotel：酒店信息
- jdb：京东商旅
- jinjiang：锦江数据
- longteng：龙腾数据
- mappingCity：城市映射
- mappingJD：京东映射
- meituan：美团数据
- nation：国家管理
- province：省份管理
- qiantao：千淘数据
- recode：记录管理
- yinglv：萤旅数据

### 工具功能
- 代码生成器（支持多种模板）
- 表单构建（drag-drop设计器）
- 系统接口（Swagger文档）

## 项目特色

### 1. 微服务架构
- 基于Spring Cloud Alibaba
- Nacos作为注册中心和配置中心
- 支持服务发现和配置管理

### 2. 多数据源集成
项目集成了多个酒店平台的数据：
- OTA平台：艺龙、美团
- 酒店集团：锦江、华住、东呈
- 差旅平台：航信、千淘、萤旅等

### 3. 完善的权限体系
- 基于RBAC的权限模型
- 支持按钮级权限控制
- JWT token认证

### 4. 开发效率工具
- 集成代码生成器
- 丰富的组件库
- 标准化的开发模板

### 5. 监控运维
- 多维度系统监控
- 完整的日志体系
- 缓存监控和管理

## 部署架构特点

### 配置管理
- 使用Nacos进行统一配置管理
- 支持不同环境的配置隔离
- 动态配置更新

### 数据库设计
- 使用Druid连接池优化性能
- MyBatis作为ORM框架
- 支持分页查询（PageHelper）

### 缓存策略
- Spring Cache抽象
- 支持多种缓存实现

### 搜索功能
- 集成ElasticSearch
- 支持复杂搜索需求

# 提议的解决方案
无（仅进行框架分析）

# 当前执行步骤："1. 项目框架分析"

# 任务进度
[2024-12-19_14:30:00]
- 已完成：项目pom.xml分析
- 已完成：前端package.json分析  
- 已完成：启动类分析
- 已完成：前端入口文件分析
- 已完成：项目结构深度分析
- 更改：创建任务文件记录分析过程
- 原因：按照RIPER-5协议要求
- 阻碍因素：无
- 状态：成功

# 最终审查
待完成（需要生成桌面文档后进行最终审查）