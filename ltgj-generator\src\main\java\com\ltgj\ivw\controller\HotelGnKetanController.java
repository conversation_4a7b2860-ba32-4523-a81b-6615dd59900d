package com.ltgj.ivw.controller;

import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.SupplierElongCityMapping;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.service.hotel.updater.ThreadPoolManager;
import com.ltgj.ivw.service.idempotent.IdempotentResult;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.sdk.cozyTime.CozyTimeSdkApi;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20CitiesResponse;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnKetan;
import com.ltgj.supplier.cozyTime.HotelCozyTimeSupplierService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 科坦酒店信息 控制器新
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Slf4j
@RestController
@RequestMapping("/ivw/cozyTime")
public class HotelGnKetanController extends AbstractIvwController<HotelGnBase, HotelGnKetan, IHotelGnKetanService> {

    @Autowired
    private HotelCozyTimeSupplierService hotelCozyTimeSupplierService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private ISupplierElongCityMappingService supplierElongCityMappingService;

    @Autowired
    private CozyTimeSdkApi cozyTimeSdkApi;


    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 科坦供应商编码
     */
    private static final String SUPPLIER_CODE_KETAN = "KETAN";

    /**
     * 测试科坦接口连通性
     */
    @GetMapping("/test")
    public AjaxResult test() {
        try {
            // 使用CozyTimeSdkApi替代KetanApi
            CozyTimeHotelContentApi20CitiesResponse citiesResponse = this.cozyTimeSdkApi.getCityList("CN");
            if (citiesResponse != null) {
                return AjaxResult.success("科坦接口连接成功", citiesResponse);
            } else {
                return AjaxResult.error("科坦接口返回数据为空");
            }
        } catch (Exception e) {
            log.error("测试科坦接口异常", e);
            return AjaxResult.error("科坦接口连接失败: " + e.getMessage());
        }
    }

    /**
     * 同步科坦城市数据（异步执行）
     */
    @PostMapping("/syncCityData")
    public AjaxResult syncCityData(@RequestParam(defaultValue = "1") String idempotent,
                                   @RequestParam(defaultValue = "1") String mappingStrategy) {
        log.info("开始异步同步科坦城市数据");

        // 使用线程池异步执行
        this.threadPoolTaskExecutor.execute(() -> {
            try {
                log.info("科坦城市数据同步任务开始执行");
                long startTime = System.currentTimeMillis();

                // 直接调用hotelKetanSupplierService的syncCityData方法
                String result = this.hotelCozyTimeSupplierService.syncCityData(idempotent, mappingStrategy);
                log.info("科坦城市数据同步结果: {}", result);

                long endTime = System.currentTimeMillis();
                long totalTime = (endTime - startTime) / 1000;
                log.info("科坦城市数据同步完成！耗时：{}秒", totalTime);

            } catch (Exception e) {
                log.error("科坦城市数据同步异常", e);
            }
        });

        return AjaxResult.success("科坦城市数据同步任务已提交，正在后台执行");
    }

    /**
     * 同步科坦城市数据（同步执行，用于测试）
     */
    @PostMapping("/syncCityDataSync")
    public AjaxResult syncCityDataSync(@RequestParam(defaultValue = "1") String mappingStrategy,
                                       @RequestParam(defaultValue = "1") String idempotent) {
        try {
            String result = this.hotelCozyTimeSupplierService.syncCityData(idempotent, mappingStrategy);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("同步科坦城市数据异常", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }

    /**
     * 单独处理城市数据
     */
    @PostMapping("/processCityData")
    public AjaxResult processCityData(@RequestBody CozyTimeHotelContentApi20CitiesResponse citiesResponse,
                                      @RequestParam(defaultValue = "1") String idempotent) {
        try {
            int count = this.hotelCozyTimeSupplierService.processCityData(citiesResponse, idempotent);
            return AjaxResult.success("处理成功，共处理城市数据: " + count + "条");
        } catch (Exception e) {
            log.error("处理城市数据异常", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 单独获取科坦城市数据
     */
    @GetMapping("/getCityData")
    public AjaxResult getCityData(@RequestParam(defaultValue = "CN") String countryCode) {
        try {
            // 使用CozyTimeSdkApi获取城市数据
            CozyTimeHotelContentApi20CitiesResponse citiesResponse = this.cozyTimeSdkApi.getCityList(countryCode);
            if (citiesResponse != null) {
                return AjaxResult.success("获取科坦城市数据成功", citiesResponse);
            } else {
                return AjaxResult.error("获取科坦城市数据失败，返回为空");
            }
        } catch (Exception e) {
            log.error("获取科坦城市数据异常", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 单独处理城市映射
     */
    @PostMapping("/processCityMapping")
    public AjaxResult processCityMapping(@RequestParam(defaultValue = "1") String idempotent) {
        try {
            Object result = this.hotelCozyTimeSupplierService.processCityMapping(idempotent);

            if (result instanceof IdempotentResult) {
                IdempotentResult<?> idempotentResult = (IdempotentResult<?>) result;
                return AjaxResult.success(String.format("处理成功，共创建本地映射: %d条（新增%d条，更新%d条，跳过%d条）",
                        idempotentResult.getTotalCount(),
                        idempotentResult.getInsertCount(),
                        idempotentResult.getUpdateCount(),
                        idempotentResult.getSkipCount()));
            } else {
                int count = (int) result;
                return AjaxResult.success("处理成功，共创建本地映射: " + count + "条");
            }
        } catch (Exception e) {
            log.error("处理城市映射异常", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 单独处理城市映射（指定策略）
     *
     * @param mappingStrategy 映射策略：1-艺龙映射；2-名称映射；3-两者结合（默认）
     * @return 处理结果
     */
    @PostMapping("/processCityMappingWithStrategy")
    public AjaxResult processCityMappingWithStrategy(@RequestParam(defaultValue = "1") String mappingStrategy,
                                                     @RequestParam(defaultValue = "1") String idempotent) {
        try {
            // 校验策略参数
            if (!StringUtils.containsAny(mappingStrategy, "1", "2", "3")) {
                return AjaxResult.error("无效的映射策略参数，有效值为：1-艺龙映射；2-名称映射；3-两者结合");
            }

            String strategyName = "";
            switch (mappingStrategy) {
                case "1":
                    strategyName = "艺龙映射";
                    break;
                case "2":
                    strategyName = "名称映射";
                    break;
                case "3":
                    strategyName = "两者结合";
                    break;
            }

            Object result = this.hotelCozyTimeSupplierService.processCityMapping(mappingStrategy, idempotent);

            if (result instanceof IdempotentResult) {
                IdempotentResult<?> idempotentResult = (IdempotentResult<?>) result;
                return AjaxResult.success(String.format("处理成功，映射策略：%s，共创建本地映射: %d条（新增%d条，更新%d条，跳过%d条）",
                        strategyName,
                        idempotentResult.getTotalCount(),
                        idempotentResult.getInsertCount(),
                        idempotentResult.getUpdateCount(),
                        idempotentResult.getSkipCount()));
            } else {
                int count = (int) result;
                return AjaxResult.success("处理成功，映射策略：" + strategyName + "，共创建本地映射: " + count + "条");
            }
        } catch (Exception e) {
            log.error("处理城市映射异常", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 单独处理艺龙城市映射
     */
    @PostMapping("/processElongMapping")
    public AjaxResult processElongMapping(@RequestParam(defaultValue = "1") String idempotent) {
        try {
            Object result = this.hotelCozyTimeSupplierService.processElongCityMapping(idempotent);

            if (result instanceof IdempotentResult) {
                IdempotentResult<?> idempotentResult = (IdempotentResult<?>) result;
                return AjaxResult.success(String.format("处理成功，共创建艺龙映射: %d条（新增%d条，更新%d条，跳过%d条）",
                        idempotentResult.getTotalCount(),
                        idempotentResult.getInsertCount(),
                        idempotentResult.getUpdateCount(),
                        idempotentResult.getSkipCount()));
            } else {
                int count = (int) result;
                return AjaxResult.success("处理成功，共创建艺龙映射: " + count + "条");
            }
        } catch (Exception e) {
            log.error("处理艺龙城市映射异常", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 导入科坦和艺龙城市映射数据
     */
    @PreAuthorize("@ss.hasPermi('ivw:ketan:import')")
    @Log(title = "科坦和艺龙城市映射", businessType = BusinessType.IMPORT)
    @PostMapping("/importCityMapping")
    public AjaxResult importCityMapping(MultipartFile file, boolean updateSupport) throws Exception {
        String operName = this.getUsername();

        // 创建一个临时对象用于导入
        SupplierElongCityMapping template = new SupplierElongCityMapping();
        template.setSupplierCode(SUPPLIER_CODE_KETAN);

        String message = this.supplierElongCityMappingService.importExcel(file, updateSupport, operName);
        return this.success(message);
    }

    /**
     * 获取导入模板
     */
    @GetMapping("/cityMappingTemplate")
    public void getCityMappingTemplate(HttpServletResponse response) {
        try {
            // 创建一个空的模板对象，不需要查询数据库
            com.ltgj.common.utils.poi.ExcelUtil<SupplierElongCityMapping> util = new com.ltgj.common.utils.poi.ExcelUtil<>(SupplierElongCityMapping.class);
            util.importTemplateExcel(response, "科坦和艺龙城市映射数据");
        } catch (Exception e) {
            log.error("获取导入模板失败", e);
        }
    }

    // ==================== 科坦特有的扩展点重写 ====================


    // ==================== 科坦特有的业务方法 ====================

    /**
     * 平台酒店导入本地
     */
    @PreAuthorize("@ss.hasPermi('ivw:ketan:remove')")
    @Log(title = "科坦酒店信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = {Exception.class})
    public AjaxResult platToLocal(@PathVariable String[] ids) throws Exception {
        return hotelService.platToLocal(ids);
    }

    /**
     * 功能描述: 更新酒店信息
     *
     * @throws Exception
     */
    @Deprecated
    @GetMapping("/updateDataOld")
    public void updateData() throws Exception {
//        updateAll();
        if (HotelUpdateStatus.expStatusKetan == 1 || HotelUpdateStatus.expStatusKetan == 2) {
            throw new RuntimeException("不可并行任务进行中，不可开启请等待任务完成！");
        }

        String username = SecurityUtils.getUsername();

        // 获取线程池
        ThreadPoolExecutor executor = ThreadPoolManager.getInstance().getExecutor();
        executor.execute(() -> {
            this.hotelCozyTimeSupplierService.updateAll(username);
        });
        executor.execute(() -> {
            this.hotelCozyTimeSupplierService.getFacilities();
        });
        // 关闭线程池并等待所有任务完成
//        executor.shutdown();
//        try {
//            // 等待最多60秒，确保所有任务完成
//            if (!executor.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS)) {
//                log.warn("部分任务未在指定时间内完成");
//            }
//        } catch (InterruptedException e) {
//            log.error("等待线程池关闭时发生中断: ", e);
//        }finally {
//
//        }
    }

    /**
     * 功能描述: 更新科坦酒店国内信息
     * 由 写 hotel_info_cozytime
     * 改 写 hotel_gn_kentan
     *
     * @throws Exception
     */
    @GetMapping("/updateData")
    public void updateDataNew() throws Exception {
        RLock rLock = redissonClient.getLock("hotel_gn_ketan_update_data_new");
        if (!rLock.tryLock(10, TimeUnit.SECONDS)) {
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        try {
            // 获取线程池
            ThreadPoolExecutor executor = ThreadPoolManager.getInstance().getExecutor();
            executor.execute(() -> {
                this.hotelCozyTimeSupplierService.getFacilities();
            });
            this.hotelCozyTimeSupplierService.updateAllNew();
        } catch (Exception e) {
           log.error("处理科坦数据异常", e);
        } finally {

            try {
                rLock.unlock();
            } catch (Exception e) {

            }
        }


    }

    /**
     * 功能描述: 获取更新状态
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ivw:ketan:query')")
    @GetMapping("/getInfoStatus")
    public AjaxResult getInfoStatus() {
        Map<String, Object> map = new HashMap<>();

        String stepName = "";
        String expStatus = this.stringRedisTemplate.opsForValue().get(HotelUpdateStatus.expStatusKey);
        if (expStatus == null) {
            expStatus = "0";
        }
        switch (expStatus) {
            case "0":
                stepName = "初始状态";
                break;
            case "1":
                stepName = "获取酒店映射中";
                break;
            case "2":
                stepName = "更新酒店及映射信息中";
                break;
            case "3":
                stepName = "更新酒店及映射信息完成";
                break;
        }
        map.put("stepName", stepName);
        String GetHotelCountKey = this.stringRedisTemplate.opsForValue().get(HotelUpdateStatus.GetHotelCountKey);
        map.put("get", GetHotelCountKey);
        String UpdateHotelCountKey = this.stringRedisTemplate.opsForValue().get(HotelUpdateStatus.UpdateHotelCountKey);
        map.put("updateJS", UpdateHotelCountKey);
        String DeleteHotelCountKey = this.stringRedisTemplate.opsForValue().get(HotelUpdateStatus.DeleteHotelCountKey);
        map.put("deleteJS", DeleteHotelCountKey);
        return AjaxResult.success(map);
    }

    /**
     * 功能描述: 重置状态
     *
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('ivw:ketan:query')")
    @GetMapping("/resetStatus")
    public void resetStatus() {
        this.stringRedisTemplate.opsForValue().getAndDelete(HotelUpdateStatus.expStatusKey);
        this.stringRedisTemplate.opsForValue().getAndDelete(HotelUpdateStatus.GetHotelCountKey);
        this.stringRedisTemplate.opsForValue().getAndDelete(HotelUpdateStatus.UpdateHotelCountKey);
        this.stringRedisTemplate.opsForValue().getAndDelete(HotelUpdateStatus.DeleteHotelCountKey);
        log.info("### HotelGnKetanController.重置状态成功！");
    }

}