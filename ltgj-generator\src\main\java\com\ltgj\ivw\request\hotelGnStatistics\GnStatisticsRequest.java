package com.ltgj.ivw.request.hotelGnStatistics;

import com.ltgj.ivw.domain.HotelGnStatistics;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class GnStatisticsRequest extends HotelGnStatistics implements Serializable {

    /**
     * 类型：0-按日期查询，1-本月数据，2-本周数据
     */
    private Integer dateType;

    /**
     * 开始时间
     */
    private LocalDate startTime;

    /**
     * 结束时间
     */
    private LocalDate endTime;
}
