package com.ltgj.supplier.cozyTime;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.google.common.collect.Lists;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.ketan.HotelMapping;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelFacilityCozyTimeMapper;
import com.ltgj.ivw.mapper.HotelInfoCozytimeMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.service.idempotent.IdempotentResult;
import com.ltgj.ivw.service.impl.HotelCityServiceImpl;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.MeituanApi;
import com.ltgj.limiter.LtgjRateLimiter;
import com.ltgj.sdk.cozyTime.CozyTimeSdkApi;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelIdsResponse;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelInfoRequest;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelInfoResponse;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20CitiesResponse;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20FacilitiesResponse;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20HotelAndRoomMappingRequest;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20HotelAndRoomMappingResponse;
import com.ltgj.supplier.common.domain.FacilitiesInfo;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping;
import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import com.ltgj.supplier.common.gn.domain.HotelGnKetan;
import com.ltgj.supplier.common.gn.enums.HotelGnCustomerTypeEnum;
import com.ltgj.supplier.common.gn.mapper.HotelGnBaseMapper;
import com.ltgj.supplier.common.gn.service.HotelGnIdMappingService;
import com.ltgj.supplier.common.utils.IdUtil;
import com.ltgj.supplier.cozyTime.mapping.CityMappingStrategy;
import com.ltgj.supplier.cozyTime.mapping.CityMappingStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RLock;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 科坦酒店供应商服务类
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Slf4j
@Service
public class HotelCozyTimeSupplierService {

    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private IBCityService bCityService;

    @Autowired
    private ICityMappingService cityMappingService;

    @Autowired
    private IHotelCityMappingService hotelCityMappingService;

    @Autowired
    private ISupplierElongCityMappingService supplierElongCityMappingService;

    @Autowired
    private CityMappingStrategyFactory cityMappingStrategyFactory;

    @Autowired
    private CozyTimeSdkApi cozyTimeSdkApi;

    @Autowired
    private HotelFacilityCozyTimeMapper hotelFacilityCozyTimeMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private CozyTimeDataSyncService cozyTimeDataSyncService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ThreadPoolTaskExecutor allKetanHotelGnExecutor;

    @Autowired
    private HotelGnBaseMapper hotelGnBaseMapper;

    @Autowired
    private HotelGnIdMappingService hotelGnIdMappingService;

    @Autowired
    private HotelCityServiceImpl hotelCityServiceImpl;

    @Autowired
    private IHotelCityGeoService hotelCityGeoService;

    @Autowired
    private IGeoCityGeoService geoCityGeoService;

    @Autowired
    private IHotelGnGeoMappingService hotelGnGeoMappingService;

    @Autowired
    private LtgjRateLimiter limiter;

    private IdUtil sequence = new IdUtil();

    /**
     * 城市ID生成器
     */
    private AtomicInteger cityIdGenerator = new AtomicInteger(10000);

    /**
     * 科坦供应商编码
     */
    public static final String SUPPLIER_CODE_KETAN = "KETAN";

    /**
     * 城市映射策略常量
     */
    public static final String MAPPING_STRATEGY_ELONG = "1"; // 艺龙映射策略
    public static final String MAPPING_STRATEGY_NAME = "2";  // 名称映射策略
    public static final String MAPPING_STRATEGY_BOTH = "3";  // 两种策略结合
    @Autowired
    private HotelInfoCozytimeMapper hotelInfoCozytimeMapper;

    /**
     * 同步科坦城市数据
     *
     * @param idempotent      是否幂等
     * @param mappingStrategy 映射策略
     * @return 处理结果
     */
    public String syncCityData(String idempotent, String mappingStrategy) {
        try {
            // 1. 调用API获取科坦城市数据（使用CozyTimeSdkApi替代KetanApi）
            CozyTimeHotelContentApi20CitiesResponse citiesResponse = this.cozyTimeSdkApi.getCityList("CN");
            if (citiesResponse == null) {
                return "获取科坦城市数据失败";
            }

            // 2. 处理城市数据 和 平台酒店-国内-商圈映射数据
            IdempotentResult<HotelCity> cityResult = null;
            int cityCount = 0;
            int hotelGnGeoMappingCount = 0;
            if (StringUtils.equals(idempotent, "1")) {
                // 2.1 处理城市数据 使用幂等框架处理，会返回详细的统计信息
                List<HotelCity> hotelCities = new ArrayList<>();
                TransformUtils.transformAndCollectTwoParam(
                        citiesResponse.getCities(),
                        citiesResponse.getCountryCode(),
                        this::createHotelCity,
                        hotelCities,
                        e -> log.error("处理科坦城市数据异常: {}",
                                citiesResponse.getCities().stream()
                                        .findFirst()
                                        .map(CozyTimeHotelContentApi20CitiesResponse.City::getNameCN)
                                        .orElse("Unknown"), e) // Error handler
                );
                cityResult = this.cozyTimeDataSyncService.syncHotelCityData(hotelCities);
                cityCount = hotelCities.size();
            } else {
                // 使用旧方式，只返回总数
                cityCount = this.processCityData(citiesResponse, idempotent);
            }

            List<List<CozyTimeHotelContentApi20CitiesResponse.City>> partition = Lists.partition(citiesResponse.getCities(), 2);
            for (List<CozyTimeHotelContentApi20CitiesResponse.City> cities : partition) {
                // 2.2处理平台酒店-国内-商圈映射数据
                List<HotelGnGeoMapping> hotelGnGeoMappings = Lists.newArrayList();
                TransformUtils.transformAndCollectAddAllParam(
                        cities,
                        PlatEnum.PLAT_KT.getValue(),
                        this::createGnGeoMapping,
                        hotelGnGeoMappings,
                        e -> log.error("处理平台酒店-国内-商圈映射数据异常: {}",
                                citiesResponse.getCities().stream()
                                        .findFirst()
                                        .map(CozyTimeHotelContentApi20CitiesResponse.City::getNameCN)
                                        .orElse("Unknown"), e) // Error handler
                );

                if (CollectionUtils.isNotEmpty(hotelGnGeoMappings)) {
                    // 2.2.1 addOrUpdate
                    hotelGnGeoMappingCount = hotelGnGeoMappingService.batchInsertOrUpdate(hotelGnGeoMappings);
                }
            }

            // 3. 处理城市映射（使用两种策略结合）
            IdempotentResult<CityMapping> mappingResult = null;
            int mappingCount = 0;
            Object mappingObj = this.processCityMapping(StringUtils.defaultString(mappingStrategy, MAPPING_STRATEGY_ELONG), idempotent);
            if (mappingObj instanceof IdempotentResult) {
                mappingResult = (IdempotentResult<CityMapping>) mappingObj;
                mappingCount = mappingResult.getTotalCount();
            } else {
                mappingCount = (int) mappingObj;
            }

            // 4. 处理与艺龙城市的映射
            IdempotentResult<HotelCityMapping> elongMappingResult = null;
            int elongMappingCount = 0;
            Object elongObj = this.processElongCityMapping(idempotent);
            if (elongObj instanceof IdempotentResult) {
                elongMappingResult = (IdempotentResult<HotelCityMapping>) elongObj;
                elongMappingCount = elongMappingResult.getTotalCount();
            } else {
                elongMappingCount = (int) elongObj;
            }

            // 构建更详细的返回信息
            StringBuilder result = new StringBuilder("同步科坦城市数据成功：");

            // 城市数据统计
            if (cityResult != null) {
                result.append(String.format("处理城市数据%d条（新增%d条，更新%d条，跳过%d条）",
                        cityResult.getTotalCount(),
                        cityResult.getInsertCount(),
                        cityResult.getUpdateCount(),
                        cityResult.getSkipCount()));
            } else {
                result.append(String.format("处理城市数据%d条", cityCount));
            }

            // 商圈映射结果
            result.append(String.format("处理平台酒店-国内-商圈映射数据%d条", hotelGnGeoMappingCount));

            // 本地映射统计
            result.append("，");
            if (mappingResult != null) {
                result.append(String.format("本地映射%d条（新增%d条，更新%d条，跳过%d条）",
                        mappingResult.getTotalCount(),
                        mappingResult.getInsertCount(),
                        mappingResult.getUpdateCount(),
                        mappingResult.getSkipCount()));
            } else {
                result.append(String.format("本地映射%d条", mappingCount));
            }

            // 艺龙映射统计
            result.append("，");
            if (elongMappingResult != null) {
                result.append(String.format("艺龙映射%d条（新增%d条，更新%d条，跳过%d条）",
                        elongMappingResult.getTotalCount(),
                        elongMappingResult.getInsertCount(),
                        elongMappingResult.getUpdateCount(),
                        elongMappingResult.getSkipCount()));
            } else {
                result.append(String.format("艺龙映射%d条", elongMappingCount));
            }

            return result.toString();
        } catch (Exception e) {
            log.error("同步科坦城市数据异常", e);
            return "同步科坦城市数据异常: " + e.getMessage();
        }
    }

    /**
     * 处理城市数据
     *
     * @param citiesResponse 科坦城市数据
     * @param idempotent
     * @return 处理的城市数量
     */
    public int processCityData(CozyTimeHotelContentApi20CitiesResponse citiesResponse, String idempotent) {
        int count = 0;
        if (citiesResponse == null || CollectionUtils.isEmpty(citiesResponse.getCities())) {
            return count;
        }

        String countryCode = citiesResponse.getCountryCode();

        for (CozyTimeHotelContentApi20CitiesResponse.City city : citiesResponse.getCities()) {
            try {
                HotelCity hotelCity = this.createHotelCity(city, countryCode);
                if (StringUtils.equals(idempotent, "1")) {
                    this.cozyTimeDataSyncService.syncHotelCityData(Collections.singletonList(hotelCity));
                } else {
                    this.hotelCityService.insertHotelCity(hotelCity);
                }
                count++;

                // 如果有区县数据，也进行处理
//                if (CollectionUtils.isNotEmpty(city.getDistricts())) {
//                    for (CozyTimeHotelContentApi20CitiesResponse.District district : city.getDistricts()) {
//                        HotelCity districtCity = createDistrictHotelCity(city, district, countryCode);
//                        hotelCityService.insertHotelCity(districtCity);
//                        count++;
//                    }
//                }
            } catch (Exception e) {
                log.error("处理科坦城市数据异常: {}", city.getNameCN(), e);
            }
        }

        return count;
    }


    public List<HotelGnGeoMapping> createGnGeoMapping(CozyTimeHotelContentApi20CitiesResponse.City city, String context) {
        List<HotelGnGeoMapping> hotelGnGeoMappingList = Lists.newArrayList();
        List<CozyTimeHotelContentApi20CitiesResponse.Pois> poisList = city.getPois();
        if (CollectionUtils.isEmpty(poisList)) {
            log.warn("没有商圈数据: {}", city.getNameCN());
            return null;
        }
        for (CozyTimeHotelContentApi20CitiesResponse.Pois pois : poisList) {
            HotelCityMapping hotelCityMapping = new HotelCityMapping();
            hotelCityMapping.setPlatNum(city.getCityCode());
            hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_KT.getValue()));
            // 通过平台城市id和平台编码拿到打底城市id
            List<HotelCityMapping> hotelCityMappings = hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
            String baseCityId = CollectionUtils.isNotEmpty(hotelCityMappings) ? hotelCityMappings.get(0).getLocalId() : StringUtils.EMPTY;
            GeoCityGeo geoCityGeo = GeoCityGeo.builder().cityId(baseCityId).geoName(pois.getPoiNameCN()).build();
            // 从base库查询对应商圈数据
            List<GeoCityGeo> geoCityGeos = geoCityGeoService.selectGeoCityGeoListBybase(geoCityGeo);
            if (CollectionUtils.isNotEmpty(geoCityGeos)) {
                hotelGnGeoMappingList.add(HotelGnGeoMapping.builder()
                        .id(city.getCityCode()+"_" + sequence.nextId())
                        .platformId(context)
                        .platformCityId(city.getCityCode())
                        .platformGeoId(pois.getPoiId())
                        .platformGeoName(pois.getPoiNameCN())
                        .mappingPlatformId(PlatEnum.PLAT_BASE.getValue())
                        .mappingCityId(geoCityGeos.get(0).getCityId())
                        .mappingGeoId(geoCityGeos.get(0).getGeoId())
                        .mappingGeoName(geoCityGeos.get(0).getGeoName())
                        .remark("科坦-打底商圈映射数据")
                        .createBy("system")
                        .createTime(DateUtils.getNowDate())
                        .updateBy("system")
                        .isDelete(Boolean.FALSE)
                        .build());
            }
        }
        return hotelGnGeoMappingList;
    }

    /**
     * 创建城市对象
     *
     * @param city        科坦城市
     * @param countryCode 国家编码
     * @return 酒店城市对象
     */
    public HotelCity createHotelCity(CozyTimeHotelContentApi20CitiesResponse.City city, String countryCode) {
        HotelCity hotelCity = new HotelCity();
        // 设置平台标识
        hotelCity.setReserve1(PlatEnum.PLAT_KT.getValue());

        // 设置国家信息
        hotelCity.setCountryId(StringUtils.defaultString(countryCode,"CN"));
        hotelCity.setCountryName("中国");
        hotelCity.setCountryNameEn("CHINA");

        // 设置省份信息
        hotelCity.setProvinceId(city.getProvinceCode());
        hotelCity.setProvinceName(city.getProvinceNameCN());
        hotelCity.setProvinceNameEn(city.getProvinceName());

        // 设置城市信息
        hotelCity.setCityId(city.getCityCode());
        hotelCity.setCityName(city.getNameCN());
        hotelCity.setCityNameEn(city.getName());

        // 处理商圈数据
        if (city.getPois() != null && !city.getPois().isEmpty()) {
            log.info("开始处理城市 {} 的商圈数据，商圈数量：{}", city.getNameCN(), city.getPois().size());
            
            List<HotelCityGeo> geoList = new ArrayList<>();
            for (CozyTimeHotelContentApi20CitiesResponse.Pois poi : city.getPois()) {
                HotelCityGeo cityGeo = new HotelCityGeo();
                cityGeo.setInterfacePlatId(PlatEnum.PLAT_KT.getValue());
                cityGeo.setCityId(city.getCityCode());
                cityGeo.setPoiId(poi.getPoiId());
                cityGeo.setPoiName(poi.getPoiName());
                cityGeo.setPoiNameCn(poi.getPoiNameCN());
                cityGeo.setRemark("科坦平台商圈数据");
                //
                if (StringUtils.isAnyEmpty(poi.getPoiName(), city.getCityCode())) {
                    log.warn("商圈数据异常：{}", poi);
                    continue;
                }
                // 设置主键 - 对商圈名称进行清理和优化
                String cleanPoiName = this.generateCleanPoiId(poi.getPoiName());
                cityGeo.setId(PlatEnum.PLAT_KT.getValue()+"_"+city.getCityCode()+"_"+cleanPoiName);
                
                geoList.add(cityGeo);

            }
            
            try {
                // 批量插入商圈数据
                int insertCount = hotelCityGeoService.batchInsertOrUpdate(geoList);
                log.info("城市 {} 商圈数据插入完成，成功插入：{} 条，样例ID：{}", 
                    city.getNameCN(), insertCount, 
                    geoList.isEmpty() ? "无" : geoList.get(0).getId());
            } catch (Exception e) {
                log.error("城市 {} 商圈数据插入失败，商圈数量：{}，错误信息：{}", 
                    city.getNameCN(), geoList.size(), e.getMessage(), e);
            }

        } else {
            log.debug("城市 {} 没有商圈数据", city.getNameCN());
        }

        // 时区信息 存入9
        if (StringUtils.isNotEmpty(city.getTimeZone())) {
            hotelCity.setReserve9(city.getTimeZone());
        }

        return hotelCity;
    }

    /**
     * todo
     * 创建区县城市对象
     *
     * @param city        科坦城市
     * @param district    区县
     * @param countryCode 国家编码
     * @return 酒店城市对象
     */
    @Deprecated
    private HotelCity createDistrictHotelCity(CozyTimeHotelContentApi20CitiesResponse.City city, CozyTimeHotelContentApi20CitiesResponse.District district, String countryCode) {
        HotelCity hotelCity = new HotelCity();
        // 设置平台标识
        hotelCity.setReserve1(PlatEnum.PLAT_KT.getValue());
        hotelCity.setReserve6("2"); // 国际城市标记

        // 设置国家信息
        hotelCity.setCountryId(countryCode);
        hotelCity.setCountryName(city.getNameCN());

        // 设置省份信息
        hotelCity.setProvinceId(city.getProvinceCode());
        hotelCity.setProvinceName(city.getProvinceNameCN());
        hotelCity.setProvinceNameEn(city.getProvinceName());

        // 设置城市信息（上级城市）
        hotelCity.setCityId(city.getCityCode());
        hotelCity.setCityName(city.getNameCN());
        hotelCity.setCityNameEn(city.getName());

        // 设置区县信息
        // 为区县生成一个唯一ID
        String districtId = city.getCityCode() + "_" + this.cityIdGenerator.getAndIncrement();
        hotelCity.setLocationId(districtId);
        hotelCity.setLocationName(district.getDistrictNameCN());
        hotelCity.setLocationNameEn(district.getDistrictName());

        // 时区信息
        if (StringUtils.isNotEmpty(city.getTimeZone())) {
            hotelCity.setReserve3(city.getTimeZone());
        }

        return hotelCity;
    }

    /**
     * 处理城市映射（与本地打底数据）
     *
     * @param mappingStrategy 映射策略：1-艺龙映射；2-名称映射；3-两者结合（默认）
     * @param idempotent      是否幂等
     * @return 处理的映射数量或详细结果
     */
    public Object processCityMapping(String mappingStrategy, String idempotent) {
        int count = 0;
        List<CityMapping> allMappings = new ArrayList<>();

        try {
            log.info("开始处理科坦城市与本地城市的映射，策略：{}", this.getMappingStrategyName(mappingStrategy));

            // 1. 查询所有科坦城市
            HotelCity queryCity = new HotelCity();
            queryCity.setReserve1(PlatEnum.PLAT_KT.getValue());
            List<HotelCity> ketanCities = this.hotelCityService.selectHotelCityList(queryCity);

            if (CollectionUtils.isEmpty(ketanCities)) {
                log.warn("未查询到科坦城市数据");
                return StringUtils.equals(idempotent, "1") ?
                        this.cozyTimeDataSyncService.syncCityMappingData(Collections.emptyList()) : 0;
            }

            log.info("查询到科坦城市数据：{}条", ketanCities.size());

            // 2. 构建科坦城市编码到城市对象的映射
            Map<String, HotelCity> ketanCityMap = new HashMap<>();
            // 记录已映射的科坦城市ID，避免重复映射
            Set<String> mappedKetanCityIds = new HashSet<>();

            for (HotelCity city : ketanCities) {
                if (StringUtils.isNotEmpty(city.getCityId())) {
                    ketanCityMap.put(city.getCityId(), city);
                }
            }

            // 根据映射策略选择映射方式
            if (StringUtils.equals(mappingStrategy, MAPPING_STRATEGY_ELONG) || StringUtils.equals(mappingStrategy, MAPPING_STRATEGY_BOTH)) {
                // 方式一：通过艺龙映射关系建立科坦与本地的映射
                List<CityMapping> elongMappings = this.processElongCityMappingForLocalDetailed(ketanCityMap, mappedKetanCityIds);
                allMappings.addAll(elongMappings);
                count += elongMappings.size();
                log.info("通过艺龙映射关系创建科坦与本地城市映射：{}条", elongMappings.size());
            }

            if (StringUtils.equals(mappingStrategy, MAPPING_STRATEGY_NAME) || StringUtils.equals(mappingStrategy, MAPPING_STRATEGY_BOTH)) {
                // 方式二：通过城市名称匹配建立科坦与本地的映射
                List<CityMapping> nameMappings = this.processNameCityMappingDetailed(ketanCities, mappedKetanCityIds);
                allMappings.addAll(nameMappings);
                count += nameMappings.size();
                log.info("通过城市名称匹配创建科坦与本地城市映射：{}条", nameMappings.size());
            }

            log.info("科坦与本地城市映射处理完成，共创建映射：{}条", count);

            // 如果是幂等模式，则使用幂等服务处理并返回详细结果
            if (StringUtils.equals(idempotent, "1") && !allMappings.isEmpty()) {
                return this.cozyTimeDataSyncService.syncCityMappingData(allMappings);
            }

            return count;
        } catch (Exception e) {
            log.error("处理科坦城市与本地城市的映射异常", e);
            throw e;
        }
    }

    /**
     * 处理城市映射（与本地打底数据），默认使用两种策略结合
     *
     * @param idempotent 是否幂等
     * @return 处理的映射数量或详细结果
     */
    public Object processCityMapping(String idempotent) {
        return this.processCityMapping(MAPPING_STRATEGY_BOTH, idempotent);
    }

    /**
     * 获取映射策略名称
     *
     * @param mappingStrategy 映射策略
     * @return 映射策略名称
     */
    private String getMappingStrategyName(String mappingStrategy) {
        switch (mappingStrategy) {
            case MAPPING_STRATEGY_ELONG:
                return "艺龙映射";
            case MAPPING_STRATEGY_NAME:
                return "名称映射";
            case MAPPING_STRATEGY_BOTH:
                return "艺龙映射+名称映射";
            default:
                return "未知策略(" + mappingStrategy + ")";
        }
    }

    /**
     * 通过城市名称匹配建立科坦与本地的映射（兜底方案）
     *
     * @param ketanCities        科坦城市列表
     * @param mappedKetanCityIds 已映射的科坦城市ID集合
     * @return 处理的映射数量
     */
    private int processNameCityMapping(List<HotelCity> ketanCities, Set<String> mappedKetanCityIds) {
        int count = 0;

        try {
            // 过滤掉已映射的城市
            List<HotelCity> unmappedCities = ketanCities.stream().filter(city -> StringUtils.isNotEmpty(city.getCityId()) && !mappedKetanCityIds.contains(city.getCityId()) && StringUtils.isNotEmpty(city.getCountryName()) && StringUtils.isNotEmpty(city.getCityName())).collect(Collectors.toList());

            log.info("未通过艺龙映射的科坦城市数量：{}条", unmappedCities.size());

            // 按国家名称分组
            Map<String, List<HotelCity>> hotelCityMap = unmappedCities.stream().collect(Collectors.groupingBy(HotelCity::getCountryName));

            // 处理每个国家的城市映射
            for (Map.Entry<String, List<HotelCity>> entry : hotelCityMap.entrySet()) {
                String nationName = entry.getKey();
                // 处理特殊国家名称
                if (StringUtils.equals(nationName, "阿拉伯联合酋长国")) {
                    nationName = "阿联酋";
                }

                List<HotelCity> hotelCityList = entry.getValue();

                // 按城市中文名称分组
                Map<String, List<HotelCity>> hotelNameGroup = hotelCityList.stream().filter(t -> StringUtils.isNotEmpty(t.getCityName())).collect(Collectors.groupingBy(HotelCity::getCityName));

                // 查询本地国家城市数据
                List<BCity> bCityList = this.queryCityByNation(nationName);

                if (CollectionUtils.isEmpty(bCityList)) {
                    continue;
                }

                // 处理每个城市组的映射
                for (Map.Entry<String, List<HotelCity>> cityEntry : hotelNameGroup.entrySet()) {
                    String cityName = cityEntry.getKey();
                    List<HotelCity> cities = cityEntry.getValue();

                    // 找到对应的本地城市
                    for (BCity bCity : bCityList) {
                        if (cityName.equals(bCity.getCityName())) {
                            // 为每个城市创建映射
                            for (HotelCity hotelCity : cities) {
                                this.saveDistrictInfoMapping(bCity, hotelCity);
                                count++;

                                // 记录已映射的科坦城市ID
                                mappedKetanCityIds.add(hotelCity.getCityId());
                            }
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("通过城市名称匹配建立科坦与本地的映射异常", e);
        }

        return count;
    }

    /**
     * 通过艺龙映射关系建立科坦与本地的映射（返回映射列表而不是直接插入）
     *
     * @param ketanCityMap       科坦城市映射
     * @param mappedKetanCityIds 已映射的科坦城市ID集合
     * @return 映射列表
     */
    private List<CityMapping> processElongCityMappingForLocalDetailed(Map<String, HotelCity> ketanCityMap, Set<String> mappedKetanCityIds) {
        List<CityMapping> mappings = new ArrayList<>();

        try {
            // 1. 查询供应商和艺龙城市映射关系表
            SupplierElongCityMapping queryMapping = new SupplierElongCityMapping();
            queryMapping.setSupplierCode(SUPPLIER_CODE_KETAN);
            List<SupplierElongCityMapping> ketanElongMappings = this.supplierElongCityMappingService.selectSupplierElongCityMappingList(queryMapping);

            if (CollectionUtils.isEmpty(ketanElongMappings)) {
                log.warn("未查询到科坦和艺龙城市映射关系数据，无法创建映射");
                return mappings;
            }

            // 2. 遍历映射关系，为每个科坦城市创建与本地城市的映射
            for (SupplierElongCityMapping mapping : ketanElongMappings) {
                String ketanCityCode = mapping.getSupplierCityCode();
                String elongCityCode = mapping.getElongCityCode();

                if (StringUtils.isEmpty(ketanCityCode) || StringUtils.isEmpty(elongCityCode)) {
                    log.warn("科坦城市编码或艺龙城市编码为空，跳过此条记录：{}", mapping);
                    continue;
                }

                // 获取科坦城市对象
                HotelCity ketanCity = ketanCityMap.get(ketanCityCode);
                if (ketanCity == null) {
                    log.warn("未找到科坦城市编码{}对应的城市数据，跳过此条记录", ketanCityCode);
                    continue;
                }

                try {
                    // 查询艺龙城市映射，获取localId
                    HotelCityMapping elongQuery = new HotelCityMapping();
                    elongQuery.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_EL.getValue()));
                    elongQuery.setPlatNum(elongCityCode);
                    List<HotelCityMapping> elongMappings = this.hotelCityMappingService.selectHotelCityMappingList(elongQuery);

                    if (CollectionUtils.isEmpty(elongMappings)) {
                        log.warn("未找到艺龙城市编码{}对应的映射数据，跳过此条记录", elongCityCode);
                        continue;
                    }

                    // 创建科坦到本地的映射
                    CityMapping ketanMapping = new CityMapping();
                    ketanMapping.setInterfacePlat(PlatEnum.PLAT_KT.getValue());
                    ketanMapping.setCplx("10910");
                    ketanMapping.setLocalId(elongMappings.get(0).getLocalId());
                    ketanMapping.setPlatId(ketanCity.getCityId());

                    mappings.add(ketanMapping);

                    // 记录已映射的科坦城市ID
                    mappedKetanCityIds.add(ketanCity.getCityId());
                } catch (Exception e) {
                    log.error("创建科坦城市映射失败：ketanCityCode={}, elongCityCode={}", ketanCityCode, elongCityCode, e);
                }
            }
        } catch (Exception e) {
            log.error("通过艺龙映射关系建立科坦与本地的映射异常", e);
        }

        return mappings;
    }

    /**
     * 通过城市名称匹配建立科坦与本地的映射（兜底方案）- 返回映射列表
     *
     * @param ketanCities        科坦城市列表
     * @param mappedKetanCityIds 已映射的科坦城市ID集合
     * @return 映射列表
     */
    private List<CityMapping> processNameCityMappingDetailed(List<HotelCity> ketanCities, Set<String> mappedKetanCityIds) {
        List<CityMapping> mappings = new ArrayList<>();

        try {
            // 过滤掉已映射的城市
            List<HotelCity> unmappedCities = ketanCities.stream().filter(city ->
                            StringUtils.isNotEmpty(city.getCityId()) &&
                                    !mappedKetanCityIds.contains(city.getCityId()) &&
                                    StringUtils.isNotEmpty(city.getCountryName()) &&
                                    StringUtils.isNotEmpty(city.getCityName()))
                    .collect(Collectors.toList());

            log.info("未通过艺龙映射的科坦城市数量：{}条", unmappedCities.size());

            // 按国家名称分组
            Map<String, List<HotelCity>> hotelCityMap = unmappedCities.stream()
                    .collect(Collectors.groupingBy(HotelCity::getCountryName));

            // 处理每个国家的城市映射
            for (Map.Entry<String, List<HotelCity>> entry : hotelCityMap.entrySet()) {
                String nationName = entry.getKey();
                // 处理特殊国家名称
                if (StringUtils.equals(nationName, "阿拉伯联合酋长国")) {
                    nationName = "阿联酋";
                }

                List<HotelCity> hotelCityList = entry.getValue();

                // 按城市中文名称分组
                Map<String, List<HotelCity>> hotelNameGroup = hotelCityList.stream()
                        .filter(t -> StringUtils.isNotEmpty(t.getCityName()))
                        .collect(Collectors.groupingBy(HotelCity::getCityName));

                // 查询本地国家城市数据
                List<BCity> bCityList = this.queryCityByNation(nationName);

                if (CollectionUtils.isEmpty(bCityList)) {
                    continue;
                }

                // 处理每个城市组的映射
                for (Map.Entry<String, List<HotelCity>> cityEntry : hotelNameGroup.entrySet()) {
                    String cityName = cityEntry.getKey();
                    List<HotelCity> cities = cityEntry.getValue();

                    // 找到对应的本地城市
                    for (BCity bCity : bCityList) {
                        if (cityName.equals(bCity.getCityName())) {
                            // 为每个城市创建映射
                            for (HotelCity hotelCity : cities) {
                                CityMapping mapping = new CityMapping();
                                mapping.setId(java.util.UUID.randomUUID().toString());
                                mapping.setInterfacePlat(PlatEnum.PLAT_KT.getValue());
                                mapping.setCplx("10910");
                                mapping.setLocalId(bCity.getId());
                                mapping.setPlatId(hotelCity.getCityId());

                                mappings.add(mapping);

                                // 记录已映射的科坦城市ID
                                mappedKetanCityIds.add(hotelCity.getCityId());
                            }
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("通过城市名称匹配建立科坦与本地的映射异常", e);
        }

        return mappings;
    }

    /**
     * 处理与艺龙城市的映射
     *
     * @param idempotent 是否幂等
     * @return 处理的映射数量或详细结果
     */
    public Object processElongCityMapping(String idempotent) {
        int count = 0;
        List<HotelCityMapping> allMappings = new ArrayList<>();

        try {
            log.info("开始处理科坦与艺龙城市的映射（使用供应商映射表）");

            // 1. 查询所有科坦城市
            HotelCity queryCity = new HotelCity();
            queryCity.setReserve1(PlatEnum.PLAT_KT.getValue());
            List<HotelCity> ketanCities = this.hotelCityService.selectHotelCityList(queryCity);

            if (CollectionUtils.isEmpty(ketanCities)) {
                log.warn("未查询到科坦城市数据");
                return StringUtils.equals(idempotent, "1") ?
                        this.cozyTimeDataSyncService.syncHotelCityMappingData(Collections.emptyList()) : 0;
            }

            log.info("查询到科坦城市数据：{}条", ketanCities.size());

            // 2. 查询供应商和艺龙城市映射关系表
            SupplierElongCityMapping queryMapping = new SupplierElongCityMapping();
            queryMapping.setSupplierCode(SUPPLIER_CODE_KETAN);
            List<SupplierElongCityMapping> cityMappings = this.supplierElongCityMappingService.selectSupplierElongCityMappingList(queryMapping);

            if (CollectionUtils.isEmpty(cityMappings)) {
                log.warn("未查询到科坦和艺龙城市映射关系数据，无法创建映射");
                return StringUtils.equals(idempotent, "1") ?
                        this.cozyTimeDataSyncService.syncHotelCityMappingData(Collections.emptyList()) : 0;
            }

            log.info("查询到科坦和艺龙城市映射关系数据：{}条", cityMappings.size());

            // 3. 构建科坦城市编码到城市对象的映射
            Map<String, HotelCity> ketanCityMap = new HashMap<>();
            for (HotelCity city : ketanCities) {
                if (StringUtils.isNotEmpty(city.getCityId())) {
                    ketanCityMap.put(city.getCityId(), city);
                }
            }

            // 4. 创建HotelCityMapping映射关系
            for (SupplierElongCityMapping mapping : cityMappings) {
                String elongCityCode = mapping.getElongCityCode();

                if (StringUtils.isEmpty(elongCityCode)) {
                    log.warn("艺龙城市编码为空，跳过此条记录：{}", mapping);
                    continue;
                }

                try {
                    // 查询艺龙城市映射，获取localId
                    HotelCityMapping elongQuery = new HotelCityMapping();
                    elongQuery.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_EL.getValue()));
                    elongQuery.setPlatNum(elongCityCode);
                    List<HotelCityMapping> elongCityMappings = this.hotelCityMappingService.selectHotelCityMappingList(elongQuery);

                    if (CollectionUtils.isEmpty(elongCityMappings)) {
                        log.warn("未找到艺龙城市编码{}对应的映射数据，跳过此条记录", elongCityCode);
                        continue;
                    }

                    // 根据映射类型选择映射策略
                    CityMappingStrategy mappingStrategy = this.cityMappingStrategyFactory.getStrategy(mapping.getMappingType());

                    // 使用策略创建映射
                    HotelCityMapping ketanMapping = mappingStrategy.createMapping(mapping, ketanCityMap, elongCityMappings);

                    if (ketanMapping != null) {
                        allMappings.add(ketanMapping);
                    }
                } catch (Exception e) {
                    log.error("创建科坦城市映射失败：mapping={}", mapping, e);
                }
            }

            log.info("科坦与艺龙城市映射处理完成，共创建映射：{}条", allMappings.size());

            // 如果是幂等模式，使用幂等服务处理并返回详细结果
            if (StringUtils.equals(idempotent, "1") && !allMappings.isEmpty()) {
                return this.cozyTimeDataSyncService.syncHotelCityMappingData(allMappings);
            }

            // 非幂等模式，直接插入
            if (!allMappings.isEmpty()) {
                this.insertBatchMappings(allMappings);
            }

            return allMappings.size();

        } catch (Exception e) {
            log.error("处理科坦与艺龙城市映射异常", e);
            throw e;
        }
    }

    /**
     * 批量插入城市映射
     *
     * @param mappings 映射列表
     */
    private void insertBatchMappings(List<HotelCityMapping> mappings) {
        for (HotelCityMapping mapping : mappings) {
            try {
                this.hotelCityMappingService.insertHotelCityMapping(mapping);
            } catch (Exception e) {
                log.error("插入城市映射失败: {}", mapping.getCityName(), e);
            }
        }
    }

    /**
     * 查询国家城市数据
     *
     * @param nationName 国家名称
     * @return 城市列表
     */
    private List<BCity> queryCityByNation(String nationName) {
        BCity query = new BCity();
        query.setNation(nationName);
        query.setInternational(0);
        return this.bCityService.selectBCityJoinGeoProvinceList(query);
    }

    /**
     * 保存城市映射关系
     *
     * @param bCity     本地城市
     * @param hotelCity 酒店城市
     */
    private void saveDistrictInfoMapping(BCity bCity, HotelCity hotelCity) {
        CityMapping mapping = new CityMapping();
        mapping.setId(java.util.UUID.randomUUID().toString());
        mapping.setInterfacePlat(PlatEnum.PLAT_KT.getValue());
        mapping.setCplx("10910");
        mapping.setLocalId(bCity.getId());
        mapping.setPlatId(hotelCity.getCityId());
        this.cityMappingService.insertCityMapping(mapping);
    }

    /**
     * 获取标准化的城市名称（用于匹配）
     *
     * @param cityName 城市名称
     * @return 标准化后的城市名称
     */
    private String getMathStr(String cityName) {
        if (StringUtils.isEmpty(cityName)) {
            return "";
        }
        // 去除空格和特殊字符
        return cityName.replaceAll("\\s+", "").replaceAll("[\\(\\)（）]", "");
    }

    public void updateAll() {
        // 调用updateAll方法，传入null参数
        this.updateAll(null);
    }

    public void updateAll(String username) {
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.expStatusKey, "0");
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.GetHotelCountKey, "0");
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.UpdateHotelCountKey, "0");
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.DeleteHotelCountKey, "0");
        HotelCity cityParam = new HotelCity();
        cityParam.setReserve1(PlatEnum.PLAT_KT.getValue());
        List<HotelCity> hotelCities = this.hotelCityService.selectHotelCityList(cityParam);
        for (HotelCity city : hotelCities) {
            try {
                CozyTimeHotelContentApi20HotelIdsResponse availableHotelIds = this.cozyTimeSdkApi.getHotelIds(city.getCityId(), "0");
                CozyTimeHotelContentApi20HotelIdsResponse soldOutHotelIds = this.cozyTimeSdkApi.getHotelIds(city.getCityId(), "1");
                List<Long> ids = new ArrayList<>();
                if (availableHotelIds != null) {
                    ids.addAll(availableHotelIds.getHotelIds());
                }
                if (soldOutHotelIds != null) {
                    ids.addAll(soldOutHotelIds.getHotelIds());
                }

                int batchSize = 10; // 每次处理的批次大小
                int totalSize = ids.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    // 计算当前批次的结束索引
                    int endIndex = Math.min(i + batchSize, totalSize);
                    // 取出当前批次的数据
                    List<Long> tenBatch = ids.subList(i, endIndex);
                    List<HotelInfoCozytime> hotelInfoCozytimeList = this.saveHotelInfosByIds(tenBatch, hotelCities);
                    Long newValue = this.stringRedisTemplate.opsForValue().increment(HotelUpdateStatus.GetHotelCountKey, hotelInfoCozytimeList.size());
                    Long updateValue = this.stringRedisTemplate.opsForValue().increment(HotelUpdateStatus.UpdateHotelCountKey, hotelInfoCozytimeList.size());

                }

            } catch (Exception e) {
                log.error(ExceptionUtil.getStackTrace(e));
                log.error("科坦城市id数据报错-城市{}", city.getCityName());
            }
        }
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.expStatusKey, "3");
    }

    public void updateAllNew() {
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.expStatusKey, "0");
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.GetHotelCountKey, "0");
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.UpdateHotelCountKey, "0");
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.DeleteHotelCountKey, "0");
        HotelCity cityParam = new HotelCity();
        cityParam.setReserve1(PlatEnum.PLAT_KT.getValue());
        List<HotelCity> hotelCities = this.hotelCityService.selectHotelCityList(cityParam);
        this.log.info("更新科坦全量酒店信息总共 {} 城市", hotelCities.size());
        int count = 0;
        for (HotelCity city : hotelCities) {
            try {
                CozyTimeHotelContentApi20HotelIdsResponse availableHotelIds = this.cozyTimeSdkApi.getHotelIds(city.getCityId(), "0");
                CozyTimeHotelContentApi20HotelIdsResponse soldOutHotelIds = this.cozyTimeSdkApi.getHotelIds(city.getCityId(), "1");
                List<Long> ids = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(availableHotelIds.getHotelIds())){
                    ids.addAll(availableHotelIds.getHotelIds());
                }
                if(CollectionUtils.isNotEmpty(soldOutHotelIds.getHotelIds())){
                    ids.addAll(soldOutHotelIds.getHotelIds());
                }

                if(CollectionUtils.isEmpty(ids)){
                    this.log.warn("更新科坦全量酒店信息总共 {} 城市 , 第 {} 个城市 {} 没有酒店数据", hotelCities.size(), count, city.getCityName());
                    continue;
                }

                int batchSize = 10; // 每次处理的批次大小
                int totalSize = ids.size();
                List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
                for (int i = 0; i < totalSize; i += batchSize) {
                    // 计算当前批次的结束索引
                    int endIndex = Math.min(i + batchSize, totalSize);
                    // 取出当前批次的数据
                    List<Long> tenBatch = ids.subList(i, endIndex);
//                    List<HotelGnBase> hotelInfoCozytimeList = this.saveHotelInfosByIdsNew(tenBatch);
//                    Long newValue = this.stringRedisTemplate.opsForValue().increment(HotelUpdateStatus.GetHotelCountKey, hotelInfoCozytimeList.size());
//                    Long updateValue = this.stringRedisTemplate.opsForValue().increment(HotelUpdateStatus.UpdateHotelCountKey, hotelInfoCozytimeList.size());

                    CompletableFuture<Void>  completableFuture = CompletableFuture.runAsync(() -> {
                        try {
                            List<HotelGnBase> hotelInfoCozytimeList = this.saveHotelInfosByIdsNew(tenBatch);
                            Long newValue = this.stringRedisTemplate.opsForValue().increment(HotelUpdateStatus.GetHotelCountKey, hotelInfoCozytimeList.size());
                            Long updateValue = this.stringRedisTemplate.opsForValue().increment(HotelUpdateStatus.UpdateHotelCountKey, hotelInfoCozytimeList.size());
                        } catch (Exception e) {
                            this.log.error("更新科坦全量酒店信息执行失败", e);
                        }
                    }, this.allKetanHotelGnExecutor);
                    completableFutures.add(completableFuture);

                }

                CompletableFuture<?> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));
                try {
                    allOf.get();
                } catch (Exception e) {
                    log.error("更新科坦全量酒店信息异常,处理城市 : {}", city.getCityName(), e);
                }

            } catch (Exception e) {
                log.error(ExceptionUtil.getStackTrace(e));
                log.error("科坦城市id数据报错-城市{}", city.getCityName());
            }finally {
                count++;
                this.log.info("更新科坦全量酒店信息总共 {} 城市 , 第 {} 个城市已经完成", hotelCities.size(), count);
            }
        }
        this.stringRedisTemplate.opsForValue().set(HotelUpdateStatus.expStatusKey, "3");

    }

    public List<HotelInfoCozytime> saveHotelInfosByIds(List<Long> idList, List<HotelCity> hotelCities) throws Exception {
        Map<String, HotelCity> cityMap = hotelCities.stream().collect(Collectors.toMap(c -> c.getCityId(), c -> c));
        CozyTimeHotelContentApi20HotelInfoResponse hotelInfos = this.cozyTimeSdkApi.getHotelInfos(idList);
        List<HotelInfoCozytime> hotelInfoCozytimeList = hotelInfos.getHotels().stream()
                .map(h -> {
                    HotelInfoCozytime hotelInfoCozytime = new HotelInfoCozytime();
                    hotelInfoCozytime.setId(h.getHotelId().toString());
                    hotelInfoCozytime.setAddressLine(h.getHotelAddressCN());
                    hotelInfoCozytime.setName(h.getHotelNameCN());
                    hotelInfoCozytime.setNameEn(h.getHotelName());
                    hotelInfoCozytime.setPhone(h.getPhone());
                    hotelInfoCozytime.setCityId(h.getCityCode());
                    if (!MapUtils.isEmpty(cityMap)) {
                        hotelInfoCozytime.setCityName(cityMap.getOrDefault(h.getCityCode(), new HotelCity()).getCityName());
                    }
                    hotelInfoCozytime.setAreaId(h.getCityCode());
                    hotelInfoCozytime.setGroupId(h.getGroupCode());
                    hotelInfoCozytime.setBrandId(h.getBrandCode());
//                                hotelInfoCozytime.setCountry(h.getHotelNameCN());
                    hotelInfoCozytime.setBusinessDistricts(h.getPoiCN());
                    hotelInfoCozytime.setOpenDate(h.getEstablishmentDate());
                    hotelInfoCozytime.setDecorationDate(h.getRenovationDate());
                    hotelInfoCozytime.setIsDelete(0);
                    hotelInfoCozytime.setStar(h.getStar());
                    if (h.getHotelImages() != null && !h.getHotelImages().isEmpty()) {
                        h.getHotelImages().stream().filter(in -> in.getImageType().equalsIgnoreCase("Primary")).collect(Collectors.toList()).stream().findFirst().ifPresent(
                                in -> hotelInfoCozytime.setImage(in.getImageUrl())
                        );
                    }
                    hotelInfoCozytime.setDescription(h.getHotelDescCN());
                    hotelInfoCozytime.setScore(h.getCommentScore());
                    if (h.getCoordinates() != null) {
                        hotelInfoCozytime.setLonBd(new BigDecimal(h.getCoordinates().getLongitude()));
                        hotelInfoCozytime.setLatBd(new BigDecimal(h.getCoordinates().getLatitude()));
                    }
                    hotelInfoCozytime.setUpdateTime(new Date());
                    hotelInfoCozytime.setCreateTime(new Date());
                    hotelInfoCozytime.setPolicyInfo(h.getHotelPolicyCN());
                    hotelInfoCozytime.setStatus(1);
                    return hotelInfoCozytime;
                }).collect(Collectors.toList());
        this.hotelInfoCozytimeMapper.batchInsertOrUpdate(hotelInfoCozytimeList);
        return hotelInfoCozytimeList;
    }

    public List<HotelGnBase> saveHotelInfosByIdsNew(List<Long> idList) throws Exception {
        List<HotelGnBase> resultList = new ArrayList<>();
        CozyTimeHotelContentApi20HotelInfoResponse hotelInfos = null;
        try {
            hotelInfos = (CozyTimeHotelContentApi20HotelInfoResponse) this.fetchDataFunction(PlatEnum.PLAT_KT.getValue() + "_hotel_content_api2.0_hotelInfo", 1, 5, (result) -> {
                try {
                   return  cozyTimeSdkApi.getHotelInfos(idList);
                } catch (Exception e) {
                    this.log.error("更新科坦全量酒店信息,分流获取信息,api调用异常", e);
                }
                return null;
            });
        } catch (Exception e) {
            this.log.error("更新科坦全量酒店信息,分流获取信息异常", e);
        }

        if(hotelInfos == null){
            return resultList;
        }

        try {
            //429 too many requests（请求次数超过阀值）
            if(429 == hotelInfos.getResultCode()){
                log.error("全量科坦酒店国内信息请求次数超过阀值, msg:{}", hotelInfos.getResultMessage());
            }
            resultList = this.doSaveOrUpdateHotelInfosByIds(hotelInfos);
        } catch (Exception e) {
            this.log.error("更新科坦全量酒店信息执行失败", e);
        }
        return resultList;
    }

    public Object fetchDataFunction(String key, long second, long num, Function<Integer, Object> function) {
        int tryNum = 1;
        RRateLimiter rRateLimiter = this.limiter.getLimiter(key, second, num);
        boolean tryAcquire = rRateLimiter.tryAcquire(1);
        while (!tryAcquire) {
            this.log.info("当前供应商渠道【{}】限流.......睡眠500ms", key);
            try {
                Thread.sleep(500);
            } catch (InterruptedException ex) {
                this.log.info("睡眠异常。。。。。。。。。。");
            }
            tryNum++;
            tryAcquire = rRateLimiter.tryAcquire(1);
        }
        return function.apply(tryNum);
    }




    public List<HotelGnBase> saveOrUpdateHotelInfosByIds(List<Long> idList) throws Exception {
        CozyTimeHotelContentApi20HotelInfoRequest request = new CozyTimeHotelContentApi20HotelInfoRequest();
        request.setHotelIds(idList);
        request.setOptions("1");
        CozyTimeHotelContentApi20HotelInfoResponse response = this.cozyTimeSdkApi.execute(request);
        return this.doSaveOrUpdateHotelInfosByIds(response);

    }

    public List<HotelGnBase> doSaveOrUpdateHotelInfosByIds(CozyTimeHotelContentApi20HotelInfoResponse response) {
        List<HotelGnBase> dealList = new ArrayList<>();
        if (response != null && response.success()) {
            for (CozyTimeHotelContentApi20HotelInfoResponse.Hotel hotel : response.getHotels()) {
                try {
                    log.info("开始处理科坦酒店国内信息, hotel:{}", JSON.toJSONString(hotel));
                    HotelCity city = this.hotelCityServiceImpl.getByCityIdAndPlatIdForCache(PlatEnum.PLAT_KT, hotel.getCityCode());
                    if (city == null) {
                        city = new HotelCity();
                    }
                    HotelGnKetan hotelGnKetan = new HotelGnKetan();
                    hotelGnKetan.setReserve0(JSON.toJSONString(hotel));
                    hotelGnKetan.setId(hotel.getHotelId().toString());
                    hotelGnKetan.setName(hotel.getHotelNameCN());
                    hotelGnKetan.setNameEn(hotel.getHotelName());
                    String hotelType = hotel.getHotelType();
                    if (StringUtils.isBlank(hotelType)) {
                        hotelGnKetan.setTypeId(1); // 默认酒店
                    }else{
                        // 防止多个
                        hotelGnKetan.setTypeId(Integer.valueOf(hotelType.split(",")[0]));
                    }

                    hotelGnKetan.setOpenDate(hotel.getEstablishmentDate());
                    hotelGnKetan.setDecorationDate(hotel.getRenovationDate());
                    hotelGnKetan.setPhone(hotel.getPhone());
                    hotelGnKetan.setCountryId(hotel.getCountryCode());
                    hotelGnKetan.setCountryName(city.getCountryName());
                    hotelGnKetan.setProvinceId(city.getProvinceId());
                    hotelGnKetan.setProvinceName(city.getProvinceName());
                    hotelGnKetan.setCityId(hotel.getCityCode());
                    hotelGnKetan.setCityName(city.getCityName());
                    hotelGnKetan.setAreaId(city.getLocationId());
                    hotelGnKetan.setAreaName(hotel.getDistrictNameCN());
                    // hotelGnKetan.setBusinessDistrictId();
                    hotelGnKetan.setBusinessDistrictName(hotel.getPoiCN());
                    hotelGnKetan.setAddress(hotel.getHotelAddressCN());
                    hotelGnKetan.setAddressEn(hotel.getHotelAddress());
                    //hotelGnKetan.setLonGoogle();
                    // hotelGnKetan.setLatGoogle();
                    if (hotel.getCoordinates() != null) {
                        hotelGnKetan.setLonBaidu(new BigDecimal(hotel.getCoordinates().getLongitude()));
                        hotelGnKetan.setLatBaidu(new BigDecimal(hotel.getCoordinates().getLatitude()));
                    }

//                hotelGnKetan.setLonGaode();
//                hotelGnKetan.setLatGaode();
                    hotelGnKetan.setGroupId(hotel.getGroupCode());
                    hotelGnKetan.setGroupName(hotel.getGroupName());
                    hotelGnKetan.setBrandId(hotel.getBrandCode());
                    hotelGnKetan.setBrandName(hotel.getBrandName());
                    if (hotel.getStar() != null) {
                        hotelGnKetan.setStar(String.valueOf(hotel.getStar()));
                    }
                    if (CollectionUtils.isNotEmpty(hotel.getHotelImages())) {
                        hotel.getHotelImages().stream().filter(in -> in.getImageType().equalsIgnoreCase("Primary")).collect(Collectors.toList()).stream().findFirst().ifPresent(
                                in -> hotelGnKetan.setImage(in.getImageUrl())
                        );
                    }
                    hotelGnKetan.setScore(hotel.getCommentScore());
                    hotelGnKetan.setSynopsis(hotel.getHotelDescCN());
                    //hotelGnKetan.setSparkle();

                    PolicyInfo policyInfo = new PolicyInfo();
                    PolicyInfo.NoticePolicy noticePolicy = new PolicyInfo.NoticePolicy();
                    noticePolicy.setNoticeInfo(hotel.getHelpfulTip());

                    PolicyInfo.CheckInOutPolicy checkInOutPolicy = new PolicyInfo.CheckInOutPolicy();
                    checkInOutPolicy.setCheckIn(hotel.getCheckInCN());
                    checkInOutPolicy.setCheckOut(hotel.getCheckOutCN());

                    String customerType = hotel.getCustomerType();
                    PolicyInfo.CustomerPolicy customerPolicy = new PolicyInfo.CustomerPolicy();
                    if (StringUtils.isNotBlank(customerType)) {
//                        科坦0=1
//                        科坦1=2
//                        科坦2=4
//                        科坦3=3
                        if ("0".equals(customerType)) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.GLOBAL_GUEST.getCode()));
                            customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.GLOBAL_GUEST.getDescription());
                        }
                        if ("1".equals(customerType)) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_GUEST.getCode()));
                            customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.MAINLAND_GUEST.getDescription());
                        }
                        if ("2".equals(customerType)) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.FOREIGN_GUEST.getCode()));
                            customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.FOREIGN_GUEST.getDescription());
                        }
                        if ("3".equals(customerType)) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getCode()));
                            customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getDescription());
                        }

                    }

                    policyInfo.setCustomerPolicy(customerPolicy);
                    policyInfo.setCheckInOutPolicy(checkInOutPolicy);
                    policyInfo.setNoticePolicy(noticePolicy);


                    hotelGnKetan.setPolicyInfo(JSON.toJSONString(policyInfo));
                    FacilitiesInfo facilitiesInfo = new FacilitiesInfo();
                    List<String> hotelFacilities = hotel.getHotelFacilities();
                    if (hotelFacilities.contains("20801") || hotelFacilities.contains("26371")) {
                        FacilitiesInfo.Parking parking = new FacilitiesInfo.Parking();
                        parking.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setParking(parking);
                    }
                    if (hotelFacilities.contains("26381")) {
                        FacilitiesInfo.Charging charging = new FacilitiesInfo.Charging();
                        charging.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setCharging(charging);
                    }

                    if (hotelFacilities.contains("2011")) {
                        FacilitiesInfo.Lift lift = new FacilitiesInfo.Lift();
                        lift.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setLift(lift);
                    }

                    if (hotelFacilities.contains("171")) {
                        FacilitiesInfo.Baggage baggage = new FacilitiesInfo.Baggage();
                        baggage.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setBaggage(baggage);
                    }
                    if (hotelFacilities.contains("13111") || hotelFacilities.contains("2251") || hotelFacilities.contains("2261") || hotelFacilities.contains("2331")) {
                        FacilitiesInfo.Restaurant restaurant = new FacilitiesInfo.Restaurant();
                        restaurant.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setRestaurant(restaurant);
                    }
                    if (hotelFacilities.contains("100541")) {
                        FacilitiesInfo.MeetingRoom meetingRoom = new FacilitiesInfo.MeetingRoom();
                        meetingRoom.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setMeetingRoom(meetingRoom);
                    }
                    if (hotelFacilities.contains("14181")) {
                        FacilitiesInfo.PublicWifi publicWifi = new FacilitiesInfo.PublicWifi();
                        publicWifi.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setPublicWifi(publicWifi);
                    }
                    if (hotelFacilities.contains("1601") || hotelFacilities.contains("3021") || hotelFacilities.contains("3031") || hotelFacilities.contains("4911")) {
                        FacilitiesInfo.Gym gym = new FacilitiesInfo.Gym();
                        gym.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setGym(gym);
                    }

                    if (hotelFacilities.contains("14481")) {
                        FacilitiesInfo.Laundry laundry = new FacilitiesInfo.Laundry();
                        laundry.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setLaundry(laundry);
                    }

                    hotelGnKetan.setFacilitiesInfo(JSON.toJSONString(facilitiesInfo));
                    //hotelGnKetan.setRoundInfo();
                    hotelGnKetan.setStatus(1);
                    hotelGnKetan.setIsDelete(0);
                    hotelGnKetan.setIncrementStatus(0);
                    hotelGnKetan.setIncrementTime(new Date());
                    hotelGnKetan.setCreateTime(new Date());
                    hotelGnKetan.setUpdateTime(new Date());
                    String username = "admin";
                    try {
                        username = SecurityUtils.getLoginUser().getUser().getUserName();
                    } catch (Exception e) {

                    }
                    hotelGnKetan.setCreateBy(username);
                    hotelGnKetan.setUpdateBy(username);

                    dealList.add(hotelGnKetan);

                } catch (Exception e) {
                    log.info("更新科坦全量酒店信息,处理酒店信息异常, hotelInfo:{}", JSON.toJSONString(hotel), e);
                }

            }

            if (CollectionUtils.isNotEmpty(dealList)) {
                this.hotelGnBaseMapper.addOrUpdateBatch(PlatEnum.PLAT_KT.getTableNameSuffix(), dealList);
            }
        }

        return dealList;

    }


    public void getFacilities() {
        try {
            this.hotelFacilityCozyTimeMapper.deleteAll();
            CozyTimeHotelContentApi20FacilitiesResponse facilities = this.cozyTimeSdkApi.getFacilities("1");

            // 获取设施列表
            List<CozyTimeHotelContentApi20FacilitiesResponse.Facilities> facilityList = facilities.getFacilities();
            if (CollectionUtils.isEmpty(facilityList)) {
                return;
            }

            // 分批插入，每 500 条插入一次
            int batchSize = 500;
            for (int i = 0; i < facilityList.size(); i += batchSize) {
                List<CozyTimeHotelContentApi20FacilitiesResponse.Facilities> subList = facilityList.subList(i, Math.min(i + batchSize, facilityList.size()));

                // 转换为 HotelFacilityCozyTimeEntity 列表
                List<HotelFacilityCozyTimeEntity> collect = subList.stream().map(f -> {
                    HotelFacilityCozyTimeEntity facilityCozyTimeEntity = new HotelFacilityCozyTimeEntity();
                    BeanUtils.copyProperties(f, facilityCozyTimeEntity);
                    facilityCozyTimeEntity.setId(f.getFacilityCode());
                    facilityCozyTimeEntity.setFacilityName(f.getFacilityNameCN());
                    return facilityCozyTimeEntity;
                }).collect(Collectors.toList());

                // 批量插入
                this.hotelFacilityCozyTimeMapper.insertBatch(collect);
            }

            // 构建 Map 并存储到 Redis
            Map<String, CozyTimeHotelContentApi20FacilitiesResponse.Facilities> facilityMap = facilityList.stream()
                    .collect(Collectors.toMap(
                            f -> f.getFacilityCode(),
                            f -> f
                    ));

            // 使用 Redis Hash 存储 Map 数据
            String redisKey = "hotel_facilities_cozy_time";
            facilityMap.forEach((key, value) -> {
                // 将 Facilities 对象序列化为 JSON 字符串
                String jsonValue = JSON.toJSONString(value);
                this.stringRedisTemplate.opsForHash().put(redisKey, key, jsonValue);
            });

        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
    }

    public void addHotelByIds(List<Long> ids) {
        try {
            log.info("添加科坦酒店国内, ids:{}", ids.toString());
            List<HotelGnBase> hotelGnBases = this.saveOrUpdateHotelInfosByIds(ids);
            if (CollectionUtils.isNotEmpty(hotelGnBases)) {
                this.addElongHotelIdMapping(hotelGnBases);
            }
        } catch (Exception e) {
            log.error("添加科坦酒店国内异常, ids:{}", ids.toString(), e);
        }

    }

    private void addElongHotelIdMapping(List<HotelGnBase> hotelGnBases) {
        if (CollectionUtils.isEmpty(hotelGnBases)) {
            return;
        }
        try {
            List<Long> ids = hotelGnBases.stream().mapToLong(base -> Long.valueOf(base.getId())).boxed().collect(Collectors.toList());
            CozyTimeHotelContentApi20HotelAndRoomMappingRequest request = new CozyTimeHotelContentApi20HotelAndRoomMappingRequest();
            request.setHotelIds(ids);
            CozyTimeHotelContentApi20HotelAndRoomMappingResponse response = this.cozyTimeSdkApi.execute(request);
            if (!response.success()) {
                log.error("处理科坦国内酒店艺龙id映射, 获取艺龙映射失败, hotelGnBases:{}, response:{}", JSON.toJSONString(hotelGnBases), JSON.toJSONString(response));
            }

            List<HotelMapping> hotelMappingList = response.getHotelMappingList();
            List<HotelGnIdMapping> mappingList = hotelMappingList.stream().map(ketanHotelMapping -> {
                HotelGnIdMapping hotelGnIdMapping = new HotelGnIdMapping();
                hotelGnIdMapping.setPlatformId(PlatEnum.PLAT_KT.getValue());
                hotelGnIdMapping.setPlatformHotelId(ketanHotelMapping.getHotelId().toString());
                hotelGnIdMapping.setMappingPlatformId(PlatEnum.PLAT_EL.getValue());
                hotelGnIdMapping.setMappingHotelId(ketanHotelMapping.getElongHotelId());
                return hotelGnIdMapping;
            }).collect(Collectors.toList());
            log.info("处理科坦国内酒店艺龙id映射,插入数据, hotelGnBases:{}, mappingList:{}", JSON.toJSONString(hotelGnBases), JSON.toJSONString(mappingList));
            this.hotelGnIdMappingService.addOrUpdateBatch(mappingList);
        } catch (Exception e) {
            log.error("处理科坦国内酒店艺龙id映射异常, hotelGnBases:{}", JSON.toJSONString(hotelGnBases), e);
        }
    }

    /**
     * 生成清洁的商圈ID
     * 优先使用商圈名称，如果名称过长或包含特殊字符则使用商圈ID
     *
     * @param poiName 商圈名称
     * @return 清洁的标识符
     */
    private String generateCleanPoiId(String poiName) {

        // 清理商圈名称：移除特殊字符和多余空格
        String cleanName = poiName
                .replaceAll("[\\s\\-/\\\\,，。.]+", "_") // 替换空格、破折号、斜杠、逗号、句号为下划线
                .replaceAll("[\\(\\)（）\\[\\]【】<>《》]+", "") // 移除各种括号
                .replaceAll("_+", "_") // 多个下划线合并为一个
                .replaceAll("^_|_$", ""); // 移除首尾下划线
        
        return cleanName;
    }

}
