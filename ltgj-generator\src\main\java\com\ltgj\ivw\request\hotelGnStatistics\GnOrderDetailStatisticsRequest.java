package com.ltgj.ivw.request.hotelGnStatistics;

import com.ltgj.common.core.domain.BaseEntity;
import com.ltgj.ivw.domain.HotelGnOrderDetailStatistics;
import lombok.Data;

import java.io.Serializable;

@Data
public class GnOrderDetailStatisticsRequest extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long interfacePlat;

    private Integer isFailure;

    private Integer pageNum;

    private Integer pageSize;

    private Long startRow;

    private Long endRow;
}
