package com.ltgj.web.job.handler;

import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelInfoChailvgjService;
import com.ltgj.ivw.service.IHotelUpdateRecodeService;
import com.ltgj.ivw.service.hotel.updater.DirectChailvgjUpdateStrategy;
import com.ltgj.ivw.service.hotel.updater.HotelDataUpdateContext;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "clgjHotelInitDataHandler")
public class ClgjHotelInitDataHandler extends IJobHandler {
    @Autowired
    private IHotelInfoChailvgjService hotelInfoChailvgjService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try{
            hotelInfoChailvgjService.resetStatus();
            hotelInfoChailvgjService.updateAll("xxl-job");
        }catch (Exception e) {
            e.printStackTrace();
            log.error("差旅管家-酒店增量同步任务异常：{}", e);
        }
        log.info("差旅管家-全量数据执行完成");
        return ReturnT.SUCCESS;
    }
}
