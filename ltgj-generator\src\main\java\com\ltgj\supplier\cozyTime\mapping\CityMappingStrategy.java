package com.ltgj.supplier.cozyTime.mapping;

import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.domain.SupplierElongCityMapping;

import java.util.List;
import java.util.Map;

/**
 * 城市映射策略接口
 * 定义不同的城市映射策略
 * 
 * <AUTHOR>
 * @date 2024-12-21
 */
public interface CityMappingStrategy {
    
    /**
     * 创建科坦到本地城市的映射
     * 
     * @param mapping 供应商与艺龙城市映射关系
     * @param ketanCityMap 科坦城市映射
     * @param elongCityMappings 艺龙城市映射列表
     * @return 创建的映射对象，如果无法创建则返回null
     */
    HotelCityMapping createMapping(SupplierElongCityMapping mapping, 
                                 Map<String, HotelCity> ketanCityMap,
                                 List<HotelCityMapping> elongCityMappings);
    
    /**
     * 获取映射策略类型
     * 
     * @return 映射类型（1-ID映射；2-名称映射）
     */
    String getMappingType();
} 