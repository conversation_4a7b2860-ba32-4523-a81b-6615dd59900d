package com.ltgj.sdk.cozyTime.model.staticdata;

import com.ltgj.sdk.cozyTime.base.CozyTimeBaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 根据国家编码查询城市信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelContentApi20CitiesResponse extends CozyTimeBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 城市列表
     */
    private List<City> cities;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class City implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 城市编码
         */
        private String cityCode;

        /**
         * 国家编码
         */
        private String countryCode;

        /**
         * 城市英文名称
         */
        private String name;

        /**
         * 城市中文名称
         */
        private String nameCN;

        /**
         * 省份编码
         */
        private String provinceCode;

        /**
         * 省份英文名称
         */
        private String provinceName;

        /**
         * 省份中文名称
         */
        private String provinceNameCN;

        /**
         * 区县列表
         */
        private List<District> districts;

        /**
         * 时区
         */
        private String timeZone;

        /**
         * 商圈列表
         */
        private List<Pois> pois;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class District implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 区县名称
         */
        private String districtName;

        /**
         * 区县中文名称
         */
        private String districtNameCN;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Pois implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 商圈id
         */
        private String poiId;

        /**
         * 商圈英文名称
         */
        private String poiName;

        /**
         * 商圈中文名称
         */
        private String poiNameCN;
    }
}
