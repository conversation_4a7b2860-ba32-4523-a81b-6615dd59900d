package com.ltgj.ivw.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HotelInfoHsjlServiceTest {

    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;
    @Autowired
    private IHotelInfoHsjlxyService hotelInfoHsjlxyService;

    @Test
    public void updateAll() {
        try {
            hotelInfoHsjlService.updateHsjlAll("test", "test");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void updateXyAll() {
        try {
            hotelInfoHsjlxyService.updateHsjlxyAll("test", "test");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
