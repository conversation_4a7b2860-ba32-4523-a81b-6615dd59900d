package com.ltgj.ivw.service;

import com.ltgj.ivw.enums.PlatEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HotelInfoHsjlServiceTest {

    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;
    @Autowired
    private IHotelInfoHsjlxyService hotelInfoHsjlxyService;
    @Autowired
    private IHotelCityService iHotelCityService;

    @Test
    public void updateAll() {
        try {
            hotelInfoHsjlService.resetStatus();
            hotelInfoHsjlService.updateHsjlAll("test", "test");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void getStatus() {
        try {
            hotelInfoHsjlService.getStatus(PlatEnum.PLAT_HSJL_XY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void updateXyAll() {
        try {
            hotelInfoHsjlxyService.resetStatus();
            hotelInfoHsjlxyService.updateHsjlxyAll("test", "test");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void platToLocal() {
        iHotelCityService.getByCityIdAndPlatIdForCache(PlatEnum.PLAT_HSJL, "sheyang");
        iHotelCityService.getByAreaIdAndPlatIdForCache(PlatEnum.PLAT_HSJL, "sheyang");
    }
}
