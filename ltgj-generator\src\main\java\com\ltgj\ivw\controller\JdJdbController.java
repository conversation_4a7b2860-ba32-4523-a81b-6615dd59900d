package com.ltgj.ivw.controller;

import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.poi.ExcelUtil;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.MyTools;
import com.ltgj.ivw.utils.hotelApi.DongchenApi;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.HuazhuApi;
import com.ltgj.ivw.utils.hotelApi.JingjiangApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 酒店基础信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@RestController
@RequestMapping("/ivw/jdb")
@Api(tags = "酒店基础信息")
public class JdJdbController extends BaseController
{
    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private IJdJdbMappingService jdJdbMappingService;
    @Autowired
    private IHotelInfoDongchengService hotelInfoDongchengService;
    @Autowired
    private IHotelInfoHuazhuService hotelInfoHuazhuService;
    @Autowired
    private IHotelInfoJinjiangService hotelInfoJinjiangService;
    @Autowired
    private IZhJdJdbMinPriceService zhJdJdbMinPriceService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IHotelInfoChailvgjService hotelInfoChailvgjService;

    private static boolean stopFlag = false;
    final static int noMappingDays = 30;


    public static String PROJECT_NAME = "ivw_hotel";
    public static String CURRENT_EXECUTION_VENDOR = "";

    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @GetMapping("/getStatus")
    public AjaxResult getStatus() {
        return AjaxResult.success().put("statusExp", HotelUpdateStatus.statusUpdate);
    }

    /**
     * 停止最低价更新
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @GetMapping("/stopHuan")
    public void stopHuan() {
        stopFlag = true;
        HotelUpdateStatus.statusUpdate = false;
    }

    /**
     * 酒店集团3最低价
     * @param cityId
     * @throws InterruptedException
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @GetMapping("/getMinPrice")
    public AjaxResult getMinPrice(String cityId) throws InterruptedException {
        if (HotelUpdateStatus.statusUpdateMinPrice) {
            return AjaxResult.error("已有低价程序在执行，执行的程序为："+CURRENT_EXECUTION_VENDOR+"，请稍后再试");
        }

        HotelUpdateStatus.statusUpdate = true;
        HotelUpdateStatus.statusUpdateMinPrice = true;
        CURRENT_EXECUTION_VENDOR =  PlatEnum.PLAT_DC.getName() + PlatEnum.PLAT_HZ.getName() + PlatEnum.PLAT_JJ.getName();


        List<String> dates = new ArrayList<>();
        for(int i = 0; i < noMappingDays; i++) {
            String date = MyTools.getDateByCurr(0 - i);
            dates.add(date);
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        JdJdbMapping jdJdbMapping = new JdJdbMapping();
        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_DC.getValue()));
        List<JdJdbMapping> jdJdbMappingsDC = jdJdbMappingService.selectJdJdbMappingList(jdJdbMapping);
        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HZ.getValue()));
        List<JdJdbMapping> jdJdbMappingsHZ = jdJdbMappingService.selectJdJdbMappingList(jdJdbMapping);
        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_JJ.getValue()));
        List<JdJdbMapping> jdJdbMappingsJJ = jdJdbMappingService.selectJdJdbMappingList(jdJdbMapping);

        AtomicInteger js = new AtomicInteger(3);

        new Thread(() -> {
            HotelInfoDongcheng hotel = new HotelInfoDongcheng();
            ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
            JdJdb jdJdb = new JdJdb();
            jdJdb.setReserve2(PlatEnum.PLAT_DC.getValue());
            for(int i = 0; i < jdJdbMappingsDC.size(); i++) {
                try{
                    JdJdbMapping jdJdbMapping0 = jdJdbMappingsDC.get(i);
                    if(StringUtils.isNotEmpty(jdJdbMapping0.getReserve9()) && dates.contains(jdJdbMapping0.getReserve9())) {
                        continue;
                    }
                    BigDecimal priceLow = DongchenApi.getMinPrice(jdJdbMapping0.getPlatId());
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if(priceLow != null) {
                        if(priceLow.compareTo(new BigDecimal(0)) == 0) {
                            i--;
                            continue;
                        }

                        jdJdbMapping0.setReserve9(sdf.format(new Date()));
                        jdJdbMappingService.updateJdJdbMapping(jdJdbMapping0);

                        hotel.setId(jdJdbMapping0.getPlatId());
                        hotel.setReserve1(priceLow.toString());
                        hotelInfoDongchengService.updateHotelInfoDongcheng(hotel);

                        jdJdb.setId(jdJdbMapping0.getLocalId());
                        jdJdb.setMinPrice(priceLow);
                        jdJdbService.updateJdJdb(jdJdb);

                        zhJdJdbMinPrice.setJdid(jdJdbMapping0.getLocalId());
                        List<ZhJdJdbMinPrice> prices = zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                        if(prices.size() == 0) {
                            try {
                                zhJdJdbMinPrice.setSxsj(sdf.parse(MyTools.getDateByCurr(1)));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            zhJdJdbMinPrice.setMinPrice(priceLow);
                            zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                        } else {
                            zhJdJdbMinPrice = prices.get(0);
                            try {
                                zhJdJdbMinPrice.setSxsj(sdf.parse(MyTools.getDateByCurr(1)));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            zhJdJdbMinPrice.setMinPrice(priceLow);
                            zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                        }
                    }
                }catch (Exception e) {
                    continue;
                }
            }
            js.decrementAndGet();
        }).start();

        new Thread(() -> {
            HotelInfoHuazhu hotel = new HotelInfoHuazhu();
            ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
            JdJdb jdJdb = new JdJdb();
            jdJdb.setReserve2(PlatEnum.PLAT_HZ.getValue());
            for(int i = 0; i < jdJdbMappingsHZ.size(); i++) {
                try{
                    JdJdbMapping jdJdbMapping0 = jdJdbMappingsHZ.get(i);
                    if(StringUtils.isNotEmpty(jdJdbMapping0.getReserve9()) && dates.contains(jdJdbMapping0.getReserve9())) {
                        continue;
                    }
                    BigDecimal priceLow = HuazhuApi.getMinPrice(jdJdbMapping0.getPlatId());
                    if(priceLow != null) {
                        if(priceLow.compareTo(new BigDecimal(0)) == 0) {
                            i--;
                            continue;
                        }

                        jdJdbMapping0.setReserve9(sdf.format(new Date()));
                        jdJdbMappingService.updateJdJdbMapping(jdJdbMapping0);

                        hotel.setId(jdJdbMapping0.getPlatId());
                        hotel.setReserve1(priceLow.toString());
                        hotelInfoHuazhuService.updateHotelInfoHuazhu(hotel);

                        jdJdb.setId(jdJdbMapping0.getLocalId());
                        jdJdb.setMinPrice(priceLow);
                        jdJdbService.updateJdJdb(jdJdb);

                        zhJdJdbMinPrice.setJdid(jdJdbMapping0.getLocalId());
                        List<ZhJdJdbMinPrice> prices = zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                        if(prices.size() == 0) {
                            try {
                                zhJdJdbMinPrice.setSxsj(sdf.parse(MyTools.getDateByCurr(1)));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            zhJdJdbMinPrice.setMinPrice(priceLow);
                            zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                        } else {
                            zhJdJdbMinPrice = prices.get(0);
                            try {
                                zhJdJdbMinPrice.setSxsj(sdf.parse(MyTools.getDateByCurr(1)));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            zhJdJdbMinPrice.setMinPrice(priceLow);
                            zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                        }

                    }
                }catch (Exception e) {
                    continue;
                }
            }
            js.decrementAndGet();
        }).start();

        new Thread(() -> {
            HotelInfoJinjiang hotel = new HotelInfoJinjiang();
            ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
            JdJdb jdJdb = new JdJdb();
            jdJdb.setReserve2(PlatEnum.PLAT_JJ.getValue());
            for(int i = 0; i < jdJdbMappingsJJ.size(); i++) {
                try {
                    JdJdbMapping jdJdbMapping0 = jdJdbMappingsJJ.get(i);
                    if (StringUtils.isNotEmpty(jdJdbMapping0.getReserve9()) && dates.contains(jdJdbMapping0.getReserve9())) {
                        continue;
                    }
                    BigDecimal priceLow = JingjiangApi.getMinPrice(jdJdbMapping0.getPlatId());
                    if (priceLow != null) {
                        if (priceLow.compareTo(new BigDecimal(0)) == 0) {
                            i--;
                            continue;
                        }

                        jdJdbMapping0.setReserve9(sdf.format(new Date()));
                        jdJdbMappingService.updateJdJdbMapping(jdJdbMapping0);

                        hotel.setId(jdJdbMapping0.getPlatId());
                        hotel.setReserve1(priceLow.toString());
                        hotelInfoJinjiangService.updateHotelInfoJinjiang(hotel);

                        jdJdb.setId(jdJdbMapping0.getLocalId());
                        jdJdb.setMinPrice(priceLow);
                        jdJdbService.updateJdJdb(jdJdb);

                        zhJdJdbMinPrice.setJdid(jdJdbMapping0.getLocalId());
                        List<ZhJdJdbMinPrice> prices = zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                        if (prices.size() == 0) {
                            try {
                                zhJdJdbMinPrice.setSxsj(sdf.parse(MyTools.getDateByCurr(1)));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            zhJdJdbMinPrice.setMinPrice(priceLow);
                            zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                        } else {
                            zhJdJdbMinPrice = prices.get(0);
                            try {
                                zhJdJdbMinPrice.setSxsj(sdf.parse(MyTools.getDateByCurr(1)));
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            zhJdJdbMinPrice.setMinPrice(priceLow);
                            zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                        }

                    }
                }catch (Exception e) {
                    continue;
                }
            }
            js.decrementAndGet();
        }).start();

        while (true) {
            System.out.println("10秒==========");
            Thread.sleep(10000);
            if(js.get() == 0) {
                HotelUpdateStatus.statusUpdate = false;
                HotelUpdateStatus.statusUpdateMinPrice = false;
                break;
            }
        }
        return AjaxResult.success();
    }

    /**
     * 千淘最低价
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @GetMapping("/listMinPriceQT")
    public AjaxResult listMinPriceQT() {

//        if (HotelUpdateStatus.statusUpdateMinPrice) {
//            return AjaxResult.error("已有低价程序在执行，执行的程序为："+CURRENT_EXECUTION_VENDOR+"，请稍后再试");
//        }
//        HotelUpdateStatus.statusUpdateMinPrice = true;
//        CURRENT_EXECUTION_VENDOR = PlatEnum.PLAT_QT.getName();
        MinPriceReq minPriceReq = new MinPriceReq();
        String now = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String ninetyDays = LocalDateTime.now().plusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        minPriceReq.setCheckInDate(now);
        minPriceReq.setCheckOutDate(ninetyDays);
        String randomKey = jdJdbService.downloadFile(minPriceReq);

        AjaxResult ajaxResult = jdJdbService.listMinPriceQT(randomKey);
        HotelUpdateStatus.statusUpdate = false;
        HotelUpdateStatus.statusUpdateMinPrice = false;
        return ajaxResult;
    }

    private void deleteRunningKey(String runningKey) {
        Boolean delete = stringRedisTemplate.delete(runningKey);
        if(Boolean.TRUE.equals(delete)){
            logger.info("runningKey删除成功");
        }else {
            logger.info("runningKey删除失败");
        }
    }

    /**
     * 红色加力协议最低价
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @PostMapping("/listMinPriceHSJL")
    public AjaxResult listMinPriceHSJL(@RequestBody MinPriceReq minPriceReq) {
//        if (HotelUpdateStatus.statusUpdateMinPrice) {
//            return AjaxResult.error("已有低价程序在执行，执行的程序为："+CURRENT_EXECUTION_VENDOR+"，请稍后再试");
//        }
//        HotelUpdateStatus.statusUpdateMinPrice = true;
//        CURRENT_EXECUTION_VENDOR = PlatEnum.PLAT_HSJL.getName();


        /**
         * {"bussinessResponse":{"hotelLowestPrices":[{"hotelId":1273455,"priceItems":[{"roomStatus":1,"saleDate":"2024-09-18","salePrice":22.0},{"roomStatus":1,"saleDate":"2024-09-19","salePrice":22.0}]},{"hotelId":561794,"priceItems":[{"roomStatus":1,"saleDate":"2024-09-18","salePrice":3.0},{"roomStatus":1,"saleDate":"2024-09-19","salePrice":3.0}]}]},"returnCode":"000","retrunMsg":"成功","returnCode":"000","returnMsg":"成功"}
         */

        jdJdbService.listMinPriceHSJL(minPriceReq);


        return AjaxResult.success();
    }


    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @PostMapping("/listMinPriceHSJLXY")
    public AjaxResult listMinPriceHSJLXY(@RequestBody MinPriceReq minPriceReq) {
//        if (HotelUpdateStatus.statusUpdateMinPrice) {
//            return AjaxResult.error("已有低价程序在执行，执行的程序为："+CURRENT_EXECUTION_VENDOR+"，请稍后再试");
//        }
//        HotelUpdateStatus.statusUpdateMinPrice = true;
//        CURRENT_EXECUTION_VENDOR = PlatEnum.PLAT_HSJL_XY.getName();

        return jdJdbService.listMinPriceHSJLXYNew(minPriceReq);
    }

    @PostMapping("/listMinPriceStatus")
    public AjaxResult listMinPriceStatus(@RequestBody JdJdbMapping jdJdbMapping) {

        Long interfacePlat = jdJdbMapping.getInterfacePlat();

        String dateKey = MyTools.getDateByCurr(noMappingDays);
        if (null != jdJdbMapping.getMappingDays()) {
            dateKey = MyTools.getDateByCurr(jdJdbMapping.getMappingDays());
        }

        if(interfacePlat.equals(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()))){
            String key = PROJECT_NAME + ":" +  "jd_min_price_hsjlxy" + ":" +PlatEnum.PLAT_HSJL_XY.getValue()+":"+ dateKey;
            String value = stringRedisTemplate.opsForValue().get(key);
            logger.info("key:{},value:{}",key,value);
            if(StringUtils.isNotEmpty(value) && value.equals("1")){
                return AjaxResult.error(PlatEnum.PLAT_HSJL_XY.getName() + "今日已执行");
            }
        }else if(interfacePlat.equals(Long.valueOf(PlatEnum.PLAT_QT.getValue()))){
            String key = PROJECT_NAME + ":" +  "jd_min_price_qt" + ":" +PlatEnum.PLAT_QT.getValue()+":"+ dateKey;
            String value = stringRedisTemplate.opsForValue().get(key);
            logger.info("key:{},value:{}",key,value);
            if(StringUtils.isNotEmpty(value) && value.equals("1")){
                return AjaxResult.error(PlatEnum.PLAT_QT.getValue() + "今日已执行");
            }
        }else if(interfacePlat.equals(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()))){
            String key = PROJECT_NAME + ":" +  "jd_min_price_hsjl" + ":" +PlatEnum.PLAT_HSJL.getValue()+":"+ dateKey;
            String value = stringRedisTemplate.opsForValue().get(key);
            logger.info("key:{},value:{}",key,value);
            if(StringUtils.isNotEmpty(value) && value.equals("1")){
                return AjaxResult.error(PlatEnum.PLAT_HSJL.getValue() + "今日已执行");
            }
        }else if(interfacePlat.equals(Long.valueOf(PlatEnum.PLAT_MT.getValue()))) {
            String key = PROJECT_NAME + ":" + "jd_min_price_mt" + ":" + PlatEnum.PLAT_MT.getValue() + ":" + dateKey;
            String value = stringRedisTemplate.opsForValue().get(key);
            logger.info("key:{},value:{}", key, value);
            if (StringUtils.isNotEmpty(value) && value.equals("1")) {
                return AjaxResult.error(PlatEnum.PLAT_MT.getValue() + "今日已执行");
            }
        } else if (interfacePlat.equals(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()))) {
            String key = PROJECT_NAME + ":" + "jd_min_price_clgj" + ":" + PlatEnum.PLAT_CLGJ.getValue() + ":" + dateKey;
            String value = stringRedisTemplate.opsForValue().get(key);
            logger.info("key:{},value:{}", key, value);
            if (StringUtils.isNotEmpty(value) && value.equals("1")) {
                return AjaxResult.error(PlatEnum.PLAT_CLGJ.getValue() + "今日已执行");
            }
        } else {
            return AjaxResult.error("暂不支持该平台");
        }

        return AjaxResult.success();
    }


    /**
     * 美团最低价
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @PostMapping("/listMinPriceMT")
    public AjaxResult listMinPriceMT(@RequestBody MinPriceReq minPriceReq) {
//        if (HotelUpdateStatus.statusUpdateMinPrice) {
//            return AjaxResult.error("已有低价程序在执行，执行的程序为："+CURRENT_EXECUTION_VENDOR+"，请稍后再试");
//        }
//        HotelUpdateStatus.statusUpdateMinPrice = true;
//        CURRENT_EXECUTION_VENDOR = PlatEnum.PLAT_MT.getName();

        return jdJdbService.listMinPriceMT(minPriceReq);

    }

    /**
     * 差旅管家最低价
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @PostMapping("/listMinPriceCLGJ")
    public void listMinPriceCLGJ(@RequestBody MinPriceReq minPriceReq) throws Exception {
        if(HotelUpdateStatus.status == 1){
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        hotelInfoChailvgjService.updateLowPrice(minPriceReq);
    }

    /**
     * 删除过期低价数据
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @DeleteMapping("/deleteTheLowestPriceForHotelsInBulk")
    public void deleteTheLowestPriceForHotelsInBulk() throws Exception {
        zhJdJdbMinPriceService.deleteTheLowestPriceForHotelsInBulk();
    }

    /**
     * 查询酒店基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:list')")
    @GetMapping("/list")
    public TableDataInfo list(JdJdb jdJdb)
    {
        startPage();
        List<JdJdb> list = jdJdbService.selectJdJdbList(jdJdb);
        TableDataInfo rspData = getDataTable(list);
//        rspData.setStatusExp(HotelUpdateStatus.statusUpdate);
        return rspData;
    }

    /**
     * 导出酒店基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:export')")
    @Log(title = "酒店基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JdJdb jdJdb)
    {
        List<JdJdb> list = jdJdbService.selectJdJdbList(jdJdb);
        ExcelUtil<JdJdb> util = new ExcelUtil<JdJdb>(JdJdb.class);
        util.exportExcel(response, list, "酒店基础信息数据");
    }

    /**
     * 获取酒店基础信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(jdJdbService.selectJdJdbById(id));
    }

    /**
     * 新增酒店基础信息
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:add')")
    @Log(title = "酒店基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody JdJdb jdJdb)
    {
        return toAjax(jdJdbService.insertJdJdb(jdJdb));
    }

    /**
     * 修改酒店基础信息
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:edit')")
    @Log(title = "酒店基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody JdJdb jdJdb)
    {
        return toAjax(jdJdbService.updateJdJdb(jdJdb));
    }

    /**
     * 删除酒店基础信息
     */
    @PreAuthorize("@ss.hasPermi('ivw:jdb:remove')")
    @Log(title = "酒店基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(jdJdbService.deleteJdJdbByIds(ids));
    }

    @RequestMapping("/createSql")
    public AjaxResult createSql(String id) {
        JdJdb hotelLocal = jdJdbService.selectJdJdbById(id);
        List<String> sqls = new ArrayList<>();
        String cityId = hotelLocal.getCityId();
        String tableName = "zh_jd_jdb_"+(Long.valueOf(cityId) % 10);
        sqls.add("DELETE FROM "+ tableName +" WHERE id='"+id+"';");
        sqls.add("DELETE FROM zh_jd_jdb_mapping WHERE local_id ='"+id+"';");
        String insertHotel = "INSERT INTO "+ tableName +" VALUES ('"+hotelLocal.getId()+"','"+hotelLocal.getInterfacePlat()+"','"
                +hotelLocal.getJdmc()+"','','','"
                +hotelLocal.getJddz()+"','','"+hotelLocal.getJddh()+"','"
                +hotelLocal.getImgUrl()+"',"+hotelLocal.getStatus()+","+hotelLocal.getLonGoogle()+","
                +hotelLocal.getLatGoogle()+","+hotelLocal.getLonBaidu()+","+hotelLocal.getLatBaidu()+","
                +hotelLocal.getLonGaode()+","+hotelLocal.getLatGaode()+",'"+hotelLocal.getCityId()+"','"
                +hotelLocal.getCityName()+"','"+hotelLocal.getBrandId()+"','"+hotelLocal.getBrandName()+"','"+hotelLocal.getDistrict()
                +"','"+hotelLocal.getDistrictName()+"','"+hotelLocal.getBusinessZone()+"','"
                +hotelLocal.getBusinessZoneName()+"','"+hotelLocal.getJdxj()+"',"+hotelLocal.getScore()+",'"
                +hotelLocal.getJtid()+"','"+hotelLocal.getJtmc()+"','"+hotelLocal.getKysj()+"','"
                +hotelLocal.getZhzxsj()+"','"+hotelLocal.getRank()+"',null,null,null,'"+hotelLocal.getMinPrice()+"','"+hotelLocal.getNoticeInfo()+"','"
                +hotelLocal.getPolicyInfo()+"','"+hotelLocal.getFacilitiesInfo()+"');";

        insertHotel = insertHotel.replace("'null'", "null");
        sqls.add(insertHotel);
        JdJdbMapping mapping = new JdJdbMapping();
        mapping.setLocalId(id);
        List<JdJdbMapping> list = jdJdbMappingService.selectJdJdbMappingList(mapping);
        for(JdJdbMapping mp : list) {
            sqls.add("INSERT INTO zh_jd_jdb_mapping (interface_plat, local_id, plat_id, is_gn_gj, status, jd_name)" +
                    " VALUES ("+ mp.getInterfacePlat() +",'"+ mp.getLocalId() +"','"+ mp.getPlatId()+"',"
                    + mp.getIsGnGj() +","+ mp.getStatus() + ",'"+ mp.getJdName() +"');");
        }
        return AjaxResult.success(sqls);
    }

}
