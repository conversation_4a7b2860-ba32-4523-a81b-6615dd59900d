package com.ltgj.ivw.mapper;

import com.ltgj.generator.mybatis.HotelGnBaseCommonSqlProvider;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * XX酒店信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Repository
public interface HotelGnBaseCommonMapper<T>
{
    /**
     * 查询XX酒店信息
     *
     * @param id 美团酒店信息主键
     * @return 美团酒店信息
     */
    @SelectProvider(type = HotelGnBaseCommonSqlProvider.class, method = "selectHotelGnBaseCommonById")
    HotelGnBase selectHotelGnBaseCommonById(@Param("id") String id, @Param("entityClass") Class<T> entityClass);
    /**
     * 查询XX酒店信息 方式二
     *
     * @param id 美团酒店信息主键
     * @return 美团酒店信息
     */
    HotelGnBase selectHotelGnBaseCommonByIdXml(@Param("id") String id, @Param("entityClass") Class<T> entityClass);

    /**
     * 查询XX酒店信息列表 方式一
     *
     * @param hotelGnBase XX酒店信息
     * @return XX酒店信息集合
     */
    @SelectProvider(type = HotelGnBaseCommonSqlProvider.class, method = "selectHotelGnBaseCommonList")
    List<HotelGnBase> selectHotelGnBaseCommonList(@Param("hotelGnBase") HotelGnBase hotelGnBase, @Param("entityClass") Class<T> entityClass);

    /**
     * 查询XX酒店信息列表
     *
     * @param params    参数集合
     * @return 美团酒店信息集合
     */
    List<HotelGnBase> selectHotelGnBaseCommonListXml(@Param("params") Map<String, Object> params);  /**
     * 查询XX酒店信息列表
     *
     * @param hotelGnBase XX酒店信息
     * @return XX酒店信息集合
     */
    @SelectProvider(type = HotelGnBaseCommonSqlProvider.class, method = "selectHotelGnBaseCommonList2")
    List<HotelGnBase> selectHotelGnBaseCommonList2(@Param("hotelGnBase") HotelGnBase hotelGnBase, @Param("entityClass") Class<T> entityClass);

    /**
     * 新增酒店信息
     *
     * @param hotelGnBase XX酒店信息
     * @return 结果
     */
    @InsertProvider(type = HotelGnBaseCommonSqlProvider.class, method = "insertHotelGnBaseCommon")
    int insertHotelGnBaseCommon(@Param("hotelGnBase") Object hotelGnBase, @Param("entityClass") Class<T> entityClass);

    /**
     * 修改entity酒店信息
     *
     * @param hotelGnBase XX酒店信息
     * @return 结果
     */
    @UpdateProvider(type = HotelGnBaseCommonSqlProvider.class, method = "updateHotelGnBaseCommon")
    int updateHotelGnBaseCommon(@Param("hotelGnBase") Object hotelGnBase, @Param("entityClass") Class<T> entityClass);


    /**
     * 批量删除酒店信息
     *
     * @param ids ID数组
     * @return 结果
     */
    @DeleteProvider(type = HotelGnBaseCommonSqlProvider.class, method = "deleteHotelGnBaseCommonByIds")
    int deleteHotelGnBaseCommonByIds(@Param("ids") String[] ids, @Param("entityClass") Class<T> entityClass);

}
