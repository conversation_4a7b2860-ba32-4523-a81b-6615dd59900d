package com.ltgj.web.controller.api;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.ltgj.common.es.domain.Result;
import com.ltgj.common.es.hotel.dto.EsHotelInfoDto;
import com.ltgj.common.es.hotel.service.EsHotelInfoManager;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.service.EsHotelInfoService;
import com.ltgj.ivw.service.IZhJdJdbMinPriceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date 2024/10/28 11:19
 * @Description：
 */
@RestController
@RequestMapping("/api/es/hotel")
public class HotelEsController {

    @Resource
    private EsHotelInfoManager hotelPoiInfoManager;
    @Autowired
    private EsHotelInfoService esHotelInfoService;

    @Autowired
    private IZhJdJdbMinPriceService zhJdJdbMinPriceService;

    private final static Logger logger = LoggerFactory.getLogger(HotelEsController.class);

    @PostMapping("/createEsHotelIndex")
    public Result createEsHotelIndex(@RequestBody EsHotelInfoDto hotelInfoDto){


        return hotelPoiInfoManager.createEsHotelInfoIndex();
    }

    @PostMapping("/testSaveEsHotel")
    public Result testSaveEsHotel(@RequestBody EsHotelInfoDto hotelInfoDto){


        return hotelPoiInfoManager.save(hotelInfoDto);
    }

    @PostMapping("/testBatchSaveEsHotel")
    public Result testBatchSaveEsHotel(@RequestBody List<EsHotelInfoDto> hotelPoiInfoDtoList){


        return hotelPoiInfoManager.saveList(hotelPoiInfoDtoList);
    }
    @RequestMapping("/syncHotelToEsNew")
    public Result syncHotelToEsNew(@RequestBody List<Long> hotelIds) {
    if (CollectionUtils.isEmpty(hotelIds)) {
        esHotelInfoService.syncHotelToEs();
    } else {
        try {
            List<String> idList = hotelIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
            List<JdJdb> hotelList = esHotelInfoService.getJdJdbByIds(idList);
            if (!CollectionUtils.isEmpty(hotelList)) {
                esHotelInfoService.syncHotelBaseInfoToEs(hotelList);
            } else {
                logger.warn("未找到指定ID的酒店数据：{}", hotelIds);
            }
        } catch (Exception e) {
            logger.error("同步指定酒店数据到ES时发生错误：", e);
            return Result.fail("同步失败：" + e.getMessage());
        }
    }
    return Result.ok();
}


    /**
     * 将酒店同步到 ES
     * @return
     */
    @RequestMapping("/syncHotelToEs")
    public Result syncHotelToEs() {
        esHotelInfoService.syncHotelToEs();
        return Result.ok();
    }

    @PostMapping("/syncMinPriceToEs")
    public Result syncMinPriceToEs() {
        zhJdJdbMinPriceService.updateMinPriceToEs();
        return Result.ok();
    }

    @PostMapping("/syncMinPriceToEs2")
    public Result syncMinPriceToEs2() {
        zhJdJdbMinPriceService.updateMinPriceToEs2();
        return Result.ok();
    }

    @GetMapping("/updateMinPriceToZero")
    public Result updateMinPriceToZero(double minPrice) throws IOException {
        esHotelInfoService.updateMinPriceToZero(minPrice);
        return Result.ok();
    }

    @GetMapping("/clearDatePriceListForInterfacePlat")
    public Result updateMinPriceToZero(String interfacePlatId) {
        esHotelInfoService.clearDatePriceListForInterfacePlat(interfacePlatId);
        return Result.ok();
    }

}
