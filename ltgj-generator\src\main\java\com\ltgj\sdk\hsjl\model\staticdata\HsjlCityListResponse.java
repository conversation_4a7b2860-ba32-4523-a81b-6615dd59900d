package com.ltgj.sdk.hsjl.model.staticdata;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 红色加力城市列表查询响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HsjlCityListResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务响应
     */
    private BusinessResponse bussinessResponse;
    
    /**
     * 返回代码
     */
    private String returnCode;
    
    /**
     * 返回消息
     */
    private String returnMsg;

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BusinessResponse implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 省份列表
         */
        private List<Province> provinces;
    }

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Province implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 国家编码
         */
        private String countryCode;
        /**
         * 国家名称
         */
        private String countryName;
        /**
         * 国家名称英文
         */
        private String countryEngName;
        /**
         * 省份编码
         */
        private String provinceCode;
        /**
         * 省份名称
         */
        private String provinceName;
        /**
         * 省份名称英文
         */
        private String provinceEngName;
        /**
         * 城市列表
         */
        private List<City> citys;
    }

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class City implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 城市编码
         */
        private String cityCode;
        /**
         * 城市名称
         */
        private String cityName;
        /**
         * 城市英文名称
         */
        private String areaEngName;
        /**
         * 父城市编码（如果存在，说明当前是区县）
         */
        private String parentCityCode;
        /**
         * 父城市名称
         */
        private String parentCityName;
        /**
         * 父城市英文名称
         */
        private String parentCityEngName;
    }
} 