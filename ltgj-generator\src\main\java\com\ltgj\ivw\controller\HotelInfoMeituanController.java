package com.ltgj.ivw.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.meituan.HotelCityMeituan;
import com.ltgj.ivw.domain.response.HotelLowestPrices;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.*;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.MeituanApi;
import com.ltgj.limiter.LtgjRateLimiter;
import com.ltgj.supplier.common.domain.FacilitiesInfo;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnMeituan;
import com.ltgj.supplier.common.gn.enums.HotelGnCustomerTypeEnum;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.meituan.HotelMeituanSupplierService;
import com.ltgj.system.service.ISysDictDataService;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.redisson.api.RLock;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 美团酒店信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@RestController
@RequestMapping("/ivw/meituan")
public class HotelInfoMeituanController extends AbstractIvwController<HotelGnBase, HotelGnMeituan, IHotelInfoMeituanService> {

    private static final Logger log = LoggerFactory.getLogger(HotelInfoMeituanController.class);
    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private IHotelCityMappingService hotelCityMappingService;
    @Autowired
    private IJdJdbMappingService jdJdbMappingService;
    @Autowired
    private IHotelUpdateRecodeService hotelUpdateRecodeService;
    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;

    @Autowired
    private LtgjRateLimiter limiter;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private HotelGnBaseService hotelGnBaseService;

    @Autowired
    private IHotelInfoMeituanService hotelInfoMeituanService;

    @Autowired
    private IHotelCityMeituanService hotelCityMeituanService;

    @Autowired
    private HotelMeituanSupplierService hotelMeituanSupplierService;

    private static ObjectMapper objectMapper = new ObjectMapper();
    private static int maxReqCount = 5;

    @Resource(name = "allMeituanHotelGnExecutor")
    public ThreadPoolTaskExecutor allMeituanHotelGnExecutor;

    //美团酒店数据
    private static final int threadNumDetailMeituan = 18;
    private static final int maxCountDetailMeituan = 20;
    private static final int threadNumPriceMeituan = 38;
    private static final int maxCountPriceMeituan = 10;

    //获取酒店更新状态信息
    @PreAuthorize("@ss.hasPermi('ivw:meituan:query')")
    @GetMapping("/getInfoStatus")
    public AjaxResult getInfoStatus() {
        Map<String, Object> map = new HashMap<>();
        String stepName = "";
        //从redis中获取更新状态
        String expStatusMT = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
        this.logger.info("获取expStatusMT:{}", expStatusMT);
        int expStatus = 0;
        if (StringUtils.isNotEmpty(expStatusMT)) {
            expStatus = Integer.parseInt(expStatusMT);
        }
        switch (expStatus) {
            case 0:
                stepName = "初始状态";
                break;
            case 1:
                stepName = "获取酒店IDS中";
                break;
            case 2:
                stepName = "获取酒店IDS新增完成";
                break;
            case 3:
                stepName = "获取酒店详情中";
                break;
            case 4:
                stepName = "获取酒店详情完成";
                break;
            case 5:
                stepName = "更新酒店详情中";
                break;
            case 6:
                stepName = "更新酒店详情完成";
                break;
            case 7:
                stepName = "获取酒店价格中";
                break;
            case 8:
                stepName = "获取酒店价格完成";
                break;
            case 9:
                stepName = "更新酒店价格中";
                break;
            case 10:
                stepName = "更新酒店价格完成";
                break;
            case 11:
                stepName = "酒店映射中";
                break;
            case 12:
                stepName = "酒店映射完成";
                break;
        }
        map.put("stepName", stepName);

        String mtIdsCountAdd = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtIdsCountAdd");
        long mtIdsCount = 0;
        if (StringUtils.isNotEmpty(mtIdsCountAdd)) {
            mtIdsCount = Long.parseLong(mtIdsCountAdd);
        }
        map.put("add", mtIdsCount);

        String elIdsCount = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.elIdsCount");
        long elIdsCountLong = 0;
        if (StringUtils.isNotEmpty(elIdsCount)) {
            elIdsCountLong = Long.parseLong(elIdsCount);
        }
        map.put("clCount", elIdsCountLong);


        String mtDetailCountGet = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDetailCountGet");
        long mtDetailCountGetLong = 0;
        if (StringUtils.isNotEmpty(mtDetailCountGet)) {
            mtDetailCountGetLong = Long.parseLong(mtDetailCountGet);
        }
        map.put("get", mtDetailCountGetLong);


        String mtDetailCountUpdate = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDetailCountUpdate");
        long mtDetailCountUpdateLong = 0;
        if (StringUtils.isNotEmpty(mtDetailCountUpdate)) {
            mtDetailCountUpdateLong = Long.parseLong(mtDetailCountUpdate);
        }
        map.put("updateJS", mtDetailCountUpdateLong);

        String mtPriceCountGet = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtPriceCountGet");
        long mtPriceCountGetLong = 0;
        if (StringUtils.isNotEmpty(mtPriceCountGet)) {
            mtPriceCountGetLong = Long.parseLong(mtDetailCountUpdate);
        }
        map.put("getPrice", mtPriceCountGetLong);

        String mtPriceCountUpdate = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtPriceCountUpdate");
        long mtPriceCountUpdateLong = 0;
        if (StringUtils.isNotEmpty(mtPriceCountUpdate)) {
            mtPriceCountUpdateLong = Long.parseLong(mtPriceCountUpdate);
        }
        map.put("updatePrice", mtPriceCountUpdateLong);


        String mtDeleteJS = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDeleteJS");
        long mtDeleteJSLong = 0;
        if (StringUtils.isNotEmpty(mtDeleteJS)) {
            mtDeleteJSLong = Long.parseLong(mtDeleteJS);
        }
        map.put("deleteJS", mtDeleteJSLong);

        String mtMappingCount = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtMappingCount");
        long mtMappingCountLong = 0;
        if (StringUtils.isNotEmpty(mtMappingCount)) {
            mtMappingCountLong = Long.parseLong(mtMappingCount);
        }
        map.put("mapping", mtMappingCountLong);

        String mtMappingCounted = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtMappingCounted");
        long mtMappingCountedLong = 0;
        if (StringUtils.isNotEmpty(mtMappingCounted)) {
            mtMappingCountedLong = Long.parseLong(mtMappingCounted);
        }
        map.put("mappingOK", mtMappingCountedLong);

        String mtMappingCountFail = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtMappingCountFail");
        long mtMappingCountFailLong = 0;
        if (StringUtils.isNotEmpty(mtMappingCountFail)) {
            mtMappingCountFailLong = Long.parseLong(mtMappingCountFail);
        }
        map.put("mappingNO", mtMappingCountFailLong);

        return AjaxResult.success(map);
    }

    @PreAuthorize("@ss.hasPermi('ivw:meituan:query')")
    @GetMapping("/updateData")
    public void updateData() throws Exception {
        try {
          // this.updateMeituanAll();
           this.updateMeituanAllNew();
        } catch (Exception e) {
            log.info("美团更新数据失败: ", e);
        }
    }

    @PreAuthorize("@ss.hasPermi('ivw:meituan:query')")
    @GetMapping("/resetStatus")
    public void resetStatus() {
//        HotelUpdateStatus.expStatusMT = 0;
//        HotelUpdateStatus.mtIdsCountAdd = 0;
//        HotelUpdateStatus.mtDetailCount = 0;
//        HotelUpdateStatus.mtDetailCountGet = new AtomicInteger(0);
//        HotelUpdateStatus.mtDetailCountUpdate = 0;
//        HotelUpdateStatus.mtPriceCountGet = new AtomicInteger(0);
//        HotelUpdateStatus.mtPriceCountUpdate = 0;
//        HotelUpdateStatus.status = 0;
//        HotelUpdateStatus.mtMappingCount = 0;
//        HotelUpdateStatus.mtMappingCounted = new AtomicInteger(0);
//        HotelUpdateStatus.mtMappingCountFail = new AtomicInteger(0);
        //改为从redis中获取
        this.doResetStatus();

    }

    public void doResetStatus(){
        this.stringRedisTemplate.delete("HotelUpdateStatus.expStatusMT");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtIdsCountAdd");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtDetailCount");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtDetailCountGet");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtDetailCountUpdate");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtPriceCountGet");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtPriceCountUpdate");
        this.stringRedisTemplate.delete("HotelUpdateStatus.status");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtMappingCount");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtMappingCounted");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtMappingCountFail");
        this.stringRedisTemplate.delete("HotelUpdateStatus.mtDeleteJS");
    }


    public void insert(HotelInfoMeituan hotelInfo, JSONObject jsonObject) {
        JSONObject mtBase = jsonObject.getJSONObject("baseInfo");
        JSONObject mtDetail = jsonObject.getJSONObject("detailInfo");
        JSONArray mtNotices = jsonObject.getJSONArray("notices");
        JSONArray mtImages = jsonObject.getJSONArray("images");

        hotelInfo.setId(jsonObject.getString("hotelId"));
        hotelInfo.setName(mtBase.getString("name"));
        hotelInfo.setNameEn(mtBase.getString("nameEn"));
        hotelInfo.setPhone(mtBase.getString("phone"));
        JSONObject mtAddr = mtBase.getJSONObject("address");
        hotelInfo.setReserve3(mtBase.getString("bookable"));
        if (mtAddr.getJSONObject("province") != null) {
            hotelInfo.setProvinceId(mtAddr.getJSONObject("province").getString("code"));
        }
        hotelInfo.setCityId(mtAddr.getJSONObject("city").getString("code"));
        hotelInfo.setCityName(mtAddr.getJSONObject("city").getString("name"));
        if (mtAddr.getJSONObject("area") != null) {
            hotelInfo.setAreaId(mtAddr.getJSONObject("area").getString("code"));
        }
        hotelInfo.setBusinessDistricts(mtAddr.getJSONArray("businessDistricts")
                .getJSONObject(0).getString("name"));
        hotelInfo.setAddressLine(mtAddr.getJSONObject("addressLine").getString("name"));
        JSONArray lonlats = mtAddr.getJSONArray("coordinates");
        for (int i = 0; i < lonlats.size(); i++) {
            JSONObject lonlat = lonlats.getJSONObject(i);
            String name = lonlat.getString("provider");
            BigDecimal lon = BigDecimal.valueOf(lonlat.getLong("longitude")).divide(BigDecimal.valueOf(1000000));
            BigDecimal lat = BigDecimal.valueOf(lonlat.getLong("latitude")).divide(BigDecimal.valueOf(1000000));
            lat = lat.setScale(11, BigDecimal.ROUND_HALF_UP);
            lon = lon.setScale(11, BigDecimal.ROUND_HALF_UP);
            switch (name) {
                case "GAODE":
                    hotelInfo.setLonGd(lon);
                    hotelInfo.setLatGd(lat);
                    break;
                case "BAIDU":
                    hotelInfo.setLonBd(lon);
                    hotelInfo.setLatBd(lat);
                    break;
                case "GOOGLE":
                    hotelInfo.setLonGg(lon);
                    hotelInfo.setLatGg(lat);
            }
        }
        hotelInfo.setOpenDate(mtDetail.getString("openDate"));
        hotelInfo.setDecorationDate(mtDetail.getString("decorationDate"));
        hotelInfo.setDescription(mtDetail.getString("description"));
        //设置楼层
        JSONArray statics = mtDetail.getJSONArray("statics");
        for (int j = 0; j < statics.size(); j++) {
            JSONObject floor = statics.getJSONObject(j);
            if (floor.getString("type").equals("FLOOR_COUNT")) {
                hotelInfo.setFloorCount(floor.getString("value"));
            }
        }
        //设置评分
        JSONArray scores = mtDetail.getJSONArray("ratings");
        for (int k = 0; k < scores.size(); k++) {
            JSONObject score = scores.getJSONObject(k);
            if (score.getString("type").equals("AVG_SCORE")) {
                hotelInfo.setReserve2(score.getString("value"));
            }
        }
        JSONObject starInfo = mtDetail.getJSONObject("star");
        if (starInfo != null) {
            hotelInfo.setStarInfo(starInfo.toString());
        }
        JSONObject typeInfo = mtDetail.getJSONObject("type");
        if (typeInfo != null) {
            hotelInfo.setTypeId(typeInfo.toString());
        }
        JSONObject brandInfo = mtDetail.getJSONObject("brand");
        if (brandInfo != null) {
            hotelInfo.setBrandId(brandInfo.getString("name"));
        }
        JSONObject groupInfo = mtDetail.getJSONObject("group");
        if (groupInfo != null) {
            hotelInfo.setGroupId(groupInfo.getString("name"));
        }
        JSONObject themesInfo = mtDetail.getJSONObject("themes");
        if (themesInfo != null) {
            hotelInfo.setThemeId(themesInfo.toString());
        }
        JSONObject typesInfo = mtDetail.getJSONObject("types");
        if (themesInfo != null) {
            hotelInfo.setTypeId(typesInfo.toString());
        }
        //设置注意事项
        String noticeText = "";
        if (mtNotices != null) {
            for (int m = 0; m < mtNotices.size(); m++) {
                JSONObject notice = mtNotices.getJSONObject(m);
                noticeText += "<br>\\n" + notice.getString("value");
            }
            hotelInfo.setNoticeInfo(noticeText);
        }

        //设置首图
        for (int n = 0; n < mtImages.size(); n++) {
            JSONObject image = mtImages.getJSONObject(n);
            if (image.getString("title").equals("酒店首图")) {
                hotelInfo.setImage(image.getJSONArray("links").getJSONObject(0).getString("url"));
            }
        }
        hotelInfo.setStatus(1);
        HotelInfoMeituan hotelInfo2 = this.hotelService.selectHotelInfoMeituanById(hotelInfo.getId());
        hotelInfo.setIsDelete(0);
        if (hotelInfo2 != null) {
            this.hotelService.updateHotelInfoMeituan(hotelInfo);
        } else {
            this.hotelService.insertHotelInfoMeituan(hotelInfo);
        }
    }


    public HotelCityMapping getMapping(String platId) {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
        mapping.setPlatNum(platId);
        List<HotelCityMapping> list = this.hotelCityMappingService.selectHotelCityMappingList(mapping);
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    //全量美团酒店更新
    public void updateMeituanAll() throws Exception {
//        if(HotelUpdateStatus.status == 1){
//            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
//        }
//        HotelUpdateStatus.status = 1;
        //根据HotelUpdateStatus.expStatusMT 判断是否可以执行
        String expStatusMT = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
        if (StringUtils.isNotEmpty(expStatusMT) && "0".equals(expStatusMT)) {
            throw new Exception("上次执行未完成，请等待！");
        }
        if (StringUtils.isEmpty(expStatusMT)) {
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", "0");
        }
        HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
        hotelUpdateRecode.setYl2("2000023");
        hotelUpdateRecode.setId("MT" + MyTools.getTimesNo());
        hotelUpdateRecode.setUpdateBy(this.getUsername());
        hotelUpdateRecode.setTimeStart(new Date());
        while (true) {
            expStatusMT = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
            log.info("updateMeituanAll_expStatusMT:{}", expStatusMT);
            int expStatusMTInt = 0;
            if (StringUtils.isNotEmpty(expStatusMT)) {
                expStatusMTInt = Integer.parseInt(expStatusMT);
            }
            if (expStatusMTInt == 0) {
                log.info("插入美团IDS开始 ... ");
                new Thread(() -> this.insertMeituanIds(hotelUpdateRecode, 1, 2)).start();
                log.info("插入美团IDS结束 ... ");
            }
            if (expStatusMTInt == 2) {
                log.info("获取美团酒店详情开始 ... ");
                new Thread(() -> this.getMeituanDetail(hotelUpdateRecode, 3, 4)).start();
                log.info("获取美团酒店详情结束 ... ");
            }
            if (expStatusMTInt == 4) {
                log.info("更新美团酒店详情开始 ... ");
                new Thread(() -> this.updateMeituanDetail(5, 6)).start();
                log.info("更新美团酒店详情结束 ... ");
            }
            if (expStatusMTInt == 6) {
                log.info("获取美团酒店价格开始 ... ");
                new Thread(() -> this.getMeituanPrice(7, 8)).start();
                log.info("获取美团酒店价格结束 ... ");
            }
            if (expStatusMTInt == 8) {
                log.info("获取美团酒店最低价格开始 ... ");
                new Thread(() -> this.updateMeituanMinPrice(hotelUpdateRecode, 9, 10)).start();
                log.info("获取美团酒店最低价格结束 ... ");
                break;
            }
            Thread.sleep(10000);
            log.info("等待10秒执行下一轮操作！当前状态" + expStatusMTInt);
//            System.err.println("等待10秒执行下一轮操作！当前状态"+HotelUpdateStatus.expStatusMT);
        }
    }

    /**
     * 由写 hotel_info_meituan
     * 改写 hotel_gn_meituan
     *
     * @throws Exception
     */
    public void updateMeituanAllNew() throws Exception {
//        String expStatusMT = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
//        if (StringUtils.isNotEmpty(expStatusMT) && "0".equals(expStatusMT)) {
//            throw new Exception("上次执行未完成，请等待！");
//        }
//        if (StringUtils.isEmpty(expStatusMT)) {
//            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", "0");
//        }

        RLock rLock = redissonClient.getLock("hotel_gn_meituan_update_data_new");
        if (!rLock.tryLock(10, TimeUnit.SECONDS)) {
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }

        // 重置状态  因为定时任务会调用
        doResetStatus();

        // 如果是job过来会报错
        String userName = "admin";
        try {
            userName = this.getUsername();
        } catch (Exception e) {
        }

        try {
            HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
            hotelUpdateRecode.setYl2("2000023");
            hotelUpdateRecode.setId("MT" + MyTools.getTimesNo());
            hotelUpdateRecode.setUpdateBy(userName);
            hotelUpdateRecode.setTimeStart(new Date());
            while (true) {
                String expStatusMT = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
                log.info("updateMeituanAll_expStatusMT:{}", expStatusMT);
                int expStatusMTInt = 0;
                if (StringUtils.isNotEmpty(expStatusMT)) {
                    expStatusMTInt = Integer.parseInt(expStatusMT);
                }
                if (expStatusMTInt == 0) {
                    log.info("插入美团IDS开始 ... ");
                    new Thread(() -> this.insertMeituanIdsNew(hotelUpdateRecode, 1, 2)).start();
                    log.info("插入美团IDS结束 ... ");
                }
                if (expStatusMTInt == 2) {
                    log.info("获取美团酒店详情开始 ... ");
                    new Thread(() -> this.getMeituanDetailNew(hotelUpdateRecode, 3, 4)).start();
                    log.info("获取美团酒店详情结束 ... ");
                }
    //            if (expStatusMTInt == 4) {
    //                log.info("更新美团酒店详情开始 ... ");
    //                new Thread(() -> this.updateMeituanDetailNew(5, 6)).start();
    //                log.info("更新美团酒店详情结束 ... ");
    //            }
                if (expStatusMTInt == 4) {
                    break;
                }
    //            if (expStatusMTInt == 6) {
    //                log.info("获取美团酒店价格开始 ... ");
    //                new Thread(() -> this.getMeituanPriceNew(7, 8)).start();
    //                log.info("获取美团酒店价格结束 ... ");
    //            }
    //            if (expStatusMTInt == 8) {
    //                log.info("获取美团酒店最低价格开始 ... ");
    //                new Thread(() -> this.updateMeituanMinPriceNew(hotelUpdateRecode, 9, 10)).start();
    //                log.info("获取美团酒店最低价格结束 ... ");
    //                break;
    //            }
                Thread.sleep(10000);
                log.info("等待10秒执行下一轮操作！当前状态" + expStatusMTInt);
    //            System.err.println("等待10秒执行下一轮操作！当前状态"+HotelUpdateStatus.expStatusMT);
            }
        } catch (Exception e) {
           log.error("更新酒店数据失败: ", e);
        } finally {
            try {
                rLock.unlock();
            } catch (Exception e11) {
            }
        }
    }

    //插入美团IDS
    @RequestMapping("/insertMeituanIds")
    public void insertMeituanIds(HotelUpdateRecode hotelUpdateRecode, int start, int end) {
        try {
            this.logger.info("开始执行插入美团IDs");
            //插入状态到redis中
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
            //HotelUpdateStatus.expStatusMT = start;
            List<String> idsAll = new ArrayList<>();
            long maxId = 0;
            List<HotelInfoMeituan> list = this.hotelService.selectHotelInfoMeituanList2(null);
            log.info("获取美团酒店总数量: {}", list.size());
            Map<String, HotelInfoMeituan> map = new HashMap<>();
            for (HotelInfoMeituan hotelInfoMeituan : list) {
                map.put(hotelInfoMeituan.getId(), hotelInfoMeituan);
            }
            for (int i = 0; i < 10000; i++) {
                String idsText = MeituanApi.hotelList(maxId);
                log.info("获取美团酒店ID列表: {},当前调用次数:{}", i, idsText);
                JSONObject jsonObject = JSONObject.parseObject(idsText);
                if (jsonObject.getInteger("code") == 0) {
                    jsonObject = jsonObject.getJSONObject("result");
                    maxId = jsonObject.getInteger("maxId");
                    if (maxId == -1) {
                        this.logger.info("进入maxId = -1,执行次数i:{}", i);
                        JSONArray ids = jsonObject.getJSONArray("hotelIds");
                        if (Objects.nonNull(ids)) {
                            HotelInfoMeituan meituan = new HotelInfoMeituan();
                            for (int j = 0; j < ids.size(); j++) {
                                if (map.get(ids.getString(j)) == null) {
                                    try {
                                        meituan.setId(ids.getString(j));
                                        meituan.setReserve9("1");
                                        this.hotelService.insertHotelInfoMeituan(meituan);
                                        this.logger.info("插入美团酒店数据成功: id=" + ids.getString(j));
                                        //HotelUpdateStatus.mtIdsCountAdd++;
                                        //使用redis的++
                                        this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtIdsCountAdd");
                                    } catch (Exception e) {
                                        log.error("插入美团酒店数据失败: id=" + ids.getString(j) + " error: ", e);
                                        continue;
                                    }
                                } else {
                                    idsAll.add(ids.getString(j));
                                }
                            }
                        }

                        //删除已失效酒店及映射
                        for (HotelInfoMeituan hotel : list) { //遍历key
                            if (!idsAll.contains(hotel.getId())) {
                                this.hotelService.deleteHotelInfoMeituanById(hotel.getId());
                                this.logger.info("删除美团酒店数据成功: id={}", hotel.getId());
                                //HotelUpdateStatus.mtDeleteJS++;
                                //使用redis的++
                                this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtDeleteJS");
                                JdJdbMapping queryMapping = new JdJdbMapping();
                                queryMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
                                queryMapping.setPlatId(hotel.getId());
                                for (JdJdbMapping jdJdbMapping : this.jdJdbMappingService.selectJdJdbMappingList(queryMapping)) {
//                                    jdJdbMappingService.deleteJdJdbMappingById(jdJdbMapping.getId());
                                    this.jdJdbMappingService.deleteJdJdbMappingByIdV1(jdJdbMapping.getId());
                                }

                                ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                                zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
                                zhJdJdbMapping.setPlatId(hotel.getId());
                                this.zhJdJdbMappingService.deleteList(this.zhJdJdbMappingService.selectZhJdJdbMappingList(zhJdJdbMapping));
                            }
                        }
                        String mtDelete = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDeleteJS");
                        if (StringUtils.isNotEmpty(mtDelete)) {
                            hotelUpdateRecode.setCountDel(Long.valueOf(mtDelete));
                        }
                        String mtIdsCountAdd = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtIdsCountAdd");
                        if (StringUtils.isNotEmpty(mtIdsCountAdd)) {
                            hotelUpdateRecode.setCountTotal(Long.valueOf(mtIdsCountAdd));
                        }
                        this.hotelUpdateRecodeService.insertHotelUpdateRecode(hotelUpdateRecode);
                        this.logger.info("获取美团酒店ID列表结束");

//                        HotelUpdateStatus.expStatusMT = end;
                        break;
                    }
                    this.logger.info("获取美团酒店ID列表: {},当前调用次数:{}", maxId, i);
                    JSONArray ids = jsonObject.getJSONArray("hotelIds");
                    HotelInfoMeituan meituan = new HotelInfoMeituan();
                    for (int j = 0; j < ids.size(); j++) {
                        if (map.get(ids.getString(j)) == null) {
                            try {
                                meituan.setId(ids.getString(j));
                                meituan.setReserve9("1");
                                this.hotelService.insertHotelInfoMeituan(meituan);
                                this.logger.info("插入美团酒店数据成功: id={}", ids.getString(j));
                                //HotelUpdateStatus.mtIdsCountAdd++;
                                //改为redis
                                this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtIdsCountAdd");
                            } catch (Exception e) {
                                log.error("插入美团酒店数据失败: id=" + ids.getString(j) + " error: ", e);
                                continue;
                            }
                        } else {
                            this.logger.info("美团酒店数据已存在: id={}", ids.getString(j));
                            idsAll.add(ids.getString(j));
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.info("美团插入酒店IDS异常: ", e);
        } finally {
            this.logger.info("获取美团酒店ID列表结束 更改HotelUpdateStatus.expStatusMT状态");
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
        }
    }

    @RequestMapping("/insertMeituanIdsNew")
    public void insertMeituanIdsNew(HotelUpdateRecode hotelUpdateRecode, int start, int end) {
        try {
            this.logger.info("开始执行插入美团IDs");
            //插入状态到redis中
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
           // List<String> idsAll = new ArrayList<>();
            long maxId = 0;
            List<HotelGnBase> list = this.hotelGnBaseService.getAllIdList(PlatEnum.PLAT_MT);
            log.info("获取美团酒店总数量: {}", list.size());
            Map<String, HotelGnBase> map = new HashMap<>();
            for (HotelGnBase hotelInfoMeituan : list) {
                map.put(hotelInfoMeituan.getId(), null);
            }
            list = null;
            HotelGnBase meituan = new HotelGnBase();
            for (int i = 0; i < 10000; i++) {
                String idsText = MeituanApi.hotelList(maxId);
                log.info("获取美团酒店ID列表: {},当前调用次数:{}", i, idsText);
                JSONObject jsonObject = JSONObject.parseObject(idsText);
                if (jsonObject.getInteger("code") == 0) {
                    jsonObject = jsonObject.getJSONObject("result");
                    maxId = jsonObject.getInteger("maxId");
                    this.logger.info("获取美团酒店ID列表进入maxId:{},执行次数i:{}", maxId, i);
                    JSONArray ids = jsonObject.getJSONArray("hotelIds");
                    if (Objects.nonNull(ids)) {
                        for (int j = 0; j < ids.size(); j++) {
                            try {
                                if(map.containsKey(ids.getString(j))){
                                    //this.logger.info("美团酒店id已存在: id={}", ids.getString(j));
                                    continue;
                                }
                                meituan.setId(ids.getString(j));
                                meituan.setReserve9("1");
                                meituan.setStatus(0);
                                meituan.setIsDelete(0);
                                meituan.setIncrementStatus(0);
                                meituan.setIncrementTime(new Date());
                                meituan.setCreateTime(new Date());
                                meituan.setUpdateTime(new Date());
                                // 如果是定时任务会报错
                                String userName = "admin";
                                try {
                                    userName = SecurityUtils.getUsername();
                                } catch (Exception e) {

                                }
                                meituan.setCreateBy(userName);
                                meituan.setUpdateBy(userName);
                                this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_MT, Collections.singletonList(meituan));
                                this.logger.info("插入美团酒店数据成功: id=" + ids.getString(j));
                                this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtIdsCountAdd");
                            } catch (Exception e) {
                                log.error("插入美团酒店数据失败: id=" + ids.getString(j) + " error: ", e);
                            }

                        }
                    }

                    this.logger.info("获取美团酒店ID列表结束");
                    if (maxId == -1) {
                        break;
                    }

                }
            }
        } catch (Exception e) {
            log.info("美团插入酒店IDS异常: ", e);
        } finally {
            String mtDelete = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDeleteJS");
            if (StringUtils.isNotEmpty(mtDelete)) {
                hotelUpdateRecode.setCountDel(Long.valueOf(mtDelete));
            }
            String mtIdsCountAdd = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtIdsCountAdd");
            if (StringUtils.isNotEmpty(mtIdsCountAdd)) {
                hotelUpdateRecode.setCountTotal(Long.valueOf(mtIdsCountAdd));
            }
            this.hotelUpdateRecodeService.insertHotelUpdateRecode(hotelUpdateRecode);
            this.logger.info("获取美团酒店ID列表结束 更改HotelUpdateStatus.expStatusMT状态");
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
        }
    }

    //获取美团酒店详情
    public void getMeituanDetail(HotelUpdateRecode recode, int start, int end) {
        //HotelUpdateStatus.expStatusMT = start;
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
        HotelInfoMeituan mt = new HotelInfoMeituan();
        mt.setStatus(0);
        List<HotelInfoMeituan> meituanList = this.hotelService.selectHotelInfoMeituanList2(mt);
        this.logger.info("美团酒店详情总数: " + meituanList.size());
        //HotelUpdateStatus.mtDetailCount = meituanList.size();
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.mtDetailCount", String.valueOf(meituanList.size()));
        recode.setCountTotal(Long.valueOf(Objects.requireNonNull(this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDetailCount"))));
        this.hotelUpdateRecodeService.updateHotelUpdateRecode(recode);
//        List<List<Long>> idsList = new ArrayList<>();
//        List<Long> ids = new ArrayList<>();
//        for (int i = 0;i<meituanList.size();i++){
//            HotelInfoMeituan meituan = meituanList.get(i);
//            if(ids.size() == maxCountDetailMeituan){
//                idsList.add(ids);
//                ids = new ArrayList<>();
//            }
//            ids.add(Long.valueOf(meituan.getId()));
//            if(i == meituanList.size()-1){
//                idsList.add(ids);
//            }
//        }
//        logger.info("美团酒店详情idsList总数: " + idsList.size());
//        int countTmp = idsList.size()/threadNumDetailMeituan;
//        if(idsList.size()%threadNumDetailMeituan != 0) {
//            countTmp = countTmp + 1;
//        }
//        if(countTmp > idsList.size()) {
//            countTmp = idsList.size();
//        }
//        int count = countTmp;
        List<Long> idList = meituanList.stream().map(hotel -> Long.valueOf(hotel.getId())).collect(Collectors.toList());
        this.logger.info("美团酒店详情idList总数: " + idList.size());
        //idList
        List<List<Long>> splitList = ListUtil.groupList(idList, threadNumDetailMeituan);
        this.logger.info("美团酒店详情splitList分组总数: " + splitList.size());
        File file = new File(ConstantList.MEITUAN_PATH + "mtDetail.txt");
        if (file.exists()) {
            file.delete();
        }
        CompletionService<TableResult> completionService = new ExecutorCompletionService<>(this.threadPoolTaskExecutor);
        IntStream.range(0, threadNumDetailMeituan).forEach(suffix -> {
            completionService.submit(() -> {
                int totalCount = 0;
                List<Long> suffixList = splitList.get(suffix);
                this.logger.info("美团酒店详情线程: {} 开始,待处理数量:{}", suffix, suffixList.size());
                List<List<Long>> idsListSub = ListUtil.subList(suffixList, threadNumDetailMeituan);
                this.logger.info("美团酒店详情线程: " + suffix + " 分组数量: " + idsListSub.size());
                for (List<Long> hotelIds : idsListSub) {
                    new getdetailMT(hotelIds, 0).run();
                    totalCount = totalCount + hotelIds.size();
                }
//                if(suffix == count - 1){
//                    //HotelUpdateStatus.expStatusMT = end;
//                    logger.info("获取美团酒店详情结束 准备更新HotelUpdateStatus.expStatusMT");
//                    stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
//                }
//                String expStatusMT = stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
                this.logger.info("美团酒店详情线程: " + suffix + " 结束");
                return new TableResult(suffix, totalCount);
            });
        });


        //获取执行结果
        try {
            for (int i = 0; i < threadNumDetailMeituan; i++) {
                TableResult tableResult = completionService.take().get();
                this.logger.info("美团异步结果完成:{},更新数据量为:{}", tableResult.getSuffix(), tableResult.getTotalCount());
            }
        } catch (InterruptedException e) {
            this.logger.error("美团获取详情InterruptedException errorMessage:{}", e.getMessage(), e);
        } catch (ExecutionException e) {
            this.logger.error("美团获取详情执行异常 errorMessage:{}", e.getMessage(), e);
        } finally {
            //修改状态 进入下一阶段
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
            this.logger.info("获取美团酒店详情结束 更改HotelUpdateStatus.expStatusMT状态完成");
        }

//        Timer timer = new Timer();
//        timer.scheduleAtFixedRate(new TimerTask() {
//            int i = 0;
//            @Override
//            public void run() {
//                List<List<Long>> idsListSub = null;
//                if (i == count - 1) {
//                    idsListSub = idsList.subList(i * threadNumDetailMeituan, idsList.size());
//                } else {
//                    idsListSub = idsList.subList(i * threadNumDetailMeituan, (i + 1) * threadNumDetailMeituan);
//                }
//                for (List<Long> ids : idsListSub) {
//                    new getdetailMT(ids, 0).start();
//                }
//                if(i == count - 1){
//                    //HotelUpdateStatus.expStatusMT = end;
//                    logger.info("获取美团酒店详情结束 准备更新HotelUpdateStatus.expStatusMT");
//                    stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
//                    timer.cancel();
//                }
//                String expStatusMT = stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
//                logger.info("getMeituanDetail.expStatusMT: {},当前i:{},当前count:{}", expStatusMT, i, count);
//                i++;
//            }
//        }, 1000, 1000);
    }

    public void getMeituanDetailNew(HotelUpdateRecode recode, int start, int end) {
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
        List<HotelGnBase> meituanList = this.hotelGnBaseService.getAllIdList(PlatEnum.PLAT_MT);
        this.logger.info("美团酒店详情总数: " + meituanList.size());
        //HotelUpdateStatus.mtDetailCount = meituanList.size();
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.mtDetailCount", String.valueOf(meituanList.size()));
        recode.setCountTotal(Long.valueOf(Objects.requireNonNull(this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDetailCount"))));
        this.hotelUpdateRecodeService.updateHotelUpdateRecode(recode);
        List<Long> idList = meituanList.stream().map(hotel -> Long.valueOf(hotel.getId())).collect(Collectors.toList());
        meituanList = null;
        this.logger.info("美团酒店详情idList总数: " + idList.size());
        //idList
        List<List<Long>> splitList = ListUtil.groupList(idList, threadNumDetailMeituan);
        this.logger.info("美团酒店详情splitList分组总数: " + splitList.size());
        File file = new File(ConstantList.MEITUAN_PATH + "mtDetail.txt");
        if (file.exists()) {
            file.delete();
        }
        CompletionService<TableResult> completionService = new ExecutorCompletionService<>(this.allMeituanHotelGnExecutor);
        IntStream.range(0, threadNumDetailMeituan).forEach(suffix -> {
            completionService.submit(() -> {
                int totalCount = 0;
                List<Long> suffixList = splitList.get(suffix);
                this.logger.info("美团酒店详情线程: {} 开始,待处理数量:{}", suffix, suffixList.size());
                List<List<Long>> idsListSub = ListUtil.subList(suffixList, threadNumDetailMeituan);
                this.logger.info("美团酒店详情线程: " + suffix + " 分组数量: " + idsListSub.size());
                for (List<Long> hotelIds : idsListSub) {
                    //new getdetailMT(hotelIds, 0).run();
                    this.processHotelGnMeituanByIds(hotelIds);
                    totalCount = totalCount + hotelIds.size();
                }
                this.logger.info("美团酒店详情线程: " + suffix + " 结束");
                return new TableResult(suffix, totalCount);
            });
        });


        //获取执行结果
        try {
            for (int i = 0; i < threadNumDetailMeituan; i++) {
                TableResult tableResult = completionService.take().get();
                this.logger.info("美团异步结果完成:{},更新数据量为:{}", tableResult.getSuffix(), tableResult.getTotalCount());
            }
        } catch (InterruptedException e) {
            this.logger.error("美团获取详情InterruptedException errorMessage:{}", e.getMessage(), e);
        } catch (ExecutionException e) {
            this.logger.error("美团获取详情执行异常 errorMessage:{}", e.getMessage(), e);
        } finally {
            //修改状态 进入下一阶段
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
            this.logger.info("获取美团酒店详情结束 更改HotelUpdateStatus.expStatusMT状态完成");
        }

    }

    public class getdetailMT extends Thread {
        private List<Long> ids;
        private int i;

        getdetailMT(List<Long> ids, int i) {
            this.ids = ids;
            this.i = i;
        }

        @Override
        public void run() {
            try {
                while (true) {
                    this.i++;
                    log.info("美团读取酒店详情请求: {},当前执行次数:{}", this.ids, this.i);
                    String infoText = (String) fetchDataFunction(PlatEnum.PLAT_MT.getValue() + "_resource_meituan_Detail", 1, 18, (result) -> MeituanApi.hotelDetail(this.ids));
//                Response<SearchModel> response = JSON.parseObject(hotelSearchResponse, new TypeReference<Response<SearchModel>>() {});
//                String infoText = MeituanApi.hotelDetail(ids);
                    log.info("美团读取酒店详情响应: {}", infoText);
                    JSONObject jsonObject = JSONObject.parseObject(infoText);
                    if (jsonObject.getInteger("code") == 0) {
                        JSONArray infos = jsonObject.getJSONObject("result").getJSONArray("hotelContents");
                        if (infos == null) {
                            return;
                        }
                        RLock lock = redissonClient.getLock("writeMTTXTLock");
                        for (int k = 0; k < infos.size(); k++) {
                            JSONObject info = infos.getJSONObject(k);
                            HotelInfoMeituan meituan = getHotelInfoMeituan(info);
                            meituan.setJsonStr(info.toString());
                            JSONObject mtPolicies = info.getJSONObject("policy");
                            meituan.setPolicyInfo(mtPolicies.toString());
                            JSONArray mtFacilities = info.getJSONArray("facilities");
                            meituan.setFacilitiesInfo(mtFacilities.toString());
                            String json = objectMapper.writeValueAsString(meituan);
                            try {
                                while (true) {
                                    boolean isLock = lock.tryLock(1, 1, TimeUnit.SECONDS);
                                    log.info("美团读取酒店详情lock:{}", isLock);
                                    if (isLock) {
                                        TXTUtil.writeMTTXT(JSONObject.parse(json).toString(), ConstantList.MEITUAN_PATH,
                                                "mtDetail.txt");
                                        break;
                                    }
                                }
                            } catch (Exception e) {
                                log.error("获取美团详情lock异常 errorMessage:{}", e.getMessage(), e);
                            } finally {
                                if (lock.isHeldByCurrentThread()) {
                                    lock.unlock();
                                }
                            }

                            //HotelUpdateStatus.mtDetailCountGet.incrementAndGet();
                            stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtDetailCountGet", 1);
                            logger.info("write success------");
                        }
                        break;
                    } else if (jsonObject.getInteger("code") == 1200) {
//                    i++;
//                    if(i < maxReqCount){
//                        new getdetailMT(ids, i);
//                    }
                        log.info("获取美团详情CODE=1200", infoText);
                        Thread.sleep(100L);
//                    new getdetailMT(ids, i).start();
                    } else {
                        log.info("获取美团详情进ELSE了: {}", infoText);
                        TXTUtil.writeTXT(new Date() + " 获取美团详情进else了： " + infoText + "----------" + this.ids,
                                ConstantList.LOG_PATH, "log.txt");
                        break;
                    }
                }
            } catch (Exception e) {
//                TXTUtil.writeTXT(new Date() +" 获取美团详情进异常了： " + e + "----------"+ ids,
//                        ConstantList.LOG_PATH, "log.txt");
                log.error("获取美团详情异常: ", e);
            }
        }
    }
    public void processHotelGnMeituanByIds(List<Long> ids){

        try {
            int i = 0;
            while (true) {
                i++;
                log.info("美团读取酒店详情请求ids: {},当前执行次数:{}", ids, i);
                String infoText = (String) fetchDataFunction(PlatEnum.PLAT_MT.getValue() + "_resource_meituan_Detail", 1, 18, (result) -> MeituanApi.hotelDetail(ids));
                log.info("美团读取酒店详情响应: {}", infoText);
                JSONObject jsonObject = JSONObject.parseObject(infoText);
                if (jsonObject.getInteger("code") == 0) {
                    JSONArray infos = jsonObject.getJSONObject("result").getJSONArray("hotelContents");
                    if (infos == null) {
                        return;
                    }
                    for (int k = 0; k < infos.size(); k++) {
                        JSONObject info = infos.getJSONObject(k);
                        try {

                            logger.info("开始处理美团报文信息jsonObject:{}", jsonObject.toJSONString());
                            HotelGnMeituan meituan = hotelInfoMeituanService.buildHotelGnMeituanFromJsonObject(info);
                            int result = this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_MT, Lists.newArrayList(meituan));
                            if(result > 0){
                                stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtDetailCountGet", 1);
                                logger.info("添加美团报文信息成功 {}------", meituan.getId());
                            }
                        } catch (Exception e) {
                            log.error("处理美团报文信息异常 info:{} errorMessage:{}", info.toString(), e.getMessage(), e);
                        }

                    }
                    break;
                } else if (jsonObject.getInteger("code") == 1200) {
                    log.info("获取美团详情CODE=1200", infoText);
                    Thread.sleep(100L);
                } else {
                    log.info("获取美团详情进ELSE了: {}", infoText);
                    TXTUtil.writeTXT(new Date() + " 获取美团详情进else了： " + infoText + "----------" + ids,
                            ConstantList.LOG_PATH, "log.txt");
                    break;
                }
            }
        } catch (Exception e) {
            log.error("获取美团详情异常: ", e);
        }
    }

    public HotelInfoMeituan getHotelInfoMeituan(JSONObject meituanJson) {
        HotelInfoMeituan hotelInfo = new HotelInfoMeituan();
        try {
            JSONObject mtBase = meituanJson.getJSONObject("baseInfo");
            JSONObject mtDetail = meituanJson.getJSONObject("detailInfo");
            JSONArray mtNotices = meituanJson.getJSONArray("notices");
            JSONArray mtImages = meituanJson.getJSONArray("images");

            hotelInfo.setId(meituanJson.getString("hotelId"));
            hotelInfo.setName(mtBase.getString("name"));
            hotelInfo.setNameEn(mtBase.getString("nameEn"));
            hotelInfo.setPhone(mtBase.getString("phone"));
            JSONObject mtAddr = mtBase.getJSONObject("address");
            hotelInfo.setReserve3(mtBase.getString("bookable"));
            if (mtAddr.getJSONObject("province") != null) {
                hotelInfo.setProvinceId(mtAddr.getJSONObject("province").getString("code"));
                hotelInfo.setProvinceName(mtAddr.getJSONObject("province").getString("name"));
            }
            hotelInfo.setCityId(mtAddr.getJSONObject("city").getString("code"));
            hotelInfo.setCityName(mtAddr.getJSONObject("city").getString("name"));
            if (mtAddr.getJSONObject("area") != null) {
                hotelInfo.setAreaId(mtAddr.getJSONObject("area").getString("code"));
                hotelInfo.setAreaName(mtAddr.getJSONObject("area").getString("name"));
            }
            hotelInfo.setBusinessDistricts(mtAddr.getJSONArray("businessDistricts")
                    .getJSONObject(0).getString("name"));
            hotelInfo.setBusinessDistrictId(mtAddr.getJSONArray("businessDistricts")
                    .getJSONObject(0).getString("code"));
            hotelInfo.setAddressLine(mtAddr.getJSONObject("addressLine").getString("name"));
            hotelInfo.setAddressLineEn(mtAddr.getJSONObject("addressLine").getString("nameEn"));
            JSONArray lonlats = mtAddr.getJSONArray("coordinates");
            for (int i = 0; i < lonlats.size(); i++) {
                JSONObject lonlat = lonlats.getJSONObject(i);
                String name = lonlat.getString("provider");
                BigDecimal lon = BigDecimal.valueOf(lonlat.getLong("longitude")).divide(BigDecimal.valueOf(1000000));
                BigDecimal lat = BigDecimal.valueOf(lonlat.getLong("latitude")).divide(BigDecimal.valueOf(1000000));
                lat = lat.setScale(11, BigDecimal.ROUND_HALF_UP);
                lon = lon.setScale(11, BigDecimal.ROUND_HALF_UP);
                switch (name) {
                    case "GAODE":
                        hotelInfo.setLonGd(lon);
                        hotelInfo.setLatGd(lat);
                        break;
                    case "BAIDU":
                        hotelInfo.setLonBd(lon);
                        hotelInfo.setLatBd(lat);
                        break;
                    case "GOOGLE":
                        hotelInfo.setLonGg(lon);
                        hotelInfo.setLatGg(lat);
                }
            }
            hotelInfo.setOpenDate(mtDetail.getString("openDate"));
            hotelInfo.setDecorationDate(mtDetail.getString("decorationDate"));
            hotelInfo.setDescription(mtDetail.getString("description"));
            //设置楼层
            JSONArray statics = mtDetail.getJSONArray("statics");
            for (int j = 0; j < statics.size(); j++) {
                JSONObject floor = statics.getJSONObject(j);
                if (floor.getString("type").equals("FLOOR_COUNT")) {
                    hotelInfo.setFloorCount(floor.getString("value"));
                }
            }
            //设置评分
            JSONArray scores = mtDetail.getJSONArray("ratings");
            for (int k = 0; k < scores.size(); k++) {
                JSONObject score = scores.getJSONObject(k);
                if (score.getString("type").equals("AVG_SCORE")) {
                    hotelInfo.setReserve2(score.getString("value"));
                }
            }
            JSONObject starInfo = mtDetail.getJSONObject("star");
            if (starInfo != null) {
                hotelInfo.setStarInfo(starInfo.toString());
                hotelInfo.setStar(starInfo.getInteger("code"));
            }
            JSONObject typeInfo = mtDetail.getJSONObject("type");
            if (typeInfo != null) {
                hotelInfo.setTypeId(typeInfo.toString());
            }
            JSONObject brandInfo = mtDetail.getJSONObject("brand");
            if (brandInfo != null) {
                hotelInfo.setBrandId(brandInfo.getString("name"));
                hotelInfo.setBrandName(brandInfo.getString("code"));
            }
            JSONObject groupInfo = mtDetail.getJSONObject("group");
            if (groupInfo != null) {
                hotelInfo.setGroupId(groupInfo.getString("name"));
                hotelInfo.setGroupName(groupInfo.getString("code"));
            }
            JSONObject themesInfo = mtDetail.getJSONObject("themes");
            if (themesInfo != null) {
                hotelInfo.setThemeId(themesInfo.toString());
            }
            JSONObject typesInfo = mtDetail.getJSONObject("types");
            if (themesInfo != null) {
                hotelInfo.setTypeId(typesInfo.toString());
            }

            JSONArray types = mtDetail.getJSONArray("types");
            if (types != null) {
                hotelInfo.setTypeCode(types.getJSONObject(0).getString("code"));
            }
            //设置注意事项
            String noticeText = "";
            if (mtNotices != null) {
                for (int m = 0; m < mtNotices.size(); m++) {
                    JSONObject notice = mtNotices.getJSONObject(m);
                    noticeText += "<br>\\n" + notice.getString("value");
                }
                hotelInfo.setNoticeInfo(noticeText);
            }

            //设置首图
            for (int n = 0; n < mtImages.size(); n++) {
                JSONObject image = mtImages.getJSONObject(n);
                if (image.getString("title").equals("酒店首图")) {
                    hotelInfo.setImage(image.getJSONArray("links").getJSONObject(0).getString("url"));
                }
            }
        } catch (Exception e) {
            this.logger.error("设置hotel进异常了： " + e);
            TXTUtil.writeTXT(new Date() + " 设置hotel进异常了： " + e + "----------" + meituanJson
                    , ConstantList.LOG_PATH, "log.txt");
        }
        return hotelInfo;
    }

    //更新美团酒店详情
    public void updateMeituanDetail(int start, int end) {
        //HotelUpdateStatus.expStatusMT = start;
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
        this.logger.info("updateMeituanDetail： " + start);
        LineIterator it = null;
        try {
            File file = new File(ConstantList.MEITUAN_PATH + "mtDetail.txt");
            if (file.exists()) {
                it = FileUtils.lineIterator(file, "utf8");
                ObjectMapper objectMapper = new ObjectMapper();
                String line = null;
                while (it.hasNext()) {
                    try {
                        line = it.nextLine();
                        HotelInfoMeituan meituan = objectMapper.readValue(line, HotelInfoMeituan.class);
                        meituan.setStatus(1);
                        this.hotelService.updateHotelInfoMeituan(meituan);
                        this.logger.info("更新美团详情成功： " + meituan.getId());
                        //HotelUpdateStatus.mtDetailCountUpdate++;
                        this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtDetailCountUpdate", 1);
                    } catch (Exception e) {
                        log.error("更新美团详情异常: ", e);
                        log.error("mtDetail异常信息", line);
//                        TXTUtil.writeTXT(new Date() +" 更新mtDetail进异常了： " + e + "----------"
//                                , ConstantList.LOG_PATH, "log.txt");
                        continue;
                    }
                }
                //HotelUpdateStatus.expStatusMT = end;
                this.logger.info("更新美团详情结束");
                this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
            }
        } catch (Exception e) {
            log.error("更新美团详情异常外: ", e);
//            TXTUtil.writeTXT(new Date() +" 更新mtDetail进异常了： " + e + "----------"
//                    , ConstantList.LOG_PATH, "log.txt");
            //HotelUpdateStatus.expStatusMT = end;
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
        } finally {
            LineIterator.closeQuietly(it);
        }
    }

    public void updateMeituanDetailNew(int start, int end) {
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
        this.logger.info("updateMeituanDetail： " + start);
        LineIterator it = null;
        try {
            File file = new File(ConstantList.MEITUAN_PATH + "mtDetail.txt");
            if (file.exists()) {
                it = FileUtils.lineIterator(file, "utf8");
                ObjectMapper objectMapper = new ObjectMapper();
                String line = null;
                while (it.hasNext()) {
                    try {
                        line = it.nextLine();
                        HotelInfoMeituan meituan = objectMapper.readValue(line, HotelInfoMeituan.class);
                        meituan.setStatus(1);
                        this.hotelService.saveOrUpdateHotelGnMeituan(meituan);
                        this.logger.info("更新美团详情成功： " + meituan.getId());
                        //HotelUpdateStatus.mtDetailCountUpdate++;
                        this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtDetailCountUpdate", 1);
                    } catch (Exception e) {
                        log.error("更新美团详情异常: ", e);
                        log.error("mtDetail异常信息", line);
//                        TXTUtil.writeTXT(new Date() +" 更新mtDetail进异常了： " + e + "----------"
//                                , ConstantList.LOG_PATH, "log.txt");
                        continue;
                    }
                }
                //HotelUpdateStatus.expStatusMT = end;
                this.logger.info("更新美团详情结束");
                this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
            }
        } catch (Exception e) {
            log.error("更新美团详情异常外: ", e);
//            TXTUtil.writeTXT(new Date() +" 更新mtDetail进异常了： " + e + "----------"
//                    , ConstantList.LOG_PATH, "log.txt");
            //HotelUpdateStatus.expStatusMT = end;
            this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
        } finally {
            LineIterator.closeQuietly(it);
        }
    }


    //获取美团酒店最小价
    public void getMeituanPrice(int start, int end) {
        //HotelUpdateStatus.expStatusMT = start;
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
        HotelInfoMeituan mt = new HotelInfoMeituan();
        mt.setStatus(1);
        List<HotelInfoMeituan> meituanList = this.hotelService.selectHotelInfoMeituanList2(mt);
        List<List<Long>> idsList = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < meituanList.size(); i++) {
            HotelInfoMeituan meituan = meituanList.get(i);
            if (ids.size() == maxCountPriceMeituan) {
                idsList.add(ids);
                ids = new ArrayList<>();
            }
            ids.add(Long.valueOf(meituan.getId()));
            if (i == meituanList.size() - 1) {
                idsList.add(ids);
            }
        }
        int countTmp = idsList.size() / threadNumPriceMeituan;
        if (idsList.size() % threadNumPriceMeituan != 0) {
            countTmp = countTmp + 1;
        }
        int count = countTmp;
        File file = new File(ConstantList.MEITUAN_PATH + "mtPrice.txt");
        if (file.exists()) {
            file.delete();
        }
        Timer timer = new Timer();
        timer.scheduleAtFixedRate(new TimerTask() {
            int i = 0;

            @Override
            public void run() {
                List<List<Long>> idsListSub = null;
                if (this.i == count - 1) {
                    idsListSub = idsList.subList(this.i * threadNumPriceMeituan, idsList.size());
                } else {
                    idsListSub = idsList.subList(this.i * threadNumPriceMeituan, (this.i + 1) * threadNumPriceMeituan);
                }
                for (List<Long> ids : idsListSub) {
                    new getMinPriceMT(ids, 0).start();
                }
                if (this.i == count - 1) {
                    //HotelUpdateStatus.expStatusMT = end;
                    stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
                    timer.cancel();
                }
                String expStatusMT = stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
                logger.info("getMeituanPrice.expStatusMT: {},当前i:{},当前count:{}", expStatusMT, this.i, count);
                this.i++;
            }
        }, 1000, 1000);
    }

    public void getMeituanPriceNew(int start, int end) {
        //HotelUpdateStatus.expStatusMT = start;
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
        List<HotelGnBase> meituanList = this.hotelGnBaseService.getAllIdList(PlatEnum.PLAT_MT);
        List<List<Long>> idsList = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < meituanList.size(); i++) {
            HotelGnBase meituan = meituanList.get(i);
            if (ids.size() == maxCountPriceMeituan) {
                idsList.add(ids);
                ids = new ArrayList<>();
            }
            ids.add(Long.valueOf(meituan.getId()));
            if (i == meituanList.size() - 1) {
                idsList.add(ids);
            }
        }
        int countTmp = idsList.size() / threadNumPriceMeituan;
        if (idsList.size() % threadNumPriceMeituan != 0) {
            countTmp = countTmp + 1;
        }
        int count = countTmp;
        File file = new File(ConstantList.MEITUAN_PATH + "mtPrice.txt");
        if (file.exists()) {
            file.delete();
        }
        Timer timer = new Timer();
        timer.scheduleAtFixedRate(new TimerTask() {
            int i = 0;

            @Override
            public void run() {
                List<List<Long>> idsListSub = null;
                if (this.i == count - 1) {
                    idsListSub = idsList.subList(this.i * threadNumPriceMeituan, idsList.size());
                } else {
                    idsListSub = idsList.subList(this.i * threadNumPriceMeituan, (this.i + 1) * threadNumPriceMeituan);
                }
                for (List<Long> ids : idsListSub) {
                    new getMinPriceMTNew(ids, 0).start();
                }
                if (this.i == count - 1) {
                    //HotelUpdateStatus.expStatusMT = end;
                    stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
                    timer.cancel();
                }
                String expStatusMT = stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
                logger.info("getMeituanPrice.expStatusMT: {},当前i:{},当前count:{}", expStatusMT, this.i, count);
                this.i++;
            }
        }, 1000, 1000);
    }

    class getMinPriceMT extends Thread {
        private List<Long> ids;
        private int i;

        getMinPriceMT(List<Long> ids, int i) {
            this.ids = ids;
            this.i = i;
        }

        @Override
        public void run() {
            try {
                String infoText = MeituanApi.hotelProduct2(this.ids);
                JSONObject jsonObject = JSONObject.parseObject(infoText);
                if (jsonObject.getInteger("code") == 0) {
                    JSONArray infos = jsonObject.getJSONObject("result").getJSONArray("hotelGoods");
                    if (infos == null) {
                        return;
                    }
                    HotelInfoMeituan meituan = new HotelInfoMeituan();
                    for (int k = 0; k < infos.size(); k++) {
                        JSONObject info = infos.getJSONObject(k);
                        meituan.setId(info.getString("hotelId"));
                        meituan.setReserve1(getMinPrice(info) + "");
                        String json = objectMapper.writeValueAsString(meituan);
                        TXTUtil.writeTXT(JSONObject.parse(json).toString(), ConstantList.MEITUAN_PATH,
                                "mtPrice.txt");
                        //HotelUpdateStatus.mtPriceCountGet.incrementAndGet();
                        stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtPriceCountGet", 1);
                        System.out.println("write------");
                    }
                } else if (jsonObject.getInteger("code") == 1200) {
                    this.i++;
                    if (this.i < maxReqCount) {
                        new getdetailMT(this.ids, this.i);
                    }
                    System.out.println(infoText);
                } else {
                    TXTUtil.writeTXT(new Date() + " 获取美团价格进else了： " + infoText + "----------" + this.ids,
                            ConstantList.LOG_PATH, "log.txt");
                }
            } catch (Exception e) {
                TXTUtil.writeTXT(new Date() + " 获取美团价格进异常了： " + e + "----------" + this.ids,
                        ConstantList.LOG_PATH, "log.txt");
            }
        }
    }

    class getMinPriceMTNew extends Thread {
        private List<Long> ids;
        private int i;

        getMinPriceMTNew(List<Long> ids, int i) {
            this.ids = ids;
            this.i = i;
        }

        @Override
        public void run() {
            try {
                String infoText = MeituanApi.hotelProduct2(this.ids);
                JSONObject jsonObject = JSONObject.parseObject(infoText);
                if (jsonObject.getInteger("code") == 0) {
                    JSONArray infos = jsonObject.getJSONObject("result").getJSONArray("hotelGoods");
                    if (infos == null) {
                        return;
                    }
                    HotelGnBase meituan = new HotelGnBase();
                    for (int k = 0; k < infos.size(); k++) {
                        JSONObject info = infos.getJSONObject(k);
                        meituan.setId(info.getString("hotelId"));
                        meituan.setReserve1(getMinPrice(info) + "");
                        String json = objectMapper.writeValueAsString(meituan);
                        TXTUtil.writeTXT(JSONObject.parse(json).toString(), ConstantList.MEITUAN_PATH,
                                "mtPrice.txt");
                        //HotelUpdateStatus.mtPriceCountGet.incrementAndGet();
                        stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtPriceCountGet", 1);
                        System.out.println("write------");
                    }
                } else if (jsonObject.getInteger("code") == 1200) {
                    this.i++;
                    if (this.i < maxReqCount) {
                        new getdetailMT(this.ids, this.i);
                    }
                    System.out.println(infoText);
                } else {
                    TXTUtil.writeTXT(new Date() + " 获取美团价格进else了： " + infoText + "----------" + this.ids,
                            ConstantList.LOG_PATH, "log.txt");
                }
            } catch (Exception e) {
                TXTUtil.writeTXT(new Date() + " 获取美团价格进异常了： " + e + "----------" + this.ids,
                        ConstantList.LOG_PATH, "log.txt");
            }
        }
    }

    public int getMinPrice(JSONObject info) {
        JSONArray ja = info.getJSONArray("goods");
        int minPrice = 9999999;
        for (int i = 0; i < ja.size(); i++) {
            JSONArray jaPrices = ja.getJSONObject(i).getJSONArray("priceModels");
            for (int j = 0; j < jaPrices.size(); j++) {
                JSONObject jo = jaPrices.getJSONObject(j);
                int price = jo.getInteger("salePrice");
                if (price < minPrice) {
                    minPrice = price;
                }
            }
        }
        return minPrice / 100;
    }

    //更新美团酒店最小价
    @RequestMapping("/updateMeituanMinPrice")
    public void updateMeituanMinPrice(HotelUpdateRecode recode, int start, int end) {
        //HotelUpdateStatus.expStatusMT = start;
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
        File file = new File(ConstantList.MEITUAN_PATH + "mtPrice.txt");
        if (file.exists()) {
            LineIterator it = null;
            try {
                it = FileUtils.lineIterator(new File(ConstantList.MEITUAN_PATH + "mtPrice.txt"), "utf8");
                while (it.hasNext()) {
                    try {
                        String line = it.nextLine();
                        try {
                            HotelInfoMeituan meituan = objectMapper.readValue(line, HotelInfoMeituan.class);
                            meituan.setStatus(2);
                            this.hotelService.updateHotelInfoMeituan(meituan);
                            this.logger.info("updateMeituanMinPrice更新成功 id:{}", meituan.getId());
                        } catch (Exception e) {
                            TXTUtil.writeTXT(new Date() + " 更新美团酒店价格进异常了： " + e, ConstantList.LOG_PATH, "log.txt");
                            continue;
                        }
                        //HotelUpdateStatus.mtPriceCountUpdate++;
                        this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtPriceCountUpdate", 1);
                    } catch (Exception e) {
                        TXTUtil.writeTXT(new Date() + " 更新美团酒店价格进异常了： " + e, ConstantList.LOG_PATH, "log.txt");
                        continue;
                    }
                }
                //HotelUpdateStatus.expStatusMT = end;
                this.logger.info("updateMeituanMinPrice.expStatusMT: {},更新结束", end);
                this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
//                HotelUpdateStatus.status = 0;
                recode.setTimeEnd(new Date());
                recode.setCountFail(Long.valueOf(Objects.requireNonNull(this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDetailCountUpdate"))));
                this.hotelUpdateRecodeService.updateHotelUpdateRecode(recode);
            } catch (Exception e) {
                //HotelUpdateStatus.expStatusMT = end;
                this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
//                HotelUpdateStatus.status = 0;
                TXTUtil.writeTXT(new Date() + " 更新美团酒店价格进异常了： " + e, ConstantList.LOG_PATH, "log.txt");
            } finally {
                LineIterator.closeQuietly(it);
            }
        }
    }

    @RequestMapping("/updateMeituanMinPriceNew")
    public void updateMeituanMinPriceNew(HotelUpdateRecode recode, int start, int end) {
        //HotelUpdateStatus.expStatusMT = start;
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(start));
        File file = new File(ConstantList.MEITUAN_PATH + "mtPrice.txt");
        if (file.exists()) {
            LineIterator it = null;
            try {
                it = FileUtils.lineIterator(new File(ConstantList.MEITUAN_PATH + "mtPrice.txt"), "utf8");
                while (it.hasNext()) {
                    try {
                        String line = it.nextLine();
                        try {
                            HotelGnBase meituan = objectMapper.readValue(line, HotelGnBase.class);
                            meituan.setStatus(2);
                            this.hotelGnBaseService.updateByPrimaryKeySelective(PlatEnum.PLAT_MT, meituan);
                            this.logger.info("updateMeituanMinPrice更新成功 id:{}", meituan.getId());
                        } catch (Exception e) {
                            TXTUtil.writeTXT(new Date() + " 更新美团酒店价格进异常了： " + e, ConstantList.LOG_PATH, "log.txt");
                            continue;
                        }
                        //HotelUpdateStatus.mtPriceCountUpdate++;
                        this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtPriceCountUpdate", 1);
                    } catch (Exception e) {
                        TXTUtil.writeTXT(new Date() + " 更新美团酒店价格进异常了： " + e, ConstantList.LOG_PATH, "log.txt");
                        continue;
                    }
                }
                //HotelUpdateStatus.expStatusMT = end;
                this.logger.info("updateMeituanMinPrice.expStatusMT: {},更新结束", end);
                this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
//                HotelUpdateStatus.status = 0;
                recode.setTimeEnd(new Date());
                recode.setCountFail(Long.valueOf(Objects.requireNonNull(this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.mtDetailCountUpdate"))));
                try {
                    RLock rLock = redissonClient.getLock("hotel_gn_meituan_update_data_new");
                    rLock.forceUnlock();
                } catch (Exception e11) {
                }
                this.hotelUpdateRecodeService.updateHotelUpdateRecode(recode);
            } catch (Exception e) {
                //HotelUpdateStatus.expStatusMT = end;
                this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(end));
                try {
                    RLock rLock = redissonClient.getLock("hotel_gn_meituan_update_data_new");
                    rLock.forceUnlock();
                } catch (Exception e11) {
                }
//                HotelUpdateStatus.status = 0;
                TXTUtil.writeTXT(new Date() + " 更新美团酒店价格进异常了： " + e, ConstantList.LOG_PATH, "log.txt");
            } finally {
                LineIterator.closeQuietly(it);
            }
        }
    }


    public List<HotelCityMapping> getCitys2(String interfacePlat) {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setInterfacePlat(Long.valueOf(interfacePlat));
        return this.hotelCityMappingService.selectHotelCityMappingList(mapping);
    }

    public List<String> getCityStr() {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setLocalData(1);
        List<HotelCityMapping> mappingList = this.hotelCityMappingService.selectHotelCityMappingList(mapping);
        List<String> cityStr = new ArrayList<>();
        for (HotelCityMapping hotelCityMapping : mappingList) {
            cityStr.add(hotelCityMapping.getCityName());
        }
        return cityStr;
    }

    //映射美团酒店数据
    //酒店名称500，酒店地址200，酒店电话200，经纬度距离100，匹配分710
    //开业日期装修日期基本对不上；很多区县不匹配的不可野蛮-1000
    //匹配率84%-85%
    @RequestMapping("/mapping")
    public AjaxResult mapping() throws Exception {
        //去掉全局锁
//        if(HotelUpdateStatus.status == 1){
//            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
//        }
        Map<String, JdJdbMapping> jdjdbmappingMap = new ConcurrentHashMap<>();
        //HotelUpdateStatus.status = 1;
        //HotelUpdateStatus.expStatusMT = 11;
        String expStatusMT = this.stringRedisTemplate.opsForValue().get("HotelUpdateStatus.expStatusMT");
        if (expStatusMT != null && expStatusMT.equals("0")) {
            return AjaxResult.warn("酒店数据映射执行中,请勿重复执行！");
        }
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", String.valueOf(11));
        //获取华住酒店品牌数据
        List<HotelCityMapping> citys = this.getCitys2(PlatEnum.PLAT_MT.getValue());
        Map<String, HotelCityMapping> mapCity = new HashedMap();
        for (HotelCityMapping city : citys) {
            mapCity.put(city.getPlatNum(), city);
        }
        HotelInfoMeituan search = new HotelInfoMeituan();
        search.setStatus(1);
        List<HotelInfoMeituan> list = this.hotelService.selectHotelInfoMeituanList2(search);
        //改为redis 存储
        this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.mtMappingCount", String.valueOf(list.size()));
        //HotelUpdateStatus.mtMappingCount = list.size();
        // 美团酒店城市映射艺龙酒店城市
        if (HotelUpdateStatus.jdMap.get("10119") == null) {
            HotelCityMapping city = new HotelCityMapping();
            city.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_EL.getValue()));
            List<HotelCityMapping> elongCityList = this.hotelCityMappingService.selectHotelCityMappingList(city);
            JdJdb jdJdb = new JdJdb();
            for (HotelCityMapping cityMapping : elongCityList) {
                jdJdb.setCityId(cityMapping.getLocalId());
                HotelUpdateStatus.jdMap.put(cityMapping.getLocalId(), this.jdJdbService.selectJdJdbList(jdJdb));
            }
        }
        List<String> cityStr = this.getCityStr();
        // 读取开启线程数量
        int tc = Integer.valueOf(this.sysDictDataService.selectDictLabel("hotel_params", "mapping_thread_count"));
        AtomicInteger threadSize = new AtomicInteger(tc);

        int count = list.size();
        int tcount = count / tc;

        for (int t = 0; t < tc; t++) {
            List<HotelInfoMeituan> listSub0;
            if (t == tc - 1) {
                listSub0 = list.subList(t * tcount, count);
            } else {
                listSub0 = list.subList(t * tcount, (t + 1) * tcount);
            }
            List<HotelInfoMeituan> listSub = listSub0;
            new Thread(() -> {
                HotelCityMapping hotelCityMapping = new HotelCityMapping();
                hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
                for (int j = 0; j < listSub.size(); j++) {
                    HotelInfoMeituan noys = listSub.get(j);
                    try {
                        if (StringUtils.isEmpty(noys.getCityId())) {
                            //HotelUpdateStatus.mtMappingCountFail.incrementAndGet();
                            this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtMappingCountFail", 1);
                            noys.setStatus(0);
                            this.hotelService.updateHotelInfoMeituan(noys);
                            continue;
                        }
                        hotelCityMapping.setPlatNum(noys.getCityId());
                        List<HotelCityMapping> mappings = this.hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
                        if (mappings.size() == 0) {
                            //HotelUpdateStatus.mtMappingCountFail.incrementAndGet();
                            this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtMappingCountFail", 1);
                            noys.setStatus(9);
                            this.hotelService.updateHotelInfoMeituan(noys);
                            continue;
                        }
                        List<JdJdb> jdList = HotelUpdateStatus.jdMap.get(mappings.get(0).getLocalId());
                        if (jdList == null || jdList.size() == 0) {
                            //HotelUpdateStatus.mtMappingCountFail.incrementAndGet();
                            //改为redis存储
                            this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtMappingCountFail", 1);
                            noys.setStatus(9);
                            this.hotelService.updateHotelInfoMeituan(noys);
                            continue;
                        }
                        String namePlat = MatchRate.getOk(noys.getName());
                        String addressPlat = MatchRate.getOk(noys.getAddressLine());
                        String telPlat = MatchRate.getOk(noys.getPhone());
                        BigDecimal lonGDPlat = noys.getLonGd();
                        BigDecimal latGDPlat = noys.getLatGd();
                        BigDecimal lonBDPlat = noys.getLonBd();
                        BigDecimal latBDPlat = noys.getLatBd();
                        JdJdbMapping jdJdbMapping = new JdJdbMapping();
                        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
                        for (int i = 0; i < jdList.size(); i++) {
                            JdJdb baseys = jdList.get(i);
                            String name = MatchRate.getOk(baseys.getJdmc());
                            String address = MatchRate.getOk(baseys.getJddz());
                            String tel = MatchRate.getOk(baseys.getJddh());
                            BigDecimal lonGD = baseys.getLonGaode();
                            BigDecimal latGD = baseys.getLatGaode();
                            BigDecimal lonBD = baseys.getLonBaidu();
                            BigDecimal latBD = baseys.getLatBaidu();

                            int matchRateAddr = MatchRate.getMatchRate(null, address, addressPlat) * 2;
                            int matchRateName = MatchRate.getMatchRate2(cityStr, name, namePlat) * 5;
                            String name0 = name.replace("酒店", "").replace("店", "");
                            String namePlat0 = namePlat.replace("酒店", "").replace("店", "");
                            if (name0.equals(namePlat0)) {
                                matchRateName = 700;
                            }
                            int matchRate = matchRateAddr + matchRateName;

                            if (StringUtils.isNotEmpty(tel) && StringUtils.isNotEmpty(telPlat)) {
                                if (tel.contains(telPlat) || telPlat.contains(tel)) {
                                    matchRate += 200;
                                }
                            } else {
                                matchRate += 100;
                            }

                            int dist = 9999;
                            if (latGDPlat != null && lonGDPlat != null && lonGD != null && latGD != null) {
                                int dist2 = GetDistance.calculateDistance(lonGD.doubleValue(), latGD.doubleValue(),
                                        lonGDPlat.doubleValue(), latGDPlat.doubleValue());
                                if (dist2 < dist) {
                                    dist = dist2;
                                }
                            }
                            if (latBDPlat != null && lonBDPlat != null && lonBD != null && latBD != null) {
                                int dist3 = GetDistance.calculateDistance(lonBD.doubleValue(), latBD.doubleValue(),
                                        lonBDPlat.doubleValue(), latBDPlat.doubleValue());
                                if (dist3 < dist) {
                                    dist = dist3;
                                }
                            }
                            if (dist < 50) {
                                matchRate += 100;
                            }

                            if (matchRate > 799) {
                                JdJdbMapping jdJdbMapping2 = jdjdbmappingMap.get(baseys.getId());
                                if (jdJdbMapping2 != null) {
                                    if (Integer.valueOf(jdJdbMapping2.getReserve0()) < matchRate) {
                                        HotelInfoMeituan noys2 = new HotelInfoMeituan();
                                        noys2.setId(jdJdbMapping2.getPlatId());
                                        noys2.setStatus(9);
                                        this.hotelService.updateHotelInfoMeituan(noys2);
                                        jdJdbMapping2.setReserve0(matchRate + "");
                                        jdJdbMapping2.setPlatId(noys.getId());
                                        jdJdbMapping2.setReserve3(noys.getName());
                                        jdjdbmappingMap.put(baseys.getId(), jdJdbMapping2);
                                        //HotelUpdateStatus.mtMappingCountFail.incrementAndGet();
                                        //改为redis 存储
                                        this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtMappingCountFail");
                                        break;
                                    } else {
                                        if (i == jdList.size() - 1) {
                                            //HotelUpdateStatus.mtMappingCountFail.incrementAndGet();
                                            //改为redis 存储
                                            this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtMappingCountFail");
                                            noys.setStatus(9);
                                            this.hotelService.updateHotelInfoMeituan(noys);
                                            break;
                                        }
                                        continue;
                                    }
                                }

                                jdJdbMapping.setPlatId(noys.getId());
                                jdJdbMapping.setLocalId(baseys.getId());
                                jdJdbMapping.setReserve0(matchRate + "");
                                jdJdbMapping.setJdName(baseys.getJdmc());
                                jdJdbMapping.setReserve3(noys.getName());
                                jdjdbmappingMap.put(baseys.getId(), jdJdbMapping);
                                //HotelUpdateStatus.mtMappingCounted.incrementAndGet();
                                //改为redis存储
                                this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtMappingCounted", 1);
                                break;
                            }

                            if (i == jdList.size() - 1) {
                                //HotelUpdateStatus.mtMappingCountFail.incrementAndGet();
                                //改为redis存储
                                this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtMappingCountFail", 1);
                                noys.setStatus(9);
                                this.hotelService.updateHotelInfoMeituan(noys);
                            }
                        }
                    } catch (Exception e) {
                        //HotelUpdateStatus.mtMappingCountFail.incrementAndGet();
                        //改为redis存储
                        this.stringRedisTemplate.opsForValue().increment("HotelUpdateStatus.mtMappingCountFail", 1);
                        noys.setStatus(9);
                        this.hotelService.updateHotelInfoMeituan(noys);
                        log.error("美团映射异常: ", e);
                        continue;
                    }
                }
                threadSize.decrementAndGet();
            }).start();
        }
        while (true) {
            if (threadSize.get() == 0) {
                Set<Map.Entry<String, JdJdbMapping>> entries = jdjdbmappingMap.entrySet();
                HotelInfoMeituan hotel = new HotelInfoMeituan();
                hotel.setStatus(8);
                for (Map.Entry<String, JdJdbMapping> entry : entries) {
                    JdJdbMapping value = entry.getValue();
                    log.info("meituan jdjdbmapping localId {},interfacePlat {}", value.getLocalId(), value.getInterfacePlat());
                    if (null == this.jdJdbMappingService.selectJdJdbMappingByLocalIdAndInterfacePlat(value.getLocalId(), value.getInterfacePlat())) {
                        this.jdJdbMappingService.insertJdJdbMapping(value);
                    }
                    hotel.setId(value.getPlatId());
                    this.hotelService.updateHotelInfoMeituan(hotel);

                    ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                    zhJdJdbMapping.setLocalId(value.getLocalId());
                    zhJdJdbMapping.setInterfacePlat(value.getInterfacePlat());
                    zhJdJdbMapping.setPlatId(value.getPlatId());
                    zhJdJdbMapping.setJdName(value.getJdName());
                    zhJdJdbMapping.setStatus(0);
                    zhJdJdbMapping.setSaveDate(new Date());
                    zhJdJdbMapping.setIsGnGj(1);
                    this.logger.info("美团酒店zh_jd_jdb_mapping插入前数据 {}", JSONObject.toJSONString(zhJdJdbMapping));
                    //插入前校验重复
                    log.info("美团酒店 zhJdJdbMapping localId {},interfacePlat {}", zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat());
                    if (null == this.zhJdJdbMappingService.findByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat())) {
                        this.zhJdJdbMappingService.insert(zhJdJdbMapping);
                        ;
                    }
                }
                //HotelUpdateStatus.expStatusMT = 12;
                //改为redis 设置状态为12
                this.stringRedisTemplate.opsForValue().set("HotelUpdateStatus.expStatusMT", "12");
                //HotelUpdateStatus.status = 0;
                break;
            }
            Thread.sleep(10000);
            System.out.println("美团 10秒巡逻一次，检测完成");
        }
        //状态重置
        this.resetStatus();
        log.info("美团映射执行完成");
        return AjaxResult.success();
    }

    /**
     * 根据酒店查询美团最低价
     *
     * @return
     */
    @GetMapping("/getMinPriceByHotelId")
    public AjaxResult getMinPriceByHotelId(@RequestParam("hotelId") Long hotelId, @RequestParam("checkInDate") String checkInDate, @RequestParam("checkOutDate") String checkOutDate) {
        List<HotelLowestPrices> hotelLowestPrices = MeituanApi.hotelProductMinPrice(Collections.singletonList(hotelId), checkInDate, checkOutDate);
        return AjaxResult.success(hotelLowestPrices);
    }

    public Object fetchDataFunction(String key, long second, long num, Function<Integer, Object> function) {
        int tryNum = 1;
        RRateLimiter rRateLimiter = this.limiter.getLimiter(key, second, num);
        boolean tryAcquire = rRateLimiter.tryAcquire(1);
        while (!tryAcquire) {
            this.logger.info("当前供应商渠道【{}】限流.......睡眠500ms", key);
            try {
                Thread.sleep(500);
            } catch (InterruptedException ex) {
                this.logger.info("睡眠异常。。。。。。。。。。");
            }
            tryNum++;
            tryAcquire = rRateLimiter.tryAcquire(1);
        }
        return function.apply(tryNum);
    }

    @GetMapping("/pullHotel")
    public void pullHotelAndAddJdJdb() {
        String s = MeituanApi.hotelSearch("10000030");
        System.out.println(s);
        JSONObject jsonResult = JSONObject.parseObject(s);
        Integer code = jsonResult.getInteger("code");
        if (Objects.equals(0, code)) {
            JSONObject result = jsonResult.getJSONObject("result");
            if (Objects.nonNull(result)) {
                JSONObject hotelListSearch = result.getJSONObject("hotelListSearch");
                if (Objects.nonNull(hotelListSearch)) {
                    String nextId = hotelListSearch.getString("nextId");
                    Long totalCount = hotelListSearch.getLong("totalCount");
                    JSONArray hotelListSearchInfo = hotelListSearch.getJSONArray("hotelListSearchInfo");
                }

            }
        }

    }

    @ResponseBody
    @PostMapping("/hotel.poi.info.change.callback")
    public String hotelInfoChangeCallback(@RequestBody String s) {
        log.info("收到美团[酒店信息变更通知]: {}", s);
        try {
            JSONObject jsonObject = JSONObject.parseObject(s);

            String changeTime = jsonObject.getString("changeTime");
            Date incrementTime = DateUtils.getNowDate();
            JSONArray changeInfos = jsonObject.getJSONArray("changeInfos");

            changeInfos.stream().forEach(element -> {
                JSONObject changeInfo = (JSONObject) element;
                Long hotelId = changeInfo.getLong("hotelId");
                HotelInfoMeituan hotelInfoMeituan = this.hotelService.selectHotelInfoMeituanById(String.valueOf(hotelId));
                JSONArray changeTypes = changeInfo.getJSONArray("changeTypes");
                changeTypes.stream().forEach(changeType -> {
                    switch (String.valueOf(changeType)) {
                        case "1":
                            if (Objects.isNull(hotelInfoMeituan)) {
                                HotelInfoMeituan hotelInfo = new HotelInfoMeituan();
                                hotelInfo.setId(String.valueOf(hotelId));
                                hotelInfo.setStatus(1);
                                hotelInfo.setIncrementStatus(3);
                                hotelInfo.setIncrementTime(incrementTime);
                                hotelInfo.setIncrementType(1);
                                this.hotelService.insertHotelInfoMeituan(hotelInfo);
                            } else {
                                hotelInfoMeituan.setIncrementStatus(3);
                                hotelInfoMeituan.setIncrementTime(incrementTime);
                                hotelInfoMeituan.setIncrementType(2);
                                this.hotelService.updateHotelInfoMeituan(hotelInfoMeituan);
                            }
                            break;
                        case "2":
                            if (Objects.nonNull(hotelInfoMeituan)) {
                                hotelInfoMeituan.setIncrementStatus(1);
                                hotelInfoMeituan.setIncrementTime(incrementTime);
                                hotelInfoMeituan.setIncrementType(3);
                                this.hotelService.updateHotelInfoMeituan(hotelInfoMeituan);
                            }
                            break;
                    }
                });
            });
        } catch (Exception e) {
            log.error("处理美团酒店信息变更回调失败: ", e);
        }

        return "{\"code\":0,\"message\":\"成功\"}";
    }

    /**
     * 平台酒店导入本地
     */
    @PreAuthorize("@ss.hasPermi('ivw:meituan:remove')")
    @Log(title = "美团酒店信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult platToLocal(@PathVariable String[] ids) throws Exception {
        return hotelService.platToLocal(ids);
    }

    /**
     * 从hotel_city_meituan表获取城市数据
     */
    @GetMapping("/getCityDataFromTable")
    public AjaxResult getCityDataFromTable() {
        try {
            List<HotelCityMeituan> cityList = this.hotelCityMeituanService.selectHotelCityMeituanList();
            if (cityList != null && !cityList.isEmpty()) {
                return AjaxResult.success("获取美团城市数据成功", cityList);
            } else {
                return AjaxResult.error("获取美团城市数据失败，表中无数据");
            }
        } catch (Exception e) {
            log.error("获取美团城市数据异常", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 处理美团城市数据
     */
    @PostMapping("/processCityData")
    public AjaxResult processCityData(@RequestParam(defaultValue = "1") String idempotent) {
        try {
            // 使用分页处理城市数据，每次处理500条
            int count = this.hotelMeituanSupplierService.processCityDataWithPagination(idempotent);
            return AjaxResult.success("处理成功，共处理美团城市数据: " + count + "条");
        } catch (Exception e) {
            log.error("处理美团城市数据异常", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }
}
