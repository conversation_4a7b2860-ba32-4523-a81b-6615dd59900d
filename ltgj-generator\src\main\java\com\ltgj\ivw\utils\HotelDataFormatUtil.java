package com.ltgj.ivw.utils;


import com.alibaba.fastjson2.JSONObject;
import com.ltgj.ivw.config.FormatConfig;
import org.springframework.core.env.Environment;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

public class HotelDataFormatUtil {
    // 定义简称与全称的映射表
    private static final Map<String, String> ABBREVIATION_MAP = new HashMap<>();
    private static FormatConfig config;

    static {
        ABBREVIATION_MAP.put("otel", "Hotel");
        ABBREVIATION_MAP.put("mt", "Mount"); // 注意：替换时会先转为小写，所以 key 使用小写形式
        // 后续可在此处添加更多缩写和全称的对应关系
        loadAbbreviationsFromNacos();
    }

    private static void loadAbbreviationsFromNacos() {
        try {
            FormatConfig formatConfig = SpringContextUtil.getBean(FormatConfig.class);
            config = formatConfig;
            System.out.println("从 Nacos 加载自定义缩写映射完成：" + config);
        } catch (Exception e) {
            System.err.println("从 Nacos 加载缩写配置失败: " + e.getMessage());
        }
    }

    public static String formatName(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // Step 1: 删除前后及中间多余的空格、换行符、制表符
        String trimmed = input.trim().replaceAll("\\s+", " ");
        // Step 3: 替换常见简称或缩写为全称
        for (Map.Entry<String, String> entry : ABBREVIATION_MAP.entrySet()) {
            trimmed = trimmed.replaceAll(Pattern.quote(entry.getKey()), entry.getValue());
        }
        // Step 2: 去除特殊字符
        String noSpecialChars = trimmed.replaceAll(config.nameReg, "");

        return noSpecialChars;
    }

    public static String formatAddress(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // Step 1: 删除前后及中间多余的空格、换行符、制表符
        String trimmed = input.trim().replaceAll("\\s+", " ");

        // Step 2: 去除特殊字符
        String noSpecialChars = trimmed.replaceAll(config.addressReg, "");

        return noSpecialChars;
    }

    public static String formatPhone(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        // 使用正则表达式清除所有非数字字符
        return input.replaceAll(config.phoneReg, "");
    }

    /**
     * 从一个 JSONObject 中选择指定的属性生成新的 JSONObject
     *
     * @param source     源 JSONObject
     * @param properties 需要提取的属性名数组
     * @return 包含指定属性的新 JSONObject
     */
    public static JSONObject selectProperties(JSONObject source, String... properties) {
        if (source == null || properties == null || properties.length == 0) {
            return new JSONObject();
        }

        JSONObject result = new JSONObject();
        for (String property : properties) {
            if (source.containsKey(property)) {
                result.put(property, source.get(property));
            }
        }
        return result;
    }

    // 测试方法
    public static void main(String[] args) {
        String testStr = "~！@中卫   天和时尚JW otel Mt.酒店  ￥%……&*（）：“”《》？|";
        System.out.println("Original: \"" + testStr + "\"");
        System.out.println("Formatted:  \"" + formatName(testStr) + "\"");
    }
}
