package com.ltgj.web.job.handler;

import com.ltgj.ivw.enums.InterfacePlatEnum;
import com.ltgj.supplier.common.dto.HotelBaseRequest;
import com.ltgj.supplier.klyx.gn.HotelKlyxManager;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "klyxHotelInitDataHandler")
public class KlyxHotelInitDataHandler extends IJobHandler {

    @Autowired
    private HotelKlyxManager hotelKlyxManager;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        HotelBaseRequest request = new HotelBaseRequest();
        request.setInterfacePlat(InterfacePlatEnum.INTERFACE_KLYX);
        try {
            hotelKlyxManager.initHotelDate(request);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("康旅严选-酒店增量同步任务异常：{}", e);
        }
        log.info("康旅严选-全量数据执行完成");
        return ReturnT.SUCCESS;
    }
}
