package com.ltgj.ivw.service;

import com.ltgj.ivw.domain.CityMapping;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.service.idempotent.IdempotentResult;
import com.ltgj.ivw.service.impl.CozyTimeSyncResult;

import java.util.List;

/**
 * 科坦数据同步服务接口
 * 提供三个表的幂等性数据同步功能
 */
public interface CozyTimeDataSyncService {
    
    /**
     * 同步HotelCity数据
     * 
     * @param hotelCityList 待同步的酒店城市数据
     * @return 同步结果
     */
    IdempotentResult<HotelCity> syncHotelCityData(List<HotelCity> hotelCityList);
    
    /**
     * 同步CityMapping数据
     * 
     * @param cityMappingList 待同步的城市映射数据
     * @return 同步结果
     */
    IdempotentResult<CityMapping> syncCityMappingData(List<CityMapping> cityMappingList);
    
    /**
     * 同步HotelCityMapping数据
     * 
     * @param hotelCityMappingList 待同步的酒店城市映射数据
     * @return 同步结果
     */
    IdempotentResult<HotelCityMapping> syncHotelCityMappingData(List<HotelCityMapping> hotelCityMappingList);
    
    /**
     * 批量同步所有相关数据
     * 
     * @param hotelCityList 酒店城市数据
     * @param cityMappingList 城市映射数据
     * @param hotelCityMappingList 酒店城市映射数据
     * @return 综合同步结果
     */
    CozyTimeSyncResult syncAllData(List<HotelCity> hotelCityList,
                                   List<CityMapping> cityMappingList,
                                   List<HotelCityMapping> hotelCityMappingList);
} 