package com.ltgj.supplier.cozyTime;

import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;

public class TransformUtils {

    public static <T, R, C> void transformAndCollectTwoParam(
            Collection<T> inputs,
            C context,
            TransformWithContext<T, R, C> transformer,
            Collection<R> outputCollection,
            Consumer<Exception> errorHandler) {
        for (T input : inputs) {
            try {
                R result = transformer.transform(input, context);
                outputCollection.add(result);
            } catch (Exception e) {
                errorHandler.accept(e);
            }
        }
    }

    public static <T, R, C> void transformAndCollectAddAllParam(
            Collection<T> inputs,
            C context,
            TransformWithContextSec<T, R, C> transformer,
            Collection<R> outputCollection,
            Consumer<Exception> errorHandler) {
        for (T input : inputs) {
            try {
                List<R> result = transformer.transform(input, context);
                outputCollection.addAll(result);
            } catch (Exception e) {
                errorHandler.accept(e);
            }
        }
    }
}