package com.ltgj.ivw.controller;

import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.core.page.PageLimit;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.bean.BeanCopierUtils;
import com.ltgj.common.utils.poi.ExcelUtil;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.dto.HotelGnPreMappingDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.service.impl.FactoryForMappingStrategy;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 酒店编号映射Controller(zh_jd_jdb_mapping代替jd_jdb_mapping)
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@RestController
@RequestMapping("/ivw/mappingJD")
@Slf4j
public class JdJdbMappingController extends BaseController
{
    @Autowired
    private IJdJdbMappingService jdJdbMappingService;
    @Autowired
    private IJdJdbService jdJdbService;

    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;

    @Autowired
    private FactoryForMappingStrategy factoryForMappingStrategy;
    @Autowired
    private ZhJdJdbMappingBackupService zhJdJdbMappingBackupService;
    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private HotelGnPreMappingService hotelGnPreMappingService;
    //  id生成器
    private IdUtil sequence = new IdUtil();
    /**
     * 查询酒店编号映射列表
     */
    @PreAuthorize("@ss.hasPermi('ivw:mappingJD:list')")
    @GetMapping("/list")
    public TableDataInfo list(ZhJdJdbMapping zhjdJdbMapping)
    {
        startPage();
        List<ZhJdJdbMapping> list = factoryForMappingStrategy
                .getStrategy("zhJdJdbMappingServiceImpl").selectAbstratMappingList(zhjdJdbMapping);
        return getDataTable(list);
    }

    /**
     * 导出酒店编号映射列表
     */
    @PreAuthorize("@ss.hasPermi('ivw:mappingJD:export')")
    @Log(title = "酒店编号映射", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ZhJdJdbMapping zhjdJdbMapping)
    {
        List<ZhJdJdbMapping> list = factoryForMappingStrategy
                .getStrategy("zhJdJdbMappingServiceImpl").selectAbstratMappingList(zhjdJdbMapping);
        ExcelUtil<ZhJdJdbMapping> util = new ExcelUtil<>(ZhJdJdbMapping.class);
        util.exportExcel(response, list, "酒店编号映射数据");
    }

    /**
     * 获取酒店编号映射详细信息
     */
    @PreAuthorize("@ss.hasPermi('ivw:mappingJD:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(jdJdbMappingService.selectJdJdbMappingById(id));
    }

    /**
     * 通用酒店映射
     */
    @PreAuthorize("@ss.hasPermi('ivw:mappingJD:add')")
    @Log(title = "酒店编号映射", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody HotelGnPreMappingDTO hotelGnPreMappingDTO)
    {
        try {
            JdJdb jdJdb = jdJdbService.selectJdJdbById(hotelGnPreMappingDTO.getLocalId());
            if(jdJdb == null) {
                return AjaxResult.error("本地酒店不存在");
            }
            String service = PlatEnum.getplatEnum(hotelGnPreMappingDTO.getInterfacePlat()+"").getService();
            IHotelGnBaseService hotelGnBaseService = SpringUtils.getBean(service);
            HotelGnBase hotelGnBase = hotelGnBaseService.selectHotelGnBaseById(hotelGnPreMappingDTO.getPlatId());

            if (Objects.isNull(hotelGnBase)) {
                throw new RuntimeException("酒店不存在!请检查请求参数~");
            }

            String id = String.valueOf(sequence.nextId());
            HotelGnPreMapping hotelGnPreMapping = new HotelGnPreMapping();
            hotelGnPreMapping.setId(id);
            hotelGnPreMapping.setSourceHotelId(hotelGnPreMappingDTO.getPlatId());
            hotelGnPreMapping.setSourceHotelName(hotelGnBase.getName());
            hotelGnPreMapping.setSourceHotelAddress(hotelGnBase.getAddress());
            hotelGnPreMapping.setSourceHotelPhone(hotelGnBase.getPhone());
            hotelGnPreMapping.setSourceHotelCityName(hotelGnBase.getCityName());
            hotelGnPreMapping.setSourePlatformId(hotelGnPreMappingDTO.getInterfacePlat());

            hotelGnPreMapping.setMappingType("mapping");
            hotelGnPreMapping.setTargetHotelId(hotelGnPreMappingDTO.getLocalId());
            hotelGnPreMapping.setTargetPlatformId("-1");

            hotelGnPreMapping.setTargetHotelName(jdJdb.getJdmc());
            hotelGnPreMapping.setTargetHotelAddress(jdJdb.getJddz());
            hotelGnPreMapping.setTargetHotelPhone(jdJdb.getJddh());
            hotelGnPreMapping.setTargetHotelCityName(jdJdb.getCityName());
            hotelGnPreMapping.setScore(100);
            hotelGnPreMapping.setStatus((byte) 0);
            hotelGnPreMapping.setIsDelete(Boolean.FALSE);
            hotelGnPreMapping.setUpdateTime(DateUtils.getNowDate());
            hotelGnPreMapping.setCreateTime(DateUtils.getNowDate());
            hotelGnPreMappingService.addHotelGnPreMapping(hotelGnPreMapping);
            hotelGnPreMappingService.addMapping(id);

            return AjaxResult.success();
        } catch (RuntimeException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 通用酒店映射
     * 1.jd_jdb_mapping 插入数据
     * 2.zh_jd_jdb_mapping 插入数据
     * 3. 更新本地酒店状态
     */
    @PreAuthorize("@ss.hasPermi('ivw:mappingJD:add')")
    @Log(title = "酒店编号映射", businessType = BusinessType.INSERT)
    @PostMapping("addOld")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addOld(@RequestBody ZhJdJdbMapping zhJdJdbMapping)
    {
        JdJdb jdJdb = jdJdbService.selectJdJdbById(zhJdJdbMapping.getLocalId());
        if(jdJdb == null) {
            return AjaxResult.error("本地酒店不存在");
        }
        String service = PlatEnum.getplatEnum(zhJdJdbMapping.getInterfacePlat()+"").getService();
        IHotelInfoService hotelInfoService = SpringUtils.getBean(service);
        HotelInfo hotelInfo = hotelInfoService.selectHotelInfoById(zhJdJdbMapping.getPlatId());
//        zhJdJdbMapping.setJdName(jdJdb.getJdmc());
//        zhJdJdbMapping.setReserve3(hotelInfo.getName());
//        zhJdJdbMapping.setReserve0(hotelInfo.getReserve2());
//        JdJdbMapping jdbMapping = jdJdbMappingService.selectJdJdbMappingByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(),zhJdJdbMapping.getInterfacePlat());
//        if(null == jdbMapping){
//            log.info("JdJdbMapping酒店映射表数据为空ID {},start,开始插入数据", jdJdbMapping.getLocalId());
//            jdJdbMappingService.insertJdJdbMapping(jdJdbMapping);
//            log.info("JdJdbMapping酒店映射表数据为空ID {},end,插入数据结束", jdJdbMapping.getLocalId());
//        }
        hotelInfo.setStatus(8);
        //hotelInfoService.updateHotelInfo(hotelInfo);
        //添加映射关系
        ZhJdJdbMapping temp = new ZhJdJdbMapping();
        temp.setLocalId(zhJdJdbMapping.getLocalId());
        temp.setInterfacePlat(zhJdJdbMapping.getInterfacePlat());
        temp.setPlatId(zhJdJdbMapping.getPlatId());
        temp.setJdName(jdJdb.getJdmc());
        temp.setPlatJdName(hotelInfo.getName());
        temp.setStatus(0);
        temp.setIsGnGj(1);
        temp.setSaveDate(new Date());
        temp.setMappingScore(hotelInfo.getReserve0());
        //todo 插入前校验是否存在
        List<ZhJdJdbMapping> dbMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(zhJdJdbMapping.getPlatId(), zhJdJdbMapping.getInterfacePlat());
        if(!ObjectUtils.isEmpty(dbMappings)){
            return AjaxResult.error("该酒店已经存在映射，请删除映射后再操作");
        }else{
            log.info("ZhJdJdbMapping酒店映射表数据为空ID {},开始插入数据", zhJdJdbMapping.getLocalId());
            zhJdJdbMappingService.insert(temp);
        }
        // todo 预映射

//        // 处理红色加力协议相关功能
//        if (StringUtils.equalsIgnoreCase(PlatEnum.PLAT_HSJL_XY.getValue(), String.valueOf(jdJdbMapping.getInterfacePlat()))) {
//            log.info("开始处理红色加力协议最低价与上传ES");
//            try {
//                zhJdJdbService.insertZhJdJdb(jdJdb, jdJdb.getCityId());
//                // 检查抓取酒店最低价
//                jdJdbService.dealListMinPriceHSJLXYNew(jdJdb.getId(), Long.valueOf(hotelInfo.getId()));
//                // 检查es中是否有酒店, 如果没有, 同步ES
//                esHotelInfoService.extracted(Lists.newArrayList(jdJdb));
//                EsHotelInfo esHotelInfo = new EsHotelInfo();
//                esHotelInfo.setHotelId(jdJdb.getId());
//                esHotelInfo.setDatePriceList(zhJdJdbMinPriceService.getHotelMinPriceList(jdJdb.getId(), Integer.valueOf(PlatEnum.PLAT_HSJL_XY.getValue())));
//                esHotelInfoService.batchUpdateMinPrice(esHotelInfo);
//            } catch (Exception e) {
//                log.error("红色加力协议酒店同步ES失败:", e);
//            }
//            log.info("处理红色加力协议最低价与上传ES结束");
//        } else if (StringUtils.equalsIgnoreCase(PlatEnum.PLAT_HSJL.getValue(), String.valueOf(jdJdbMapping.getInterfacePlat()))) {
//            log.info("开始处理红色加力最低价与上传ES");
//            try {
//                zhJdJdbService.insertZhJdJdb(jdJdb, jdJdb.getCityId());
//                // 检查抓取酒店最低价
//                jdJdbService.dealListMinPriceHSJLNew(jdJdb.getId(), Long.valueOf(hotelInfo.getId()));
//                // 检查es中是否有酒店, 如果没有, 同步ES
//                esHotelInfoService.extracted(Lists.newArrayList(jdJdb));
//                EsHotelInfo esHotelInfo = new EsHotelInfo();
//                esHotelInfo.setHotelId(jdJdb.getId());
//                esHotelInfo.setDatePriceList(zhJdJdbMinPriceService.getHotelMinPriceList(jdJdb.getId(), Integer.valueOf(PlatEnum.PLAT_HSJL.getValue())));
//                esHotelInfoService.batchUpdateMinPrice(esHotelInfo);
//            } catch (Exception e) {
//                log.error("红色加力酒店同步ES失败:", e);
//            }
//            log.info("处理红色加力最低价与上传ES结束");
//        } else if (StringUtils.equalsIgnoreCase(PlatEnum.PLAT_MT.getValue(), String.valueOf(jdJdbMapping.getInterfacePlat()))) {
//            log.info("### add_开始处理美团最低价与上传ES");
//            try {
//                zhJdJdbService.insertZhJdJdb(jdJdb, jdJdb.getCityId());
//                // 检查抓取酒店最低价
//                jdJdbService.dealListMinPriceMtNew(jdJdb.getId(), Long.valueOf(hotelInfo.getId()));
//                // 检查es中是否有酒店, 如果没有, 同步ES
//                esHotelInfoService.extracted(Lists.newArrayList(jdJdb));
//                EsHotelInfo esHotelInfo = new EsHotelInfo();
//                esHotelInfo.setHotelId(jdJdb.getId());
//                esHotelInfo.setDatePriceList(zhJdJdbMinPriceService.getHotelMinPriceList(jdJdb.getId(), Integer.valueOf(PlatEnum.PLAT_MT.getValue())));
//                esHotelInfoService.batchUpdateMinPrice(esHotelInfo);
//            } catch (Exception e) {
//                log.error("### add_美团酒店同步ES失败:", e);
//            }
//            log.info("### add_处理美团最低价与上传ES结束");
//        } else if (StringUtils.equalsIgnoreCase(PlatEnum.PLAT_QT.getValue(), String.valueOf(jdJdbMapping.getInterfacePlat()))){
//            log.info("### add_开始处理千淘最低价与上传ES");
//            try {
//                zhJdJdbService.insertZhJdJdb(jdJdb, jdJdb.getCityId());
//                // 检查抓取酒店最低价
//                jdJdbService.dealListMinPriceQtNew(jdJdb.getId(), hotelInfo.getId());
//                // 检查es中是否有酒店, 如果没有, 同步ES
//                esHotelInfoService.extracted(Lists.newArrayList(jdJdb));
//                EsHotelInfo esHotelInfo = new EsHotelInfo();
//                esHotelInfo.setHotelId(jdJdb.getId());
//                esHotelInfo.setDatePriceList(zhJdJdbMinPriceService.getHotelMinPriceList(jdJdb.getId(), Integer.valueOf(PlatEnum.PLAT_QT.getValue())));
//                esHotelInfoService.batchUpdateMinPrice(esHotelInfo);
//            } catch (Exception e) {
//                log.error("### add_千淘酒店同步ES失败:", e);
//            }
//            log.info("### add_处理千淘最低价与上传ES结束");
//        }
        return AjaxResult.success();
    }

    /**
     * 检查酒店映射
     */
    @PreAuthorize("@ss.hasPermi('ivw:mappingJD:edit')")
    @PutMapping()
    public AjaxResult checkMapping(@RequestBody ZhJdJdbMapping zhjdJdbMapping)
    {
        String plat = zhjdJdbMapping.getInterfacePlat()+"";
        String service = PlatEnum.getplatEnum(plat).getService();
        IHotelGnBaseService hotelGnBaseService = SpringUtils.getBean(service);
        HotelGnBase hotelGnBase = hotelGnBaseService.selectHotelGnBaseById(zhjdJdbMapping.getPlatId());
        if (Objects.isNull(hotelGnBase)) {
            return AjaxResult.errorFor("平台酒店不存在!");
        }
        if(StringUtils.isEmpty(hotelGnBase.getCityName())) {
            HotelCity hotelCity = new HotelCity();
            hotelCity.setReserve1(plat);
            hotelCity.setCityId(hotelGnBase.getCityId());
            hotelCityService.selectHotelCityList(hotelCity).stream().findFirst().ifPresent(item -> hotelGnBase.setCityName(item.getCityName()));
        }
        if(StringUtils.isNotEmpty(hotelGnBase.getAreaId()) &&
                (plat.equals(PlatEnum.PLAT_FX.getValue()) || plat.equals(PlatEnum.PLAT_MT.getValue()))) {
            HotelCity hotelCity = new HotelCity();
            hotelCity.setReserve1(plat);
            hotelCity.setLocationId(hotelGnBase.getAreaId());
            hotelCityService.selectHotelCityList(hotelCity).stream().findFirst().ifPresent(item -> hotelGnBase.setAreaId(item.getLocationName()));
        }
        JdJdb jdLocal = jdJdbService.selectJdJdbById(zhjdJdbMapping.getLocalId());
        if (Objects.isNull(jdLocal)) {
            return AjaxResult.fail("平台酒店不存在!");
        }
        if(StringUtils.isNotEmpty(jdLocal.getDistrict())) {
            HotelCity hotelCity = new HotelCity();
            hotelCity.setReserve1(PlatEnum.PLAT_EL.getValue());
            hotelCity.setLocationId(jdLocal.getDistrict());
            hotelCityService.selectHotelCityList(hotelCity).stream().findFirst().ifPresent(item -> jdLocal.setDistrict(item.getLocationName()));
        }
        return AjaxResult.success().put("plat", hotelGnBase).put("local", jdLocal);
    }

    /**
     * 1.删除酒店编号映射 2.备份backup表
     * @param ids
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ivw:mappingJD:remove')")
    @Log(title = "酒店编号映射", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult remove(@PathVariable Long[] ids){
        List<ZhJdJdbMapping> zhJdJdbMappings = zhJdJdbMappingService.selectJdJdbMappingByIds(ids);
        if(CollectionUtils.isEmpty(zhJdJdbMappings)){
            return AjaxResult.warn("未找到对应数据");
        }
        zhJdJdbMappings.stream().forEach(zhJdJdbMapping->{
            //备份
            ZhJdJdbMappingBackup zhJdJdbMappingBackup = ZhJdJdbMappingBackup.builder().build();
            BeanCopierUtils.copyProperties(zhJdJdbMapping, zhJdJdbMappingBackup, null);
            zhJdJdbMappingBackup.setSavedate(new Date());
            if (ObjectUtils.isEmpty(zhJdJdbMappingBackup.getPlatJdName())){
                zhJdJdbMappingBackup.setPlatJdName("");
            }
            if (ObjectUtils.isEmpty(zhJdJdbMappingBackup.getMappingScore())){
                zhJdJdbMappingBackup.setMappingScore("");
            }
            //加之前先查
            Boolean checkRepeat = zhJdJdbMappingBackupService.checkRepeat(zhJdJdbMappingBackup);
            if(checkRepeat) {
                int result = zhJdJdbMappingBackupService.insertZhJdJdbMappingBackup(zhJdJdbMappingBackup);
                log.info("ZhJdJdbMappingBackup酒店映射表备份:{},酒店id:{}", result>=0,zhJdJdbMapping.getId());
            }else {
                log.info("ZhJdJdbMappingBackup酒店映射表重复添加跳过,酒店id:{}",zhJdJdbMapping.getId());
            }
        });
        zhJdJdbMappingService.deleteThoroughByIds(zhJdJdbMappings.stream().collect(Collectors.mapping(ZhJdJdbMapping::getId, Collectors.toList())));
        return AjaxResult.success();
    }

    /**
     * 匹配分和平台酒店名称同步
     * @param pageLimit
     * @return
     */
    @GetMapping("/migrationForMapping")
    public AjaxResult migrationForMapping(PageLimit pageLimit) {
        String migrationRedisKey=String.format("%s_%s","JdJdbMappingController","migrationForMapping");
        RLock rLock = redissonClient.getLock(migrationRedisKey);
        boolean lock = false;
        try {
            lock = rLock.tryLock(2L,1000*60*60*5, TimeUnit.SECONDS);
            if (lock) {
                log.info("### JdJdbMappingController.migrationForMapping,匹配分和平台酒店名称同步,pageLimit:{}",pageLimit);
                zhJdJdbMappingService.selectJdJdbMappingListByPage(pageLimit);
                logger.info("### JdJdbMappingController_migrationForMapping_匹配分和平台酒店名称同步结束");
            } else {
                logger.info("匹配分和平台酒店名称同步已经在执行。。。。。。。。。。。。。。。。");
            }
        } catch (InterruptedException e) {
            logger.info("### JdJdbMappingController_migrationForMapping_匹配分和平台酒店名称同步异常", e);
        } finally {
            if (lock) {
                try {
                    rLock.unlock();
                } catch (Exception e) {
                    logger.error("### JdJdbMappingController_migrationForMapping_解锁异常,key:{}",migrationRedisKey);
                }
            }
        }
        return AjaxResult.success();
    }
}
