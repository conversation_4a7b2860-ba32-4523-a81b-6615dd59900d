package com.ltgj.web.canal;


import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.CanalListenerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * canal监听酒店相关表业务处理
 */
@Slf4j
@Component
public class HotelGnHsjlxyCanalListener implements MessageListener {

    @Autowired
    private CanalListenerService canalListenerService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String msg = MsgConvertUtil.bytes2String(message.getBody(), StandardCharsets.UTF_8.name());
        log.info("canal监听hotel_gn_hsjlxy表binlog消费日志 == {}", msg);
        try {
            canalListenerService.dealHotelCanalMsg(msg, PlatEnum.PLAT_HSJL_XY);
        } catch (Exception e) {
            log.error("canal监听hotel_gn_hsjlxy表binlog消费日志异常 == {}, msg == {}", e, msg);
        }
        return Action.CommitMessage;

    }
}
