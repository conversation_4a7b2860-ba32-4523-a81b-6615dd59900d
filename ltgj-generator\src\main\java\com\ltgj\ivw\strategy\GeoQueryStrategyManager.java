package com.ltgj.ivw.strategy;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 地理位置查询策略管理器
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@Slf4j
public class GeoQueryStrategyManager {
    
    @Autowired
    private List<GeoQueryStrategy> strategies;
    
    private Map<String, GeoQueryStrategy> strategyMap = new HashMap<>();
    
    @PostConstruct
    public void initStrategies() {
        for (GeoQueryStrategy strategy : strategies) {
            strategyMap.put(strategy.getSupportedLevel(), strategy);
        }
        log.info("初始化地理位置查询策略完成，策略数量：{}", strategyMap.size());
    }
    
    /**
     * 根据查询请求自动选择合适的策略
     * 
     * @param request 查询请求
     * @return 查询结果
     */
    public List<GeoHierarchyNode> executeQuery(GeoLevelQueryReq request) {
        String level = determineQueryLevel(request);
        GeoQueryStrategy strategy = strategyMap.get(level);
        
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的查询级别：" + level);
        }
        
        log.info("使用{}策略执行查询", level);
        return strategy.execute(request);
    }
    
    /**
     * 根据请求参数确定查询级别
     */
    private String determineQueryLevel(GeoLevelQueryReq request) {
        // 优先使用显式指定的level
        if (StringUtils.isNotEmpty(request.getLevel())) {
            return request.getLevel();
        }
        
        // 兼容旧版本：根据参数自动判断
        if (StringUtils.isNotEmpty(request.getParentId())) {
            // 根据上下文推断，这里需要根据实际业务逻辑来判断
            // 可以通过parentId查询对应的实体类型来确定下级类型
            return "unknown"; // 这种情况下建议明确指定level
        }
        
        return "country"; // 默认返回国家级别
    }
    
    /**
     * 获取指定级别的策略
     */
    public GeoQueryStrategy getStrategy(String level) {
        return strategyMap.get(level);
    }
    
} 