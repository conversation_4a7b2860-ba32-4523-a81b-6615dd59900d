package com.ltgj.ivw.utils.hotelApi;

import com.google.common.collect.Lists;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.sign.Md5Utils;
import com.ltgj.ivw.domain.MinPriceReq;
import com.ltgj.ivw.utils.MyTools;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class HsjlxyApi {
    private static OkHttpClient okHttpClient = new OkHttpClient();

    public static String urlKey = "hsjlxy.url";
    public static String clientIdKey = "hsjlxy.clientid";
    public static String licenseKeyKey = "hsjlxy.licensekey";
    private static String version = "1.0.0";

    //空港国际（领端）
    private static String partnerCode = "P1043";
    private static String secureKey = "9iTvANmla";

    public static void main(String[] args) throws InterruptedException {
        PropertiesUtil.isTest = true;
        PropertiesUtil.testMap.put(urlKey, "http://www.fangcang.com/tmc-hub/");
        PropertiesUtil.testMap.put(clientIdKey, partnerCode);
        PropertiesUtil.testMap.put(licenseKeyKey, secureKey);
        String s = HsjlxyApi.queryHotelInfo(Lists.newArrayList(2149181L), "", "");
        System.out.println(s);

        if (true) {
            return;
        }

        MinPriceReq minPriceReq = new MinPriceReq();
        List<Long> hotelIdList = new ArrayList<>() ;
        hotelIdList.add(1127146L);
        String tomorrow = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String ninetyDays = LocalDateTime.now().plusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        minPriceReq.setCheckInDate(tomorrow);
        minPriceReq.setCheckOutDate(ninetyDays);
        minPriceReq.setHotelIdList(hotelIdList);
        String s1 = HsjlxyApi.queryHotelLowestPrice(minPriceReq);
        System.out.println(s1);
//        queryHotelLowestPrice(minPriceReq)


        if (true) {
            return;
        }


//        partnerCodeBase = "*********";
//        secureKeyBase = "Bi92CjV82hVqPjPeaGbYtNcQ";
//       System.out.println();
        String cityInfoText = HsjlxyApi.queryCityList(Boolean.TRUE,partnerCode,secureKey);
        log.info("城市列表返回报文信息：{}", cityInfoText);
        if(StringUtils.isNotEmpty(cityInfoText)) {
            JSONObject bussinessResponse = JSONObject.parseObject(cityInfoText).getJSONObject("bussinessResponse");
            JSONArray provinces = bussinessResponse.getJSONArray("provinces");
            for (int i = 0; i < provinces.size(); i++) {
                JSONObject province = provinces.getJSONObject(i);
                log.info("省份：{}", province);
                JSONArray cities = province.getJSONArray("citys");
                for (int j = 0; j < cities.size(); j++){
                    JSONObject city = cities.getJSONObject(j);
                    String parentCityCode = city.getString("parentCityCode");
                    log.info("parentCityCode：{}", parentCityCode);
                    if(StringUtils.isNotEmpty(parentCityCode)){
                        continue;
                    }
                    String cityId = city.getString("cityId");
                    String cityCode = city.getString("cityCode");
                    log.info("cityId：{},cityCode:{}", cityId,cityCode);


                }
            }

        }

//
//        String s = queryHotelIdList(1, partnerCode, secureKeyBase);
//        System.out.println(s);

//        System.out.println(queryDistrictList());


//        List<Long> ids = new ArrayList<>();
//        ids.add(112241l);
//        ids.add(112244l);
//        ids.add(112246l);
//        ids.add(112248l);
//        ids.add(112249l);
//        ids.add(112250l);
//        ids.add(112251l);
//        ids.add(112267l);
//        ids.add(112269l);
//        ids.add(112270l);

//        while (true) {
//            for (int i = 0; i < 110; i++) {
//                new Thread(() -> System.out.println(queryHotelIdList(1))).start();
//            }
//            Thread.sleep(5000);
//        }

//        System.out.println(getMinPrice("112270"));

//        System.out.println(queryHotelInfo(ids));

//        System.out.println(queryHotelIncrement("2024-03-03 00:00:00", "2024-03-04 00:00:00", 1));

//        System.out.println(queryHotelImage());
//        MinPriceReq minPriceReq = new MinPriceReq();
//        List<Long> hotelIdList = new ArrayList<>();
//        hotelIdList.add(1273455L);
//        hotelIdList.add(561794L);
//        String checkInDate = "2024-09-18";
//        String checkOutDate = "2024-09-20";
//        minPriceReq.setCheckInDate(checkInDate);
//        minPriceReq.setCheckOutDate(checkOutDate);
//        minPriceReq.setHotelIdList(hotelIdList);
//        System.out.println(queryHotelLowestPrice(minPriceReq));


//        System.out.println(queryProductDetail());

//        System.out.println(queryCityList());


//        String queryHotelIdList = queryHotelIdList(1, "*********", "Bi92CjV82hVqPjPeaGbYtNcQ");
//        System.out.println(queryHotelIdList);
//        List<Long> list = new ArrayList<>();
//        list.add(561794L);
//        list.add(1273455L);
//        System.out.println(queryHotelInfo(list, "*********", "Bi92CjV82hVqPjPeaGbYtNcQ"));

    }

    public static Request createRequest(String requestType, JSONObject req){
        long timestamp = new Date().getTime();
        String signature = Md5Utils.hash(timestamp+partnerCode+(Md5Utils.hash(secureKey)).toUpperCase()+requestType).toUpperCase();
        JSONObject header = new JSONObject();
        header.put("partnerCode", partnerCode);
        header.put("requestType", requestType);
        header.put("signature", signature);
        header.put("timestamp", timestamp+"");
        header.put("version", version);
        req.put("header", header);
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody requestBody = RequestBody.create(mediaType,req.toString());
        String url = PropertiesUtil.getProp(urlKey) + requestType;
        log.info("红色加力协议查询请求: 请求url={}, 请求header={}, 请求参数={}",
            url, header.toJSONString(), req.toJSONString());
        Request request = new Request.Builder().post(requestBody).url(url).build();//构建
        return request;
    }

    //城市信息查询接口

    /**
     *
     * @param haveLowestPrice:是否含有起价酒店：true-只返回有起价酒店的城市，false-全量城市(不考虑是否含起价的酒店);当获取全量 城市时传true，当同步酒店列表时，建议先传false获取有起价的城市，再按城市酒店列表，以减少没必要的请求
     * @return
     */
    public static String queryCityList(Boolean haveLowestPrice, String pc, String sk){
        partnerCode = PropertiesUtil.getProp(clientIdKey);
        secureKey = PropertiesUtil.getProp(licenseKeyKey);
        if(StringUtils.isNotEmpty(pc) && StringUtils.isNotEmpty(sk)) {
            partnerCode = pc;
            secureKey = sk;
        }
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("countryCode", "CN");
        if(haveLowestPrice!=null){
            businessRequest.put("haveLowestPrice", haveLowestPrice);
        }
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryCityList", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            return response.body().string();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //行政区信息查询接口
    public static String queryDistrictList(){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("countryCode", "CN");
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryDistrictList", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            return response.body().string();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //酒店ID列表查询接口
    public static String queryHotelIdList(String cityCode,int pageNo, String pc, String sk){
        partnerCode = PropertiesUtil.getProp(clientIdKey);
        secureKey = PropertiesUtil.getProp(licenseKeyKey);
        if(StringUtils.isNotEmpty(pc) && StringUtils.isNotEmpty(sk)) {
            partnerCode = pc;
            secureKey = sk;
        }
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("checkInType", 1);
        businessRequest.put("pageNo", pageNo);
        if(StringUtils.isNotEmpty(cityCode)){
            businessRequest.put("cityCode",cityCode);
        }
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().connectTimeout(60, TimeUnit.SECONDS).readTimeout(60, TimeUnit.SECONDS).build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelIdList", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            ResponseBody body = response.body();
            if (Objects.isNull(body)) {
                log.info("红色加力协议查询请求: 响应参数=null");
                return null;
            }
            String resultStr = body.string();
            log.info("红色加力协议查询请求: 响应参数={}", resultStr);
            return resultStr;
        }catch (Exception e){
            log.error("拉取红色加力协议酒店数据异常 errorMessage:{}",e.getMessage(),e);
            return null;
        }
    }

    //酒店基本信息查询接口
    public static String queryHotelInfo(List<Long> ids, String pc, String sk){
        partnerCode = PropertiesUtil.getProp(clientIdKey);
        secureKey = PropertiesUtil.getProp(licenseKeyKey);
        if(StringUtils.isNotEmpty(pc) && StringUtils.isNotEmpty(sk)) {
            partnerCode = pc;
            secureKey = sk;
        }
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("hotelIds", ids);
        JSONArray jsonArray = new JSONArray();
        jsonArray.add("comment");
        jsonArray.add("hotelFacilityNew");
        jsonArray.add("hotelStructuredPolicies.childPolicy");
        jsonArray.add("hotelStructuredPolicies.extraBedPolicy");
        businessRequest.put("settings", jsonArray);
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelInfo", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            String respString = response.body().string();
            log.info("红色协议查询酒店信息, id:{}, response:{}", ids, respString);
            return respString;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //酒店增量查询接口
    public static String queryHotelIncrement(String startDate, String endDate, int pageNo){
        partnerCode = PropertiesUtil.getProp(clientIdKey);
        secureKey = PropertiesUtil.getProp(licenseKeyKey);
        JSONObject req = new JSONObject();
        req.put("data", "");
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("endTime", endDate);
        businessRequest.put("pageNo", pageNo);
        businessRequest.put("startTime", startDate);
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().connectTimeout(60, TimeUnit.SECONDS).readTimeout(60, TimeUnit.SECONDS).build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelIncrement", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            return response.body().string();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //酒店图片查询接口
    public static String queryHotelImage(){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        JSONArray hotelIds = new JSONArray();
        hotelIds.add(163011);
        businessRequest.put("hotelIds", hotelIds);
        businessRequest.put("type", 0);
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelImage", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            return response.body().string();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //酒店每日起价查询接口
    public static String queryHotelLowestPrice(MinPriceReq minPriceReq){
        partnerCode = PropertiesUtil.getProp(clientIdKey);
        secureKey = PropertiesUtil.getProp(licenseKeyKey);
        if(StringUtils.isNotEmpty(minPriceReq.getPartnerCode()) && StringUtils.isNotEmpty(minPriceReq.getSecureKey())) {
            partnerCode = minPriceReq.getPartnerCode();
            secureKey = minPriceReq.getSecureKey();
        }
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        JSONArray hotelIds = new JSONArray();     //与城市编码二选一
        hotelIds.addAll(minPriceReq.getHotelIdList());
        businessRequest.put("hotelIds", hotelIds);
//        businessRequest.put("cityCode", "SHA");     //与酒店ID二选一，按城市编码提示，系统异常
        businessRequest.put("checkInDate", minPriceReq.getCheckInDate());
        businessRequest.put("checkOutDate", minPriceReq.getCheckOutDate());
        req.put("businessRequest", businessRequest);
        Response response = null;
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelLowestPrice", req));
            //执行同步请求，获取Response对象
            response = call.execute();
            return response.body().string();
        }catch (Exception e){
            log.error("红色加力协议 响应参数异常 response:{}", response, e);
            return null;
        }
    }

    //酒店实时产品查询接口
    public static BigDecimal getMinPrice(String id){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("hotelId", Long.valueOf(id));     //与酒店ID二选一，按城市编码提示，系统异常
        businessRequest.put("checkInDate", MyTools.getDateByCurr(2));
        businessRequest.put("checkOutDate", MyTools.getDateByCurr(3));
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryProductDetail", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            String res = response.body().string();
            System.out.println(res);
            JSONArray roomItems = JSONObject.parseObject(res).getJSONObject("bussinessResponse").getJSONArray("roomItems");
            BigDecimal minPrice = null;
            for (int i = 0; i < roomItems.size(); i++) {
                JSONObject roomItem = roomItems.getJSONObject(i);
                JSONArray products = roomItem.getJSONArray("products");
                for (int j = 0; j < products.size(); j++) {
                    JSONObject product = products.getJSONObject(j);
                    JSONArray priceItems = product.getJSONArray("priceItems");
                    for (int k = 0; k < priceItems.size(); k++) {
                        JSONObject priceItem = priceItems.getJSONObject(k);
                        BigDecimal price = priceItem.getBigDecimal("salePrice");
                        if (minPrice == null || price.compareTo(minPrice) < 0) {
                            minPrice = price;
                        }
                    }
                }
            }
            return minPrice;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}
