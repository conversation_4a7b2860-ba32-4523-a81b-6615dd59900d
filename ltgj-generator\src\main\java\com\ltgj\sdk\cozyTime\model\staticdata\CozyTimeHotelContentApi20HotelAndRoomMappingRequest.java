package com.ltgj.sdk.cozyTime.model.staticdata;

import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 根据国家编码查询城市信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelContentApi20HotelAndRoomMappingRequest implements CozyTimeRequestInterface<CozyTimeHotelContentApi20HotelAndRoomMappingResponse> {
    @Override
    public String getRequestMethod() {
        return "POST";
    }

    @Override
    public String getEndpoint() {
        return "/hotel_content_api/2.0/hotelAndRoomMapping";
    }

    @Override
    public Class<CozyTimeHotelContentApi20HotelAndRoomMappingResponse> getResponseClass() {
        return CozyTimeHotelContentApi20HotelAndRoomMappingResponse.class;
    }

    /**
     * 国家编码国家编码
     * 必填: true
     */
    private List<Long> hotelIds;
}
