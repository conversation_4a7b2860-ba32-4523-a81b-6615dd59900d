package com.ltgj.supplier.common.gn.service;

import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.mapper.HotelGnBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class HotelGnBaseService {

    @Autowired
    private HotelGnBaseMapper hotelGnBaseMapper;

    public HotelGnBase getById(PlatEnum platEnum, String id) {
        return this.hotelGnBaseMapper.selectById(platEnum.getTableNameSuffix(), id);
    }

    public List<HotelGnBase> getByIdList(PlatEnum platEnum, List<String> idList) {
        return this.hotelGnBaseMapper.selectByIdList(platEnum.getTableNameSuffix(), idList);
    }

    /**
     * 批量新增
     * @param idList
     * @param userName
     * @param platEnum
     * @return list[0] = 新增，list[1] = 更新
     */
    public List<List<HotelGnBase>> addOrUpdateBatch(List<String> idList, String userName, PlatEnum platEnum) {
        List<HotelGnBase> list = getByIdList(platEnum, idList);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>(idList.size());
            List<HotelGnBase> finalList = list;
            idList.stream().forEach(id ->{
                finalList.add(buildHotelGnBase(id, userName));
            });
            addOrUpdate(platEnum, list);
            return Arrays.asList(list);
        }
        List<HotelGnBase> addList = new ArrayList<>();
        List<HotelGnBase> updateList = new ArrayList<>();
        List<HotelGnBase> finalList1 = list;
        idList.stream().forEach(id ->{
            HotelGnBase hotelGnBase = finalList1.stream().filter(item -> item.getId().equals(id)).findFirst().orElse(null);
            if (hotelGnBase == null) {
                addList.add(buildHotelGnBase(id, userName));
            } else {
                hotelGnBase.setUpdateTime(new Date());
                hotelGnBase.setUpdateBy(userName);
                hotelGnBase.setIsDelete(0);
                updateList.add(hotelGnBase);
            }
        });
        list.clear();
        list.addAll(addList);
        list.addAll(updateList);
        addOrUpdate(platEnum, list);
        return Arrays.asList(addList, updateList);
    }

    public int addOrUpdate(PlatEnum platEnum, List<HotelGnBase> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return this.hotelGnBaseMapper.addOrUpdateBatch(platEnum.getTableNameSuffix(), list);
    }

    public int addOrUpdateBatchNoFunction(PlatEnum platEnum, List<HotelGnBase> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return this.hotelGnBaseMapper.addOrUpdateBatchNoFunction(platEnum.getTableNameSuffix(), list);
    }

    // **返回字段不会包含reverse0, 原始报文太大
    public List<HotelGnBase> selectHotelGnBaseList(PlatEnum platEnum, HotelGnBase hotelGnBase) {
        return this.hotelGnBaseMapper.selectHotelGnBaseList(platEnum.getTableNameSuffix(), hotelGnBase);
    }

    public int deleteHotelGnById(PlatEnum platEnum, String id) {
        if (platEnum == null || StringUtils.isBlank(id)) {
            return -1;
        }
        return this.hotelGnBaseMapper.deleteHotelGnById(platEnum.getTableNameSuffix(), id);
    }

    public int insertSelective(PlatEnum platEnum, HotelGnBase hotelGnBase) {
        if (platEnum == null || StringUtils.isBlank(hotelGnBase.getId())) {
            return -1;
        }
        return this.hotelGnBaseMapper.insertSelective(platEnum.getTableNameSuffix(), hotelGnBase);

    }

    public int updateByPrimaryKeySelective(PlatEnum platEnum, HotelGnBase hotelGnBase) {
        if (platEnum == null || StringUtils.isBlank(hotelGnBase.getId())) {
            return -1;
        }
        return this.hotelGnBaseMapper.updateByPrimaryKeySelective(platEnum.getTableNameSuffix(), hotelGnBase);

    }

    public List<HotelGnBase> getAll(PlatEnum platEnum) {
        return hotelGnBaseMapper.selectAll(platEnum.getTableNameSuffix());
    }

    public List<HotelGnBase> getAllIdList(PlatEnum platEnum) {
        return hotelGnBaseMapper.selectAllId(platEnum.getTableNameSuffix());
    }

    public List<HotelGnBase> getByIncrementStatus(PlatEnum platEnum, int incrementStatus) {
        return hotelGnBaseMapper.selectByIncrementStatus(platEnum.getTableNameSuffix(), incrementStatus);
    }

    public List<HotelGnBase> getByStatus(PlatEnum platEnum, int status) {
        return hotelGnBaseMapper.selectByStatus(platEnum.getTableNameSuffix(), status);
    }

    public int selectCount(PlatEnum platEnum) {
        return hotelGnBaseMapper.selectCount(platEnum.getTableNameSuffix());
    }

    public List<String> selectIdsByPage(PlatEnum platEnum, int start, int size) {
        return hotelGnBaseMapper.selectIdsByPage(platEnum.getTableNameSuffix(), start, size);
    }

    public int updateStatusById(PlatEnum platEnum, String id, Integer status, String updateBy, Date updateTime) {
        return hotelGnBaseMapper.updateStatusById(platEnum.getTableNameSuffix(), id, status, updateBy, updateTime);
    }

    public List<HotelGnBase> getByLimit(PlatEnum platEnum, Integer offset, Integer limit, Integer notStatus, String minId, String maxId) {
        return hotelGnBaseMapper.selectByLimit(platEnum.getTableNameSuffix(), offset, limit, notStatus, minId, maxId);
    }

    public List<HotelGnBase> getByCityIds(PlatEnum platEnum, List<String> cityIds) {
        return hotelGnBaseMapper.selectByCityIds(platEnum.getTableNameSuffix(), cityIds);
    }

    private HotelGnBase buildHotelGnBase(String id, String userName) {
        HotelGnBase hotelGnBase = new HotelGnBase();
        hotelGnBase.setStatus(0);
        hotelGnBase.setId(id);
        hotelGnBase.setCreateTime(new Date());
        hotelGnBase.setCreateBy(userName);
        hotelGnBase.setUpdateTime(new Date());
        hotelGnBase.setUpdateBy(userName);
        return hotelGnBase;
    }
}
