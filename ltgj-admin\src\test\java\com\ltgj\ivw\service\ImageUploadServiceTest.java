package com.ltgj.ivw.service;

import com.ltgj.common.utils.file.ImageUploadService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * 图片上传服务测试类
 * 演示如何使用封装后的ImageUploadService API进行图片上传
 * 
 * <AUTHOR>
 * @Date 2025/6/5
 * @description: ImageUploadService API使用示例
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ImageUploadServiceTest {

    @Autowired
    private ImageUploadService imageUploadService;

    /**
     * 测试使用File对象上传图片
     * 演示最简单的文件上传方式
     */
    @Test
    public void testUploadImageWithFile() {
        System.out.println("=== 测试File对象上传图片 ===");
        
        // 调用封装后的API
        ImageUploadService.ImageUploadResult result = imageUploadService.uploadImageWithFile(
            "C:\\Users\\<USER>\\Pictures\\021a69c1cefb835fd7c85186b2338ddf.jpg",
            "test/hotel/images"
        );
        
        // 处理结果
        handleUploadResult(result, "File对象上传");
    }

    /**
     * 测试使用MultipartFile上传图片（推荐方式）
     * 演示Web表单文件上传的标准方式
     */
    @Test
    public void testUploadImageWithMultipartFile() {
        System.out.println("=== 测试MultipartFile上传图片 ===");
        
        try {
            // 1. 创建模拟的MultipartFile对象（测试代码中可以使用MockMultipartFile）
            File imageFile = new File("C:\\Users\\<USER>\\Pictures\\021a69c1cefb835fd7c85186b2338ddf.jpg");
            if (!imageFile.exists()) {
                System.err.println("❌ 测试文件不存在，跳过测试");
                return;
            }
            
            byte[] fileContent = Files.readAllBytes(imageFile.toPath());
            MockMultipartFile multipartFile = new MockMultipartFile(
                "file",
                imageFile.getName(),
                "image/jpeg",
                fileContent
            );

            // 2. 调用封装后的API
            ImageUploadService.ImageUploadResult result = imageUploadService.uploadImageWithMultipartFile(
                multipartFile,
                "test/hotel/images"
            );
            
            // 3. 处理结果
            handleUploadResult(result, "MultipartFile上传");
            
        } catch (IOException e) {
            System.err.println("❌ 文件读取失败：" + e.getMessage());
        }
    }



    /**
     * 测试使用自定义文件名上传图片（高级用法）
     * 演示如何控制上传文件的命名规则
     */
    @Test
    public void testUploadImageWithCustomName() {
        System.out.println("=== 测试自定义文件名上传图片 ===");
        
        // 生成自定义文件名
        String customFileName = "hotel_test_" + System.currentTimeMillis() + ".jpg";
        
        // 调用封装后的API
        ImageUploadService.ImageUploadResult result = imageUploadService.uploadImageWithCustomName(
            "C:\\Users\\<USER>\\Pictures\\021a69c1cefb835fd7c85186b2338ddf.jpg",
            "test/hotel/custom",
            customFileName,
            "local"
        );
        
        // 处理结果
        handleUploadResult(result, "自定义文件名上传");
    }

    /**
     * 处理上传结果的通用方法
     * 统一展示上传结果信息
     * 
     * @param result 上传结果
     * @param method 上传方式描述
     */
    private void handleUploadResult(ImageUploadService.ImageUploadResult result, String method) {
        System.out.println("\n📊 " + method + "结果：");
        
        if (result.isSuccess()) {
            System.out.println("✅ 上传成功！");
            System.out.println("🌐 完整访问地址：" + result.getFullAccessUrl());
            System.out.println("📋 可直接在浏览器中打开：");
            System.out.println("   " + result.getFullAccessUrl());
            
            // 显示原始上传结果的详细信息
            if (result.getUploadResult() != null) {
                System.out.println("\n📄 详细信息：");
                if (result.getUploadResult().getFileId() != null) {
                    System.out.println("  - 文件ID：" + result.getUploadResult().getFileId());
                }
                if (result.getUploadResult().getFileName() != null) {
                    System.out.println("  - 文件名：" + result.getUploadResult().getFileName());
                }
                if (result.getUploadResult().getFileType() != null) {
                    System.out.println("  - 文件类型：" + result.getUploadResult().getFileType());
                }
                if (result.getUploadResult().getBizType() != null) {
                    System.out.println("  - 业务类型：" + result.getUploadResult().getBizType());
                }
                if (result.getUploadResult().getSize() != null) {
                    System.out.println("  - 文件大小：" + result.getUploadResult().getSize() + " bytes");
                }
            }
        } else {
            System.err.println("❌ 上传失败！");
            System.err.println("🔍 错误信息：" + result.getErrorMessage());
        }
        
    }

    /**
     * 批量测试所有上传方式
     * 一次性测试所有封装的上传方法
     */
    @Test
    public void testAllUploadMethods() {
        System.out.println("🚀 开始批量测试所有上传方式...\n");
        
        // 测试1：File对象上传
        testUploadImageWithFile();
        
        // 测试2：MultipartFile上传
        testUploadImageWithMultipartFile();
        
        // 测试3：自定义文件名上传
        testUploadImageWithCustomName();
        
        System.out.println("�� 所有测试完成！");

    }
} 