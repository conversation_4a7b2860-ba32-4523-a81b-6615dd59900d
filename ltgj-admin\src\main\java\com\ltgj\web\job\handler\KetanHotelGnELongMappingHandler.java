package com.ltgj.web.job.handler;

import com.ltgj.ivw.service.IHotelInfoKetanPreMappingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 科坦酒店国内国内酒店艺龙映射
 */
@Slf4j
@Component
@JobHandler(value = "ketanHotelGnELongMappingHandler")
public class KetanHotelGnELongMappingHandler extends IJobHandler {

    @Autowired
    private IHotelInfoKetanPreMappingService ketanPreMappingService;

    /**
     * 定时任务处理预映射
     *
     * @param s 标记： 1：请求接口获取与艺龙酒店关系存入预映射表  2：根据预映射表更新酒店映射关系表
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        long startTime = System.currentTimeMillis();

        try {
            XxlJobLogger.log("----科坦酒店国内国内酒店艺龙映射----开始");
            log.info("----科坦酒店国内国内酒店艺龙映射----开始");
            this.ketanPreMappingService.processHotelGnELongMapping();
            XxlJobLogger.log("----科坦酒店国内国内酒店艺龙映射----完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("---科坦酒店国内国内酒店艺龙映射---失败：" + e);
        }


        log.info("---科坦酒店国内国内酒店艺龙映射---结束 执行结果：失败");
        XxlJobLogger.log("科坦任务处理 执行用时：" + (System.currentTimeMillis() - startTime) / 1000 + "秒");
        return ReturnT.FAIL;
    }
}
