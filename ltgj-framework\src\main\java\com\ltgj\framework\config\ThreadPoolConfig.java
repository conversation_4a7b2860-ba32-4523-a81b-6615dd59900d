package com.ltgj.framework.config;

import com.ltgj.common.utils.Threads;
import com.ltgj.framework.web.exception.GlobalExceptionHandler;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

/**
 * 线程池配置
 *
 * <AUTHOR>
 **/
@Configuration
public class ThreadPoolConfig implements AsyncConfigurer {
    // 核心线程池大小
    private int corePoolSize = 50;

    // 最大可创建的线程数
    private int maxPoolSize = 200;

    // 队列最大长度
    private int queueCapacity = 5000;

    // 线程池维护线程所允许的空闲时间
    private int keepAliveSeconds = 300;

    @Autowired
    private GlobalExceptionHandler globalExceptionHandler;


    @Bean(name = "threadPoolTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(this.maxPoolSize);
        executor.setCorePoolSize(this.corePoolSize);
        executor.setQueueCapacity(this.queueCapacity);
        executor.setKeepAliveSeconds(this.keepAliveSeconds);
        // 线程池对拒绝任务(无线程可用)的处理策略
        /**
         * AbortPolicy:默认策略，在需要拒绝任务时抛出RejectedExecutionException；
         * CallerRunsPolicy:直接在 execute 方法的调用线程中运行被拒绝的任务，如果线程池已经关闭，任务将被丢弃；
         * DiscardPolicy：直接丢弃任务；
         * DiscardOldestPolicy：丢弃队列中等待时间最长的任务，并执行当前提交的任务，如果线程池已经关闭，任务将被丢弃。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "threadPoolTaskExecutor2")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor2() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(this.maxPoolSize);
        executor.setCorePoolSize(this.corePoolSize);
        executor.setQueueCapacity(this.queueCapacity);
        executor.setKeepAliveSeconds(this.keepAliveSeconds);
        // 线程池对拒绝任务(无线程可用)的处理策略
        /**
         * AbortPolicy:默认策略，在需要拒绝任务时抛出RejectedExecutionException；
         * CallerRunsPolicy:直接在 execute 方法的调用线程中运行被拒绝的任务，如果线程池已经关闭，任务将被丢弃；
         * DiscardPolicy：直接丢弃任务；
         * DiscardOldestPolicy：丢弃队列中等待时间最长的任务，并执行当前提交的任务，如果线程池已经关闭，任务将被丢弃。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "cozytimePriceExecutor")
    public ThreadPoolTaskExecutor cozytimePriceExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(5);
        executor.setCorePoolSize(5);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setKeepAliveSeconds(this.keepAliveSeconds);
        executor.setThreadNamePrefix("cozytimePriceExecutor-");
        // 线程池对拒绝任务(无线程可用)的处理策略
        /**
         * AbortPolicy:默认策略，在需要拒绝任务时抛出RejectedExecutionException；
         * CallerRunsPolicy:直接在 execute 方法的调用线程中运行被拒绝的任务，如果线程池已经关闭，任务将被丢弃；
         * DiscardPolicy：直接丢弃任务；
         * DiscardOldestPolicy：丢弃队列中等待时间最长的任务，并执行当前提交的任务，如果线程池已经关闭，任务将被丢弃。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "allKetanHotelGnExecutor")
    public ThreadPoolTaskExecutor allKetanHotelGnExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(5);
        executor.setCorePoolSize(5);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setKeepAliveSeconds(this.keepAliveSeconds);
        executor.setThreadNamePrefix("allKetanHotelGnExecutor-");
        // 线程池对拒绝任务(无线程可用)的处理策略
        /**
         * AbortPolicy:默认策略，在需要拒绝任务时抛出RejectedExecutionException；
         * CallerRunsPolicy:直接在 execute 方法的调用线程中运行被拒绝的任务，如果线程池已经关闭，任务将被丢弃；
         * DiscardPolicy：直接丢弃任务；
         * DiscardOldestPolicy：丢弃队列中等待时间最长的任务，并执行当前提交的任务，如果线程池已经关闭，任务将被丢弃。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
    @Bean(name = "allKetanHotelGnElongExecutor")
    public ThreadPoolTaskExecutor allKetanHotelGnElongExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(5);
        executor.setCorePoolSize(5);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setKeepAliveSeconds(this.keepAliveSeconds);
        executor.setThreadNamePrefix("allKetanHotelGnElongExecutor-");
        // 线程池对拒绝任务(无线程可用)的处理策略
        /**
         * AbortPolicy:默认策略，在需要拒绝任务时抛出RejectedExecutionException；
         * CallerRunsPolicy:直接在 execute 方法的调用线程中运行被拒绝的任务，如果线程池已经关闭，任务将被丢弃；
         * DiscardPolicy：直接丢弃任务；
         * DiscardOldestPolicy：丢弃队列中等待时间最长的任务，并执行当前提交的任务，如果线程池已经关闭，任务将被丢弃。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "allMeituanHotelGnExecutor")
    public ThreadPoolTaskExecutor allMeituanHotelGnExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(5);
        executor.setCorePoolSize(5);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setKeepAliveSeconds(this.keepAliveSeconds);
        executor.setThreadNamePrefix("allMeituanHotelGnExecutor-");
        // 线程池对拒绝任务(无线程可用)的处理策略
        /**
         * AbortPolicy:默认策略，在需要拒绝任务时抛出RejectedExecutionException；
         * CallerRunsPolicy:直接在 execute 方法的调用线程中运行被拒绝的任务，如果线程池已经关闭，任务将被丢弃；
         * DiscardPolicy：直接丢弃任务；
         * DiscardOldestPolicy：丢弃队列中等待时间最长的任务，并执行当前提交的任务，如果线程池已经关闭，任务将被丢弃。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    /**
     * 执行周期性或定时任务
     */
    @Bean(name = "scheduledExecutorService")
    protected ScheduledExecutorService scheduledExecutorService() {
        return new ScheduledThreadPoolExecutor(this.corePoolSize,
                new BasicThreadFactory.Builder().namingPattern("schedule-pool-%d").daemon(true).build(),
                new ThreadPoolExecutor.CallerRunsPolicy()) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                Threads.printException(r, t);
            }
        };
    }

    /**
     * @return
     */
    @Bean("forkJoinPool")
    public ForkJoinPool forkJoinPool() {
        ForkJoinPool forkJoinPool = new ForkJoinPool(Runtime.getRuntime().availableProcessors(),
                ForkJoinPool.defaultForkJoinWorkerThreadFactory,
                null,
                true);
        return forkJoinPool;
    }

    @Bean(name = "asyncExecutorPreMapping")
    public ThreadPoolTaskExecutor asyncExecutorPreMapping() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setMaxPoolSize(10);
        executor.setCorePoolSize(10);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setKeepAliveSeconds(this.keepAliveSeconds);
        executor.setThreadNamePrefix("allKetanHotelGnExecutor-");
        // 线程池对拒绝任务(无线程可用)的处理策略
        /**
         * AbortPolicy:默认策略，在需要拒绝任务时抛出RejectedExecutionException；
         * CallerRunsPolicy:直接在 execute 方法的调用线程中运行被拒绝的任务，如果线程池已经关闭，任务将被丢弃；
         * DiscardPolicy：直接丢弃任务；
         * DiscardOldestPolicy：丢弃队列中等待时间最长的任务，并执行当前提交的任务，如果线程池已经关闭，任务将被丢弃。
         */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    /**
     * 获取异步执行器
     * <p>
     * 本方法用于提供一个异步任务执行器，用于执行异步任务
     * 由于此处返回null，表明不提供异步执行器实例所以
     * 最终会使用 TaskExecutionAutoConfiguration 自动化配置类创建出来的 ThreadPoolTaskExecutor 任务执行器，作为默认执行器
     *
     * @return Executor 异步执行器实例，用于执行异步任务此处应返回null
     */
    @Override
    public Executor getAsyncExecutor() {
        return null;
    }

    /**
     * 获取异步任务未捕获异常处理器
     * <p>
     * 该方法用于指定一个全局的异常处理器，当异步任务中抛出未被捕获的异常时，异常处理器会对其进行处理
     *
     * @return AsyncUncaughtExceptionHandler 返回全局异常处理器实例
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return this.globalExceptionHandler;
    }
}
