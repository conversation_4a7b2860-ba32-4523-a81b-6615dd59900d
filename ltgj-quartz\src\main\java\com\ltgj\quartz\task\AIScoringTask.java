package com.ltgj.quartz.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.service.AiMappingService;
import com.ltgj.quartz.domain.TmpAiScoring;
import com.ltgj.quartz.mapper.SysJobMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Component("aIScoringTask")
public class AIScoringTask {

    final static String partnerCode = "1064";
    final static String secretKey = "XeQcsibSTiZPT";
    final static String testUrl = "http://openapi-test.cozy-time.com:8135";

    private static OkHttpClient okHttpClient = new OkHttpClient();
    private static ExecutorService executorService = new ForkJoinPool(); // 使用 ForkJoinPool 作为线程池

    @Autowired
    private AiMappingService aiMappingService;

    @Autowired
    SysJobMapper sysJobMapper;

    public void execute() {
        Map<String, String> regionMap = new HashMap<>();
        regionMap.put("CT510100", "成都");
        regionMap.put("CT430100", "长沙");
        regionMap.put("CT370200", "青岛");
        regionMap.put("CT460100", "海口");

        String region = "CT460100";
        JSONArray hotelIdsJsonArray = getRequest("/hotel_content_api/2.0/hotelIds?countryCode=CN&cityCode=" + region, "hotelIds");
        int batchSize = 10; // 每次处理的批次大小
        int totalSize = hotelIdsJsonArray.size();
        sysJobMapper.deleteOldDate(regionMap.get(region));

        // 使用 CompletableFuture 进行异步处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < totalSize; i += batchSize) {
            // 计算当前批次的结束索引
            int endIndex = Math.min(i + batchSize, totalSize);
            // 取出当前批次的数据
            List<Object> tenBatch = hotelIdsJsonArray.subList(i, endIndex);

            // 异步执行每个批次的逻辑
//            CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
            // 构造请求参数
            JSONObject param = new JSONObject();
            param.put("hotelIds", tenBatch);

            // 调用 sendRequest 方法
            JSONObject hotelInfo = sendRequest("/hotel_content_api/2.0/hotelInfo", "", param.toJSONString());
            JSONArray hotelInfoArray = hotelInfo.getJSONArray("hotels");
            Map<String, JSONObject> stringJSONObjectMap = hotelInfoArray.stream()
                    .map(h -> (JSONObject) h)
                    .collect(Collectors.toMap(h -> h.getString("hotelId"), h -> h));
            JSONObject elongIds = sendRequest("/hotel_content_api/2.0/hotelAndRoomMapping", "", param.toJSONString());
            JSONArray elongIdsArray = elongIds.getJSONArray("hotelMappingList");
            Map<String, JSONObject> stringJSONObjectMap1 = elongIdsArray.stream()
                    .map(h -> (JSONObject) h)
                    .collect(Collectors.toMap(h -> h.getString("hotelId"), h -> h));
            List<TmpAiScoring> aslist = new ArrayList<>();
            stringJSONObjectMap.forEach((key, value) -> {
                TmpAiScoring tmpAiScoring = new TmpAiScoring();
                tmpAiScoring.setId(Long.valueOf(key));
                tmpAiScoring.setAddressLine(value.getString("hotelAddressCN"));
                tmpAiScoring.setName(value.getString("hotelNameCN"));
                tmpAiScoring.setPhone(value.getString("phone"));
                tmpAiScoring.setCityName(regionMap.get(region));//value.getString("cityCode")
                JSONObject orDefault = stringJSONObjectMap1.getOrDefault(key, null);
                if (orDefault != null) {
                    tmpAiScoring.setElongId(orDefault.getLong("elongHotelId"));
                }
                tmpAiScoring.setVendorType(1);
                aslist.add(tmpAiScoring);
            });
            sysJobMapper.insertTmpAiScoring(aslist);
//                return aslist;
//            }, executorService).thenAccept(sysJobMapper::insertTmpAiScoring);

//            futures.add(future);
        }

        // 等待所有异步任务完成
//        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    public void getFacilities() {

        JSONArray facilities = getRequest("/hotel_content_api/2.0/facilities?region=1", "facilities");
        log.info("{}", facilities.toJSONString());
    }

    public void getOrderDetail(){
        JSONArray facilities = getRequest("/hotel_api/2.0/orderDetail?clientReference=1110851108524545", "bookings");
        log.info("{}", facilities.toJSONString());
    }

    public void getCancel(){
        JSONObject param = new JSONObject();
        param.put("orderNo", "1123775299511");

        JSONObject cancelCode = sendRequest("/hotel_api/2.0/cancel","", param.toJSONString());
        log.info("{}", cancelCode.toJSONString());
    }

    public void mappingScore() {
        aiMappingService.aiKeTanMapping();
    }

    private JSONArray getRequest(String urlPath, String rootKey) {
        try {
            final Call call = okHttpClient.newCall(createRequest2(urlPath));
            Response response = null;
            response = call.execute();
            String result = response.body().string();
            log.info(result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            JSONArray hotelIdsArray = jsonObject.getJSONArray(rootKey);
            return hotelIdsArray;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static Map getHeaderMap(String partnerCode, Long timeStamp) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("partnerCode", partnerCode);
        headerMap.put("timeStamp", timeStamp + "");
        return headerMap;
    }

    private static Request buildRequest(String urlPath, String body) {
        long timeMillis = System.currentTimeMillis();
        Map<String, String> headerMap = getHeaderMap(partnerCode, timeMillis);
        headerMap.put("x-Signature", generateXSignature(partnerCode, secretKey, timeMillis));

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody requestBody = RequestBody.create(mediaType, body);
        String url = testUrl + urlPath;
        log.info("康旅严选, 请求url={}, 请求header={}, 请求参数={}",
                url, JSON.toJSONString(headerMap), body);
        return new Request.Builder().post(requestBody).url(url).headers(Headers.of(headerMap)).build();//构建
    }

    public static Request createRequest2(String path) {
        long timeMillis = System.currentTimeMillis();
        Map<String, String> headerMap = getHeaderMap(partnerCode, timeMillis);
        headerMap.put("x-Signature", generateXSignature(partnerCode, secretKey, timeMillis));
        Request request = new Request.Builder().get().url(testUrl + path).headers(Headers.of(headerMap)).build();//构建
        System.out.println(testUrl + path);
        return request;
    }

    private static JSONObject sendRequest(String urlPath, String methodDesc, String requestBody) {
        try {
            OkHttpClient client = okHttpClient.newBuilder().connectTimeout(60, TimeUnit.SECONDS).readTimeout(60, TimeUnit.SECONDS).build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(buildRequest(urlPath, requestBody));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            ResponseBody body = response.body();
            if (Objects.isNull(body)) {
                log.info("[康旅严选酒店-{}], 查询请求: 响应参数=null", methodDesc);
                return null;
            }
            String resultStr = body.string();
            log.info("[康旅严选酒店-{}], 查询请求: 响应参数={}", methodDesc, resultStr);
            if (StringUtils.isNotBlank(resultStr)) {
                JSONObject json = JSON.parseObject(resultStr);
                if (json.getIntValue("resultCode") != 0) {
                    log.info("[康旅严选酒店-{}], 请求失败：{}", methodDesc, resultStr);
                    return null;
                }
                return json;
            }
            return null;
        } catch (Exception e) {
            log.error("[康旅严选酒店-{}] 数据异常 errorMessage:{}, error:{}", methodDesc, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据 partnerCode、secretKey 和 timestamp 生成 x-Signature
     *
     * @param partnerCode 合作方编号
     * @param secretKey   密钥
     * @param timestamp   时间戳 (毫秒)
     * @return x-Signature 签名值
     */
    public static String generateXSignature(String partnerCode, String secretKey, long timestamp) {
        try {
            // 拼接字符串
            String input = partnerCode + secretKey + timestamp;

            // 获取 SHA-512 MessageDigest 实例
            MessageDigest md = MessageDigest.getInstance("SHA-512");

            // 执行哈希计算
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为 Base64 编码字符串
            // 将字节数组转换为 Hex 字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // 补零
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成 x-Signature 签名失败", e);
        }
    }

}
