package com.ltgj.supplier.common.gn.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 平台酒店-国内-id映射
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotelGnIdMapping {

    private String id;

    /**
     * 平台编号
     */
    private String platformId;

    /**
     * 平台酒店id
     */
    private String platformHotelId;

    /**
     * 映射的平台编号，-1，基础酒店;
     */
    private String mappingPlatformId;

    /**
     * 映射的平台酒店id
     */
    private String mappingHotelId;

    /**
     * 备注
     */
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
    private Integer isDelete;
}
