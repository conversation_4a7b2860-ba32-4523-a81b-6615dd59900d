package com.ltgj.ivw.service;

import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelCityAll;
import com.ltgj.ivw.dto.HotelSupplierSearchCityReq;
import com.ltgj.ivw.enums.PlatEnum;

import java.util.List;

/**
 * 酒店城市管理Service接口
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
public interface IHotelCityService
{
    /**
     * 查询酒店城市管理
     *
     * @param id 酒店城市管理主键
     * @return 酒店城市管理
     */
    public HotelCity selectHotelCityById(Integer id);

    HotelCity selectHotelCityByCityId(String cityId);

    HotelCity selectHotelCityByLocationId(String locationId);

    /**
     * 查询酒店城市管理列表
     *
     * @param hotelCity 酒店城市管理
     * @return 酒店城市管理集合
     */
    public List<HotelCity> selectHotelCityList(HotelCity hotelCity);

    /**
     * 查询酒店城市管理列表符合条件的总数
     * @return
     */
    Long selectHotelCityCount(HotelCity hotelCity);
    /**
     * 新增酒店城市管理
     *
     * @param hotelCity 酒店城市管理
     * @return 结果
     */
    public int insertHotelCity(HotelCity hotelCity);

    int batchInsertHotelCity(List<HotelCity> hotelCity);

    /**
     * 新增酒店城市管理
     *
     * @param hotelCity 酒店城市管理
     * @return 结果
     */
    public int insertHotelCityAll(HotelCityAll hotelCity);

    /**
     * 修改酒店城市管理
     *
     * @param hotelCity 酒店城市管理
     * @return 结果
     */
    public int updateHotelCity(HotelCity hotelCity);

    int updateHotelCityByCityId(List<HotelCity> hotelCitys);

    /**
     * 批量删除酒店城市管理
     *
     * @param ids 需要删除的酒店城市管理主键集合
     * @return 结果
     */
    public int deleteHotelCityByIds(Integer[] ids);

    /**
     * 删除酒店城市管理信息
     *
     * @param id 酒店城市管理主键
     * @return 结果
     */
    public int deleteHotelCityById(Integer id);

    /**
     * 导入城市数据
     *
     * @param cityList 用户数据列表
     * @return 结果
     */
    public String importCity(List<HotelCity> cityList);

    public List<HotelCity> selectHotelCityListByName(HotelCity hotelCityQuery);

    HotelCity selectByHsjlLocation(String locationId);

    List<HotelCity> selectByCityName(HotelSupplierSearchCityReq hotelCity);

    /**
     * 通过本地缓存 根据平台ID和城市ID获取城市信息
     * @param platEnum
     * @param cityId
     * @return
     */
    HotelCity getByCityIdAndPlatIdForCache(PlatEnum platEnum, String cityId);

    HotelCity getByAreaIdAndPlatIdForCache(PlatEnum platEnum, String areaId);
}
