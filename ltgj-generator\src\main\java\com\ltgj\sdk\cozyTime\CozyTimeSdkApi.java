package com.ltgj.sdk.cozyTime;


import com.google.gson.Gson;
import com.ltgj.sdk.cozyTime.base.CozyTimeBaseResponse;
import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelIdsRequest;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelIdsResponse;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelInfoRequest;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelInfoResponse;
import com.ltgj.sdk.cozyTime.model.staticdata.*;
import com.ltgj.sdk.cozyTime.model.staticdata.*;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 科坦酒店API SDK
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Component
public class CozyTimeSdkApi {
    private static final Logger log = LoggerFactory.getLogger(CozyTimeSdkApi.class);

    private OkHttpClient httpClient;

    @Value("${cozyTime.url:http://openapi-test.cozy-time.com:8135}")
    private String apiUrl;

    @Value("${cozyTime.partnerCode:1064}")
    private String partnerCode;

    @Value("${cozyTime.secretKey:XeQcsibSTiZPT}")
    private String secureCode;

    @Value("${cozyTime.connectTimeout:20000}")
    private Long connectTimeout;

    @Value("${cozyTime.readTimeout:20000}")
    private Long readTimeout;

    private final Gson gson = new Gson();

    @PostConstruct
    public void init() {
        log.info("初始化科坦SDK客户端, API地址: {}, 合作方编码: {}, 连接超时: {}ms, 读取超时: {}ms",
                this.apiUrl, this.partnerCode, this.connectTimeout, this.readTimeout);

        if (this.httpClient == null) {
            synchronized (CozyTimeSdkApi.class) {
                if (this.httpClient == null) {
                    this.httpClient = new OkHttpClient.Builder()
                            .connectTimeout(this.connectTimeout, TimeUnit.MILLISECONDS)
                            .readTimeout(this.readTimeout, TimeUnit.MILLISECONDS)
                            .build();
                    log.info("科坦SDK HTTP客户端初始化完成");
                }
            }
        }
    }

    /**
     * HTTP请求方法枚举
     */
    public enum HttpMethod {
        GET, POST
    }

    /**
     * 执行API请求
     *
     * @param request API请求接口
     * @param <T>     响应类型
     * @return API响应
     * @throws Exception 请求异常
     */
    public <T extends CozyTimeBaseResponse> T execute(CozyTimeRequestInterface<T> request) throws Exception {
        String methodName = request.getClass().getSimpleName();
        log.info("[科坦SDK-{}] 开始执行请求, 请求方法: {}, 接口路径: {}",
                methodName, request.getRequestMethod(), request.getEndpoint());

        if ("GET".equals(request.getRequestMethod())) {
            return this._get(request);
        } else if ("POST".equals(request.getRequestMethod())) {
            return this._post(request);
        } else {
            log.error("[科坦SDK-{}] 不支持的请求方法: {}", methodName, request.getRequestMethod());
            throw new RuntimeException("请求方法错误: " + request.getRequestMethod());
        }
    }

    /**
     * 获取城市列表
     *
     * @param countryCode 国家编码
     * @return 城市列表响应
     * @throws Exception 请求异常
     */
    public CozyTimeHotelContentApi20CitiesResponse getCityList(String countryCode) throws Exception {
        log.info("[科坦SDK-城市列表] 开始获取城市列表, 国家编码: {}", countryCode);

        CozyTimeHotelContentApi20CitiesRequest request = new CozyTimeHotelContentApi20CitiesRequest();
        request.setCountryCode(countryCode);

        CozyTimeHotelContentApi20CitiesResponse response = this.execute(request);

        if (response != null) {
            int cityCount = (response.getCities() != null) ? response.getCities().size() : 0;
            log.info("[科坦SDK-城市列表] 获取城市列表成功, 国家编码: {}, 城市数量: {}", countryCode, cityCount);
        } else {
            log.error("[科坦SDK-城市列表] 获取城市列表失败, 国家编码: {}", countryCode);
        }

        return response;
    }

    public CozyTimeHotelContentApi20FacilitiesResponse getFacilities(String regen) throws Exception {
        CozyTimeHotelContentApi20FacilitiesRequest request = new CozyTimeHotelContentApi20FacilitiesRequest();
        request.setRegion(regen);

        CozyTimeHotelContentApi20FacilitiesResponse response = this.execute(request);

        if (response != null) {
            int cityCount = (response.getFacilities() != null) ? response.getFacilities().size() : 0;
        } else {
            log.error("[科坦SDK-设施列表] 获取设施列表失败, : {}", regen);
        }

        return response;
    }

    public CozyTimeHotelContentApi20HotelIdsResponse getHotelIds(String cityCode, String hotelStatus) throws Exception {
        CozyTimeHotelContentApi20HotelIdsRequest request = new CozyTimeHotelContentApi20HotelIdsRequest();
        request.setCityCode(cityCode);
        request.setHotelStatus(hotelStatus);
        CozyTimeHotelContentApi20HotelIdsResponse response = this.execute(request);
        if (response != null) {
            int cityCount = (response.getHotelIds() != null) ? response.getHotelIds().size() : 0;
        } else {
            log.error("[科坦SDK-设施列表] 获取设施列表失败, : {}", cityCode);
        }
        return response;
    }

    public CozyTimeHotelContentApi20HotelInfoResponse getHotelInfos(List<Long> ids) throws Exception {
        CozyTimeHotelContentApi20HotelInfoRequest request = new CozyTimeHotelContentApi20HotelInfoRequest();
        request.setHotelIds(ids);
        request.setOptions("1");
        CozyTimeHotelContentApi20HotelInfoResponse response = this.execute(request);
        if (response != null) {
            int count = (response.getHotels() != null) ? response.getHotels().size() : 0;
        } else {
            log.error("[科坦SDK-酒店列表] 获取酒店列表失败, : {}", ids);
        }
        return response;
    }


    public CozyTimeHotelContentApi20HotelAndRoomMappingResponse getHotelAndRoomMapping(List<Long> regen) throws Exception {
        CozyTimeHotelContentApi20HotelAndRoomMappingRequest request = new CozyTimeHotelContentApi20HotelAndRoomMappingRequest();
        request.setHotelIds(regen);

        CozyTimeHotelContentApi20HotelAndRoomMappingResponse response = this.execute(request);

        if (response != null) {
            int hotelMappingCount = (response.getHotelMappingList() != null) ? response.getHotelMappingList().size() : 0;
        } else {
            log.error("[科坦SDK-设施列表] 获取酒店和房型匹配关系列表失败, : {}", regen);
        }

        return response;
    }

    /**
     * 执行POST请求
     *
     * @param request 请求接口
     * @param <T>     响应类型
     * @return API响应
     * @throws Exception 请求异常
     */
    private <T extends CozyTimeBaseResponse> T _post(CozyTimeRequestInterface<T> request) throws Exception {
        String methodName = request.getClass().getSimpleName();
        String url = this.apiUrl + request.getEndpoint();
        long timestamp = System.currentTimeMillis() / 1000;
        String signature = this.generateSignature(timestamp, this.partnerCode, this.secureCode);
        String requestJson = this.gson.toJson(request);

        RequestBody requestBody = RequestBody.create(requestJson, MediaType.parse("application/json"));

        Request httpRequest = new Request.Builder()
                .url(url)
                .post(requestBody)
                .header("timestamp", String.valueOf(timestamp))
                .header("partnerCode", String.valueOf(this.partnerCode))
                .header("x-Signature", signature)
                .header("Content-Type", "application/json")
                .build();

        log.info("[科坦SDK-{}] 发送POST请求, URL: {}, 请求体: {}", methodName, url, requestJson);
        log.info("[科坦SDK-{}] 请求头: timestamp={}, partnerCode={}, x-Signature={}",
                methodName, timestamp, this.partnerCode, signature);

        try (Response response = this.httpClient.newCall(httpRequest).execute()) {
            String responseBody = "";
            if (response.body() != null) {
                responseBody = response.body().string();
            }
            //  此方法是判断http的code ，科坦会把业务的码放到http的code返回
            // 429 too many requests（请求次数超过阀值）
//            if (!response.isSuccessful()) {
//                log.error("[科坦SDK-{}] 请求失败, 状态码: {}, 响应: {}", methodName, response.code(), responseBody);
//                throw new RuntimeException("API请求失败: " + response.code() + ", 响应: " + responseBody);
//            }

            log.info("[科坦SDK-{}] 请求成功, 状态码: {}, 响应: {}", methodName, response.code(), responseBody);
            T result = this.gson.fromJson(responseBody, request.getResponseClass());
            return result;
        } catch (IOException e) {
            log.error("[科坦SDK-{}] 请求异常: {}", methodName, e.getMessage(), e);
            throw new Exception("网络请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 执行GET请求
     *
     * @param request 请求接口
     * @param <T>     响应类型
     * @return API响应
     * @throws Exception 请求异常
     */
    private <T extends CozyTimeBaseResponse> T _get(CozyTimeRequestInterface<T> request) throws Exception {
        String methodName = request.getClass().getSimpleName();
        String endpoint = request.getEndpoint();
        String url = this.apiUrl + endpoint;
        String requestJson = this.gson.toJson(request);

        Map<String, String> params = this.gson.fromJson(requestJson, Map.class);
        StringBuilder urlParams = new StringBuilder();

        if (params != null && !params.isEmpty()) {
            HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
            int paramCount = 0;

            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (entry.getValue() != null) {
                    String encodedValue = URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.name());
                    urlBuilder.addQueryParameter(entry.getKey(), encodedValue);

                    if (paramCount > 0) {
                        urlParams.append("&");
                    }
                    urlParams.append(entry.getKey()).append("=").append(encodedValue);
                    paramCount++;
                }
            }

            url = urlBuilder.build().toString();
        }

        long timestamp = System.currentTimeMillis() / 1000;
        String signature = this.generateSignature(timestamp, this.partnerCode, this.secureCode);

        Request httpRequest = new Request.Builder()
                .url(url)
                .get()
                .header("timestamp", String.valueOf(timestamp))
                .header("partnerCode", String.valueOf(this.partnerCode))
                .header("x-Signature", signature)
                .header("Accept", "application/json")
                .build();

        log.info("[科坦SDK-{}] 发送GET请求, URL: {}", methodName, url);
        log.info("[科坦SDK-{}] 请求参数: {}", methodName, urlParams.toString());
        log.info("[科坦SDK-{}] 请求头: timestamp={}, partnerCode={}, x-Signature={}",
                methodName, timestamp, this.partnerCode, signature);

        try (Response response = this.httpClient.newCall(httpRequest).execute()) {
            String responseBody = "";
            if (response.body() != null) {
                responseBody = response.body().string();
            }

            //  此方法是判断http的code ，科坦会把业务的码放到http的code返回
            // 429 too many requests（请求次数超过阀值）
//            if (!response.isSuccessful()) {
//                log.error("[科坦SDK-{}] 请求失败, 状态码: {}, 响应: {}", methodName, response.code(), responseBody);
//                throw new RuntimeException("API请求失败: " + response.code() + ", 响应: " + responseBody);
//            }

            log.info("[科坦SDK-{}] 请求成功, 状态码: {}, 响应: {}", methodName, response.code(), responseBody);
            T result = this.gson.fromJson(responseBody, request.getResponseClass());
            return result;
        } catch (IOException e) {
            log.error("[科坦SDK-{}] 请求异常: {}", methodName, e.getMessage(), e);
            throw new Exception("网络请求异常: " + e.getMessage(), e);
        }
    }

    /**
     * 生成签名
     *
     * @param timestamp   时间戳
     * @param partnerCode 合作方编码
     * @param secureCode  安全码
     * @return 签名字符串
     */
    protected static String generateSignature(long timestamp, String partnerCode, String secureCode) {
        try {
            String raw = partnerCode + secureCode + timestamp;
            log.debug("签名原始字符串: {}", raw);

            MessageDigest digest = MessageDigest.getInstance("SHA-512");
            byte[] hash = digest.digest(raw.getBytes(StandardCharsets.UTF_8));

            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }

            String signature = hexString.toString();
            log.debug("生成的签名: {}", signature);
            return signature;
        } catch (Exception e) {
            log.error("签名生成失败: {}", e.getMessage(), e);
            throw new RuntimeException("签名生成失败: " + e.getMessage(), e);
        }
    }
}