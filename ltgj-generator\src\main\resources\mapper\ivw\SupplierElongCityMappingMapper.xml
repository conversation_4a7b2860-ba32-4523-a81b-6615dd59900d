<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.SupplierElongCityMappingMapper">
    
    <resultMap type="com.ltgj.ivw.domain.SupplierElongCityMapping" id="SupplierElongCityMappingResult">
        <id     property="id"               column="id"                />
        <result property="supplierCode"     column="supplier_code"     />
        <result property="supplierProvinceCode" column="supplier_province_code"/>
        <result property="provinceName"     column="province_name"     />
        <result property="supplierCityCode" column="supplier_city_code"/>
        <result property="supplierCityName" column="supplier_city_name"/>
        <result property="elongProvinceCode" column="elong_province_code"/>
        <result property="elongCityCode"    column="elong_city_code"    />
        <result property="mappingType"      column="mapping_type"       />
        <result property="mappingScore"     column="mapping_score"      />
        <result property="delFlag"          column="del_flag"           />
        <result property="status"           column="status"            />
        <result property="remark"           column="remark"            />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
    </resultMap>

    <sql id="selectSupplierElongCityMappingVo">
        select id, supplier_code, supplier_province_code, province_name, supplier_city_code, supplier_city_name, 
               elong_province_code, elong_city_code, mapping_type, mapping_score, del_flag, status, remark, create_by, create_time, update_by, update_time
        from supplier_elong_city_mapping
    </sql>

    <select id="selectSupplierElongCityMappingList" parameterType="com.ltgj.ivw.domain.SupplierElongCityMapping" resultMap="SupplierElongCityMappingResult">
        <include refid="selectSupplierElongCityMappingVo"/>
        <where>  
            <if test="supplierCode != null and supplierCode != ''">and supplier_code = #{supplierCode}</if>
            <if test="supplierProvinceCode != null and supplierProvinceCode != ''">and supplier_province_code = #{supplierProvinceCode}</if>
            <if test="provinceName != null and provinceName != ''">and province_name like concat('%', #{provinceName}, '%')</if>
            <if test="supplierCityCode != null and supplierCityCode != ''">and supplier_city_code = #{supplierCityCode}</if>
            <if test="supplierCityName != null and supplierCityName != ''">and supplier_city_name like concat('%', #{supplierCityName}, '%')</if>
            <if test="elongProvinceCode != null and elongProvinceCode != ''">and elong_province_code = #{elongProvinceCode}</if>
            <if test="elongCityCode != null and elongCityCode != ''">and elong_city_code = #{elongCityCode}</if>
            <if test="status != null and status != ''">and status = #{status}</if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="selectSupplierElongCityMappingById" parameterType="Long" resultMap="SupplierElongCityMappingResult">
        <include refid="selectSupplierElongCityMappingVo"/>
        where id = #{id} and del_flag = '0'
    </select>
    
    <select id="selectBySupplierAndCityCode" resultMap="SupplierElongCityMappingResult">
        <include refid="selectSupplierElongCityMappingVo"/>
        where supplier_code = #{supplierCode} and supplier_city_code = #{supplierCityCode} and del_flag = '0'
        limit 1
    </select>
    
    <select id="selectBySupplierAndElongCityCode" resultMap="SupplierElongCityMappingResult">
        <include refid="selectSupplierElongCityMappingVo"/>
        <where>
            1=1
            <if test="supplierCode != null and supplierCode != ''">
                and supplier_code = #{supplierCode}
            </if>
            <if test="elongCityCode != null and elongCityCode != ''">
                and elong_city_code =  #{elongCityCode}
            </if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="checkExistMapping" parameterType="com.ltgj.ivw.domain.SupplierElongCityMapping" resultMap="SupplierElongCityMappingResult">
        <include refid="selectSupplierElongCityMappingVo"/>
        <where>
            <if test="supplierCode != null and supplierCode != ''">
                and supplier_code = #{supplierCode}
            </if>
            <if test="supplierCityCode != null and supplierCityCode != ''">
                and supplier_city_code = #{supplierCityCode}
            </if>
            <if test="elongCityCode != null and elongCityCode != ''">
                and elong_city_code = #{elongCityCode}
            </if>
            and del_flag = '0'
        </where>
        limit 1
    </select>
        
    <insert id="insertSupplierElongCityMapping" parameterType="com.ltgj.ivw.domain.SupplierElongCityMapping" useGeneratedKeys="true" keyProperty="id">
        insert into supplier_elong_city_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null">supplier_code,</if>
            <if test="supplierProvinceCode != null">supplier_province_code,</if>
            <if test="provinceName != null">province_name,</if>
            <if test="supplierCityCode != null">supplier_city_code,</if>
            <if test="supplierCityName != null">supplier_city_name,</if>
            <if test="elongProvinceCode != null">elong_province_code,</if>
            <if test="elongCityCode != null">elong_city_code,</if>
            <if test="mappingType != null">mapping_type,</if>
            <if test="mappingScore != null">mapping_score,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="supplierCode != null">#{supplierCode},</if>
            <if test="supplierProvinceCode != null">#{supplierProvinceCode},</if>
            <if test="provinceName != null">#{provinceName},</if>
            <if test="supplierCityCode != null">#{supplierCityCode},</if>
            <if test="supplierCityName != null">#{supplierCityName},</if>
            <if test="elongProvinceCode != null">#{elongProvinceCode},</if>
            <if test="elongCityCode != null">#{elongCityCode},</if>
            <if test="mappingType != null">#{mappingType},</if>
            <if test="mappingScore != null">#{mappingScore},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSupplierElongCityMapping" parameterType="com.ltgj.ivw.domain.SupplierElongCityMapping">
        update supplier_elong_city_mapping
        <trim prefix="SET" suffixOverrides=",">
            <if test="supplierCode != null">supplier_code = #{supplierCode},</if>
            <if test="supplierProvinceCode != null">supplier_province_code = #{supplierProvinceCode},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="supplierCityCode != null">supplier_city_code = #{supplierCityCode},</if>
            <if test="supplierCityName != null">supplier_city_name = #{supplierCityName},</if>
            <if test="elongProvinceCode != null">elong_province_code = #{elongProvinceCode},</if>
            <if test="elongCityCode != null">elong_city_code = #{elongCityCode},</if>
            <if test="mappingType != null">mapping_type = #{mappingType},</if>
            <if test="mappingScore != null">mapping_score = #{mappingScore},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteSupplierElongCityMappingById" parameterType="Long">
        update supplier_elong_city_mapping set del_flag = '1' where id = #{id}
    </update>

    <update id="deleteSupplierElongCityMappingByIds" parameterType="Long">
        update supplier_elong_city_mapping set del_flag = '1' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    
    <insert id="batchInsertSupplierElongCityMapping" parameterType="java.util.List">
        insert into supplier_elong_city_mapping (
            supplier_code, supplier_province_code, province_name, supplier_city_code, supplier_city_name, 
            elong_province_code, elong_city_code, mapping_type, mapping_score, del_flag, status, remark, 
            create_by, create_time, update_by, update_time
        ) values 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.supplierCode}, #{item.supplierProvinceCode}, #{item.provinceName}, #{item.supplierCityCode}, #{item.supplierCityName}, 
                #{item.elongProvinceCode}, #{item.elongCityCode}, #{item.mappingType}, #{item.mappingScore}, #{item.delFlag}, #{item.status}, #{item.remark}, 
                #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}
            )
        </foreach>
    </insert>
    
    <update id="batchUpdateSupplierElongCityMapping" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update supplier_elong_city_mapping
            <set>
                <if test="item.supplierCode != null">supplier_code = #{item.supplierCode},</if>
                <if test="item.supplierProvinceCode != null">supplier_province_code = #{item.supplierProvinceCode},</if>
                <if test="item.provinceName != null">province_name = #{item.provinceName},</if>
                <if test="item.supplierCityCode != null">supplier_city_code = #{item.supplierCityCode},</if>
                <if test="item.supplierCityName != null">supplier_city_name = #{item.supplierCityName},</if>
                <if test="item.elongProvinceCode != null">elong_province_code = #{item.elongProvinceCode},</if>
                <if test="item.elongCityCode != null">elong_city_code = #{item.elongCityCode},</if>
                <if test="item.mappingType != null">mapping_type = #{item.mappingType},</if>
                <if test="item.mappingScore != null">mapping_score = #{item.mappingScore},</if>
                <if test="item.status != null">status = #{item.status},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.updateBy != null">update_by = #{item.updateBy},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>
</mapper>