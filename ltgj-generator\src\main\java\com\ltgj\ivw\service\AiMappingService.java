package com.ltgj.ivw.service;

import java.util.Date;
import java.util.List;

/**
 * @description: ai映射服务
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2024年11月11日 20:36
 */
public interface AiMappingService {

    void aiMeituanMapping();

    void aiKeTanMapping();

    void aiQiantaoMapping();

    void aiHsjlMapping();

    void aiHsjlxyMapping();

    void aiLastMapping();

    void uploadKnowledge();

    void uploadKnowledgeForScore();

    void uploadKnowledgeByDocument(Integer pageNumber);


    void uploadKnowledgeByCity(String knowledgeId);

    void uploadKnowledgeByCity(String knowledgeId, Date date, List<String> cityList);
    void uploadKnowledgeByCity2(String knowledgeId, Date date, List<String> cityList);

    void formatJdjdb();

    void uploadKnowledge(int pageNo);

    void aiKeTanMultipleMapping();

}
