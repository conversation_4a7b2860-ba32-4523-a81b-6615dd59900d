package com.ltgj.web.job.handler;

import com.ltgj.ivw.service.HotelGnOrderDetailStatisticsService;
import com.ltgj.ivw.service.HotelGnStatisticsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@JobHandler(value = "hotelStatisticsOrderDetailHandler")
public class HotelStatisticsOrderDetailHandler extends IJobHandler {

    @Resource
    private HotelGnOrderDetailStatisticsService orderDetailStatisticsService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            log.info("定时订单数据统计开始执行");
            orderDetailStatisticsService.insertHotelGnOrderDetailStatistics();
            log.info("定时订单数据统计执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时订单数据统计任务执行异常: {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行异常：" + e.getMessage());
        }
    }
}
