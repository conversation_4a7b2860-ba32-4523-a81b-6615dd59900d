package com.ltgj.ivw.service.hotel.updater;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 处理结果类
 *
 * <AUTHOR>
 */
public class ProcessResult {
    private final AtomicInteger processedCount;
    private final AtomicInteger addCount;
    private final AtomicInteger updateCount;
    private final AtomicInteger totalCount;
    
    public ProcessResult(AtomicInteger processedCount, AtomicInteger addCount, AtomicInteger updateCount, AtomicInteger totalCount) {
        this.processedCount = processedCount;
        this.addCount = addCount;
        this.updateCount = updateCount;
        this.totalCount = totalCount;
    }
    
    public AtomicInteger getProcessedCount() {
        return processedCount;
    }
    
    public AtomicInteger getAddCount() {
        return addCount;
    }

    public AtomicInteger getUpdateCount() {
        return updateCount;
    }
    
    public AtomicInteger getTotalCount() {
        return totalCount;
    }
} 