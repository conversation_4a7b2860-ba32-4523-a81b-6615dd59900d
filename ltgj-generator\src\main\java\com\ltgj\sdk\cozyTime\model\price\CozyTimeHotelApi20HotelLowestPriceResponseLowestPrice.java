package com.ltgj.sdk.cozyTime.model.price;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelApi20HotelLowestPriceResponseLowestPrice implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 酒店ID
     */
    private Long hotelId;

    /**
     * 酒店报价列表
     */
    private List<CozyTimeHotelApi20HotelLowestPriceResponsePriceItem> priceItems;
}
