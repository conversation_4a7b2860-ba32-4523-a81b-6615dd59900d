package com.ltgj.ivw.service;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.dto.CityLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;

/**
 * 独立的地理数据查询服务接口
 * 用于处理需要切换数据源的地理查询，避免事务传播导致的数据源切换失效
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface IGeoDataQueryService {
    
    /**
     * 通过条件获取所有区域结构（独立事务）
     * 该方法使用REQUIRES_NEW事务传播级别，确保可以正确切换到devyibase数据源
     * 
     * @param cityLevelQueryReq 查询条件
     * @return 地理层次结构
     */
    AjaxResult<GeoHierarchyNode> getAllRegionByConditionNewTx(CityLevelQueryReq cityLevelQueryReq);
} 