package com.ltgj.ivw.mapper;


import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.request.hotelGnPreMapping.ListRequest;
import com.ltgj.ivw.request.hotelGnPreMapping.ListResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HotelGnPreMappingMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(String id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(HotelGnPreMapping row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(HotelGnPreMapping row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    HotelGnPreMapping selectByPrimaryKey(String id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(HotelGnPreMapping row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(HotelGnPreMapping row);

    List<ListResponse> selectList(ListRequest row);

    int deleteByIds(@Param("ids") String[] ids);

    /**
     * 查询需要处理的预映射数据
     * @param ids
     * @param highScore
     * @param lowScore
     * @return
     */
    List<HotelGnPreMapping> selectNeedProcess(@Param("ids") List<String> ids,
                                              @Param("interfacePlats") List<String> interfacePlats,
                                              @Param("highScore") Integer highScore,
                                              @Param("lowScore") Integer lowScore,
                                              @Param("batchSize") Integer batchSize);

    List<HotelGnPreMapping> selectListByStatus(@Param("aiHotelList") List<HotelGnPreMapping> aiHotelList, @Param("plat") String plat);

}