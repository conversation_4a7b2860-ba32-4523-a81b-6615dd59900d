package com.ltgj.ivw.service;


import com.alibaba.fastjson.JSONObject;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.ivw.controller.HotelInfoMeituanController;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.impl.JdJdbServiceImpl;
import com.ltgj.ivw.utils.MyTools;
import com.ltgj.ivw.utils.hotelApi.ChailvgjApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class JdJdbServiceTest {

    @Autowired
    private JdJdbServiceImpl jdJdbServiceImpl;

    @Autowired
    private ChailvgjApi chailvgjApi;

    @Autowired
    private IHotelCityMappingService hotelCityMappingService;

    @Test
    public void listMinPriceTravelButlerTest() {
//        MinPriceReq minPriceReq = new MinPriceReq();
//        long start = System.currentTimeMillis();
//        jdJdbServiceImpl.listMinPriceTravelButler(minPriceReq);

        //设置数据处理范围（不算今天的后七天）
        MinPriceReq minPriceReq = new MinPriceReq();
        if (null == minPriceReq.getCheckInDate()) {
            String tomorrow = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String sevenDays = LocalDateTime.now().plusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            minPriceReq.setCheckInDate(tomorrow);
            minPriceReq.setCheckOutDate(sevenDays);
        }
        List<String> dateList = DateUtils.getDateList(minPriceReq.getCheckInDate(), 7);

        //根据平台编码获取差旅管家所有的城市映射数据
        HotelCityMapping hotelCityMapping = new HotelCityMapping();
        hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
        List<HotelCityMapping> hotelCityMappings = hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
        log.info("差旅管家拉取最低价-》城市总数量：{}", hotelCityMappings.size());


        long start = System.currentTimeMillis();
        jdJdbServiceImpl.fetchChaiLvData(dateList, hotelCityMappings.subList(40,60));
        log.info("listMinPriceTravelButler 总耗时：{}", (System.currentTimeMillis() - start)/1000);
    }

    @Test
    public void refreshTokenTest() {
        chailvgjApi.refreshTokenNew();
    }

    @Test
    public void hotelSearchTest() {
        chailvgjApi.hotelSearch(238089, "2024-12-12", null, 1, 10);
    }

    @Test
    public void dealListMinPriceCLGJNewTest() {
        jdJdbServiceImpl.dealListMinPriceCLGJNew("200000490200163", 1000021L);
    }


    @Test
    public void dealJdJdbCanalMsgTest() {
        String insertStr = "{\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": \"2025010371803729\",\n" +
                "            \"interfacePlat\": \"2000004\",\n" +
                "            \"jdmc\": \"测试环境酒店-1\",\n" +
                "            \"jdmc_en\": null,\n" +
                "            \"jdlx\": null,\n" +
                "            \"jddz\": \"天九共享测试\",\n" +
                "            \"jddz_en\": null,\n" +
                "            \"jddh\": \"13403332333\",\n" +
                "            \"img_url\": null,\n" +
                "            \"status\": \"0\",\n" +
                "            \"lon_google\": null,\n" +
                "            \"lat_google\": null,\n" +
                "            \"lon_baidu\": null,\n" +
                "            \"lat_baidu\": null,\n" +
                "            \"lon_gaode\": null,\n" +
                "            \"lat_gaode\": null,\n" +
                "            \"city_id\": \"0101\",\n" +
                "            \"city_name\": \"北京市\",\n" +
                "            \"brand_id\": null,\n" +
                "            \"brand_name\": null,\n" +
                "            \"district\": null,\n" +
                "            \"district_name\": null,\n" +
                "            \"business_zone\": null,\n" +
                "            \"business_zone_name\": null,\n" +
                "            \"jdxj\": null,\n" +
                "            \"score\": null,\n" +
                "            \"jtid\": null,\n" +
                "            \"jtmc\": null,\n" +
                "            \"kysj\": null,\n" +
                "            \"zhzxsj\": null,\n" +
                "            \"rank\": null,\n" +
                "            \"createdate\": null,\n" +
                "            \"mender\": null,\n" +
                "            \"savedate\": null,\n" +
                "            \"min_price\": null,\n" +
                "            \"reserve1\": null,\n" +
                "            \"reserve2\": null,\n" +
                "            \"notice_info\": null,\n" +
                "            \"policy_info\": null,\n" +
                "            \"facilities_info\": null,\n" +
                "            \"reserve6\": null,\n" +
                "            \"reserve7\": null,\n" +
                "            \"reserve8\": null,\n" +
                "            \"reserve9\": null,\n" +
                "            \"reserve0\": \"0\",\n" +
                "            \"reserve3\": null,\n" +
                "            \"reserve4\": null,\n" +
                "            \"reserve5\": null,\n" +
                "            \"origin_id\": null,\n" +
                "            \"recommend_level\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"database\": \"ivw_hotel\",\n" +
                "    \"es\": 1735871803000,\n" +
                "    \"gtid\": \"\",\n" +
                "    \"id\": 81643,\n" +
                "    \"isDdl\": false,\n" +
                "    \"mysqlType\": {\n" +
                "        \"id\": \"varchar(40)\",\n" +
                "        \"interfacePlat\": \"varchar(10)\",\n" +
                "        \"jdmc\": \"varchar(100)\",\n" +
                "        \"jdmc_en\": \"varchar(200)\",\n" +
                "        \"jdlx\": \"varchar(500)\",\n" +
                "        \"jddz\": \"varchar(500)\",\n" +
                "        \"jddz_en\": \"varchar(500)\",\n" +
                "        \"jddh\": \"varchar(60)\",\n" +
                "        \"img_url\": \"varchar(255)\",\n" +
                "        \"status\": \"tinyint(4)\",\n" +
                "        \"lon_google\": \"decimal(15,11)\",\n" +
                "        \"lat_google\": \"decimal(15,11)\",\n" +
                "        \"lon_baidu\": \"decimal(15,11)\",\n" +
                "        \"lat_baidu\": \"decimal(15,11)\",\n" +
                "        \"lon_gaode\": \"decimal(15,11)\",\n" +
                "        \"lat_gaode\": \"decimal(15,11)\",\n" +
                "        \"city_id\": \"varchar(32)\",\n" +
                "        \"city_name\": \"varchar(20)\",\n" +
                "        \"brand_id\": \"varchar(32)\",\n" +
                "        \"brand_name\": \"varchar(100)\",\n" +
                "        \"district\": \"varchar(32)\",\n" +
                "        \"district_name\": \"varchar(32)\",\n" +
                "        \"business_zone\": \"varchar(32)\",\n" +
                "        \"business_zone_name\": \"varchar(255)\",\n" +
                "        \"jdxj\": \"tinyint(4)\",\n" +
                "        \"score\": \"decimal(5,1)\",\n" +
                "        \"jtid\": \"varchar(100)\",\n" +
                "        \"jtmc\": \"varchar(100)\",\n" +
                "        \"kysj\": \"varchar(20)\",\n" +
                "        \"zhzxsj\": \"varchar(20)\",\n" +
                "        \"rank\": \"int(11)\",\n" +
                "        \"createdate\": \"datetime\",\n" +
                "        \"mender\": \"varchar(60)\",\n" +
                "        \"savedate\": \"datetime\",\n" +
                "        \"min_price\": \"decimal(7,2)\",\n" +
                "        \"reserve1\": \"longtext\",\n" +
                "        \"reserve2\": \"varchar(100)\",\n" +
                "        \"notice_info\": \"longtext\",\n" +
                "        \"policy_info\": \"longtext\",\n" +
                "        \"facilities_info\": \"longtext\",\n" +
                "        \"reserve6\": \"varchar(100)\",\n" +
                "        \"reserve7\": \"varchar(100)\",\n" +
                "        \"reserve8\": \"varchar(100)\",\n" +
                "        \"reserve9\": \"varchar(100)\",\n" +
                "        \"reserve0\": \"varchar(100)\",\n" +
                "        \"reserve3\": \"varchar(1000)\",\n" +
                "        \"reserve4\": \"varchar(100)\",\n" +
                "        \"reserve5\": \"varchar(100)\",\n" +
                "        \"origin_id\": \"varchar(80)\",\n" +
                "        \"recommend_level\": \"int(11)\"\n" +
                "    },\n" +
                "    \"old\": null,\n" +
                "    \"pkNames\": [\n" +
                "        \"id\"\n" +
                "    ],\n" +
                "    \"sql\": \"\",\n" +
                "    \"sqlType\": {\n" +
                "        \"id\": 12,\n" +
                "        \"interfacePlat\": 12,\n" +
                "        \"jdmc\": 12,\n" +
                "        \"jdmc_en\": 12,\n" +
                "        \"jdlx\": 12,\n" +
                "        \"jddz\": 12,\n" +
                "        \"jddz_en\": 12,\n" +
                "        \"jddh\": 12,\n" +
                "        \"img_url\": 12,\n" +
                "        \"status\": -6,\n" +
                "        \"lon_google\": 3,\n" +
                "        \"lat_google\": 3,\n" +
                "        \"lon_baidu\": 3,\n" +
                "        \"lat_baidu\": 3,\n" +
                "        \"lon_gaode\": 3,\n" +
                "        \"lat_gaode\": 3,\n" +
                "        \"city_id\": 12,\n" +
                "        \"city_name\": 12,\n" +
                "        \"brand_id\": 12,\n" +
                "        \"brand_name\": 12,\n" +
                "        \"district\": 12,\n" +
                "        \"district_name\": 12,\n" +
                "        \"business_zone\": 12,\n" +
                "        \"business_zone_name\": 12,\n" +
                "        \"jdxj\": -6,\n" +
                "        \"score\": 3,\n" +
                "        \"jtid\": 12,\n" +
                "        \"jtmc\": 12,\n" +
                "        \"kysj\": 12,\n" +
                "        \"zhzxsj\": 12,\n" +
                "        \"rank\": 4,\n" +
                "        \"createdate\": 93,\n" +
                "        \"mender\": 12,\n" +
                "        \"savedate\": 93,\n" +
                "        \"min_price\": 3,\n" +
                "        \"reserve1\": 2005,\n" +
                "        \"reserve2\": 12,\n" +
                "        \"notice_info\": 2005,\n" +
                "        \"policy_info\": 2005,\n" +
                "        \"facilities_info\": 2005,\n" +
                "        \"reserve6\": 12,\n" +
                "        \"reserve7\": 12,\n" +
                "        \"reserve8\": 12,\n" +
                "        \"reserve9\": 12,\n" +
                "        \"reserve0\": 12,\n" +
                "        \"reserve3\": 12,\n" +
                "        \"reserve4\": 12,\n" +
                "        \"reserve5\": 12,\n" +
                "        \"origin_id\": 12,\n" +
                "        \"recommend_level\": 4\n" +
                "    },\n" +
                "    \"table\": \"jd_jdb\",\n" +
                "    \"ts\": 1735871803785,\n" +
                "    \"type\": \"INSERT\"\n" +
                "}";
//        jdJdbServiceImpl.dealJdJdbCanalMsg(insertStr);
    }

    @Test
    public void insertJdJdbTest() {
        String insert  = "{\n" +
                "    \"policyInfo\": \"{\\\"noticePolicy\\\":{\\\"notice_info\\\":null},\\\"checkInOutPolicy\\\":{\\\"checkIn\\\":null,\\\"checkOut\\\":null},\\\"childPolicy\\\":{\\\"content\\\":null},\\\"PetPolicy\\\":{\\\"isAllows\\\":null},\\\"customerPolicy\\\":{\\\"customerType\\\":null,\\\"customerDesc\\\":null}}\",\n" +
                "    \"facilitiesInfo\": \"{\\\"parking\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null},\\\"charging\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null},\\\"lift\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null},\\\"baggage\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null},\\\"restaurant\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null},\\\"meetingRoom\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null},\\\"publicWifi\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null},\\\"gym\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null},\\\"laundry\\\":{\\\"isHave\\\":null,\\\"isCharge\\\":null}}\",\n" +
                "    \"jddz\": \"临沧万合汽车销售服务有限公司\",\n" +
                "    \"businessZone\": \"767729\",\n" +
                "    \"jtid\": null,\n" +
                "    \"brandId\": null,\n" +
                "    \"jdxj\": 5,\n" +
                "    \"score\": 5,\n" +
                "    \"rank\": 0,\n" +
                "    \"interfacePlat\": \"2000077\",\n" +
                "    \"jdmc\": \"测试1234567\",\n" +
                "    \"jddh\": \"123456\",\n" +
                "    \"status\": 0,\n" +
                "    \"kysj\": \"2020\",\n" +
                "    \"jdlx\": \"2\",\n" +
                "    \"countryId\": \"00002\",\n" +
                "    \"provinceName\": \"云南\",\n" +
                "    \"provinceId\": \"2022\",\n" +
                "    \"cityId\": \"10203\",\n" +
                "    \"district\": \"25110002\",\n" +
                "    \"lonBaidu\": 100.113439,\n" +
                "    \"latBaidu\": 23.927931,\n" +
                "    \"imgUrl\": \"https://dev-img-trip.lvtuguanjia.com/temp/jd/image/2025/5/23/2_1750649471083.jpg\",\n" +
                "    \"reserve1\": \"1\",\n" +
                "    \"countryName\": \"中国\",\n" +
                "    \"cityName\": \"临沧\",\n" +
                "    \"districtName\": \"临翔区\",\n" +
                "    \"businessZoneName\": \"临沧市中医医院\"\n" +
                "}";

        JdJdb jdJdb = JSONObject.parseObject(insert, JdJdb.class);
        jdJdbServiceImpl.insertJdJdb(jdJdb);
    }


}
