# 企业微信自建应用开发技术文档

## 目录
1. [认证凭证获取](#1-认证凭证获取)
2. [Access Token获取机制](#2-access-token获取机制)
3. [发送消息API接口](#3-发送消息api接口)
4. [消息参数详解](#4-消息参数详解)
5. [权限配置要求](#5-权限配置要求)
6. [官方开发者文档](#6-官方开发者文档)
7. [开发最佳实践](#7-开发最佳实践)

---

## 1. 认证凭证获取

### 1.1 企业ID（corpid）
- **位置**: 企业微信管理后台 → "我的企业" → "企业信息"
- **格式**: 以`ww`开头的18位字符串
- **示例**: `ww17f8d10783494584`
- **用途**: 标识企业身份，用于API调用

### 1.2 应用密钥（corpsecret）
- **位置**: 企业微信管理后台 → "应用管理" → 选择自建应用 → "查看Secret"
- **格式**: 64位字符串
- **安全性**: 
  - 仅在创建应用时显示一次
  - 需妥善保存，丢失需重新生成
  - 不可在客户端代码中暴露

### 1.3 应用ID（agentid）
- **位置**: 自建应用详情页面
- **用途**: 标识具体应用，发送消息时需要

---

## 2. Access Token获取机制

### 2.1 获取接口
```http
GET https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
```

### 2.2 请求参数
| 参数 | 类型 | 必须 | 说明 |
|------|------|------|------|
| corpid | string | 是 | 企业ID |
| corpsecret | string | 是 | 应用密钥 |

### 2.3 返回结果
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "access_token": "accesstoken000001",
    "expires_in": 7200
}
```

### 2.4 有效期和刷新机制
- **有效期**: 7200秒（2小时）
- **刷新策略**: 
  - 建议在到期前30分钟刷新
  - 使用缓存机制避免频繁请求
  - 同一个应用的access_token全局唯一

### 2.5 示例代码（Python）
```python
import requests
import json
import time

class WeChatWorkAPI:
    def __init__(self, corpid, corpsecret):
        self.corpid = corpid
        self.corpsecret = corpsecret
        self.access_token = None
        self.token_expires_at = 0
    
    def get_access_token(self):
        if self.access_token and time.time() < self.token_expires_at:
            return self.access_token
        
        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken"
        params = {
            'corpid': self.corpid,
            'corpsecret': self.corpsecret
        }
        
        response = requests.get(url, params=params)
        result = response.json()
        
        if result['errcode'] == 0:
            self.access_token = result['access_token']
            self.token_expires_at = time.time() + result['expires_in'] - 300  # 提前5分钟过期
            return self.access_token
        else:
            raise Exception(f"获取access_token失败: {result['errmsg']}")
```

---

## 3. 发送消息API接口

### 3.1 接口地址
```http
POST https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}
```

### 3.2 基本文本消息示例
```json
{
    "touser": "UserID1|UserID2",
    "toparty": "",
    "totag": "",
    "msgtype": "text",
    "agentid": 1000002,
    "text": {
        "content": "你的快递已到，请携带工卡前往邮件中心领取。\n出发前可查看<a href=\"http://work.weixin.qq.com\">邮件中心视频实况</a>，聪明避开排队。"
    },
    "safe": 0,
    "enable_id_trans": 0,
    "enable_duplicate_check": 0,
    "duplicate_check_interval": 1800
}
```

### 3.3 图文消息示例
```json
{
    "touser": "UserID1|UserID2",
    "msgtype": "news",
    "agentid": 1000002,
    "news": {
        "articles": [
            {
                "title": "中秋节礼品领取",
                "description": "今年中秋节公司为大家准备了美味的月饼...",
                "url": "URL",
                "picurl": "http://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg.png"
            }
        ]
    }
}
```

### 3.4 返回结果
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "invaliduser": "",
    "invalidparty": "",
    "invalidtag": "",
    "unlicenseduser": "",
    "msgid": "xxxx",
    "response_code": "xxxxxx"
}
```

---

## 4. 消息参数详解

### 4.1 接收者参数
| 参数 | 类型 | 说明 |
|------|------|------|
| touser | string | 成员ID列表，多个用竖线分隔，最多1000个 |
| toparty | string | 部门ID列表，多个用竖线分隔，最多100个 |
| totag | string | 标签ID列表，多个用竖线分隔，最多100个 |

**注意**: 三个参数至少指定一个，全部为空时表示发送给应用可见范围内的所有用户

### 4.2 消息类型（msgtype）
- `text`: 文本消息
- `image`: 图片消息
- `voice`: 语音消息
- `video`: 视频消息
- `file`: 文件消息
- `textcard`: 文本卡片消息
- `news`: 图文消息
- `mpnews`: 图文消息（mpnews类型）
- `markdown`: Markdown消息

### 4.3 用户ID获取方式
1. **通过通讯录同步接口获取**:
   ```http
   GET https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token={access_token}&department_id=1
   ```

2. **通过网页授权获取**:
   ```http
   GET https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token={access_token}&code={code}
   ```

### 4.4 消息安全参数
- `safe`: 表示是否是保密消息，0表示可对外分享，1表示不能分享且内容显示水印
- `enable_id_trans`: 表示是否开启id转译，0表示否，1表示是
- `enable_duplicate_check`: 表示是否开启重复消息检查，0表示否，1表示是

---

## 5. 权限配置要求

### 5.1 应用可见范围
在企业微信管理后台设置应用可见范围：
- **位置**: "应用管理" → 选择应用 → "可见范围"
- **配置**: 添加可以接收消息的成员、部门或标签

### 5.2 应用权限
自建应用默认具有以下权限：
- 发送消息给可见范围内的成员
- 接收成员发送的消息
- 获取成员的基本信息

### 5.3 API权限说明
- 只能给应用可见范围内的成员发送消息
- 发送消息的频率限制：每个应用每分钟最多发送200次
- 每次发送消息最多支持1000个成员

### 5.4 管理后台配置步骤
1. 登录企业微信管理后台
2. 进入"应用管理" → "自建"
3. 创建应用或选择已有应用
4. 配置应用信息：
   - 应用名称
   - 应用Logo
   - 应用描述
5. 设置可见范围
6. 获取AgentId和Secret

---

## 6. 官方开发者文档

### 6.1 主要文档链接
- **企业微信开发者中心**: https://developer.work.weixin.qq.com/
- **API文档首页**: https://developer.work.weixin.qq.com/document/
- **消息推送接口**: https://developer.work.weixin.qq.com/document/path/90236
- **身份验证**: https://developer.work.weixin.qq.com/document/path/91022

### 6.2 核心接口文档
- **获取access_token**: https://developer.work.weixin.qq.com/document/path/91039
- **发送应用消息**: https://developer.work.weixin.qq.com/document/path/90236
- **获取成员信息**: https://developer.work.weixin.qq.com/document/path/90196
- **部门管理**: https://developer.work.weixin.qq.com/document/path/90208

### 6.3 SDK和工具
- **官方SDK**: 提供多种编程语言的SDK
- **调试工具**: 在线API调试工具
- **错误码查询**: 详细的错误码说明

---

## 7. 开发最佳实践

### 7.1 安全性考虑
```python
# 安全存储配置
import os
from cryptography.fernet import Fernet

class SecureConfig:
    def __init__(self):
        self.cipher = Fernet(os.environ.get('ENCRYPT_KEY'))
    
    def get_corp_secret(self):
        encrypted_secret = os.environ.get('CORP_SECRET_ENCRYPTED')
        return self.cipher.decrypt(encrypted_secret.encode()).decode()
```

### 7.2 错误处理和重试机制
```python
import time
import random

def send_message_with_retry(api, message_data, max_retries=3):
    for attempt in range(max_retries):
        try:
            result = api.send_message(message_data)
            if result['errcode'] == 0:
                return result
            elif result['errcode'] == 42001:  # access_token过期
                api.refresh_access_token()
                continue
            else:
                raise Exception(f"发送失败: {result['errmsg']}")
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            time.sleep(random.uniform(1, 3))  # 随机退避
```

### 7.3 消息模板化管理
```python
class MessageTemplates:
    @staticmethod
    def system_notification(content):
        return {
            "msgtype": "textcard",
            "textcard": {
                "title": "系统通知",
                "description": content,
                "url": "https://your-system.com/notifications",
                "btntxt": "查看详情"
            }
        }
    
    @staticmethod
    def urgent_alert(title, content):
        return {
            "msgtype": "text",
            "text": {
                "content": f"🚨 紧急通知\n\n标题：{title}\n内容：{content}\n\n请立即处理！"
            }
        }
```

### 7.4 批量消息发送优化
```python
def send_batch_messages(api, user_list, message_template, batch_size=50):
    """批量发送消息，避免单次发送用户过多"""
    results = []
    
    for i in range(0, len(user_list), batch_size):
        batch_users = user_list[i:i + batch_size]
        message_data = message_template.copy()
        message_data['touser'] = '|'.join(batch_users)
        
        try:
            result = api.send_message(message_data)
            results.append(result)
            time.sleep(1)  # 避免频率限制
        except Exception as e:
            print(f"批次 {i//batch_size + 1} 发送失败: {e}")
    
    return results
```

### 7.5 日志记录和监控
```python
import logging
from datetime import datetime

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('wechat_work_api.log'),
            logging.StreamHandler()
        ]
    )

def log_message_send(user_id, message_type, result):
    logging.info(f"消息发送 - 用户: {user_id}, 类型: {message_type}, "
                f"结果: {result['errcode']}, 消息ID: {result.get('msgid', 'N/A')}")
```

---

## 附录

### A. 常见错误码
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40014 | 不合法的access_token | 重新获取access_token |
| 42001 | access_token超时 | 刷新access_token |
| 40003 | 不合法的UserID | 检查用户ID是否正确 |
| 60020 | not allow to access from your ip | 检查IP白名单设置 |

### B. 测试用企业账号
企业微信提供测试企业账号供开发调试：
- **corpid**: `ww17f8d10783494584`
- **secret**: `i5t-rh8bXeNCgihcYPrG9ZPpWkivzPJ69sv570osk6I`

### C. 相关文档更新时间
- 文档创建时间: 2024年1月
- 最后更新时间: 根据官方API文档实时更新
- 企业微信API版本: v1.0

---

*注意：本文档基于企业微信官方开发者文档整理，具体API细节请以官方最新文档为准。* 