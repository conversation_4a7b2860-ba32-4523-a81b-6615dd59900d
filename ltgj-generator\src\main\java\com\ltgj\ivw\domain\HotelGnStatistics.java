package com.ltgj.ivw.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * @ClassName: HotelGnStatistics
 * @Description: 酒店情况总计
 * @Author: nxy
 * @CreateDate: 2025/6/6
 * @UpdateUser: nxy
 * @UpdateDate: 2025/6/6
 */
@Data
public class HotelGnStatistics implements Serializable {
    /**
     * Database Column Remarks:
     *   主键
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.id
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   平台名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.interface_name
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private String interfaceName;

    /**
     * Database Column Remarks:
     *   平台标识
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.interface_plat
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private Long interfacePlat;

    /**
     * Database Column Remarks:
     *   类型1-酒店，2-订单
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.type
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private Integer type;

    /**
     * Database Column Remarks:
     *   总数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.total_num
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private Integer totalNum;

    /**
     * Database Column Remarks:
     *   成功数（映射成功数）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.success_num
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private Integer successNum;

    /**
     * Database Column Remarks:
     *   失败数（未映射数）
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.fail_num
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private Integer failNum;

    /**
     * Database Column Remarks:
     *   年
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.create_year
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private String createYear;

    /**
     * Database Column Remarks:
     *   月
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.create_month
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private String createMonth;

    /**
     * Database Column Remarks:
     *   日
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.create_day
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private String createDay;

    /**
     * Database Column Remarks:
     *   统计日期
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.statistics_time
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private LocalDate statisticsTime;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_statistics.create_time
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_gn_statistics
     *
     * @mbg.generated 2025/06/06 15:04
     */
    private static final long serialVersionUID = 1L;
}
