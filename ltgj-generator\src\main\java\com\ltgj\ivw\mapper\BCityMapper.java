package com.ltgj.ivw.mapper;

import java.util.List;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.BCity;

import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 城市管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@DataSource(DataSourceType.DEVYIXBASE)
@Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
public interface BCityMapper
{
    /**
     * 查询城市管理
     *
     * @param id 城市管理主键
     * @return 城市管理
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    public BCity selectBCityById(String id);

    /**
     * 查询城市管理列表
     *
     * @param bCity 城市管理
     * @return 城市管理集合
     */
    public List<BCity> selectBCityList(BCity bCity);

    /**
     * 查询城市管理列表
     *
     * @param bCity 城市管理
     * @return 城市管理集合
     */
     public List<BCity> selectBCityJoinGeoProvinceList(BCity bCity);

    /**
     * 新增城市管理
     *
     * @param bCity 城市管理
     * @return 结果
     */
    public int insertBCity(BCity bCity);

    /**
     * 修改城市管理
     *
     * @param bCity 城市管理
     * @return 结果
     */
    public int updateBCity(BCity bCity);

    /**
     * 删除城市管理
     *
     * @param id 城市管理主键
     * @return 结果
     */
    public int deleteBCityById(String id);

    /**
     * 批量删除城市管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBCityByIds(String[] ids);

    long getMaxId();

    BCity selectBCityByName(@Param("cityName") String cityName);

    List<BCity> selectCityListByName(@Param("cityName") String name);

    /**
     * 根据省份ID查询城市列表
     *
     * @param provinceId 省份ID
     * @param validOnly 是否只查询有效数据
     * @return 城市信息集合
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    List<BCity> selectCitiesByProvince(@Param("provinceId") String provinceId, @Param("validOnly") Boolean validOnly);
}
