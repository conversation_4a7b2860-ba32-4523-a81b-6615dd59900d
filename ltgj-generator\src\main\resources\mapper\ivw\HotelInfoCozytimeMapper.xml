<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.HotelInfoCozytimeMapper">
    <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.HotelInfoCozytime">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="name_en" jdbcType="VARCHAR" property="nameEn"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="dept_id" jdbcType="BIGINT" property="deptId"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="province_id" jdbcType="VARCHAR" property="provinceId"/>
        <result column="city_id" jdbcType="VARCHAR" property="cityId"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="area_id" jdbcType="VARCHAR" property="areaId"/>
        <result column="business_districts" jdbcType="VARCHAR" property="businessDistricts"/>
        <result column="address_line" jdbcType="VARCHAR" property="addressLine"/>
        <result column="open_date" jdbcType="VARCHAR" property="openDate"/>
        <result column="decoration_date" jdbcType="VARCHAR" property="decorationDate"/>
        <result column="FLOOR_COUNT" jdbcType="VARCHAR" property="floorCount"/>
        <result column="brand_id" jdbcType="VARCHAR" property="brandId"/>
        <result column="group_id" jdbcType="VARCHAR" property="groupId"/>
        <result column="theme_id" jdbcType="VARCHAR" property="themeId"/>
        <result column="type_id" jdbcType="VARCHAR" property="typeId"/>
        <result column="star" jdbcType="INTEGER" property="star"/>
        <result column="star_info" jdbcType="VARCHAR" property="starInfo"/>
        <result column="image" jdbcType="VARCHAR" property="image"/>
        <result column="plat_num" jdbcType="VARCHAR" property="platNum"/>
        <result column="plat_hotel_id" jdbcType="VARCHAR" property="platHotelId"/>
        <result column="recommendation_level" jdbcType="INTEGER" property="recommendationLevel"/>
        <result column="lon_bd" jdbcType="DECIMAL" property="lonBd"/>
        <result column="lat_bd" jdbcType="DECIMAL" property="latBd"/>
        <result column="lon_gd" jdbcType="DECIMAL" property="lonGd"/>
        <result column="lat_gd" jdbcType="DECIMAL" property="latGd"/>
        <result column="lon_gg" jdbcType="DECIMAL" property="lonGg"/>
        <result column="lat_gg" jdbcType="DECIMAL" property="latGg"/>
        <result column="score" jdbcType="VARCHAR" property="score"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="reserve1" jdbcType="VARCHAR" property="reserve1"/>
        <result column="reserve2" jdbcType="VARCHAR" property="reserve2"/>
        <result column="reserve3" jdbcType="VARCHAR" property="reserve3"/>
        <result column="reserve4" jdbcType="VARCHAR" property="reserve4"/>
        <result column="reserve5" jdbcType="VARCHAR" property="reserve5"/>
        <result column="reserve6" jdbcType="VARCHAR" property="reserve6"/>
        <result column="reserve7" jdbcType="VARCHAR" property="reserve7"/>
        <result column="reserve8" jdbcType="VARCHAR" property="reserve8"/>
        <result column="reserve9" jdbcType="VARCHAR" property="reserve9"/>
        <result column="reserve0" jdbcType="VARCHAR" property="reserve0"/>
        <result column="increment_status" jdbcType="INTEGER" property="incrementStatus"/>
        <result column="increment_time" jdbcType="DATE" property="incrementTime"/>
        <result column="increment_type" jdbcType="INTEGER" property="incrementType"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ltgj.ivw.domain.HotelInfoCozytime">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
        <result column="notice_info" jdbcType="LONGVARCHAR" property="noticeInfo"/>
        <result column="policy_info" jdbcType="LONGVARCHAR" property="policyInfo"/>
        <result column="facilities_info" jdbcType="LONGVARCHAR" property="facilitiesInfo"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, `name`, name_en, phone, dept_id, country, province_id, city_id, city_name, area_id,
        business_districts, address_line, open_date, decoration_date, FLOOR_COUNT, brand_id,
        group_id, theme_id, type_id, star, star_info, image, plat_num, plat_hotel_id, recommendation_level,
        lon_bd, lat_bd, lon_gd, lat_gd, lon_gg, lat_gg, score, `status`, remark, create_time,
        update_time, create_by, update_by, is_delete, reserve1, reserve2, reserve3, reserve4,
        reserve5, reserve6, reserve7, reserve8, reserve9, reserve0, increment_status, increment_time,
        increment_type
    </sql>
    <sql id="Blob_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        description, notice_info, policy_info, facilities_info
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from hotel_info_cozytime
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        delete from hotel_info_cozytime
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.ltgj.ivw.domain.HotelInfoCozytime">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into hotel_info_cozytime (`name`, name_en, phone,
        dept_id, country, province_id,
        city_id, city_name, area_id,
        business_districts, address_line, open_date,
        decoration_date, FLOOR_COUNT, brand_id,
        group_id, theme_id, type_id,
        star, star_info, image,
        plat_num, plat_hotel_id, recommendation_level,
        lon_bd, lat_bd, lon_gd,
        lat_gd, lon_gg, lat_gg,
        score, `status`, remark,
        create_time, update_time, create_by,
        update_by, is_delete, reserve1,
        reserve2, reserve3, reserve4,
        reserve5, reserve6, reserve7,
        reserve8, reserve9, reserve0,
        increment_status, increment_time, increment_type,
        description, notice_info, policy_info,
        facilities_info)
        values (#{name,jdbcType=VARCHAR}, #{nameEn,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
        #{deptId,jdbcType=BIGINT}, #{country,jdbcType=VARCHAR}, #{provinceId,jdbcType=VARCHAR},
        #{cityId,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{areaId,jdbcType=VARCHAR},
        #{businessDistricts,jdbcType=VARCHAR}, #{addressLine,jdbcType=VARCHAR}, #{openDate,jdbcType=VARCHAR},
        #{decorationDate,jdbcType=VARCHAR}, #{floorCount,jdbcType=VARCHAR}, #{brandId,jdbcType=VARCHAR},
        #{groupId,jdbcType=VARCHAR}, #{themeId,jdbcType=VARCHAR}, #{typeId,jdbcType=VARCHAR},
        #{star,jdbcType=INTEGER}, #{starInfo,jdbcType=VARCHAR}, #{image,jdbcType=VARCHAR},
        #{platNum,jdbcType=VARCHAR}, #{platHotelId,jdbcType=VARCHAR}, #{recommendationLevel,jdbcType=INTEGER},
        #{lonBd,jdbcType=DECIMAL}, #{latBd,jdbcType=DECIMAL}, #{lonGd,jdbcType=DECIMAL},
        #{latGd,jdbcType=DECIMAL}, #{lonGg,jdbcType=DECIMAL}, #{latGg,jdbcType=DECIMAL},
        #{score,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
        #{updateBy,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, #{reserve1,jdbcType=VARCHAR},
        #{reserve2,jdbcType=VARCHAR}, #{reserve3,jdbcType=VARCHAR}, #{reserve4,jdbcType=VARCHAR},
        #{reserve5,jdbcType=VARCHAR}, #{reserve6,jdbcType=VARCHAR}, #{reserve7,jdbcType=VARCHAR},
        #{reserve8,jdbcType=VARCHAR}, #{reserve9,jdbcType=VARCHAR}, #{reserve0,jdbcType=VARCHAR},
        #{incrementStatus,jdbcType=INTEGER}, #{incrementTime,jdbcType=DATE}, #{incrementType,jdbcType=INTEGER},
        #{description,jdbcType=LONGVARCHAR}, #{noticeInfo,jdbcType=LONGVARCHAR}, #{policyInfo,jdbcType=LONGVARCHAR},
        #{facilitiesInfo,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.ltgj.ivw.domain.HotelInfoCozytime">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into hotel_info_cozytime
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="nameEn != null">
                name_en,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="country != null">
                country,
            </if>
            <if test="provinceId != null">
                province_id,
            </if>
            <if test="cityId != null">
                city_id,
            </if>
            <if test="cityName != null">
                city_name,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="businessDistricts != null">
                business_districts,
            </if>
            <if test="addressLine != null">
                address_line,
            </if>
            <if test="openDate != null">
                open_date,
            </if>
            <if test="decorationDate != null">
                decoration_date,
            </if>
            <if test="floorCount != null">
                FLOOR_COUNT,
            </if>
            <if test="brandId != null">
                brand_id,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="themeId != null">
                theme_id,
            </if>
            <if test="typeId != null">
                type_id,
            </if>
            <if test="star != null">
                star,
            </if>
            <if test="starInfo != null">
                star_info,
            </if>
            <if test="image != null">
                image,
            </if>
            <if test="platNum != null">
                plat_num,
            </if>
            <if test="platHotelId != null">
                plat_hotel_id,
            </if>
            <if test="recommendationLevel != null">
                recommendation_level,
            </if>
            <if test="lonBd != null">
                lon_bd,
            </if>
            <if test="latBd != null">
                lat_bd,
            </if>
            <if test="lonGd != null">
                lon_gd,
            </if>
            <if test="latGd != null">
                lat_gd,
            </if>
            <if test="lonGg != null">
                lon_gg,
            </if>
            <if test="latGg != null">
                lat_gg,
            </if>
            <if test="score != null">
                score,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="reserve1 != null">
                reserve1,
            </if>
            <if test="reserve2 != null">
                reserve2,
            </if>
            <if test="reserve3 != null">
                reserve3,
            </if>
            <if test="reserve4 != null">
                reserve4,
            </if>
            <if test="reserve5 != null">
                reserve5,
            </if>
            <if test="reserve6 != null">
                reserve6,
            </if>
            <if test="reserve7 != null">
                reserve7,
            </if>
            <if test="reserve8 != null">
                reserve8,
            </if>
            <if test="reserve9 != null">
                reserve9,
            </if>
            <if test="reserve0 != null">
                reserve0,
            </if>
            <if test="incrementStatus != null">
                increment_status,
            </if>
            <if test="incrementTime != null">
                increment_time,
            </if>
            <if test="incrementType != null">
                increment_type,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="noticeInfo != null">
                notice_info,
            </if>
            <if test="policyInfo != null">
                policy_info,
            </if>
            <if test="facilitiesInfo != null">
                facilities_info,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameEn != null">
                #{nameEn,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=BIGINT},
            </if>
            <if test="country != null">
                #{country,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                #{areaId,jdbcType=VARCHAR},
            </if>
            <if test="businessDistricts != null">
                #{businessDistricts,jdbcType=VARCHAR},
            </if>
            <if test="addressLine != null">
                #{addressLine,jdbcType=VARCHAR},
            </if>
            <if test="openDate != null">
                #{openDate,jdbcType=VARCHAR},
            </if>
            <if test="decorationDate != null">
                #{decorationDate,jdbcType=VARCHAR},
            </if>
            <if test="floorCount != null">
                #{floorCount,jdbcType=VARCHAR},
            </if>
            <if test="brandId != null">
                #{brandId,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="themeId != null">
                #{themeId,jdbcType=VARCHAR},
            </if>
            <if test="typeId != null">
                #{typeId,jdbcType=VARCHAR},
            </if>
            <if test="star != null">
                #{star,jdbcType=INTEGER},
            </if>
            <if test="starInfo != null">
                #{starInfo,jdbcType=VARCHAR},
            </if>
            <if test="image != null">
                #{image,jdbcType=VARCHAR},
            </if>
            <if test="platNum != null">
                #{platNum,jdbcType=VARCHAR},
            </if>
            <if test="platHotelId != null">
                #{platHotelId,jdbcType=VARCHAR},
            </if>
            <if test="recommendationLevel != null">
                #{recommendationLevel,jdbcType=INTEGER},
            </if>
            <if test="lonBd != null">
                #{lonBd,jdbcType=DECIMAL},
            </if>
            <if test="latBd != null">
                #{latBd,jdbcType=DECIMAL},
            </if>
            <if test="lonGd != null">
                #{lonGd,jdbcType=DECIMAL},
            </if>
            <if test="latGd != null">
                #{latGd,jdbcType=DECIMAL},
            </if>
            <if test="lonGg != null">
                #{lonGg,jdbcType=DECIMAL},
            </if>
            <if test="latGg != null">
                #{latGg,jdbcType=DECIMAL},
            </if>
            <if test="score != null">
                #{score,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="reserve1 != null">
                #{reserve1,jdbcType=VARCHAR},
            </if>
            <if test="reserve2 != null">
                #{reserve2,jdbcType=VARCHAR},
            </if>
            <if test="reserve3 != null">
                #{reserve3,jdbcType=VARCHAR},
            </if>
            <if test="reserve4 != null">
                #{reserve4,jdbcType=VARCHAR},
            </if>
            <if test="reserve5 != null">
                #{reserve5,jdbcType=VARCHAR},
            </if>
            <if test="reserve6 != null">
                #{reserve6,jdbcType=VARCHAR},
            </if>
            <if test="reserve7 != null">
                #{reserve7,jdbcType=VARCHAR},
            </if>
            <if test="reserve8 != null">
                #{reserve8,jdbcType=VARCHAR},
            </if>
            <if test="reserve9 != null">
                #{reserve9,jdbcType=VARCHAR},
            </if>
            <if test="reserve0 != null">
                #{reserve0,jdbcType=VARCHAR},
            </if>
            <if test="incrementStatus != null">
                #{incrementStatus,jdbcType=INTEGER},
            </if>
            <if test="incrementTime != null">
                #{incrementTime,jdbcType=DATE},
            </if>
            <if test="incrementType != null">
                #{incrementType,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="noticeInfo != null">
                #{noticeInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="policyInfo != null">
                #{policyInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="facilitiesInfo != null">
                #{facilitiesInfo,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        insert into hotel_info_cozytime
        (id,`name`, name_en, phone,
        dept_id, country, province_id,
        city_id, city_name, area_id,
        business_districts, address_line, open_date,
        decoration_date, FLOOR_COUNT, brand_id,
        group_id, theme_id, type_id,
        star, star_info, image,
        plat_num, plat_hotel_id, recommendation_level,
        lon_bd, lat_bd, lon_gd,
        lat_gd, lon_gg, lat_gg,
        score, `status`, remark,
        create_time, update_time, create_by,
        update_by, is_delete, reserve1,
        reserve2, reserve3, reserve4,
        reserve5, reserve6, reserve7,
        reserve8, reserve9, reserve0,
        increment_status, increment_time, increment_type,
        description, notice_info, policy_info,
        facilities_info)values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.name,jdbcType=VARCHAR}, #{item.nameEn,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR},
            #{item.deptId,jdbcType=BIGINT}, #{item.country,jdbcType=VARCHAR}, #{item.provinceId,jdbcType=VARCHAR},
            #{item.cityId,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR}, #{item.areaId,jdbcType=VARCHAR},
            #{item.businessDistricts,jdbcType=VARCHAR}, #{item.addressLine,jdbcType=VARCHAR},
            #{item.openDate,jdbcType=VARCHAR},
            #{item.decorationDate,jdbcType=VARCHAR}, #{item.floorCount,jdbcType=VARCHAR},
            #{item.brandId,jdbcType=VARCHAR},
            #{item.groupId,jdbcType=VARCHAR}, #{item.themeId,jdbcType=VARCHAR}, #{item.typeId,jdbcType=VARCHAR},
            #{item.star,jdbcType=INTEGER}, #{item.starInfo,jdbcType=VARCHAR}, #{item.image,jdbcType=VARCHAR},
            #{item.platNum,jdbcType=VARCHAR}, #{item.platHotelId,jdbcType=VARCHAR},
            #{item.recommendationLevel,jdbcType=INTEGER},
            #{item.lonBd,jdbcType=DECIMAL}, #{item.latBd,jdbcType=DECIMAL}, #{item.lonGd,jdbcType=DECIMAL},
            #{item.latGd,jdbcType=DECIMAL}, #{item.lonGg,jdbcType=DECIMAL}, #{item.latGg,jdbcType=DECIMAL},
            #{item.score,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.createBy,jdbcType=VARCHAR},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, #{item.reserve1,jdbcType=VARCHAR},
            #{item.reserve2,jdbcType=VARCHAR}, #{item.reserve3,jdbcType=VARCHAR}, #{item.reserve4,jdbcType=VARCHAR},
            #{item.reserve5,jdbcType=VARCHAR}, #{item.reserve6,jdbcType=VARCHAR}, #{item.reserve7,jdbcType=VARCHAR},
            #{item.reserve8,jdbcType=VARCHAR}, #{item.reserve9,jdbcType=VARCHAR}, #{item.reserve0,jdbcType=VARCHAR},
            #{item.incrementStatus,jdbcType=INTEGER}, #{item.incrementTime,jdbcType=DATE},
            #{item.incrementType,jdbcType=INTEGER},
            #{item.description,jdbcType=LONGVARCHAR}, #{item.noticeInfo,jdbcType=LONGVARCHAR},
            #{item.policyInfo,jdbcType=LONGVARCHAR},
            #{item.facilitiesInfo,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.ltgj.ivw.domain.HotelInfoCozytime">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update hotel_info_cozytime
        <set>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameEn != null">
                name_en = #{nameEn,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=BIGINT},
            </if>
            <if test="country != null">
                country = #{country,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=VARCHAR},
            </if>
            <if test="cityName != null">
                city_name = #{cityName,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null">
                area_id = #{areaId,jdbcType=VARCHAR},
            </if>
            <if test="businessDistricts != null">
                business_districts = #{businessDistricts,jdbcType=VARCHAR},
            </if>
            <if test="addressLine != null">
                address_line = #{addressLine,jdbcType=VARCHAR},
            </if>
            <if test="openDate != null">
                open_date = #{openDate,jdbcType=VARCHAR},
            </if>
            <if test="decorationDate != null">
                decoration_date = #{decorationDate,jdbcType=VARCHAR},
            </if>
            <if test="floorCount != null">
                FLOOR_COUNT = #{floorCount,jdbcType=VARCHAR},
            </if>
            <if test="brandId != null">
                brand_id = #{brandId,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=VARCHAR},
            </if>
            <if test="themeId != null">
                theme_id = #{themeId,jdbcType=VARCHAR},
            </if>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=VARCHAR},
            </if>
            <if test="star != null">
                star = #{star,jdbcType=INTEGER},
            </if>
            <if test="starInfo != null">
                star_info = #{starInfo,jdbcType=VARCHAR},
            </if>
            <if test="image != null">
                image = #{image,jdbcType=VARCHAR},
            </if>
            <if test="platNum != null">
                plat_num = #{platNum,jdbcType=VARCHAR},
            </if>
            <if test="platHotelId != null">
                plat_hotel_id = #{platHotelId,jdbcType=VARCHAR},
            </if>
            <if test="recommendationLevel != null">
                recommendation_level = #{recommendationLevel,jdbcType=INTEGER},
            </if>
            <if test="lonBd != null">
                lon_bd = #{lonBd,jdbcType=DECIMAL},
            </if>
            <if test="latBd != null">
                lat_bd = #{latBd,jdbcType=DECIMAL},
            </if>
            <if test="lonGd != null">
                lon_gd = #{lonGd,jdbcType=DECIMAL},
            </if>
            <if test="latGd != null">
                lat_gd = #{latGd,jdbcType=DECIMAL},
            </if>
            <if test="lonGg != null">
                lon_gg = #{lonGg,jdbcType=DECIMAL},
            </if>
            <if test="latGg != null">
                lat_gg = #{latGg,jdbcType=DECIMAL},
            </if>
            <if test="score != null">
                score = #{score,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="reserve1 != null">
                reserve1 = #{reserve1,jdbcType=VARCHAR},
            </if>
            <if test="reserve2 != null">
                reserve2 = #{reserve2,jdbcType=VARCHAR},
            </if>
            <if test="reserve3 != null">
                reserve3 = #{reserve3,jdbcType=VARCHAR},
            </if>
            <if test="reserve4 != null">
                reserve4 = #{reserve4,jdbcType=VARCHAR},
            </if>
            <if test="reserve5 != null">
                reserve5 = #{reserve5,jdbcType=VARCHAR},
            </if>
            <if test="reserve6 != null">
                reserve6 = #{reserve6,jdbcType=VARCHAR},
            </if>
            <if test="reserve7 != null">
                reserve7 = #{reserve7,jdbcType=VARCHAR},
            </if>
            <if test="reserve8 != null">
                reserve8 = #{reserve8,jdbcType=VARCHAR},
            </if>
            <if test="reserve9 != null">
                reserve9 = #{reserve9,jdbcType=VARCHAR},
            </if>
            <if test="reserve0 != null">
                reserve0 = #{reserve0,jdbcType=VARCHAR},
            </if>
            <if test="incrementStatus != null">
                increment_status = #{incrementStatus,jdbcType=INTEGER},
            </if>
            <if test="incrementTime != null">
                increment_time = #{incrementTime,jdbcType=DATE},
            </if>
            <if test="incrementType != null">
                increment_type = #{incrementType,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=LONGVARCHAR},
            </if>
            <if test="noticeInfo != null">
                notice_info = #{noticeInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="policyInfo != null">
                policy_info = #{policyInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="facilitiesInfo != null">
                facilities_info = #{facilitiesInfo,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.ltgj.ivw.domain.HotelInfoCozytime">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update hotel_info_cozytime
        set `name` = #{name,jdbcType=VARCHAR},
        name_en = #{nameEn,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        dept_id = #{deptId,jdbcType=BIGINT},
        country = #{country,jdbcType=VARCHAR},
        province_id = #{provinceId,jdbcType=VARCHAR},
        city_id = #{cityId,jdbcType=VARCHAR},
        city_name = #{cityName,jdbcType=VARCHAR},
        area_id = #{areaId,jdbcType=VARCHAR},
        business_districts = #{businessDistricts,jdbcType=VARCHAR},
        address_line = #{addressLine,jdbcType=VARCHAR},
        open_date = #{openDate,jdbcType=VARCHAR},
        decoration_date = #{decorationDate,jdbcType=VARCHAR},
        FLOOR_COUNT = #{floorCount,jdbcType=VARCHAR},
        brand_id = #{brandId,jdbcType=VARCHAR},
        group_id = #{groupId,jdbcType=VARCHAR},
        theme_id = #{themeId,jdbcType=VARCHAR},
        type_id = #{typeId,jdbcType=VARCHAR},
        star = #{star,jdbcType=INTEGER},
        star_info = #{starInfo,jdbcType=VARCHAR},
        image = #{image,jdbcType=VARCHAR},
        plat_num = #{platNum,jdbcType=VARCHAR},
        plat_hotel_id = #{platHotelId,jdbcType=VARCHAR},
        recommendation_level = #{recommendationLevel,jdbcType=INTEGER},
        lon_bd = #{lonBd,jdbcType=DECIMAL},
        lat_bd = #{latBd,jdbcType=DECIMAL},
        lon_gd = #{lonGd,jdbcType=DECIMAL},
        lat_gd = #{latGd,jdbcType=DECIMAL},
        lon_gg = #{lonGg,jdbcType=DECIMAL},
        lat_gg = #{latGg,jdbcType=DECIMAL},
        score = #{score,jdbcType=VARCHAR},
        `status` = #{status,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=VARCHAR},
        update_by = #{updateBy,jdbcType=VARCHAR},
        is_delete = #{isDelete,jdbcType=INTEGER},
        reserve1 = #{reserve1,jdbcType=VARCHAR},
        reserve2 = #{reserve2,jdbcType=VARCHAR},
        reserve3 = #{reserve3,jdbcType=VARCHAR},
        reserve4 = #{reserve4,jdbcType=VARCHAR},
        reserve5 = #{reserve5,jdbcType=VARCHAR},
        reserve6 = #{reserve6,jdbcType=VARCHAR},
        reserve7 = #{reserve7,jdbcType=VARCHAR},
        reserve8 = #{reserve8,jdbcType=VARCHAR},
        reserve9 = #{reserve9,jdbcType=VARCHAR},
        reserve0 = #{reserve0,jdbcType=VARCHAR},
        increment_status = #{incrementStatus,jdbcType=INTEGER},
        increment_time = #{incrementTime,jdbcType=DATE},
        increment_type = #{incrementType,jdbcType=INTEGER},
        description = #{description,jdbcType=LONGVARCHAR},
        notice_info = #{noticeInfo,jdbcType=LONGVARCHAR},
        policy_info = #{policyInfo,jdbcType=LONGVARCHAR},
        facilities_info = #{facilitiesInfo,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.ltgj.ivw.domain.HotelInfoCozytime">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        update hotel_info_cozytime
        set `name` = #{name,jdbcType=VARCHAR},
        name_en = #{nameEn,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        dept_id = #{deptId,jdbcType=BIGINT},
        country = #{country,jdbcType=VARCHAR},
        province_id = #{provinceId,jdbcType=VARCHAR},
        city_id = #{cityId,jdbcType=VARCHAR},
        city_name = #{cityName,jdbcType=VARCHAR},
        area_id = #{areaId,jdbcType=VARCHAR},
        business_districts = #{businessDistricts,jdbcType=VARCHAR},
        address_line = #{addressLine,jdbcType=VARCHAR},
        open_date = #{openDate,jdbcType=VARCHAR},
        decoration_date = #{decorationDate,jdbcType=VARCHAR},
        FLOOR_COUNT = #{floorCount,jdbcType=VARCHAR},
        brand_id = #{brandId,jdbcType=VARCHAR},
        group_id = #{groupId,jdbcType=VARCHAR},
        theme_id = #{themeId,jdbcType=VARCHAR},
        type_id = #{typeId,jdbcType=VARCHAR},
        star = #{star,jdbcType=INTEGER},
        star_info = #{starInfo,jdbcType=VARCHAR},
        image = #{image,jdbcType=VARCHAR},
        plat_num = #{platNum,jdbcType=VARCHAR},
        plat_hotel_id = #{platHotelId,jdbcType=VARCHAR},
        recommendation_level = #{recommendationLevel,jdbcType=INTEGER},
        lon_bd = #{lonBd,jdbcType=DECIMAL},
        lat_bd = #{latBd,jdbcType=DECIMAL},
        lon_gd = #{lonGd,jdbcType=DECIMAL},
        lat_gd = #{latGd,jdbcType=DECIMAL},
        lon_gg = #{lonGg,jdbcType=DECIMAL},
        lat_gg = #{latGg,jdbcType=DECIMAL},
        score = #{score,jdbcType=VARCHAR},
        `status` = #{status,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=VARCHAR},
        update_by = #{updateBy,jdbcType=VARCHAR},
        is_delete = #{isDelete,jdbcType=INTEGER},
        reserve1 = #{reserve1,jdbcType=VARCHAR},
        reserve2 = #{reserve2,jdbcType=VARCHAR},
        reserve3 = #{reserve3,jdbcType=VARCHAR},
        reserve4 = #{reserve4,jdbcType=VARCHAR},
        reserve5 = #{reserve5,jdbcType=VARCHAR},
        reserve6 = #{reserve6,jdbcType=VARCHAR},
        reserve7 = #{reserve7,jdbcType=VARCHAR},
        reserve8 = #{reserve8,jdbcType=VARCHAR},
        reserve9 = #{reserve9,jdbcType=VARCHAR},
        reserve0 = #{reserve0,jdbcType=VARCHAR},
        increment_status = #{incrementStatus,jdbcType=INTEGER},
        increment_time = #{incrementTime,jdbcType=DATE},
        increment_type = #{incrementType,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM hotel_info_cozytime
        WHERE is_delete = 0
    </select>

    <select id="selectIdsByPage" resultType="java.lang.String">
        select id
        from hotel_info_cozytime
        where is_delete = 0
        order by id
            limit #{start}, #{size}
    </select>


    <select id="selectBatchByIds" resultType="com.ltgj.ivw.domain.HotelInfoCozytime">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from hotel_info_cozytime where is_delete = 0 and id in
        <foreach item="item" index="index" collection="sourceIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE hotel_info_cozytime
            <set>
                <if test="item.name != null">`name` = #{item.name},</if>
                <if test="item.nameEn != null">name_en = #{item.nameEn},</if>
                <if test="item.phone != null">phone = #{item.phone},</if>
                <if test="item.deptId != null">dept_id = #{item.deptId},</if>
                <if test="item.country != null">country = #{item.country},</if>
                <if test="item.provinceId != null">province_id = #{item.provinceId},</if>
                <if test="item.cityId != null">city_id = #{item.cityId},</if>
                <if test="item.cityName != null">city_name = #{item.cityName},</if>
                <if test="item.areaId != null">area_id = #{item.areaId},</if>
                <if test="item.businessDistricts != null">business_districts = #{item.businessDistricts},</if>
                <if test="item.addressLine != null">address_line = #{item.addressLine},</if>
                <if test="item.openDate != null">open_date = #{item.openDate},</if>
                <if test="item.decorationDate != null">decoration_date = #{item.decorationDate},</if>
                <if test="item.floorCount != null">FLOOR_COUNT = #{item.floorCount},</if>
                <if test="item.brandId != null">brand_id = #{item.brandId},</if>
                <if test="item.groupId != null">group_id = #{item.groupId},</if>
                <if test="item.themeId != null">theme_id = #{item.themeId},</if>
                <if test="item.typeId != null">type_id = #{item.typeId},</if>
                <if test="item.star != null">star = #{item.star},</if>
                <if test="item.starInfo != null">star_info = #{item.starInfo},</if>
                <if test="item.image != null">image = #{item.image},</if>
                <if test="item.platNum != null">plat_num = #{item.platNum},</if>
                <if test="item.platHotelId != null">plat_hotel_id = #{item.platHotelId},</if>
                <if test="item.recommendationLevel != null">recommendation_level = #{item.recommendationLevel},</if>
                <if test="item.lonBd != null">lon_bd = #{item.lonBd},</if>
                <if test="item.latBd != null">lat_bd = #{item.latBd},</if>
                <if test="item.lonGd != null">lon_gd = #{item.lonGd},</if>
                <if test="item.latGd != null">lat_gd = #{item.latGd},</if>
                <if test="item.lonGg != null">lon_gg = #{item.lonGg},</if>
                <if test="item.latGg != null">lat_gg = #{item.latGg},</if>
                <if test="item.score != null">score = #{item.score},</if>
                <if test="item.status != null">status = #{item.status},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.createTime != null">create_time = #{item.createTime},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
                <if test="item.createBy != null">create_by = #{item.createBy},</if>
                <if test="item.updateBy != null">update_by = #{item.updateBy},</if>
                <if test="item.isDelete != null">is_delete = #{item.isDelete},</if>
                <if test="item.reserve1 != null">reserve1 = #{item.reserve1},</if>
                <if test="item.reserve2 != null">reserve2 = #{item.reserve2},</if>
                <if test="item.reserve3 != null">reserve3 = #{item.reserve3},</if>
                <if test="item.reserve4 != null">reserve4 = #{item.reserve4},</if>
                <if test="item.reserve5 != null">reserve5 = #{item.reserve5},</if>
                <if test="item.reserve6 != null">reserve6 = #{item.reserve6},</if>
                <if test="item.reserve7 != null">reserve7 = #{item.reserve7},</if>
                <if test="item.reserve8 != null">reserve8 = #{item.reserve8},</if>
                <if test="item.reserve9 != null">reserve9 = #{item.reserve9},</if>
                <if test="item.reserve0 != null">reserve0 = #{item.reserve0},</if>
                <if test="item.incrementStatus != null">increment_status = #{item.incrementStatus},</if>
                <if test="item.incrementTime != null">increment_time = #{item.incrementTime},</if>
                <if test="item.incrementType != null">increment_type = #{item.incrementType},</if>
                <if test="item.description != null">description = #{item.description},</if>
                <if test="item.noticeInfo != null">notice_info = #{item.noticeInfo},</if>
                <if test="item.policyInfo != null">policy_info = #{item.policyInfo},</if>
                <if test="item.facilitiesInfo != null">facilities_info = #{item.facilitiesInfo},</if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>


    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO hotel_info_cozytime (
            id, `name`, name_en, phone, dept_id, country, province_id, city_id, city_name, area_id,
            business_districts, address_line, open_date, decoration_date, FLOOR_COUNT, brand_id,
            group_id, theme_id, type_id, star, star_info, image, plat_num, plat_hotel_id, recommendation_level,
            lon_bd, lat_bd, lon_gd, lat_gd, lon_gg, lat_gg, score, `status`, remark, create_time, update_time,
            create_by, update_by, is_delete, reserve1, reserve2, reserve3, reserve4, reserve5, reserve6,
            reserve7, reserve8, reserve9, reserve0, increment_status, increment_time, increment_type,
            description, notice_info, policy_info, facilities_info
            ) VALUES (
            #{item.id}, #{item.name}, #{item.nameEn}, #{item.phone}, #{item.deptId}, #{item.country},
            #{item.provinceId}, #{item.cityId},
            #{item.cityName}, #{item.areaId}, #{item.businessDistricts}, #{item.addressLine}, #{item.openDate},
            #{item.decorationDate}, #{item.floorCount},
            #{item.brandId}, #{item.groupId}, #{item.themeId}, #{item.typeId}, #{item.star}, #{item.starInfo},
            #{item.image}, #{item.platNum},
            #{item.platHotelId}, #{item.recommendationLevel}, #{item.lonBd}, #{item.latBd}, #{item.lonGd},
            #{item.latGd}, #{item.lonGg}, #{item.latGg},
            #{item.score}, #{item.status}, #{item.remark}, #{item.createTime}, #{item.updateTime}, #{item.createBy},
            #{item.updateBy},
            #{item.isDelete}, #{item.reserve1}, #{item.reserve2}, #{item.reserve3}, #{item.reserve4}, #{item.reserve5},
            #{item.reserve6},
            #{item.reserve7}, #{item.reserve8}, #{item.reserve9}, #{item.reserve0}, #{item.incrementStatus},
            #{item.incrementTime}, #{item.incrementType},
            #{item.description}, #{item.noticeInfo}, #{item.policyInfo}, #{item.facilitiesInfo}
            )
            ON DUPLICATE KEY UPDATE
            `name` = VALUES(`name`), name_en = VALUES(name_en), phone = VALUES(phone), dept_id = VALUES(dept_id),
            country = VALUES(country),
            province_id = VALUES(province_id), city_id = VALUES(city_id), city_name = VALUES(city_name), area_id =
            VALUES(area_id),
            business_districts = VALUES(business_districts), address_line = VALUES(address_line), open_date =
            VALUES(open_date),
            decoration_date = VALUES(decoration_date), FLOOR_COUNT = VALUES(FLOOR_COUNT), brand_id = VALUES(brand_id),
            group_id = VALUES(group_id),
            theme_id = VALUES(theme_id), type_id = VALUES(type_id), star = VALUES(star), star_info = VALUES(star_info),
            image = VALUES(image),
            plat_num = VALUES(plat_num), plat_hotel_id = VALUES(plat_hotel_id), recommendation_level =
            VALUES(recommendation_level),
            lon_bd = VALUES(lon_bd), lat_bd = VALUES(lat_bd), lon_gd = VALUES(lon_gd), lat_gd = VALUES(lat_gd), lon_gg =
            VALUES(lon_gg),
            lat_gg = VALUES(lat_gg), score = VALUES(score), remark = VALUES(remark),
            update_time = VALUES(update_time),
            update_by = VALUES(update_by), is_delete = VALUES(is_delete), reserve1 = VALUES(reserve1), reserve2 =
            VALUES(reserve2),
            reserve3 = VALUES(reserve3), reserve4 = VALUES(reserve4), reserve5 = VALUES(reserve5), reserve6 =
            VALUES(reserve6),
            reserve7 = VALUES(reserve7), reserve8 = VALUES(reserve8), reserve9 = VALUES(reserve9), reserve0 =
            VALUES(reserve0),
            increment_status = VALUES(increment_status), increment_time = VALUES(increment_time), increment_type =
            VALUES(increment_type),
            description = VALUES(description), notice_info = VALUES(notice_info), policy_info = VALUES(policy_info),
            facilities_info = VALUES(facilities_info)
        </foreach>
    </insert>

</mapper>