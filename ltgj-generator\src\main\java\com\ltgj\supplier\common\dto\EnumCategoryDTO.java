package com.ltgj.supplier.common.dto;

import java.util.List;

/**
 * 枚举分类 DTO
 * 用于封装枚举分类信息，包含分类标识和对应的枚举项列表
 */
public class EnumCategoryDTO {
    /**
     * 分类标识（如 "customerType", "facility"）
     */
    private String categoryKey;
    
    /**
     * 分类名称（如 "客户类型政策信息", "酒店设施信息"）
     */
    private String categoryName;
    
    /**
     * 分类描述
     */
    private String categoryDesc;
    
    /**
     * 枚举项列表
     */
    private List<EnumItemDTO> items;

    public EnumCategoryDTO() {
    }

    public EnumCategoryDTO(String categoryKey, String categoryName, String categoryDesc, List<EnumItemDTO> items) {
        this.categoryKey = categoryKey;
        this.categoryName = categoryName;
        this.categoryDesc = categoryDesc;
        this.items = items;
    }

    public String getCategoryKey() {
        return categoryKey;
    }

    public void setCategoryKey(String categoryKey) {
        this.categoryKey = categoryKey;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategoryDesc() {
        return categoryDesc;
    }

    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    public List<EnumItemDTO> getItems() {
        return items;
    }

    public void setItems(List<EnumItemDTO> items) {
        this.items = items;
    }

    @Override
    public String toString() {
        return "EnumCategoryDTO{" +
                "categoryKey='" + categoryKey + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", categoryDesc='" + categoryDesc + '\'' +
                ", items=" + items +
                '}';
    }
} 