package com.ltgj.ivw.enums;

public enum HotelPlatEnum {

    HSJL("2000054","红色加力", "hotel_gn_hsjl"),
    HSJLXY("2000077","红色加力协议", "hotel_gn_hsjlxy"),
    KT("2000082","科坦", "hotel_gn_ketan"),
    MT("2000023", "美团", "hotel_gn_meituan"),
    CLGJ("2000079", "差旅管家", "hotel_gn_clgj"),
    KLYX("2000080", "康旅严选", "hotel_gn_klyx"),
    QT("2000010", "千淘", "hotel_gn_qiantao"),

    ;

    //平台编号
    private String value;
    //平台名称
    private String name;
    private String tableName;

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public String getTableName() {
        return tableName;
    }


    HotelPlatEnum(String value,String name, String tableName) {
        this.value = value;
        this.name = name;
        this.tableName = tableName;
    }

}
