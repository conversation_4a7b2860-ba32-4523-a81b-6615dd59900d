package com.ltgj.supplier.common.gn.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetAllGBResp {

    @ApiModelProperty("集团id")
    private String groupId;
    @ApiModelProperty("集团名称")
    private String groupName;
    @ApiModelProperty("集团英文名称")
    private String groupNameEn;

    @ApiModelProperty("品牌列表")
    private List<BrandResp> brandList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BrandResp {

        @ApiModelProperty("品牌id")
        private String brandId;

        @ApiModelProperty("品牌名称")
        private String brandName;

        @ApiModelProperty("品牌英文名称")
        private String brandNameEn;
    }
}
