//package com.ltgj.ivw.service;
//
//import com.ltgj.common.utils.file.ImgClientUtils;
//import com.tem.imgserver.client.UploadResult;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.io.File;
//import java.io.IOException;
//import java.nio.file.Files;
//
///**
// * 图片客户端测试类
// * 测试本地图片上传到图片服务器的功能
// *
// * <AUTHOR>
// * @Date 2025/6/5
// * @description: ImgClient图片上传测试
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class ImgClientTest {
//
//    /**
//     * 测试使用File对象上传本地图片（方式一：推荐）
//     * 使用ImgClientUtils.uploadImg(File file, String path)方法
//     */
//    @Test
//    public void testUploadLocalImageWithFile() {
//        // 本地图片文件路径
//        String localImagePath = "C:\\Users\\<USER>\\Pictures\\meaomao.jpg";
//        // 业务类型（相当于文件目录）
//        String bizType = "test/hotel/images";
//
//        try {
//            // 1. 验证文件是否存在
//            File imageFile = new File(localImagePath);
//            if (!imageFile.exists()) {
//                System.err.println("❌ 错误：指定的图片文件不存在！路径：" + localImagePath);
//                return;
//            }
//
//            // 2. 验证是否为图片文件
//            String fileName = imageFile.getName().toLowerCase();
//            if (!ImgClientUtils.isImageFile(fileName)) {
//                System.err.println("❌ 错误：文件格式不支持！仅支持jpg、jpeg、png、gif、bmp格式");
//                return;
//            }
//
//            // 3. 显示文件信息
//            long fileSize = imageFile.length();
//            System.out.println("=== 开始上传图片（File方式）===");
//            System.out.println("📁 文件路径：" + localImagePath);
//            System.out.println("📄 文件名称：" + imageFile.getName());
//            System.out.println("📊 文件大小：" + fileSize + " bytes (" + String.format("%.2f", fileSize / 1024.0 / 1024.0) + " MB)");
//            System.out.println("📂 业务类型：" + bizType);
//
//            // 4. 调用ImgClientUtils静态方法上传图片
//            System.out.println("🚀 正在上传图片...");
//            UploadResult uploadResult = ImgClientUtils.uploadImg(imageFile, bizType);
//
//            // 5. 验证上传结果并获取完整访问地址
//            if (uploadResult != null) {
//                System.out.println("📊 上传结果详情：");
//                System.out.println("  - 返回码：" + uploadResult.getCode());
//                System.out.println("  - 消息：" + uploadResult.getMsg());
//
//                if (uploadResult.getCode() == 0) {
//                    System.out.println("✅ 图片上传成功！");
//
//                    // 获取完整的可访问URL地址
//                    String fullAccessUrl = buildFullImageUrl(uploadResult);
//                    System.out.println("🌐 完整访问地址：" + fullAccessUrl);
//                    System.out.println("📋 可直接复制到浏览器访问：");
//                    System.out.println("   " + fullAccessUrl);
//
//                    // 显示其他详细信息
//                    if (uploadResult.getUrlPath() != null) {
//                        System.out.println("  - 相对路径：" + uploadResult.getUrlPath());
//                    }
//                    if (uploadResult.getSize() != null) {
//                        System.out.println("  - 文件大小：" + uploadResult.getSize() + " bytes");
//                    }
//                } else {
//                    System.err.println("❌ 图片上传失败！错误码：" + uploadResult.getCode() + "，错误信息：" + uploadResult.getMsg());
//                }
//            } else {
//                System.err.println("❌ 上传失败：返回结果为空");
//            }
//
//        } catch (Exception e) {
//            System.err.println("❌ 图片上传过程中发生异常：" + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 测试使用MultipartFile上传本地图片（方式二：推荐）
//     * 使用ImgClientUtils.upload(MultipartFile multipartFile, String bizType)方法
//     */
//    @Test
//    public void testUploadLocalImageWithMultipartFile() {
//        // 本地图片文件路径
//        String localImagePath = "C:\\Users\\<USER>\\Pictures\\meaomao.jpg";
//        // 业务类型（相当于文件目录）
//        String bizType = "test/hotel/images";
//
//        try {
//            // 1. 验证文件是否存在
//            File imageFile = new File(localImagePath);
//            if (!imageFile.exists()) {
//                System.err.println("❌ 错误：指定的图片文件不存在！路径：" + localImagePath);
//                return;
//            }
//
//            // 2. 验证是否为图片文件
//            String fileName = imageFile.getName().toLowerCase();
//            if (!ImgClientUtils.isImageFile(fileName)) {
//                System.err.println("❌ 错误：文件格式不支持！仅支持jpg、jpeg、png、gif、bmp格式");
//                return;
//            }
//
//            // 3. 将File转换为MockMultipartFile
//            byte[] fileContent = Files.readAllBytes(imageFile.toPath());
//            MockMultipartFile multipartFile = new MockMultipartFile(
//                "file",                           // 参数名
//                imageFile.getName(),              // 原始文件名
//                "image/jpeg",                     // 内容类型
//                fileContent                       // 文件内容
//            );
//
//            // 4. 显示文件信息
//            System.out.println("=== 开始上传图片（MultipartFile方式）===");
//            System.out.println("📁 文件路径：" + localImagePath);
//            System.out.println("📄 文件名称：" + multipartFile.getOriginalFilename());
//            System.out.println("📊 文件大小：" + multipartFile.getSize() + " bytes (" + String.format("%.2f", multipartFile.getSize() / 1024.0 / 1024.0) + " MB)");
//            System.out.println("📂 业务类型：" + bizType);
//            System.out.println("📋 内容类型：" + multipartFile.getContentType());
//
//            // 5. 调用ImgClientUtils静态方法上传图片（推荐方法）
//            System.out.println("🚀 正在上传图片...");
//            UploadResult uploadResult = ImgClientUtils.upload(multipartFile, bizType);
//
//            // 6. 验证上传结果并获取完整访问地址
//            if (uploadResult != null) {
//                System.out.println("📊 上传结果详情：");
//                System.out.println("  - 返回码：" + uploadResult.getCode());
//                System.out.println("  - 消息：" + uploadResult.getMsg());
//
//                if (uploadResult.getCode() == 0) {
//                    System.out.println("✅ 图片上传成功！");
//
//                    // 获取完整的可访问URL地址
//                    String fullAccessUrl = buildFullImageUrl(uploadResult);
//                    System.out.println("🌐 完整访问地址：" + fullAccessUrl);
//                    System.out.println("📋 可直接复制到浏览器访问：");
//                    System.out.println("   " + fullAccessUrl);
//
//                    // 显示详细信息
//                    System.out.println("  - 文件ID：" + uploadResult.getFileId());
//                    System.out.println("  - 文件名：" + uploadResult.getFileName());
//                    System.out.println("  - 文件类型：" + uploadResult.getFileType());
//                    System.out.println("  - 业务类型：" + uploadResult.getBizType());
//                    System.out.println("  - 文件大小：" + uploadResult.getSize() + " bytes");
//                    if (uploadResult.getUrlPath() != null) {
//                        System.out.println("  - 相对路径：" + uploadResult.getUrlPath());
//                    }
//                } else {
//                    System.err.println("❌ 图片上传失败！错误码：" + uploadResult.getCode() + "，错误信息：" + uploadResult.getMsg());
//                }
//            } else {
//                System.err.println("❌ 上传失败：返回结果为空");
//            }
//
//        } catch (IOException e) {
//            System.err.println("❌ 文件读取失败：" + e.getMessage());
//            e.printStackTrace();
//        } catch (Exception e) {
//            System.err.println("❌ 图片上传过程中发生异常：" + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 测试使用自定义文件名上传图片（方式三：高级用法）
//     * 使用ImgClientUtils.uploadImg2(File file, String path, String fileName, String storeType)方法
//     */
//    @Test
//    public void testUploadLocalImageWithCustomName() {
//        // 本地图片文件路径
//        String localImagePath = "C:\\Users\\<USER>\\Pictures\\meaomao.jpg";
//        // 业务路径
//        String businessPath = "test/hotel/custom";
//        // 自定义文件名
//        String customFileName = "hotel_image_" + System.currentTimeMillis() + ".jpg";
//        // 存储类型
//        String storeType = "local";
//
//        try {
//            // 1. 验证文件是否存在
//            File imageFile = new File(localImagePath);
//            if (!imageFile.exists()) {
//                System.err.println("❌ 错误：指定的图片文件不存在！路径：" + localImagePath);
//                return;
//            }
//
//            // 2. 显示文件信息
//            System.out.println("=== 开始上传图片（自定义文件名方式）===");
//            System.out.println("📁 源文件路径：" + localImagePath);
//            System.out.println("📄 原始文件名：" + imageFile.getName());
//            System.out.println("🎯 自定义文件名：" + customFileName);
//            System.out.println("📂 业务路径：" + businessPath);
//            System.out.println("💾 存储类型：" + storeType);
//            System.out.println("📊 文件大小：" + imageFile.length() + " bytes");
//
//            // 3. 调用ImgClientUtils静态方法上传图片
//            System.out.println("🚀 正在上传图片...");
//            UploadResult uploadResult = ImgClientUtils.uploadImg2(imageFile, businessPath, customFileName, storeType);
//
//            // 4. 验证上传结果并获取完整访问地址
//            if (uploadResult != null) {
//                System.out.println("📊 上传结果详情：");
//                System.out.println("  - 返回码：" + uploadResult.getCode());
//                System.out.println("  - 消息：" + uploadResult.getMsg());
//
//                if (uploadResult.getCode() == 0) {
//                    System.out.println("✅ 图片上传成功！");
//
//                    // 获取完整的可访问URL地址
//                    String fullAccessUrl = buildFullImageUrl(uploadResult);
//                    System.out.println("🌐 完整访问地址：" + fullAccessUrl);
//                    System.out.println("📋 可直接复制到浏览器访问：");
//                    System.out.println("   " + fullAccessUrl);
//
//                    if (uploadResult.getUrlPath() != null) {
//                        System.out.println("  - 相对路径：" + uploadResult.getUrlPath());
//                    }
//                    if (uploadResult.getSize() != null) {
//                        System.out.println("  - 文件大小：" + uploadResult.getSize() + " bytes");
//                    }
//                } else {
//                    System.err.println("❌ 图片上传失败！错误码：" + uploadResult.getCode() + "，错误信息：" + uploadResult.getMsg());
//                }
//            } else {
//                System.err.println("❌ 上传失败：返回结果为空");
//            }
//
//        } catch (Exception e) {
//            System.err.println("❌ 图片上传过程中发生异常：" + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 测试配置和URL构建
//     * 验证图片服务器配置是否正确
//     */
//    @Test
//    public void testImageServerConfig() {
//        System.out.println("=== 图片服务器配置测试 ===");
//        System.out.println("📊 当前配置的图片服务器基础URL：" + ImgClientUtils.getPathDoMainStatic());
//
//        // 模拟一个上传结果来测试URL构建
//        UploadResult mockResult = new UploadResult();
//        mockResult.setCode(0);
//        mockResult.setUrlPath("/test/images/example.jpg");
//
//        String fullUrl = buildFullImageUrl(mockResult);
//        System.out.println("🔗 示例完整URL：" + fullUrl);
//        System.out.println("📋 请确认这个URL格式是否正确");
//
//        System.out.println("\n💡 提示：");
//        System.out.println("  - 如果URL格式不正确，请检查application.yml中的img.server.base.url配置");
//        System.out.println("  - 确保配置的URL可以正常访问");
//        System.out.println("  - 如果是本地测试，确保图片服务器正在运行");
//    }
//
//    /**
//     * 构建完整的图片访问URL
//     * @param uploadResult 上传结果
//     * @return 完整的可访问URL
//     */
//    private String buildFullImageUrl(UploadResult uploadResult) {
//        if (uploadResult == null) {
//            return null;
//        }
//
//        // 尝试获取图片路径信息
//        String urlPath = null;
//
//        // 方式1：优先使用getUrlPath()方法
//        if (uploadResult.getUrlPath() != null && !uploadResult.getUrlPath().isEmpty()) {
//            urlPath = uploadResult.getUrlPath();
//        }
//        // 方式2：如果urlPath为空，尝试使用getFileKey()
//        else if (uploadResult.getFileKey() != null && !uploadResult.getFileKey().isEmpty()) {
//            urlPath = uploadResult.getFileKey();
//        }
//
//        if (urlPath == null || urlPath.isEmpty()) {
//            System.err.println("⚠️ 警告：无法获取图片路径信息");
//            System.err.println("  - getUrlPath(): " + uploadResult.getUrlPath());
//            System.err.println("  - getFileKey(): " + uploadResult.getFileKey());
//            return "无法构建访问URL - 缺少路径信息";
//        }
//
//        // 检查是否已经是完整的URL
//        if (urlPath.startsWith("http://") || urlPath.startsWith("https://")) {
//            return urlPath;
//        }
//
//        // 构建完整URL
//        String baseUrl = ImgClientUtils.getPathDoMainStatic();
//        if (!baseUrl.endsWith("/")) {
//            baseUrl += "/";
//        }
//
//        if (urlPath.startsWith("/")) {
//            urlPath = urlPath.substring(1);
//        }
//
//        String fullUrl = baseUrl + urlPath;
//
//        // 输出调试信息
//        System.out.println("🔧 URL构建详情：");
//        System.out.println("  - 基础URL：" + ImgClientUtils.getPathDoMainStatic());
//        System.out.println("  - 相对路径：" + uploadResult.getUrlPath());
//        System.out.println("  - FileKey：" + uploadResult.getFileKey());
//        System.out.println("  - 构建结果：" + fullUrl);
//
//        return fullUrl;
//    }
//}
