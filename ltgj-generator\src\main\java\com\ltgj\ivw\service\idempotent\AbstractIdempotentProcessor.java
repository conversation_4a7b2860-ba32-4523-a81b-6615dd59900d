package com.ltgj.ivw.service.idempotent;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 抽象幂等性处理器
 * 实现通用的幂等性处理逻辑
 *
 * @param <T> 实体类型
 * @param <K> 主键类型
 * @param <O> 幂等实体类型
 */
@Slf4j
public abstract class AbstractIdempotentProcessor<T, K, O> implements IdempotentProcessor<T, K, O> {

    /**
     * 获取处理器名称，用于日志标识
     */
    protected abstract String getProcessorName();

    @Override
    public IdempotentResult<T> processIdempotent(List<T> dataList) {
        long startTime = System.currentTimeMillis();
        String processorName = getProcessorName();

        log.info("[{}] 开始处理幂等性数据，总数量: {}", processorName, dataList.size());

        IdempotentResult.IdempotentResultBuilder<T> resultBuilder = IdempotentResult.<T>builder()
                .totalCount(dataList.size());

        if (CollectionUtils.isEmpty(dataList)) {
            log.warn("[{}] 待处理数据为空", processorName);
            return resultBuilder
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .build();
        }

        try {
            // 1. 提取所有幂等键
            List<O> keys = dataList.stream()
                    .map(this::getEntityAttributeKey)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            log.info("[{}] 提取到 {} 个有效主键", processorName, keys.size());

            Function<T, String> oStringFunction = o -> String.valueOf(this.getEntityAttributeKey(o));

            // 2. 批量查询现有数据
            List<T> existingData = batchQueryExistingByIdempotentKey(keys);
            Map<String, T> existingDataMap = existingData.stream()
                    .collect(Collectors.toMap(oStringFunction, Function.identity()));

            log.info("[{}] 查询到 {} 条现有数据", processorName, existingData.size());

            // 3. 分类处理数据
            List<T> insertList = Lists.newArrayListWithExpectedSize(dataList.size());
            List<T> updateList = new ArrayList<>();
            List<T> skipList = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();

            for (T entity : dataList) {
                try {
                    if (MapUtils.isEmpty(existingDataMap) || Objects.isNull(existingDataMap.get(String.valueOf(getEntityAttributeKey(entity))))) {
                        // 新数据，需要插入
                        insertList.add(entity);
                        log.debug("[{}] 新增数据: {}", processorName, getEntityAttributeKey(entity));
                    } else {
                        String entityAttributeKey = String.valueOf(getEntityAttributeKey(entity));
                        // 现有数据，检查是否需要更新
                        if (isEntityEqual(existingDataMap.get(entityAttributeKey), entity)) {
                            // 数据无变化，跳过
                            skipList.add(entity);
                            log.debug("[{}] 数据无变化，跳过: {}", processorName, entityAttributeKey);
                        } else {
                            // 数据有变化，需要更新
                            updateList.add(entity);
                            String changedFields = getChangedFields(existingDataMap.get(entityAttributeKey), entity);
                            log.debug("[{}] 数据有变化，更新: {}, 变更字段: {}", processorName, getEntityAttributeKey(entity), changedFields);
                        }
                    }
                } catch (Exception e) {
                    log.error("[{}] 处理单条数据时发生错误: {}", processorName, entity, e);
                    errorMessages.add("处理数据错误: " + e.getMessage());
                }
            }

            // 4. 执行批量操作
            int insertCount = 0;
            int updateCount = 0;

            if (!insertList.isEmpty()) {
                log.info("[{}] 开始批量插入 {} 条数据", processorName, insertList.size());
                insertCount = batchInsert(insertList);
                log.info("[{}] 批量插入完成，实际插入: {} 条", processorName, insertCount);
            }

            if (!updateList.isEmpty()) {
                log.info("[{}] 开始批量更新 {} 条数据", processorName, updateList.size());
                updateCount = batchUpdate(updateList);
                log.info("[{}] 批量更新完成，实际更新: {} 条", processorName, updateCount);
            }

            // 5. 构建结果
            IdempotentResult<T> result = resultBuilder
                    .insertCount(insertCount)
                    .updateCount(updateCount)
                    .skipCount(skipList.size())
                    .errorCount(errorMessages.size())
                    .insertedData(insertList)
                    .updatedData(updateList)
                    .skippedData(skipList)
                    .errorMessages(errorMessages)
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .build();

            log.info("[{}] {}", processorName, result.getSummary());

            return result;

        } catch (Exception e) {
            log.error("[{}] 处理幂等性数据时发生异常", processorName, e);
            return resultBuilder
                    .errorCount(dataList.size())
                    .errorMessages(Collections.singletonList("处理异常: " + e.getMessage()))
                    .processingTimeMs(System.currentTimeMillis() - startTime)
                    .build();
        }
    }
} 