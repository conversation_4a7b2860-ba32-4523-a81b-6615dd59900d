package com.ltgj.ivw.controller;

import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.request.hotelGnPreMapping.ListRequest;
import com.ltgj.ivw.request.hotelGnPreMapping.ListResponse;
import com.ltgj.ivw.service.HotelGnPreMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 待处理酒店映射controller
 */
@Slf4j
@RestController
@RequestMapping("/ivw/hotelGnPreMapping")
public class HotelGnPreMappingController extends BaseController {

    @Autowired
    private HotelGnPreMappingService hotelGnPreMappingService;

    /**
     * 列表查询
     * @param listRequest
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ivw:trbHotel:list')")
    @GetMapping("/list")
    public TableDataInfo list(ListRequest listRequest)
    {
        startPage();
        List<ListResponse> list = hotelGnPreMappingService.selectList(listRequest);
        processData(list);
        return getDataTable(list);
    }

    private void processData(List<ListResponse> list) {
        for (ListResponse item : list) {
            //平台名称转换
            if (item.getSourePlatformId() != null){
                item.setSourePlatformName(PlatEnum.getplatEnum(item.getSourePlatformId()).getName());
            }
        }
    }

    /**
     * 查询详情
     * @param id
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ivw:trbHotel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(hotelGnPreMappingService.selectById(id));
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ivw:trbHotel:delete')")
    @Log(title = "预映射记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(hotelGnPreMappingService.deleteByIds(ids));
    }

    /**
     * 添加映射
     * @param param
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ivw:trbHotel:mapping')")
    @PostMapping("/addMapping")
    public AjaxResult addMapping(@RequestBody HotelGnPreMapping param)
    {
        try {
            hotelGnPreMappingService.addMapping(param.getId());
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 添加基础信息+映射
     * @param param
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ivw:trbHotel:add')")
    @PostMapping("/addBasic")
    public AjaxResult addBasic(@RequestBody HotelGnPreMapping param)
    {
        try {
            hotelGnPreMappingService.addBasic(param.getId());
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success();
    }
}
