package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.GeoStructureQueryReq;
import com.ltgj.ivw.dto.TmpAiScoringDto;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.TmpJdJdbMappingMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.HotelDataFormatUtil;
import com.ltgj.ivw.utils.HttpUtil;
import com.ltgj.ivw.utils.dto.AIMappingResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @description: ai映射服务
 * @author: GuangFu Wei
 * @date: 2024年11月12日 9:03
 */
@Slf4j
@Service
public class AiMappingServiceImpl implements AiMappingService {

    public static final String DifyURL = "http://dify-web.aitojoy.com/v1";//http://test-dify.aitojoy.com/v1";
    public static final String workflowsKey = "app-jVrcBYbzhEyY03SlJL3fwmOF";
    public static final String workflowBatchKey = "app-YjKTkYwHNtCRxEOvFf3K1dCl";
    public static final String knowledgeId = "ea5e289f-d3ca-40fc-a839-97e81d4a1572";
    public static final String knowledgeKey = "dataset-1e02uVYmcwKpPwQl5SoRxaru";

//    AtomicBoolean atomicBoolean = new AtomicBoolean(false);

    @Autowired
    private TmpJdJdbMappingMapper tmpJdJdbMappingMapper;
    @Autowired
    private IHotelInfoMeituanService hotelInfoMeituanService;
    @Autowired
    private IHotelInfoQiantaoService hotelInfoQiantaoService;
    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;
    @Autowired
    private IHotelInfoHsjlxyService hotelInfoHsjlxyService;
    @Autowired
    private IJdJdbService jdJdbService;

    @Autowired
    private DifyApiService difyApiService;
    @Autowired
    @Qualifier("difyApiService2Impl")
    private DifyApiService difyApiService2;
    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private IGeoHierarchyService geoHierarchyService;

    public static <T> List<List<T>> splitList(List<T> list, final int size) {
        List<List<T>> newList = new ArrayList<>();
        int num = list.size() / size;
        for (int i = 0; i < size; i++) {
            if (i == size - 1) {
                newList.add(list.subList(i * num, list.size()));
            } else {
                newList.add(list.subList(i * num, (i + 1) * num));
            }
        }
        return newList;
    }

    // 新增方法：按指定大小拆分列表，不足一个size时返回剩余元素
    public static <T> List<List<T>> splitListWithRemainder(List<T> list, final int size) {
        List<List<T>> newList = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            newList.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return newList;
    }

    @Override
    public void aiMeituanMapping() {
        int poolSize = 1;
//        List<String> idList = hotelInfoMeituanService.selectNotMappingIdList();
        List<HotelInfoMeituan> meituanList = hotelInfoMeituanService.selectHotelInfoNotMapping(null, 1);
        List<List<HotelInfoMeituan>> subList = splitList(meituanList, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        List<HotelInfoMeituan> subMeituanList = subList.get(finalI);
                        for (HotelInfoMeituan hotel : subMeituanList) {
                            AIMappingResultDto aiMappingResultDto;
                            try {
                                aiMappingResultDto = requestAI(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                            } catch (Exception e) {
                                aiMappingResultDto = new AIMappingResultDto();
                                aiMappingResultDto.setMark(e.getMessage());
                            }
                            aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_MT.getValue());
                            aiMappingResultDto.setPlatId(hotel.getId());
                            tmpJdJdbMappingMapper.insert(aiMappingResultDto);
                            Thread.sleep(500);
                        }
                    } catch (Exception e) {
                        log.error("美团执行错误: ", e);
                    }
                }
            });
            try {
                Thread.sleep(7000);
            } catch (InterruptedException e) {
                log.error("美团线程等待错误: ", e);
            }
        }
        executorService.shutdown();
        log.info("美团执行结束: ...");
    }

    @Override
    public void aiKeTanMapping() {
        int poolSize = 5;
        List<TmpAiScoringDto> meituanList = tmpJdJdbMappingMapper.selectKeTanList();
        tmpJdJdbMappingMapper.deleteKeTanScore();
        List<List<TmpAiScoringDto>> subList = splitList(meituanList, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        List<TmpAiScoringDto> subMeituanList = subList.get(finalI);
                        for (TmpAiScoringDto hotel : subMeituanList) {
                            AIMappingResultDto aiMappingResultDto;
                            try {
                                aiMappingResultDto = requestAIKetan(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                            } catch (Exception e) {
                                aiMappingResultDto = new AIMappingResultDto();
                                aiMappingResultDto.setMark(e.getMessage());
                            }
                            aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_MT.getValue());
                            aiMappingResultDto.setPlatId(hotel.getId().toString());
                            tmpJdJdbMappingMapper.insertKeTan(aiMappingResultDto);
                            Thread.sleep(200);
                        }
                    } catch (Exception e) {
                        log.error("科坦执行错误: ", e);
                    }
                }
            });
            try {
                Thread.sleep(7000);
            } catch (InterruptedException e) {
                log.error("科坦线程等待错误: ", e);
            }
        }

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        try {
            // 等待最多60秒，确保所有任务完成
            if (!executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS)) {
                log.warn("部分任务未在指定时间内完成");
            }
        } catch (InterruptedException e) {
            log.error("等待线程池关闭时发生中断: ", e);
        }

        log.info("科坦执行结束: ...");
    }

    private AIMappingResultDto requestAIKetan(String name, String cityName, String address, String phone) {
        JSONObject params = new JSONObject();

        JSONObject inputs = new JSONObject();
        inputs.put("name", name);
        inputs.put("city", cityName);
        inputs.put("address", address);
        inputs.put("phone", phone);
        params.put("inputs", inputs);

        params.put("response_mode", "blocking");
        params.put("user", "hotel-mapping-system");
        JSONObject jsonObject = null;
        try {
            jsonObject = HttpUtil.postJson(DifyURL + "/workflows/run", "Bearer " + workflowsKey, params);
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        log.info("ai响应结果: {}", jsonObject);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject outputs = data.getJSONObject("outputs");
        if (outputs.size() == 0) {
            throw new RuntimeException(data.getString("error"));
        }
        String text = outputs.getString("text");
        JSONObject textObj = JSONObject.parseObject(text);
        String id = textObj.getString("id");
        Integer score = textObj.getInteger("score");
        String reason = textObj.getString("reason");
        log.info("酒店匹配结果: id={}, score={}", id, score);
        AIMappingResultDto aiMappingResultDto = new AIMappingResultDto();
        aiMappingResultDto.setId(id);
        aiMappingResultDto.setScore(score);
        aiMappingResultDto.setReason(reason);
        return aiMappingResultDto;
    }

    private List<AIMappingResultDto> requestAIKetanList(com.alibaba.fastjson.JSONArray list) {
        JSONObject params = new JSONObject();

        JSONObject inputs = new JSONObject();
        inputs.put("hotelList", list.toJSONString());

        params.put("inputs", inputs);

        params.put("response_mode", "blocking");
        params.put("user", "hotel-mapping-system");
        JSONObject jsonObject = null;
        try {
            jsonObject = HttpUtil.postJson(DifyURL + "/workflows/run", "Bearer " + workflowBatchKey, params);
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        log.info("ai响应结果: {}", jsonObject);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject outputs = data.getJSONObject("outputs");
        if (outputs.size() == 0) {
            throw new RuntimeException(data.getString("error"));
        }
        String text = outputs.getString("output");
        List<String> textObj = null;
        try {
            textObj = JSONArray.parseArray(text, String.class);
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        log.info("酒店匹配结果: output={}", text);
        List<AIMappingResultDto> resultDtos = new ArrayList<>();
        for (String s : textObj) {
            AIMappingResultDto aiMappingResultDto = JSONObject.parseObject(s, AIMappingResultDto.class);
            resultDtos.add(aiMappingResultDto);
        }
        return resultDtos;
    }

    @Override
    public void aiQiantaoMapping() {
        int poolSize = 1;
//        List<String> idList = hotelInfoQiantaoService.selectNotMappingIdList();
        List<HotelInfoQiantao> qiantaoList = hotelInfoQiantaoService.selectHotelInfoNotMapping(null, 1);
        List<List<HotelInfoQiantao>> subList = splitList(qiantaoList, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        List<HotelInfoQiantao> subs = subList.get(finalI);
                        for (HotelInfoQiantao hotel : subs) {
                            AIMappingResultDto aiMappingResultDto;
                            try {
                                aiMappingResultDto = requestAI(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                            } catch (Exception e) {
                                aiMappingResultDto = new AIMappingResultDto();
                                aiMappingResultDto.setMark(e.getMessage());
                            }
                            aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_QT.getValue());
                            aiMappingResultDto.setPlatId(hotel.getId());
                            tmpJdJdbMappingMapper.insert(aiMappingResultDto);
                            Thread.sleep(500);
                        }
                    } catch (Exception e) {
                        log.error("千淘执行错误: ", e);
                    }
                }

            });
            try {
                Thread.sleep(7000);
            } catch (InterruptedException e) {
                log.error("千淘线程等待错误: ", e);
            }
        }
        executorService.shutdown();
        log.info("千淘执行结束: ...");
    }

    @Override
    public void aiHsjlMapping() {
        int poolSize = 1;
//        List<String> idList = hotelInfoHsjlService.selectNotMappingIdList();
        List<HotelInfoHsjl> qiantaoList = hotelInfoHsjlService.selectHotelInfoNotMapping(null, 1);
        List<List<HotelInfoHsjl>> subList = splitList(qiantaoList, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(() -> {
                try {
                    List<HotelInfoHsjl> subs = subList.get(finalI);
                    for (HotelInfoHsjl hotel : subs) {
                        AIMappingResultDto aiMappingResultDto;
                        try {
                            aiMappingResultDto = requestAI(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                        } catch (RuntimeException e) {
                            aiMappingResultDto = new AIMappingResultDto();
                            aiMappingResultDto.setMark(e.getMessage());
                        }
                        aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_HSJL.getValue());
                        aiMappingResultDto.setPlatId(hotel.getId());
                        tmpJdJdbMappingMapper.insert(aiMappingResultDto);
                        Thread.sleep(500);
                    }
                } catch (Exception e) {
                    log.error("红色加力执行错误: ", e);
                }
            });
            try {
                Thread.sleep(7000);
            } catch (InterruptedException e) {
                log.error("红色加力线程等待错误: ", e);
            }
        }
        executorService.shutdown();
        log.info("红色加力执行结束: ...");
    }

    @Override
    public void aiHsjlxyMapping() {
        int poolSize = 1;
        List<HotelInfoHsjlxy> list = hotelInfoHsjlxyService.selectHotelInfoxyNotMapping(null, 1);
        List<List<HotelInfoHsjlxy>> subList = splitList(list, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(() -> {
                try {
                    List<HotelInfoHsjlxy> subs = subList.get(finalI);
                    for (HotelInfoHsjlxy hotel : subs) {
                        AtomicInteger count = new AtomicInteger(1);
                        while (true) {
                            try {
                                AIMappingResultDto aiMappingResultDto;
                                try {
                                    aiMappingResultDto = requestAI(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                                } catch (RuntimeException e) {
                                    aiMappingResultDto = new AIMappingResultDto();
                                    aiMappingResultDto.setMark(e.getMessage());
                                }
                                aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                                aiMappingResultDto.setPlatId(hotel.getId());
                                tmpJdJdbMappingMapper.insert(aiMappingResultDto);
                                Thread.sleep(500);
                                break;
                            } catch (Exception e) {
                                log.error("红色加力协议调用错误: ", e);
                                if (count.getAndAdd(1) > 10) {
                                    break;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("红色加力协议执行错误: ", e);
                }
            });
            try {
                Thread.sleep(7000);
            } catch (InterruptedException e) {
                log.error("红色加力协议线程等待错误: ", e);
            }
        }
        executorService.shutdown();
        log.info("红色加力协议执行结束: ...");
    }

    /**
     * 处理异常的ai数据
     */
    @Override
    public void aiLastMapping() {
        List<AIMappingResultDto> interfacePlatList = tmpJdJdbMappingMapper.selectInterfacePlatList();
        log.info("获取到需要处理的映射的数据: {}", interfacePlatList.size());
        if (CollectionUtils.isEmpty(interfacePlatList)) {
            return;
        }

        Map<String, List<AIMappingResultDto>> collect = interfacePlatList.stream().collect(Collectors.groupingBy(AIMappingResultDto::getInterfacePlat));
        for (Map.Entry<String, List<AIMappingResultDto>> entry : collect.entrySet()) {
            List<AIMappingResultDto> value = entry.getValue();
            List<String> idList = value.stream().map(AIMappingResultDto::getPlatId).collect(Collectors.toList());
            Map<String, String> idMap = value.stream().collect(Collectors.toMap(AIMappingResultDto::getPlatId, AIMappingResultDto::getId));
            switch (entry.getKey()) {
                case "2000023":
                    List<HotelInfoMeituan> meituanList = hotelInfoMeituanService.selectListByIdList(idList);
                    for (HotelInfoMeituan hotel : meituanList) {
                        AIMappingResultDto aiMappingResultDto;
                        try {
                            log.info("开始处理美团映射数据: {}", JSONObject.toJSONString(hotel));
                            aiMappingResultDto = requestAI(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                            log.info("处理美团映射数据结束: {}", JSONObject.toJSONString(aiMappingResultDto));
                            aiMappingResultDto.setMark(null);
                        } catch (Exception e) {
                            log.error("处理美团映射数据失败: ", e);
                            aiMappingResultDto = new AIMappingResultDto();
                            aiMappingResultDto.setMark(e.getMessage());
                        }
                        aiMappingResultDto.setTmpId(Long.valueOf(idMap.get(hotel.getId())));
                        aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_MT.getValue());
                        aiMappingResultDto.setPlatId(hotel.getId());
                        tmpJdJdbMappingMapper.update(aiMappingResultDto);
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            log.info("美团线程睡眠失败: ", e);
                        }
                    }
                    break;
                case "2000010":
                    List<HotelInfoQiantao> hotelList = hotelInfoQiantaoService.selectListByIdList(idList);
                    for (HotelInfoQiantao hotel : hotelList) {
                        AIMappingResultDto aiMappingResultDto;
                        try {
                            log.info("开始处理千淘映射数据: {}", JSONObject.toJSONString(hotel));
                            aiMappingResultDto = requestAI(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                            log.info("处理千淘映射数据结束: {}", JSONObject.toJSONString(aiMappingResultDto));
                            aiMappingResultDto.setMark(null);
                        } catch (Exception e) {
                            log.error("处理千淘映射数据失败: ", e);
                            aiMappingResultDto = new AIMappingResultDto();
                            aiMappingResultDto.setMark(e.getMessage());
                        }
                        aiMappingResultDto.setTmpId(Long.valueOf(idMap.get(hotel.getId())));
                        aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_QT.getValue());
                        aiMappingResultDto.setPlatId(hotel.getId());
                        tmpJdJdbMappingMapper.update(aiMappingResultDto);
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            log.info("千淘线程睡眠失败: ", e);
                        }
                    }
                    break;
                case "2000054":
                    List<HotelInfoHsjl> list = hotelInfoHsjlService.selectListByIdList(idList);
                    for (HotelInfoHsjl hotel : list) {
                        AIMappingResultDto aiMappingResultDto;
                        try {
                            log.info("开始处理红色加力映射数据: {}", JSONObject.toJSONString(hotel));
                            aiMappingResultDto = requestAI(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                            log.info("处理红色加力映射数据结束: {}", JSONObject.toJSONString(aiMappingResultDto));
                            aiMappingResultDto.setMark(null);
                        } catch (Exception e) {
                            log.error("处理红色加力映射数据失败: ", e);
                            aiMappingResultDto = new AIMappingResultDto();
                            aiMappingResultDto.setMark(e.getMessage());
                        }
                        aiMappingResultDto.setTmpId(Long.valueOf(idMap.get(hotel.getId())));
                        aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_HSJL.getValue());
                        aiMappingResultDto.setPlatId(hotel.getId());
                        tmpJdJdbMappingMapper.update(aiMappingResultDto);
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            log.info("红色加力线程睡眠失败: ", e);
                        }
                    }
                    break;
                case "2000077":
                    List<HotelInfoHsjlxy> hotelInfoHsjlxieList = hotelInfoHsjlxyService.selectListByIdList(idList);
                    for (HotelInfoHsjlxy hotel : hotelInfoHsjlxieList) {
                        AIMappingResultDto aiMappingResultDto;
                        try {
                            log.info("开始处理红色加力协议映射数据: {}", JSONObject.toJSONString(hotel));
                            aiMappingResultDto = requestAI(hotel.getName(), hotel.getCityName(), hotel.getAddressLine(), hotel.getPhone());
                            log.info("处理红色加力协议映射数据结束: {}", JSONObject.toJSONString(aiMappingResultDto));
                            aiMappingResultDto.setMark(null);
                        } catch (Exception e) {
                            log.error("处理红色加力协议映射数据失败: ", e);
                            aiMappingResultDto = new AIMappingResultDto();
                            aiMappingResultDto.setMark(e.getMessage());
                        }
                        aiMappingResultDto.setTmpId(Long.valueOf(idMap.get(hotel.getId())));
                        aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                        aiMappingResultDto.setPlatId(hotel.getId());
                        tmpJdJdbMappingMapper.update(aiMappingResultDto);
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            log.info("红色加力协议线程睡眠失败: ", e);
                        }
                    }
                    break;
            }

        }
    }

    @Override
    public void uploadKnowledge() {
//        boolean andSet = atomicBoolean.getAndSet(true);
//        if (andSet) {
//            return;
//        }
        log.info("开始上传知识库");
        int pageSize = 999;
        for (int pageNo = 0; true; pageNo++) {
            List<String> jsonList = jdJdbService.selectAllJson(pageNo * pageSize, pageSize);
            if (CollectionUtils.isEmpty(jsonList)) {
                break;
            }
            String text = jsonList.stream().collect(Collectors.joining("\n"));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", pageNo);
            jsonObject.put("text", text);
            uploadText(jsonObject);
        }
        log.info("知识库上传完毕");
    }

    @Override
    public void uploadKnowledgeForScore() {
//        boolean andSet = atomicBoolean.getAndSet(true);
//        if (andSet) {
//            return;
//        }
        log.info("开始上传知识库");
        int pageSize = 999;
        for (int pageNo = 0; true; pageNo++) {
//            List<String> jsonList = jdJdbService.selectAllJsonForScore(pageNo * pageSize, pageSize);
            List<String> jsonList = jdJdbService.selectAllJson(pageNo * pageSize, pageSize);
            if (CollectionUtils.isEmpty(jsonList)) {
                break;
            }
            jsonList = jsonList.stream().map(oneText -> {
                JSONObject hotelObj = JSONObject.parse(oneText);
                hotelObj.put("name", HotelDataFormatUtil.formatName(hotelObj.getString("name")));
                hotelObj.put("address", HotelDataFormatUtil.formatAddress(hotelObj.getString("address")));
                hotelObj.put("phone", HotelDataFormatUtil.formatPhone(hotelObj.getString("phone")));
                return hotelObj.toJSONString();
            }).collect(Collectors.toList());

            String text = jsonList.stream().collect(Collectors.joining("\n"));
            difyApiService.createDocumentByText(pageNo + "", text, null);
        }
        log.info("知识库上传完毕");
    }

    @Override
    public void uploadKnowledgeByDocument(Integer pageNumber) {
//        boolean andSet = atomicBoolean.getAndSet(true);
//        if (andSet) {
//            return;
//        }
        log.info("开始上传知识库");
        int pageSize = 999;

        for (int pageNo = pageNumber; true; pageNo++) {
            log.info("上传知识库第{}页", pageNo);
            List<String> jsonList = jdJdbService.selectAllJson(pageNo * pageSize, pageSize);
            if (CollectionUtils.isEmpty(jsonList)) {
                break;
            }

            // 创建线程池
            int threadPoolSize = Runtime.getRuntime().availableProcessors() * 2;
            ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);
            CountDownLatch latch = new CountDownLatch(jsonList.size());

            // 提交任务到线程池
            for (String oneText : jsonList) {
                executorService.submit(() -> {
                    try {
                        if (StringUtils.isBlank(oneText)) {
                            log.warn("跳过空文本");
                            return;
                        }
                        JSONObject hotelObj = JSONObject.parse(oneText);
                        hotelObj.put("name", HotelDataFormatUtil.formatName(hotelObj.getString("name")));
                        hotelObj.put("address", HotelDataFormatUtil.formatAddress(hotelObj.getString("address")));
                        hotelObj.put("phone", HotelDataFormatUtil.formatPhone(hotelObj.getString("phone")));
                        String documentId = difyApiService.createDocumentByText(hotelObj.getString("id"), hotelObj.toJSONString(), null);
                        JdJdb jdJdb = new JdJdb();
                        jdJdb.setId(hotelObj.getString("id"));
                        jdJdb.setAiDocumentId(documentId);
                        jdJdbService.updateJdJdb(jdJdb);
                    } catch (Exception e) {
                        log.error("处理文档时发生错误: {}", ExceptionUtil.getStackTrace(e));
                    } finally {
                        latch.countDown(); // 每个任务完成后计数器减一
                    }
                });
            }

            // 等待所有任务完成
            try {
                latch.await();
            } catch (InterruptedException e) {
                log.error("等待线程池任务完成时发生中断: {}", ExceptionUtil.getStackTrace(e));
            }

            // 关闭线程池
            executorService.shutdown();
        }
        log.info("知识库上传完毕");
    }

    @Override
    public void uploadKnowledgeByCity(String knowledgeId) {
        log.info("开始上传知识库");
        List<String> cityIds = jdJdbService.selectCityId();
        // 创建线程池
        int threadPoolSize = Runtime.getRuntime().availableProcessors() * 2;
        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);
        CountDownLatch latch = new CountDownLatch(cityIds.size());
        cityIds.forEach(cityId -> {
            log.info("上传知识库cityid{}", cityId);
            executorService.submit(() -> {
                try {
                    List<JdJdb> jsonList = jdJdbService.selectByCityId(cityId, null);
                    if (CollectionUtils.isEmpty(jsonList)) {
                        return;
                    }

                    List<HotelInfoAiDTO> aiDTOS = jsonList.stream().map(oneText -> {
                        log.info("文本------{}", JSONObject.toJSONString(oneText));
                        HotelInfoAiDTO hotelInfoAiDTO = new HotelInfoAiDTO();
                        hotelInfoAiDTO.setId(oneText.getId());
                        hotelInfoAiDTO.setName(HotelDataFormatUtil.formatName(oneText.getJdmc()));
                        hotelInfoAiDTO.setPhone(HotelDataFormatUtil.formatPhone(oneText.getJddh()));
                        hotelInfoAiDTO.setAddress(HotelDataFormatUtil.formatAddress(oneText.getJddz()));
                        hotelInfoAiDTO.setCity(oneText.getCityName());
//                        hotelInfoAiDTO.setPlatform(oneText.getInterfacePlat());
                        return hotelInfoAiDTO;
                    }).collect(Collectors.toList());

                    String text = aiDTOS.stream().map(JSONObject::toJSONString).collect(Collectors.joining("\n"));
                    difyApiService.createDocumentByText(cityId == null ? "其他" : cityId, text, knowledgeId);
                } catch (Exception e) {
                    log.error("上传知识库----{}", ExceptionUtil.getStackTrace(e));
                } finally {
                    latch.countDown();
                }
            }, cityId);

        });
        // 等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("等待线程池任务完成时发生中断: {}", ExceptionUtil.getStackTrace(e));
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("知识库上传完毕");
    }

    //增量手动传新知识库保存知识库id
    @Override
    public void uploadKnowledgeByCity(String knowledgeId, Date date, List<String> cityList) {
        log.info("开始上传知识库");
        List<String> cityIds = null;
        if (cityList == null || cityList.isEmpty()) {
            cityIds = jdJdbService.selectCityId();
        } else {
            cityIds = cityList;
        }
        // 创建线程池
        int threadPoolSize = Runtime.getRuntime().availableProcessors() * 2;
        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);
        CountDownLatch latch = new CountDownLatch(cityIds.size());
        cityIds.forEach(cityId -> {
            log.info("上传知识库cityid{}", cityId);
            executorService.submit(() -> {
                try {
                    List<JdJdb> jsonList = jdJdbService.selectByCityId(cityId, date);
                    if (CollectionUtils.isEmpty(jsonList)) {
                        return;
                    }

                    List<HotelInfoAiDTO> aiDTOS = jsonList.stream().map(oneText -> {
                        log.info("文本------{}", JSONObject.toJSONString(oneText));
                        HotelInfoAiDTO hotelInfoAiDTO = new HotelInfoAiDTO();
                        hotelInfoAiDTO.setId(oneText.getId());
                        hotelInfoAiDTO.setName(HotelDataFormatUtil.formatName(oneText.getJdmc()));
                        hotelInfoAiDTO.setPhone(HotelDataFormatUtil.formatPhone(oneText.getJddh()));
                        hotelInfoAiDTO.setAddress(HotelDataFormatUtil.formatAddress(oneText.getJddz()));
                        hotelInfoAiDTO.setCity(oneText.getCityName());
//                        hotelInfoAiDTO.setPlatform(oneText.getInterfacePlat());
                        return hotelInfoAiDTO;
                    }).collect(Collectors.toList());

                    String text = aiDTOS.stream().map(JSONObject::toJSONString).collect(Collectors.joining("\n"));
                    String documentId = difyApiService.createDocumentByText(StringUtils.isEmpty(cityId) ? "其他" : cityId, text, knowledgeId);

                    try {
                        HashMap<String, String> map = new HashMap<>();
                        map.put("city_name",aiDTOS.get(0).getCity());
                        difyApiService.updateKnowledgeMetadata( map,documentId,null);
                    } catch (Exception e) {
                        log.error("保存metadata报错----{}", ExceptionUtil.getStackTrace(e));
                    }

                    jdJdbService.updateKnowledgeIdByCityIdAndDate(cityId, knowledgeId, date);
                } catch (Exception e) {
                    log.error("上传知识库----{}", ExceptionUtil.getStackTrace(e));
                } finally {
                    latch.countDown();
                }
            }, cityId);

        });
        // 等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("等待线程池任务完成时发生中断: {}", ExceptionUtil.getStackTrace(e));
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("知识库上传完毕");
    }

    @Override
    public void uploadKnowledgeByCity2(String knowledgeId, Date date, List<String> cityList) {
        log.info("开始上传知识库");
        List<String> cityIds = null;
        if (cityList == null || cityList.isEmpty()) {
            cityIds = jdJdbService.selectCityId();
        } else {
            cityIds = cityList;
        }

        // 创建线程池
        int threadPoolSize = Runtime.getRuntime().availableProcessors() * 2;
        ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);
        CountDownLatch latch = new CountDownLatch(cityIds.size());
        cityIds.forEach(cityId -> {
            log.info("上传知识库cityid{}", cityId);
            executorService.submit(() -> {
                try {
                    List<JdJdb> jsonList = jdJdbService.selectByCityId(cityId, date);
                    if (CollectionUtils.isEmpty(jsonList)) {
                        return;
                    }

                    List<HotelInfoAiDTO> aiDTOS = jsonList.stream().map(oneText -> {
                        log.info("文本------{}", JSONObject.toJSONString(oneText));
                        HotelInfoAiDTO hotelInfoAiDTO = new HotelInfoAiDTO();
                        hotelInfoAiDTO.setId(oneText.getId());
                        hotelInfoAiDTO.setName(HotelDataFormatUtil.formatName(oneText.getJdmc()));
                        hotelInfoAiDTO.setPhone(HotelDataFormatUtil.formatPhone(oneText.getJddh()));
                        hotelInfoAiDTO.setAddress(HotelDataFormatUtil.formatAddress(oneText.getJddz()));
                        hotelInfoAiDTO.setCity(oneText.getCityName());
//                        hotelInfoAiDTO.setPlatform(oneText.getInterfacePlat());
                        return hotelInfoAiDTO;
                    }).collect(Collectors.toList());

                    String text = aiDTOS.stream().map(JSONObject::toJSONString).collect(Collectors.joining("\n"));
                    String documentId =     difyApiService2.createDocumentByText(StringUtils.isEmpty(cityId) ? "其他" : cityId, text, knowledgeId);

                    try {
                        HashMap<String, String> map = new HashMap<>();
                        map.put("city_name",aiDTOS.get(0).getCity());
                        difyApiService.updateKnowledgeMetadata( map,documentId,null);
                    } catch (Exception e) {
                        log.error("保存metadata报错----{}", ExceptionUtil.getStackTrace(e));
                    }

                    jdJdbService.updateKnowledgeIdByCityIdAndDate(cityId, knowledgeId, date);
                } catch (Exception e) {
                    log.error("上传知识库----{}", ExceptionUtil.getStackTrace(e));
                } finally {
                    latch.countDown();
                }
            }, cityId);

        });
        // 等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("等待线程池任务完成时发生中断: {}", ExceptionUtil.getStackTrace(e));
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("知识库上传完毕");
    }

    @Override
    public void formatJdjdb() {
        log.info("开始格式化");
        int pageSize = 500;
        int poolSize = Runtime.getRuntime().availableProcessors() * 2; // 根据CPU核心数设定线程池大小
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        int count = jdJdbService.count();
        int totalPages = (int) Math.ceil((double) count / pageSize); // 计算总页数，确保向上取整

        for (int pageNo = 0; pageNo < totalPages; pageNo++) { // 修改循环条件，确保所有页都处理
            int finalPageNo = pageNo;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    List<JdJdb> jsonList = jdJdbService.selectAll(finalPageNo * pageSize, pageSize);
                    if (CollectionUtils.isEmpty(jsonList)) {
                        return;
                    }

                    jsonList.forEach(oneText -> {
                        try {
                            GeoStructureQueryReq geoStructureQueryReq = GeoStructureQueryReq.builder()
                                    .id(oneText.getCityId())
                                    .idType("city")
                                    .includeParents(Boolean.TRUE)
                                    .includeFullChildren(false)
                                    .build();
                            log.info("获取省信息入参{}", JSON.toJSONString(geoStructureQueryReq));
                            AjaxResult<GeoHierarchyNode> geoStructureById = geoHierarchyService.getGeoStructureById(geoStructureQueryReq);
                            log.info("获取省信息出参{}", JSON.toJSONString(geoStructureById));

                            if ((int) geoStructureById.get("code") == 500) {
                                log.error("获取国家信息失败{}", geoStructureById.get("message"));
                            } else if ((int) geoStructureById.get("code") == 200) {
                                oneText.setCountryId(geoStructureById.getData().getId());
                                oneText.setCountryName(geoStructureById.getData().getName());
                                if (CollectionUtils.isNotEmpty(geoStructureById.getData().getChildren())) {
                                    GeoHierarchyNode orDefault = geoStructureById.getData().getChildren().get(0);
                                    oneText.setProvinceId(orDefault.getId());
                                    oneText.setProvinceName(orDefault.getName());
                                }
                            }
                        } catch (Exception e) {
                            log.error("格式化数据时发生错误id: {},getCityId(),{}", oneText.getId(), oneText.getCityId());
                            log.error("格式化数据时发生错误: {}", ExceptionUtil.getStackTrace(e));
                        }
                    });

                    jdJdbService.updateAllForeElements(jsonList); // 更新当前批次数据

                } catch (Exception e) {
                    log.error("分页处理异常：pageNo={}", finalPageNo, e);
                }
            }, executorService);
            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        executorService.shutdown(); // 关闭线程池
        log.info("格式化完毕");
    }

    @Override
    public void uploadKnowledge(int pageNo) {
//        boolean andSet = atomicBoolean.getAndSet(true);
//        if (andSet) {
//            return;
//        }
        log.info("开始上传知识库");
        int pageSize = 999;
        List<String> jsonList = jdJdbService.selectAllJson(pageNo * pageSize, pageSize);
        if (CollectionUtils.isEmpty(jsonList)) {
            return;
        }
        String text = jsonList.stream().collect(Collectors.joining("\n"));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", pageNo);
        jsonObject.put("text", text);
        uploadText(jsonObject);
        log.info("知识库上传完毕");
    }

    @Override
    public void aiKeTanMultipleMapping() {
        int poolSize = 10;
        List<TmpAiScoringDto> meituanList = tmpJdJdbMappingMapper.selectKeTanList();
        tmpJdJdbMappingMapper.deleteKeTanScore();
        List<List<TmpAiScoringDto>> subList = splitList(meituanList, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        int multipleSize = 7;
                        List<TmpAiScoringDto> subMeituanList = subList.get(finalI);
                        List<List<TmpAiScoringDto>> sendList = splitListWithRemainder(subMeituanList, multipleSize);

                        for (List<TmpAiScoringDto> innerList : sendList) {
                            List<AIMappingResultDto> resultDtos = new ArrayList<>();
                            JSONArray params = new JSONArray();
                            for (TmpAiScoringDto hotel : innerList) {
                                JSONObject hotelObj = new JSONObject();
                                hotelObj.put("name", hotel.getName());
                                hotelObj.put("city", hotel.getCityName());
                                hotelObj.put("address", hotel.getAddressLine());
                                hotelObj.put("phone", hotel.getPhone());
                                params.add(hotelObj);
                            }
                            try {
                                resultDtos = difyApiService.runBatchWorkflow(params);
                                if (resultDtos == null) {
                                    return;
                                }
                            } catch (Exception e) {
                                log.error(ExceptionUtil.getStackTrace(e));
                            }
                            for (int j = 0; j < resultDtos.size(); j++) {
                                AIMappingResultDto aiMappingResultDto = resultDtos.get(j);
                                aiMappingResultDto.setInterfacePlat(PlatEnum.PLAT_MT.getValue());
                                aiMappingResultDto.setPlatId(innerList.get(j).getId().toString());
                            }
                            tmpJdJdbMappingMapper.batchInsertKeTan(resultDtos);
                        }
                    } catch (Exception e) {
                        log.error("科坦执行错误: ", e);
                    }
                }
            });
            try {
                Thread.sleep(7000);
            } catch (InterruptedException e) {
                log.error("科坦线程等待错误: ", e);
            }
        }

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        try {
            // 等待最多60秒，确保所有任务完成
            if (!executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS)) {
                log.warn("部分任务未在指定时间内完成");
            }
        } catch (InterruptedException e) {
            log.error("等待线程池关闭时发生中断: ", e);
        }

        log.info("科坦执行结束: ...");
    }

    private AIMappingResultDto requestAI(String name, String cityName, String address, String phone) {
        JSONObject params = new JSONObject();

        JSONObject inputs = new JSONObject();
        inputs.put("name", name);
        inputs.put("city", cityName);
        inputs.put("address", address);
        inputs.put("phone", phone);
        params.put("inputs", inputs);

        params.put("response_mode", "blocking");
        params.put("user", "hotel-mapping-system");
        JSONObject jsonObject = HttpUtil.postJson(PropertiesUtil.getProp("dify.url"), "Bearer " + PropertiesUtil.getProp("dify.token"), params);
        log.info("ai响应结果: {}", jsonObject);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject outputs = data.getJSONObject("outputs");
        if (outputs.size() == 0) {
            throw new RuntimeException(data.getString("error"));
        }
        String text = outputs.getString("text");
        JSONObject textObj = JSONObject.parseObject(text);
        String id = textObj.getString("id");
        Integer score = textObj.getInteger("score");
        String reason = textObj.getString("reason");
        log.info("酒店匹配结果: id={}, score={}", id, score);
        AIMappingResultDto aiMappingResultDto = new AIMappingResultDto();
        aiMappingResultDto.setId(id);
        aiMappingResultDto.setScore(score);
        aiMappingResultDto.setReason(reason);
        return aiMappingResultDto;
    }


    private void uploadText(JSONObject jsonObject) {
        AtomicInteger count = new AtomicInteger(0);
        int maxCount = 20;
        while (true) {
            jsonObject.put("indexing_technique", "high_quality");
            JSONObject processRule = new JSONObject();
            processRule.put("mode", "custom");
            JSONObject rules = new JSONObject();
            rules.put("pre_processing_rules", new JSONArray());
            JSONObject segmentation = new JSONObject();
            segmentation.put("separator", "\\n");
            segmentation.put("max_tokens", 1000);
            rules.put("segmentation", segmentation);
            processRule.put("rules", rules);
            jsonObject.put("process_rule", processRule);

            JSONObject responseObj = HttpUtil.postJson(PropertiesUtil.getProp("dify.knowledge.url"), "Bearer " + PropertiesUtil.getProp("dify.knowledge.token"), jsonObject);
            JSONObject document = responseObj.getJSONObject("document");
            Object id = document.get("id");

            if (count.addAndGet(1) > maxCount
                    || (Objects.nonNull(responseObj) && StringUtils.isNotBlank(responseObj.getString("batch")))) {
                break;
            }
        }
    }
}
