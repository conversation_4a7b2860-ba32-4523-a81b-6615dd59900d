package com.ltgj.supplier.map.response;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * 搜索结果范围内下属poi地址
 * <AUTHOR>
 * @date 2023-05-04
 */
public class BmaPlaceSearchChildren implements Serializable {

    private String uid;

    private String name;

    @JSONField(name = "show_name")
    private String showName;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }
}
