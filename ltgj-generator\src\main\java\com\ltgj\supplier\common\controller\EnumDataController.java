package com.ltgj.supplier.common.controller;

import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.supplier.common.dto.CustomerTypeDTO;
import com.ltgj.supplier.common.dto.EnumCategoryDTO;
import com.ltgj.supplier.common.dto.HotelFacilityDetailDTO;
import com.ltgj.supplier.common.service.EnumDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 枚举数据控制器
 * 提供统一的枚举数据接口供前端调用
 */
@RestController
@RequestMapping("/api/enums")
public class EnumDataController extends BaseController {

    @Autowired
    private EnumDataService enumDataService;

    /**
     * 获取所有枚举分类信息
     * GET /api/enums
     */
    @GetMapping
    public AjaxResult getAllEnumCategories() {
        try {
            Map<String, Object> result = enumDataService.getAllEnumCategories();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取所有枚举分类信息失败", e);
            return AjaxResult.error("获取枚举数据失败");
        }
    }

    /**
     * 根据分类key获取枚举数据
     * GET /api/enums/{categoryKey}
     */
    @GetMapping("/{categoryKey}")
    public AjaxResult getEnumDataByCategory(@PathVariable String categoryKey) {
        try {
            Object result = enumDataService.getEnumDataByCategory(categoryKey);
            if (result == null) {
                return AjaxResult.error("不支持的枚举分类: " + categoryKey);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取枚举数据失败，分类: " + categoryKey, e);
            return AjaxResult.error("获取枚举数据失败");
        }
    }

    /**
     * 获取客户类型政策信息（专用接口）
     * GET /api/enums/customerType
     */
    @GetMapping("/customerType")
    public AjaxResult getCustomerTypePolicyInfo() {
        try {
            Map<String, Object> result = enumDataService.getCustomerTypePolicyInfo();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取客户类型政策信息失败", e);
            return AjaxResult.error("获取客户类型政策信息失败");
        }
    }

    /**
     * 获取客户类型列表（返回 customerType 和 customerDesc 字段）
     * GET /api/enums/customerType/list
     */
    @GetMapping("/customerType/list")
    public AjaxResult getCustomerTypeList() {
        try {
            List<CustomerTypeDTO> result = enumDataService.getCustomerTypeList();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取客户类型列表失败", e);
            return AjaxResult.error("获取客户类型列表失败");
        }
    }



    /**
     * 获取通用格式的枚举分类数据
     * GET /api/enums/category/{categoryKey}
     */
    @GetMapping("/category/{categoryKey}")
    public AjaxResult getEnumCategoryDTO(@PathVariable String categoryKey) {
        try {
            EnumCategoryDTO result = enumDataService.getEnumCategoryDTO(categoryKey);
            if (result == null) {
                return AjaxResult.error("不支持的枚举分类: " + categoryKey);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取枚举分类数据失败，分类: " + categoryKey, e);
            return AjaxResult.error("获取枚举分类数据失败");
        }
    }

    /**
     * 获取政策信息（客户类型的别名接口）
     * GET /api/enums/policyInfo
     */
    @GetMapping("/policyInfo")
    public AjaxResult getPolicyInfo() {
        return getCustomerTypePolicyInfo();
    }

    /**
     * 获取酒店设施信息（使用详细设施信息）
     * GET /api/enums/facility
     */
    @GetMapping("/facility")
    public AjaxResult getHotelFacilityInfo() {
        try {
            Map<String, Object> result = enumDataService.getHotelFacilityDetailInfo();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取酒店设施信息失败", e);
            return AjaxResult.error("获取酒店设施信息失败");
        }
    }

    /**
     * 获取酒店设施列表（使用详细设施信息）
     * GET /api/enums/facility/list
     */
    @GetMapping("/facility/list")
    public AjaxResult getHotelFacilityList() {
        try {
            List<HotelFacilityDetailDTO> result = enumDataService.getHotelFacilityDetailList();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取酒店设施列表失败", e);
            return AjaxResult.error("获取酒店设施列表失败");
        }
    }

    /**
     * 获取酒店设施详细信息
     * GET /api/enums/facilityDetail
     */
    @GetMapping("/facilityDetail")
    public AjaxResult getHotelFacilityDetailInfo() {
        try {
            Map<String, Object> result = enumDataService.getHotelFacilityDetailInfo();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取酒店设施详细信息失败", e);
            return AjaxResult.error("获取酒店设施详细信息失败");
        }
    }

    /**
     * 获取酒店设施详细信息列表（返回完整属性配置）
     * GET /api/enums/facilityDetail/list
     */
    @GetMapping("/facilityDetail/list")
    public AjaxResult getHotelFacilityDetailList() {
        try {
            List<HotelFacilityDetailDTO> result = enumDataService.getHotelFacilityDetailList();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取酒店设施详细信息列表失败", e);
            return AjaxResult.error("获取酒店设施详细信息列表失败");
        }
    }

    /**
     * 根据设施ID获取设施详细信息
     * GET /api/enums/facilityDetail/id/{facilityId}
     */
    @GetMapping("/facilityDetail/id/{facilityId}")
    public AjaxResult getFacilityDetailById(@PathVariable Integer facilityId) {
        try {
            HotelFacilityDetailDTO result = enumDataService.getFacilityDetailById(facilityId);
            if (result == null) {
                return AjaxResult.error("未找到对应的设施信息，ID: " + facilityId);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("根据ID获取设施详细信息失败，ID: " + facilityId, e);
            return AjaxResult.error("获取设施详细信息失败");
        }
    }

    /**
     * 根据设施key获取设施详细信息
     * GET /api/enums/facilityDetail/key/{facilityKey}
     */
    @GetMapping("/facilityDetail/key/{facilityKey}")
    public AjaxResult getFacilityDetailByKey(@PathVariable String facilityKey) {
        try {
            HotelFacilityDetailDTO result = enumDataService.getFacilityDetailByKey(facilityKey);
            if (result == null) {
                return AjaxResult.error("未找到对应的设施信息，Key: " + facilityKey);
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("根据Key获取设施详细信息失败，Key: " + facilityKey, e);
            return AjaxResult.error("获取设施详细信息失败");
        }
    }

    /**
     * 获取设施配置模板（供前端初始化使用）
     * GET /api/enums/facilityDetail/template
     */
    @GetMapping("/facilityDetail/template")
    public AjaxResult createFacilityConfigTemplate() {
        try {
            List<HotelFacilityDetailDTO> result = enumDataService.createFacilityConfigTemplate();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("创建设施配置模板失败", e);
            return AjaxResult.error("创建设施配置模板失败");
        }
    }
} 