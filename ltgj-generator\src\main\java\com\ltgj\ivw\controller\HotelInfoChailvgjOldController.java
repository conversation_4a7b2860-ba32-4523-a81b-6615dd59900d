package com.ltgj.ivw.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.chalvgj.City;
import com.ltgj.ivw.domain.chalvgj.Data;
import com.ltgj.ivw.domain.chalvgj.Response;
import com.ltgj.ivw.domain.dto.HotelDetailDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.service.hotel.updater.ThreadPoolManager;
import com.ltgj.ivw.utils.ConstantList;
import com.ltgj.ivw.utils.ListUtil;
import com.ltgj.ivw.utils.MyTools;
import com.ltgj.ivw.utils.TXTUtil;
import com.ltgj.ivw.utils.hotelApi.ChailvgjApi;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.sdk.chailvgj.model.staticdata.ChailvgjCityListResponse;
import com.ltgj.supplier.chailvgj.HotelChailvgjSupplierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 差旅管家数据Controller
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Slf4j
@RestController
@RequestMapping("/ivw/chailvgjOld")
public class HotelInfoChailvgjOldController extends BaseController
{
    @Autowired
    private IHotelInfoChailvgjService hotelInfoChailvgjService;
    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private IJdJdbMappingService jdJdbMappingService;
    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;
    @Autowired
    private IHotelUpdateRecodeService hotelUpdateRecodeService;
    @Autowired
    private IHotelCityMappingService hotelCityMappingService;

    @Autowired
    private IBCityService ibCityService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private IZhJdJdbMinPriceService zhJdJdbMinPriceService;

    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private HotelChailvgjSupplierService hotelChailvgjSupplierService;

    @GetMapping("/hotelPriceList")
    public AjaxResult hotelPriceList() throws Exception {
        int pageSize = 20;
        int totalPage = 2;

        int chailvCityId = 238099;
        String chailvCityName = "胡杨河";
        String clgjAreaName = "";
        long cityId = 1638355782983L;
        String cityName = "胡杨河";
        Integer district = null;
        String districtName = null;

        try {
            for (int page = 1; page <= totalPage; page++) {
                log.info("正在执行: page={}, totalPage={}", page, totalPage);
                Thread.sleep(3000);
                String s = ChailvgjApi.hotelSearch(chailvCityId, "2020-11-30", page, pageSize);
                log.info("酒店搜索: {}", s);
                JSONObject jsonObject = JSONObject.parseObject(s);
                String code = jsonObject.getString("Code");
                if (StringUtils.equalsIgnoreCase("200", code)) {
                    JSONObject data = jsonObject.getJSONObject("Data");
                    totalPage = data.getInteger("PageTotalCount");
                    JSONArray hotelList = data.getJSONArray("HotelList");
                    for (int i = 0; i < hotelList.size(); i++) {
                        JSONObject hotel = hotelList.getJSONObject(i);
                        Long hotelId = hotel.getLong("HotelId");
                        Integer minPrice = hotel.getInteger("MinPrice");
                        JSONObject geo = hotel.getJSONObject("Geo");
                        BigDecimal lat = geo.getBigDecimal("Lat");
                        BigDecimal lon = geo.getBigDecimal("Lon");
                        String hotelDetailResult = ChailvgjApi.hotelDetail(hotelId);
                        JSONObject hotelDetail = JSONObject.parseObject(hotelDetailResult);
                        String code1 = hotelDetail.getString("Code");
                        if (StringUtils.equalsIgnoreCase("200", code1)) {
                            JSONObject data1 = hotelDetail.getJSONObject("Data");
                            toSql(data1, cityId, cityName, district, districtName, chailvCityId, chailvCityName, clgjAreaName, lon, lat, minPrice);
                        } else {
                            log.error("获取酒店详情失败: page={}, hotelCity={}, district={}, hotelId={}", page, cityId, district, hotelId);
                        }
                        Thread.sleep(1500);
                    }
                } else {
                    log.error("差旅管家搜索酒店失败: page={}, hotelCity={}, district={}", page, cityId, district);
                }
            }
        } catch (Exception e) {
            log.error("执行异常: ", e);
        }
        log.info("执行结束. . .");

        chailvCityId = 78900;
        chailvCityName = "铁门关";
        clgjAreaName = "";
        cityId = 1638355532887L;
        cityName = "铁门关";
        district = null;
        districtName = null;

        try {
            for (int page = 1; page <= totalPage; page++) {
                log.info("正在执行: page={}, totalPage={}", page, totalPage);
                Thread.sleep(3000);
                String s = ChailvgjApi.hotelSearch(chailvCityId, "2020-11-30", page, pageSize);
                log.info("酒店搜索: {}", s);
                JSONObject jsonObject = JSONObject.parseObject(s);
                String code = jsonObject.getString("Code");
                if (StringUtils.equalsIgnoreCase("200", code)) {
                    JSONObject data = jsonObject.getJSONObject("Data");
                    totalPage = data.getInteger("PageTotalCount");
                    JSONArray hotelList = data.getJSONArray("HotelList");
                    for (int i = 0; i < hotelList.size(); i++) {
                        JSONObject hotel = hotelList.getJSONObject(i);
                        Long hotelId = hotel.getLong("HotelId");
                        Integer minPrice = hotel.getInteger("MinPrice");
                        JSONObject geo = hotel.getJSONObject("Geo");
                        BigDecimal lat = geo.getBigDecimal("Lat");
                        BigDecimal lon = geo.getBigDecimal("Lon");
                        String hotelDetailResult = ChailvgjApi.hotelDetail(hotelId);
                        JSONObject hotelDetail = JSONObject.parseObject(hotelDetailResult);
                        String code1 = hotelDetail.getString("Code");
                        if (StringUtils.equalsIgnoreCase("200", code1)) {
                            JSONObject data1 = hotelDetail.getJSONObject("Data");
                            toSql(data1, cityId, cityName, district, districtName, chailvCityId, chailvCityName, clgjAreaName, lon, lat, minPrice);
                        } else {
                            log.error("获取酒店详情失败: page={}, hotelCity={}, district={}, hotelId={}", page, cityId, district, hotelId);
                        }
                        Thread.sleep(1500);
                    }
                } else {
                    log.error("差旅管家搜索酒店失败: page={}, hotelCity={}, district={}", page, cityId, district);
                }
            }
        } catch (Exception e) {
            log.error("执行异常: ", e);
        }
        log.info("执行结束. . .");

        chailvCityId = 238084;
        chailvCityName = "双河";
        clgjAreaName = "";
        cityId = 1588757802138L;
        cityName = "双河";
        district = null;
        districtName = null;

        try {
            for (int page = 1; page <= totalPage; page++) {
                log.info("正在执行: page={}, totalPage={}", page, totalPage);
                Thread.sleep(3000);
                String s = ChailvgjApi.hotelSearch(chailvCityId, "2020-11-30", page, pageSize);
                log.info("酒店搜索: {}", s);
                JSONObject jsonObject = JSONObject.parseObject(s);
                String code = jsonObject.getString("Code");
                if (StringUtils.equalsIgnoreCase("200", code)) {
                    JSONObject data = jsonObject.getJSONObject("Data");
                    totalPage = data.getInteger("PageTotalCount");
                    JSONArray hotelList = data.getJSONArray("HotelList");
                    for (int i = 0; i < hotelList.size(); i++) {
                        JSONObject hotel = hotelList.getJSONObject(i);
                        Long hotelId = hotel.getLong("HotelId");
                        Integer minPrice = hotel.getInteger("MinPrice");
                        JSONObject geo = hotel.getJSONObject("Geo");
                        BigDecimal lat = geo.getBigDecimal("Lat");
                        BigDecimal lon = geo.getBigDecimal("Lon");
                        String hotelDetailResult = ChailvgjApi.hotelDetail(hotelId);
                        JSONObject hotelDetail = JSONObject.parseObject(hotelDetailResult);
                        String code1 = hotelDetail.getString("Code");
                        if (StringUtils.equalsIgnoreCase("200", code1)) {
                            JSONObject data1 = hotelDetail.getJSONObject("Data");
                            toSql(data1, cityId, cityName, district, districtName, chailvCityId, chailvCityName, clgjAreaName, lon, lat, minPrice);
                        } else {
                            log.error("获取酒店详情失败: page={}, hotelCity={}, district={}, hotelId={}", page, cityId, district, hotelId);
                        }
                        Thread.sleep(1500);
                    }
                } else {
                    log.error("差旅管家搜索酒店失败: page={}, hotelCity={}, district={}", page, cityId, district);
                }
            }
        } catch (Exception e) {
            log.error("执行异常: ", e);
        }
        log.info("执行结束. . .");

        chailvCityId = 238087;
        chailvCityName = "可克达拉";
        clgjAreaName = "";
        cityId = 1638355708991L;
        cityName = "可克达拉";
        district = null;
        districtName = null;

        try {
            for (int page = 1; page <= totalPage; page++) {
                log.info("正在执行: page={}, totalPage={}", page, totalPage);
                Thread.sleep(3000);
                String s = ChailvgjApi.hotelSearch(chailvCityId, "2020-11-30", page, pageSize);
                log.info("酒店搜索: {}", s);
                JSONObject jsonObject = JSONObject.parseObject(s);
                String code = jsonObject.getString("Code");
                if (StringUtils.equalsIgnoreCase("200", code)) {
                    JSONObject data = jsonObject.getJSONObject("Data");
                    totalPage = data.getInteger("PageTotalCount");
                    JSONArray hotelList = data.getJSONArray("HotelList");
                    for (int i = 0; i < hotelList.size(); i++) {
                        JSONObject hotel = hotelList.getJSONObject(i);
                        Long hotelId = hotel.getLong("HotelId");
                        Integer minPrice = hotel.getInteger("MinPrice");
                        JSONObject geo = hotel.getJSONObject("Geo");
                        BigDecimal lat = geo.getBigDecimal("Lat");
                        BigDecimal lon = geo.getBigDecimal("Lon");
                        String hotelDetailResult = ChailvgjApi.hotelDetail(hotelId);
                        JSONObject hotelDetail = JSONObject.parseObject(hotelDetailResult);
                        String code1 = hotelDetail.getString("Code");
                        if (StringUtils.equalsIgnoreCase("200", code1)) {
                            JSONObject data1 = hotelDetail.getJSONObject("Data");
                            toSql(data1, cityId, cityName, district, districtName, chailvCityId, chailvCityName, clgjAreaName, lon, lat, minPrice);
                        } else {
                            log.error("获取酒店详情失败: page={}, hotelCity={}, district={}, hotelId={}", page, cityId, district, hotelId);
                        }
                        Thread.sleep(1500);
                    }
                } else {
                    log.error("差旅管家搜索酒店失败: page={}, hotelCity={}, district={}", page, cityId, district);
                }
            }
        } catch (Exception e) {
            log.error("执行异常: ", e);
        }
        log.info("执行结束. . .");

        return AjaxResult.success();
    }

    private void toSql(JSONObject data, Long cityId, String cityName, Integer district, String districtName,
                       int clgjCityId, String clgjCityName, String clgjAreaName,
                       BigDecimal lon, BigDecimal lat, Integer minPrice) {
//        String path1 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据/"+cityName+"jd_jdb.sql";
//        String path2 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据/"+cityName+"hotel_info_chailvgj.sql";
//        String path3 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据/"+cityName+"zh_jd_jdb_X.sql";
//        String path4 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据/"+cityName+"zh_jd_jdb_mapping.sql";
//        String path5 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据/"+cityName+"zh_jd_jdb_min_price.sql";
        String path1 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据2/total.sql";
        String path2 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据2/total.sql";
        String path3 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据2/total.sql";
        String path4 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据2/total.sql";
        String path5 = "/工作备份/供应链/酒店/差旅管家/手动拉取数据2/total.sql";

        log.info("处理差旅管家详情信息: data={}, cityName={}, lon={}, lat={}", data.toJSONString(), cityName, lon, lat);

        Long hotelId = data.getLong("HotelId");
        String hotelName = data.getString("HotelName");
        JSONObject geo = data.getJSONObject("Geo");
        BigDecimal gd_lat = geo.getBigDecimal("Lat");
        BigDecimal gd_lon = geo.getBigDecimal("Lon");
        String openYear = data.getString("OpenYear");
        String renovationYear = data.getString("RenovationYear");
        String address = data.getString("Address");
        String logUrl = data.getString("LogUrl");
        Integer starRating = data.getInteger("StarRating");
        String starRateDesc = data.getString("StarRateDesc");
        String business = data.getString("Business");
        BigDecimal reviewScore = data.getBigDecimal("ReviewScore");
        Integer clgjcityId = data.getInteger("CityId");
        String clgjdistrict = data.getString("District");
        String telephoneNum = data.getString("TelephoneNum");
        Integer brandCode = data.getInteger("BrandCode");
        String brandName = data.getString("BrandName");
//        JSONArray facilityList = data.getJSONArray("FacilityList");
//        for (int i = 0; i < facilityList.size(); i++) {
//            Integer integer = facilityList.getInteger(i);
//
//
//        }
        try {
            StringBuilder sb = new StringBuilder("INSERT INTO `jd_jdb` (`id`, `interfacePlat`, `jdmc`, `jddz`, `jddh`, `img_url`, `status`,  `lon_baidu`, `lat_baidu`, `city_id`, `city_name`, `brand_id`, `brand_name`, `district`, `district_name`, `business_zone_name`, `jdxj`, `score`, `kysj`, `zhzxsj`, `rank`, `createdate`, `savedate`, `reserve9`) VALUES");
            sb.append("(");
            sb.append("'").append(PlatEnum.PLAT_CLGJ.getValue()).append(hotelId).append("',");
            sb.append("'").append(PlatEnum.PLAT_CLGJ.getValue()).append("',");
            sb.append("'").append(hotelName).append("',");
            sb.append("'").append(address).append("',");
            sb.append("'").append(telephoneNum).append("',");
            sb.append("'").append(logUrl).append("',");
            sb.append("'").append(0).append("',");
            sb.append("").append(lon).append(",");
            sb.append("").append(lat).append(",");
            sb.append("'").append(cityId).append("',");
            sb.append("'").append(cityName).append("',");
            sb.append("'").append(brandCode).append("',");
            sb.append("'").append(brandName).append("',");
            sb.append("'").append(district).append("',");
            sb.append("'").append(districtName).append("',");
            sb.append("'").append(StringUtils.isBlank(business)?"":business).append("',");

            sb.append("").append(starRating).append(",");
            sb.append("").append(reviewScore).append(",");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
            SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sb.append("'").append(StringUtils.isBlank(openYear)?"":simpleDateFormat2.format(simpleDateFormat.parse(openYear))).append("',");
            sb.append("'").append(StringUtils.isBlank(renovationYear)?"":simpleDateFormat2.format(simpleDateFormat.parse(renovationYear))).append("',");
            sb.append("").append("100").append(",");
            sb.append("").append("now()").append(",");
            sb.append("").append("now()").append(",");
            sb.append("'").append("2000079").append("'");
            sb.append(");");
            try (PrintWriter printWriter = new PrintWriter(new FileWriter(path1, true))){
                printWriter.println(sb.toString());
            }

            StringBuilder sb2 = new StringBuilder("INSERT INTO `hotel_info_chailvgj` (`id`, `create_time`, `update_time`, `create_by`, `update_by`, `is_delete`, `status`, `phone`, `country`,  `city_id`, `city_name`, `area_id`, `business_districts`, `address_line`, `open_date`, `decoration_date`, `brand_id`, `star`, `star_info`, `image`, `plat_num`, `plat_hotel_id`, `lon_bd`, `lat_bd`, `lon_gd`, `lat_gd`, `reserve9`) VALUES ");
            sb2.append("(");
            sb2.append("").append(hotelId).append(",");
            sb2.append("").append("now()").append(",");
            sb2.append("").append("now()").append(",");
            sb2.append("'").append("system").append("',");
            sb2.append("'").append("system").append("',");
            sb2.append("").append("0").append(",");
            sb2.append("").append("8").append(",");
            sb2.append("'").append(telephoneNum).append("',");
            sb2.append("'").append("CN").append("',");
            sb2.append("'").append(clgjCityId).append("',");
            sb2.append("'").append(clgjCityName).append("',");
            sb2.append("'").append(clgjdistrict).append("',");
            sb2.append("'").append(StringUtils.isBlank(business)?"":business).append("',");
            sb2.append("'").append(address).append("',");
            sb2.append("'").append(StringUtils.isBlank(openYear)?"":simpleDateFormat2.format(simpleDateFormat.parse(openYear))).append("',");
            sb2.append("'").append(StringUtils.isBlank(renovationYear)?"":simpleDateFormat2.format(simpleDateFormat.parse(renovationYear))).append("',");
            sb2.append("'").append(brandCode).append("',");
            sb2.append("").append(starRating).append(",");
            sb2.append("'").append(starRateDesc).append("',");
            sb2.append("'").append(logUrl).append("',");
            sb2.append("'").append("2000079").append("',");
            sb2.append("'").append(hotelId).append("',");
            sb2.append("").append(lon).append(",");
            sb2.append("").append(lat).append(",");
            sb2.append("").append(gd_lon).append(",");
            sb2.append("").append(gd_lat).append(",");
            sb2.append("'").append("2000079").append("'");
            sb2.append(");");
            try (PrintWriter printWriter = new PrintWriter(new FileWriter(path2, true))){
                printWriter.println(sb2.toString());
            }

            long i = cityId % 10;
            StringBuilder sb3 = new StringBuilder("INSERT INTO `zh_jd_jdb_"+i+"` (`id`, `interfacePlat`, `jdmc`, `jddz`, `jddh`, `img_url`, `status`,  `lon_baidu`, `lat_baidu`, `city_id`, `city_name`, `brand_id`, `brand_name`, `district`, `district_name`, `business_zone_name`, `jdxj`, `score`, `kysj`, `zhzxsj`, `rank`, `createdate`, `savedate`) VALUES");
            sb3.append("(");
            sb3.append("'").append(PlatEnum.PLAT_CLGJ.getValue()).append(hotelId).append("',");
            sb3.append("'").append(PlatEnum.PLAT_CLGJ.getValue()).append("',");
            sb3.append("'").append(hotelName).append("',");
            sb3.append("'").append(address).append("',");
            sb3.append("'").append(telephoneNum).append("',");
            sb3.append("'").append(logUrl).append("',");
            sb3.append("'").append(0).append("',");
            sb3.append("").append(lon).append(",");
            sb3.append("").append(lat).append(",");
            sb3.append("'").append(cityId).append("',");
            sb3.append("'").append(cityName).append("',");
            sb3.append("'").append(brandCode).append("',");
            sb3.append("'").append(brandName).append("',");
            sb3.append("'").append(district).append("',");
            sb3.append("'").append(districtName).append("',");
            sb3.append("'").append(StringUtils.isBlank(business)?"":business).append("',");

            sb3.append("").append(starRating).append(",");
            sb3.append("").append(reviewScore).append(",");
            sb3.append("'").append(StringUtils.isBlank(openYear)?"":simpleDateFormat2.format(simpleDateFormat.parse(openYear))).append("',");
            sb3.append("'").append(StringUtils.isBlank(renovationYear)?"":simpleDateFormat2.format(simpleDateFormat.parse(renovationYear))).append("',");
            sb3.append("").append("100").append(",");
            sb3.append("").append("now()").append(",");
            sb3.append("").append("now()").append("");
            sb3.append(");");
            try (PrintWriter printWriter = new PrintWriter(new FileWriter(path3, true))){
                printWriter.println(sb3.toString());
            }

            StringBuilder sb4 = new StringBuilder("INSERT INTO `zh_jd_jdb_mapping` (`interface_plat`, `local_id`, `plat_id`, `is_gn_gj`, `status`, `jd_name`, `savedate`) VALUES");
            sb4.append("(");
            sb4.append("").append(PlatEnum.PLAT_CLGJ.getValue()).append(",");
            sb4.append("'").append(PlatEnum.PLAT_CLGJ.getValue()).append(hotelId).append("',");
            sb4.append("'").append(hotelId).append("',");
            sb4.append("").append("1").append(",");
            sb4.append("").append("0").append(",");
            sb4.append("'").append(hotelName).append("',");
            sb4.append("").append("now()").append("");
            sb4.append(");");
            try (PrintWriter printWriter = new PrintWriter(new FileWriter(path4, true))){
                printWriter.println(sb4.toString());
            }

            StringBuilder sb5 = new StringBuilder("INSERT INTO `zh_jd_jdb_min_price` (`jdid`, `min_price`, `sxsj`, `interface_plat`) VALUES");
            sb5.append("(");
            sb5.append("'").append(PlatEnum.PLAT_CLGJ.getValue()).append(hotelId).append("',");
            sb5.append("").append(minPrice).append(",");
            sb5.append("'").append("2024-11-30").append("',");
            sb5.append("").append(PlatEnum.PLAT_CLGJ.getValue()).append("");
            sb5.append(");");
            StringBuilder sb6 = new StringBuilder("INSERT INTO `zh_jd_jdb_min_price` (`jdid`, `min_price`, `sxsj`, `interface_plat`) VALUES");
            sb6.append("(");
            sb6.append("'").append(PlatEnum.PLAT_CLGJ.getValue()).append(hotelId).append("',");
            sb6.append("").append(minPrice).append(",");
            sb6.append("'").append("2024-11-30").append("',");
            sb6.append("").append(PlatEnum.PLAT_CLGJ.getValue()).append("");
            sb6.append(");");
            StringBuilder sb7 = new StringBuilder("INSERT INTO `zh_jd_jdb_min_price` (`jdid`, `min_price`, `sxsj`, `interface_plat`) VALUES");
            sb7.append("(");
            sb7.append("'").append(PlatEnum.PLAT_CLGJ.getValue()).append(hotelId).append("',");
            sb7.append("").append(minPrice).append(",");
            sb7.append("'").append("2024-11-30").append("',");
            sb7.append("").append(PlatEnum.PLAT_CLGJ.getValue()).append("");
            sb7.append(");");
            try (PrintWriter printWriter = new PrintWriter(new FileWriter(path5, true))){
                printWriter.println(sb5.toString());
                printWriter.println(sb6.toString());
                printWriter.println(sb7.toString());
            }
        } catch (Exception e) {
            log.error("处理数据失败: ", e);
        }
    }

    //获取艺龙酒店更新状态信息
    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:query')")
    @GetMapping("/getInfoStatus")
    public AjaxResult getInfoStatus() {
        Map<String, Object> map = new HashMap<>();
        String stepName = "";
        switch (HotelUpdateStatus.expStatusCLGJ) {
            case 0:
                stepName = "初始状态";
                break;
            case 1:
                stepName = "获取酒店映射中";
                break;
            case 2:
                stepName = "更新酒店及映射信息中";
                break;
            case 3:
                stepName = "更新酒店及映射信息完成";
                break;
        }
        map.put("stepName", stepName);
        map.put("get", HotelUpdateStatus.clgjDetailCountGet);
        map.put("updateJS", HotelUpdateStatus.clgjDetailCountUpdate);
        map.put("deleteJS", HotelUpdateStatus.clgjDetailCountDelete);
        return AjaxResult.success(map);
    }

    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:query')")
    @GetMapping("/updateData")
    public void updateData() throws Exception {
//        updateAll();
        if (HotelUpdateStatus.expStatusCLGJ == 1 || HotelUpdateStatus.expStatusCLGJ == 2){
            throw new RuntimeException("不可并行任务进行中，不可开启请等待任务完成！");
        }

        String username = SecurityUtils.getUsername();
        log.info("差旅管家-数据初始化, userId:{}, userName:{}",SecurityUtils.getUserId(),  username);
        // 获取线程池
        ThreadPoolExecutor executor = ThreadPoolManager.getInstance().getExecutor();
        executor.execute(() -> { hotelInfoChailvgjService.updateAll(username);});
    }

    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:query')")
    @GetMapping("/resetStatus")
    public void resetStatus() {
        HotelUpdateStatus.expStatusCLGJ = 0;
        HotelUpdateStatus.clgjDetailCountGet = 0;
        HotelUpdateStatus.clgjDetailCountUpdate = 0;
        HotelUpdateStatus.clgjDetailCountDelete = 0;
        HotelUpdateStatus.status = 0;
    }

    //全量差旅管家酒店更新
    @RequestMapping("/updateAll")
    public void updateAll() throws Exception {
//        if(HotelUpdateStatus.status == 1){
//            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
//        }
        HotelUpdateStatus.expStatusCLGJ = 1;
//        HotelUpdateStatus.status = 1;
        String fileName = "clgjDetail.txt";
        File file = new File(ConstantList.CHAILVGUANJIA_PATH+ fileName);
        if(file.exists()) {
            file.delete();
        }
        HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
        hotelUpdateRecode.setYl2(PlatEnum.PLAT_CLGJ.getValue());
        hotelUpdateRecode.setId("CLGJ" + MyTools.getTimesNo());
        hotelUpdateRecode.setUpdateBy(getUsername());
        hotelUpdateRecode.setTimeStart(new Date());
        threadPoolTaskExecutor.execute(() -> {
            logger.info("差旅管家mapping开始执行");
            List<String> ids = new ArrayList<>();
            List<String> idsDelete = new ArrayList<>();
            //改为分批查询
            //分页查询Id
            String minId = "0";
            int pageSize = 1;
            int pageNumber = 50000;
            int totalCount = 0;
            int successCount = 0;
            //分页查询Id
            minId = "0";
            //记录调用执行次数
            int count = 0;
            while (true) {
                List<String> jdJdbList = jdJdbService.selectIdList(minId, pageNumber);
                logger.info("获取酒店数量：{},pageSize:{}", jdJdbList.size(), pageSize);
                if (CollectionUtils.isEmpty(jdJdbList)) {
                    logger.info("获取酒店Id结束 pageSize:{},totalCount:{}", pageSize, totalCount);
                    break;
                }
                totalCount = totalCount + jdJdbList.size();
                for (String id : jdJdbList) {
                    if (id.contains(PlatEnum.PLAT_EL.getValue())) {
                        ids.add(id.substring(7));
                    }
                    if (ids.size() == 50) {
                        successCount = successCount + doExec(ids, count, fileName, idsDelete);
                        logger.info("执行一次，当前页数为：{}", pageSize);
                    }
                }
                logger.info("ids数量:{}", ids.size());
                if (CollectionUtils.isNotEmpty(ids)) {
                    successCount = successCount + doExec(ids, count, fileName, idsDelete);
                }
                logger.info("当前循环最小Id为:{},totalCount:{},successCount:{},pageSize:{}", minId, totalCount, successCount, pageSize);
                minId = jdJdbList.get(jdJdbList.size() - 1);
                pageSize++;
            }
            logger.info("写入文件执行结束，idsDelete数量：{}", idsDelete.size());
            String[] deleteIds = new String[idsDelete.size()];
            for (int c = 0; c < idsDelete.size(); c++) {
                String id = PlatEnum.PLAT_EL.getValue() + idsDelete.get(c);
                deleteIds[c] = id;
            }
            //改为批量删除
            int deletes = idsDelete.size();
            if (CollectionUtils.isNotEmpty(idsDelete)) {
                int batchSize = 5000;
                List<List<String>> batchList = ListUtil.subList(idsDelete, batchSize);
                logger.info("酒店基础数据分组数量：{},总数量:{}", batchList.size(), idsDelete);
                for (List<String> list : batchList) {
                    int deleteCount = hotelInfoChailvgjService.deleteHotelInfoChailvgjByIds(list);
                    logger.info("酒店基础数据删除 deleteCount:{}", deleteCount);
                }
                logger.info("酒店基础数据删除完成");
            }
            if (deleteIds.length > 0) {
                //分批删除酒店映射数据
                int batchSize = 5000;
                List<List<String>> batchList = ListUtil.subList(Arrays.asList(deleteIds), batchSize);
                logger.info("jd_jdb_mapping数量：{},总数量：{}", batchList.size(), deleteIds.length);
                for (List<String> list : batchList) {
                    int deleteCount = jdJdbMappingService.deleteJdJdbMappingByLocalId(PlatEnum.PLAT_CLGJ.getValue(), list);
                    logger.info("jd_jdb_mapping酒店映射数据删除 deleteCount:{}", deleteCount);
                    int zhJdJdbMappingCount = zhJdJdbMappingService.deleteByLocalIds(PlatEnum.PLAT_CLGJ.getValue(), list);
                    logger.info("zh_jd_jdb_mapping酒店映射数据删除 zhJdJdbMappingCount:{}", zhJdJdbMappingCount);
                }
                logger.info("酒店映射数据删除完成");
            }
            HotelUpdateStatus.clgjDetailCountDelete = deletes;
            hotelUpdateRecode.setCountDel(Long.valueOf(deletes));
            Map<String, HotelCity> hotelCityMap = new HashMap<>();
            //查询差旅管家城市数据
            HotelCity hotelCityQuery = new HotelCity();
            hotelCityQuery.setReserve1(PlatEnum.PLAT_CLGJ.getValue());
            List<HotelCity> hotelCities = hotelCityService.selectHotelCityList(hotelCityQuery);
            if (CollectionUtils.isNotEmpty(hotelCities)) {
                hotelCityMap = hotelCities.stream().collect(Collectors.toMap(HotelCity::getCityName, t -> t, (o, n) -> n));
            }
            HotelInfoChailvgj hotelInfoChailvgj = new HotelInfoChailvgj();
            hotelInfoChailvgj.setStatus(8);
            JdJdbMapping jdJdbMapping = new JdJdbMapping();
            ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
            jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
            zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
            File newFile = new File(ConstantList.CHAILVGUANJIA_PATH + fileName);
            log.info("校验差旅管家文件是否存在: {}", newFile.exists());
            if (newFile.exists()) {
                HotelUpdateStatus.expStatusCLGJ = 2;
                LineIterator it = null;
                try {
                    it = FileUtils.lineIterator(newFile, "utf8");
                } catch (IOException e) {
                    logger.error("读取文件异常");
                }
                try {
                    while (it.hasNext()) {
                        String line = it.nextLine();
                        try {
                            JSONObject hotelJson = JSONObject.parseObject(line);
                            hotelInfoChailvgj.setId(hotelJson.getString("tripwiseHotelId"));
                            hotelInfoChailvgj.setPlatHotelId(PlatEnum.PLAT_EL.getValue() + hotelJson.getString("mappingHotelCode"));
                            hotelInfoChailvgj.setPhone(hotelJson.getString("hotelPhone"));
                            String cityName = hotelJson.getString("cityName");
                            hotelInfoChailvgj.setCityName(cityName);
                            if(StringUtils.isNotEmpty(cityName)){
                                HotelCity hotelCity = hotelCityMap.get(cityName);
                                if(Objects.nonNull(hotelCity)){
                                    hotelInfoChailvgj.setCityId(hotelCity.getCityId());
                                }
                            }
                            hotelInfoChailvgj.setName(hotelJson.getString("hotelName"));
                            hotelInfoChailvgj.setAddressLine(hotelJson.getString("hotelAddress"));
                            hotelInfoChailvgj.setStatus(8);
                            hotelInfoChailvgjService.insertHotelInfoChailvgj(hotelInfoChailvgj);
                            jdJdbMapping.setLocalId(PlatEnum.PLAT_EL.getValue() + hotelJson.getString("mappingHotelCode"));
                            jdJdbMapping.setPlatId(hotelJson.getString("tripwiseHotelId"));
                            jdJdbMapping.setReserve3(hotelJson.getString("hotelName"));
                            jdJdbMappingService.insertJdJdbMapping(jdJdbMapping);
                            logger.info("插入jd_jdb_mapping成功 hotelId:{}", JSONObject.toJSONString(jdJdbMapping));
                            zhJdJdbMapping.setLocalId(PlatEnum.PLAT_EL.getValue() + hotelJson.getString("mappingHotelCode"));
                            zhJdJdbMapping.setPlatId(hotelJson.getString("tripwiseHotelId"));
                            zhJdJdbMapping.setIsGnGj(1);
                            zhJdJdbMapping.setStatus(0);
                            zhJdJdbMapping.setJdName(hotelJson.getString("hotelName"));
                            zhJdJdbMapping.setSaveDate(new Date());
                            zhJdJdbMappingService.insert(zhJdJdbMapping);
                            logger.info("插入zh_jd_jdb_mapping成功 hotelId:{}", JSONObject.toJSONString(zhJdJdbMapping));
                            HotelUpdateStatus.clgjDetailCountUpdate++;
                        } catch (Exception e) {
                            log.error("差旅管家内抓取数据异常: ", e);
                        }
                    }
                } catch (Exception e) {
                    log.error("差旅管家抓取数据异常: errorMessage:{}", e.getMessage(), e);
                } finally {
                    HotelUpdateStatus.expStatusCLGJ = 3;
                }
            }
        });


//        HotelUpdateStatus.status = 0;
        hotelUpdateRecode.setCountAdd(Long.valueOf(HotelUpdateStatus.clgjDetailCountUpdate));
        hotelUpdateRecode.setTimeEnd(new Date());
        hotelUpdateRecodeService.insertHotelUpdateRecode(hotelUpdateRecode);
    }

    private Integer doExec(List<String> ids, int count, String fileName, List<String> idsDelete) {
        int resultCount = 0;
        String res = "";
        try {
            int retryCount = 3;
            res = ChailvgjApi.listHotel(ids, retryCount);
            count++;
            logger.info("当前执行次数：{},res:{},ids:{}", count, res, JSONObject.toJSONString(ids));
            if (StringUtils.isNotEmpty(res)) {
                JSONObject resJson = JSONObject.parseObject(res);
                if (resJson.get("Code").equals("200")) {
                    JSONArray data = resJson.getJSONArray("Data");
                    if (null != data) {
                        log.info("单次获取酒店数据：{}", data.size());
                        resultCount = data.size();
                        for (int i = 0; i < data.size(); i++) {
                            JSONObject hotelJson = data.getJSONObject(i);
                            TXTUtil.writeTXT(hotelJson.toJSONString(), ConstantList.CHAILVGUANJIA_PATH,
                                    fileName);
                            log.info("写入文件: {}", hotelJson.toJSONString());
                            HotelUpdateStatus.clgjDetailCountGet++;
                            String mappingHotelCode = hotelJson.getString("mappingHotelCode");
                            idsDelete.add(mappingHotelCode);
                            ids.remove(mappingHotelCode);
                        }
                    } else {
                        log.error("获取酒店数据为空，请检查！,请求参数为：{},返回参数:{}", JSONObject.toJSONString(ids), JSONObject.toJSONString(res));
                    }
                }
            } else {
                logger.error("获取酒店数据异常，请检查！");
            }
            ids.clear();
        } catch (Exception e) {
            log.error("差旅管家执行异常：res:{},errorMessage:{}", res, e.getMessage(), e);
        }

        return resultCount;
    }

    /**
     * 查询差旅管家数据列表
     */
    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:list')")
    @GetMapping("/list")
    public TableDataInfo list(HotelInfoChailvgj hotelInfoChailvgj)
    {
//        startPage();
//        List<HotelInfoChailvgj> list = hotelInfoChailvgjService.selectHotelInfoChailvgjList(hotelInfoChailvgj);
        return getDataTable(null);
    }

    /**
     * 导出差旅管家数据列表
     */
    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:export')")
    @Log(title = "差旅管家数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HotelInfoChailvgj hotelInfoChailvgj)
    {
//        List<HotelInfoChailvgj> list = hotelInfoChailvgjService.selectHotelInfoChailvgjList(hotelInfoChailvgj);
//        ExcelUtil<HotelInfoChailvgj> util = new ExcelUtil<HotelInfoChailvgj>(HotelInfoChailvgj.class);
//        util.exportExcel(response, list, "差旅管家数据数据");
    }

    /**
     * 获取差旅管家数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success();
//        return success(hotelInfoChailvgjService.selectHotelInfoChailvgjById(id));
    }

    /**
     * 新增差旅管家数据
     */
    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:add')")
    @Log(title = "差旅管家数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HotelInfoChailvgj hotelInfoChailvgj)
    {
        String res = ChailvgjApi.hotelDetail(Long.valueOf(hotelInfoChailvgj.getId()));
        JSONObject resJson = JSONObject.parseObject(res);
        if(resJson.getInteger("Code") == 997) {
            return AjaxResult.error(resJson.getString("Msg"));
        }
        insert(hotelInfoChailvgj, resJson.getJSONObject("Data"));
        return AjaxResult.success();
    }

    public void insert(HotelInfoChailvgj hotelInfo, JSONObject jsonObject) {
        HotelDetailDTO detailDTO = hotelInfoChailvgjService.buildHotelDetailDTO(jsonObject);
        hotelInfoChailvgjService.saveHotelInfo(detailDTO);
    }

    /**
     * 平台酒店导入本地
     */
    @PreAuthorize("@ss.hasPermi('ivw:dongcheng:remove')")
    @Log(title = "差旅管家酒店数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult platToLocal(@PathVariable String[] ids) throws Exception {
//        HotelInfo hotelInfo = hotelInfoChailvgjService.selectHotelInfoChailvgjById(ids[0]);
        HotelInfo hotelInfo = new HotelInfo();
        JdJdb jdLocal = new JdJdb();
        jdLocal.setId(PlatEnum.PLAT_CLGJ.getValue()+ hotelInfo.getId());
        jdLocal.setInterfacePlat(PlatEnum.PLAT_CLGJ.getValue());
        jdLocal.setCreatedate(new Date());
        jdLocal.setJdmc(hotelInfo.getName());
        jdLocal.setJdmcEn(hotelInfo.getNameEn());
        jdLocal.setJdlx(hotelInfo.getTypeId());
        jdLocal.setJddz(hotelInfo.getAddressLine());
        jdLocal.setJddh(hotelInfo.getPhone());
        jdLocal.setImgUrl(hotelInfo.getImage());
        jdLocal.setLonGoogle(hotelInfo.getLonGg());
        jdLocal.setLatGoogle(hotelInfo.getLatGg());
        jdLocal.setLonBaidu(hotelInfo.getLonBd());
        jdLocal.setLatBaidu(hotelInfo.getLatBd());
        jdLocal.setLonGaode(hotelInfo.getLonGd());
        jdLocal.setLatGaode(hotelInfo.getLatGd());

        String cityId = hotelInfo.getCityId();
        if(StringUtils.isEmpty(cityId)){
            throw new Exception("酒店所在城市为空，请先维护正确以避免后续BUG！");
        }
        HotelCityMapping hotelCityMapping = new HotelCityMapping();
        hotelCityMapping.setPlatNum(cityId);
        hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
        List<HotelCityMapping> hotelCityMappingList = hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
        if(hotelCityMappingList.size() == 0) {
            throw new Exception("该酒店所在城市映射关系不存在，请先维护正确以避免后续BUG！");
        }
        hotelCityMapping = hotelCityMappingList.get(0);
        jdLocal.setCityId(hotelCityMapping.getLocalId());
        jdLocal.setCityName(hotelCityMapping.getCityName());


        List<ZhJdJdbMapping> zhJdJdbMappingList = zhJdJdbMappingService.findByInterfacePlatAndPlatId(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()), hotelInfo.getId());
        if(zhJdJdbMappingList != null && zhJdJdbMappingList.size() > 0) {
            throw new Exception("酒店映射已存在，确认要导入请先删除！");
        }


        jdLocal.setBrandName(hotelInfo.getBrandId());

        jdLocal.setBusinessZoneName(hotelInfo.getBusinessDistricts());
        if (hotelInfo.getStar() != null) {
            jdLocal.setJdxj(String.valueOf(hotelInfo.getStar()));
        }

        if(StringUtils.isNotEmpty(hotelInfo.getReserve2())) {
            jdLocal.setScore(BigDecimal.valueOf(Double.valueOf(hotelInfo.getReserve2())));
        }
        jdLocal.setJtmc(hotelInfo.getGroupId());
        jdLocal.setKysj(hotelInfo.getOpenDate());
        jdLocal.setZhzxsj(hotelInfo.getDecorationDate());
        if(StringUtils.isNotEmpty(hotelInfo.getReserve1())) {
            jdLocal.setMinPrice(BigDecimal.valueOf(Double.valueOf(hotelInfo.getReserve1())));
        }
        jdLocal.setReserve1(hotelInfo.getDescription());
        jdLocal.setNoticeInfo(hotelInfo.getNoticeInfo());
        jdLocal.setPolicyInfo(hotelInfo.getPolicyInfo());
        jdLocal.setFacilitiesInfo(hotelInfo.getFacilitiesInfo());
        jdJdbService.insertJdJdb(jdLocal);

        //添加映射
        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
        zhJdJdbMapping.setPlatId(hotelInfo.getId());
        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
        zhJdJdbMapping.setLocalId(jdLocal.getId());
        zhJdJdbMapping.setJdName(jdLocal.getJdmc());
        zhJdJdbMapping.setPlatJdName(hotelInfo.getName());
        zhJdJdbMapping.setIsGnGj(1);
        zhJdJdbMapping.setStatus(0);
        zhJdJdbMapping.setSaveDate(new Date());
        zhJdJdbMapping.setPlatJdName(jdLocal.getJdmc());
        zhJdJdbMappingService.insert(zhJdJdbMapping);

        //更新映射状态
        /*HotelInfoChailvgj hi = new HotelInfoChailvgj();
        hi.setId(hotelInfo.getId());
        hi.setStatus(8);
        hotelInfoChailvgjService.updateHotelInfoChailvgj(hi);*/

        return AjaxResult.success();
    }

    /**
     * 修改差旅管家数据
     */
    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:edit')")
    @Log(title = "差旅管家数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HotelInfoChailvgj hotelInfoChailvgj)
    {
        return toAjax(hotelInfoChailvgjService.updateHotelInfoChailvgj(hotelInfoChailvgj));
    }


    /**
     * 差旅管家城市数据拉取
     */
    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:edit')")
    @Log(title = "差旅管家数据城市数据", businessType = BusinessType.UPDATE)
    @GetMapping("getCityList")
    public AjaxResult getCityList() {
        threadPoolTaskExecutor.execute(()->{
            try{
                String cityStr = ChailvgjApi.getCityList();
                Response<Data> response = JSONObject.parseObject(cityStr, new TypeReference<Response<Data>>() {
                    @Override
                    public Response<Data> parseObject(String text) {
                        return super.parseObject(text);
                    }
                });
                if(response.getCode().equals("200")){
                    List<City> cityList = response.getData().getCityList();
                    logger.info("拉取差旅管家城市数据总数：{}",cityList.size());
                    int count = 0;
                    for (City city : cityList) {
                        logger.info("城市数据：{}", city);
                        //插入hotel_city_mapping表
                        //根据cityId 和 reservel查询
                        HotelCity paramCity = new HotelCity();
                        paramCity.setCityId(String.valueOf(city.getCityId()));
                        paramCity.setReserve1(PlatEnum.PLAT_CLGJ.getValue());
                        List<HotelCity> hotelCities = hotelCityService.selectHotelCityList(paramCity);
                        HotelCity hotelCity = initHotelCity(city);
                        if(CollectionUtils.isEmpty(hotelCities)){
                            int hotelCityCount = hotelCityService.insertHotelCity(hotelCity);
                            logger.info("hotelCity插入完成 cityId:{},插入状态：{}",hotelCity.getCityId(),hotelCityCount);
                        }else {
                            logger.info("hotelCity已经存在 cityId:{}",city.getCityId());
                        }

                        HotelCityMapping mapping = new HotelCityMapping();
                        mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
                        mapping.setPlatNum(String.valueOf(city.getCityId()));
                        List<HotelCityMapping> hotelCityMappings = hotelCityMappingService.selectHotelCityMappingList(mapping);
                        logger.info("根据cityId和平台编码查询城市映射 cityId:{},interfacePlate:{},count:{}",city.getCityId(),PlatEnum.PLAT_CLGJ.getValue(),hotelCityMappings.size());
                        if (CollectionUtils.isEmpty(hotelCityMappings)) {
                            count++;
                            //艺龙打底映射
                            mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_EL.getValue()));
                            mapping.setCityName(hotelCity.getCityName());
                            hotelCityMappings = hotelCityMappingService.selectHotelCityMappingList(mapping);
                            logger.info("艺龙映射数据 hotelCityMappings:{}",hotelCityMappings.size());
                            if(hotelCityMappings.size() == 1) {
                                HotelCityMapping mapping1 = new HotelCityMapping();
                                mapping1.setCityName(hotelCityMappings.get(0).getCityName());
                                mapping1.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
                                mapping1.setLocalId(hotelCityMappings.get(0).getLocalId());
                                mapping1.setPlatNum(hotelCity.getCityId());
                                hotelCityMappingService.insertHotelCityMapping(mapping1);
                            }else {
                                mapping.setPlatNum(null);
                                mapping.setCityName(hotelCity.getCityName()+"市");
                                hotelCityMappings = hotelCityMappingService.selectHotelCityMappingList(mapping);
                                if(hotelCityMappings.size() == 1) {
                                    HotelCityMapping mapping1 = new HotelCityMapping();
                                    mapping1.setCityName(hotelCityMappings.get(0).getCityName());
                                    mapping1.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
                                    mapping1.setLocalId(hotelCityMappings.get(0).getLocalId());
                                    mapping1.setPlatNum(hotelCity.getCityId());
                                    hotelCityMappingService.insertHotelCityMapping(mapping1);
                                }else {
                                    mapping.setCityName(hotelCity.getCityName().replace("市", ""));
                                    hotelCityMappings = hotelCityMappingService.selectHotelCityMappingList(mapping);
                                    if(hotelCityMappings.size() == 1) {
                                        HotelCityMapping mapping1 = new HotelCityMapping();
                                        mapping1.setCityName(hotelCityMappings.get(0).getCityName());
                                        mapping1.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
                                        mapping1.setLocalId(hotelCityMappings.get(0).getLocalId());
                                        mapping1.setPlatNum(hotelCity.getCityId());
                                        hotelCityMappingService.insertHotelCityMapping(mapping1);
                                    }else {
                                        logger.info("hotel_city_mapping匹配失败 cityId:{}",hotelCity.getCityId());
                                        List<BCity> bCities = ibCityService.selectCityListByName(city.getCity());
                                        logger.info("bcity查询结果 {}",bCities.size());
                                        if(CollectionUtils.isNotEmpty(bCities)){
                                            logger.info("b_city表查询出来的基础数据 city:{},size:{}",city,bCities.size());
                                            BCity bCity = bCities.get(0);
                                            mapping.setLocalId(bCity.getId());
                                        }

                                        //添加
                                        mapping.setCityName(city.getCity());
                                        mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
                                        mapping.setPlatNum(String.valueOf(city.getCityId()));
                                        int updateCount = hotelCityMappingService.insertHotelCityMapping(mapping);
                                        logger.info("通过b_city插入成功 cityId:{},updateCount:{}",city.getCityId(),updateCount);

                                    }
                                }
                            }
                        } else {
                            logger.info("城市数据已存在 cityId:{},cityName:{}", city.getCityId(),city.getCity());
                        }
                    }

                    logger.info("差旅管家城市数据拉取完成 拉取总条数：{}",count);

                }else {
                    logger.error("差旅管家城市数据拉取失败 code:{},msg:{}",response.getCode(),response.getMsg());
                }
            }catch (Exception e){
                logger.error("获取差旅管家城市数据失败：{}",e.getMessage(),e);
            }
        });
        return success("执行完毕");
    }

    private HotelCity initHotelCity(City city) {
        HotelCity hotelCity = new HotelCity();
        hotelCity.setCityId(String.valueOf(city.getCityId()));
        hotelCity.setCityName(city.getCity());
        hotelCity.setCityNameEn(city.getCity());
        hotelCity.setLocationId(String.valueOf(city.getCityId()));
        hotelCity.setLocationName(city.getCity());
        hotelCity.setLocationNameEn(city.getCity());
        hotelCity.setRemark("");
        hotelCity.setProvinceId(String.valueOf(city.getProvinceId()));
        hotelCity.setProvinceName(city.getProvince());
        hotelCity.setProvinceNameEn(city.getProvince());
//        hotelCity.setCountryId(String.valueOf(city.getCountryId()));
//        hotelCity.setCountryName(city.getCountry());
//        hotelCity.setCountryNameEn(city.getCountry());
        hotelCity.setCreateBy("system");
        hotelCity.setReserve1(PlatEnum.PLAT_CLGJ.getValue());
        return hotelCity;
    }


    /**
     * 差旅管家酒店最低价拉取
     */

    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:edit')")
    @PostMapping("getHotelPrice")
    public AjaxResult getHotelPrice(@RequestBody MinPriceReq minPriceReq) {
        logger.info("更新差旅管家最低价开始");
        return jdJdbService.listMinPriceTravelButler(minPriceReq);
    }

    /**
     * 单独处理差旅管家城市数据
     */
    @PostMapping("/processCityData")
    public AjaxResult processCityData(@RequestBody ChailvgjCityListResponse citiesResponse,
                                      @RequestParam(defaultValue = "1") String idempotent) {
        try {
            int count = this.hotelChailvgjSupplierService.processCityData(citiesResponse, idempotent);
            return AjaxResult.success("处理成功，共处理城市数据: " + count + "条");
        } catch (Exception e) {
            log.error("处理差旅管家城市数据异常", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 单独获取差旅管家城市数据
     */
    @GetMapping("/getCityData")
    public AjaxResult getCityData() {
        try {
            String cityListJson = ChailvgjApi.getCityList();
            if (StringUtils.isNotEmpty(cityListJson)) {
                return AjaxResult.success("获取差旅管家城市数据成功", JSONObject.parseObject(cityListJson));
            } else {
                return AjaxResult.error("获取差旅管家城市数据失败，返回为空");
            }
        } catch (Exception e) {
            log.error("获取差旅管家城市数据异常", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 同步差旅管家城市数据（异步执行）
     */
    @PostMapping("/syncCityData")
    public AjaxResult syncCityData(@RequestParam(defaultValue = "1") String idempotent) {
        log.info("开始异步同步差旅管家城市数据");

        // 使用线程池异步执行
        this.threadPoolTaskExecutor.execute(() -> {
            try {
                log.info("差旅管家城市数据同步任务开始执行");
                long startTime = System.currentTimeMillis();

                String result = this.hotelChailvgjSupplierService.syncCityData(idempotent);
                log.info("差旅管家城市数据同步结果: {}", result);

                long endTime = System.currentTimeMillis();
                long totalTime = (endTime - startTime) / 1000;
                log.info("差旅管家城市数据同步完成！耗时：{}秒", totalTime);

            } catch (Exception e) {
                log.error("差旅管家城市数据同步异常", e);
            }
        });

        return AjaxResult.success("差旅管家城市数据同步任务已提交，正在后台执行");
    }

    /**
     * 同步差旅管家城市数据（同步执行，用于测试）
     */
    @PostMapping("/syncCityDataSync")
    public AjaxResult syncCityDataSync(@RequestParam(defaultValue = "1") String idempotent) {
        try {
            String result = this.hotelChailvgjSupplierService.syncCityData(idempotent);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("同步差旅管家城市数据异常", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }

}
