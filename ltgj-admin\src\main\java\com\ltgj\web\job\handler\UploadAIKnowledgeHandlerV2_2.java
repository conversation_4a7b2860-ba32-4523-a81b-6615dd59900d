package com.ltgj.web.job.handler;

import com.alibaba.fastjson2.JSONObject;
import com.ltgj.ivw.service.AiMappingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
@JobHandler(value = "uploadAIKnowledgeHandlerV2_2")
public class UploadAIKnowledgeHandlerV2_2 extends IJobHandler {
    @Autowired
    private AiMappingService aiMappingService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("开始上传知识库，入参{}",param);
        JSONObject jsonObject = JSONObject.parseObject(param);
        String knowledgeId = jsonObject.getString("knowledgeId");
        Date date = jsonObject.getDate("date");
        List<String> cityIds = jsonObject.getList("cityIds", String.class);

        try {
            aiMappingService.uploadKnowledgeByCity2(knowledgeId, date, cityIds);
        } catch (Exception e) {
            log.error("上传知识库失败", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
