package com.ltgj.common.utils.file;

import com.tem.imgserver.client.DeleteResult;
import com.tem.imgserver.client.ImgService;
import com.tem.imgserver.client.UploadResult;
import com.tem.imgserver.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.multipart.FilePart;
import org.apache.commons.httpclient.methods.multipart.MultipartRequestEntity;
import org.apache.commons.httpclient.methods.multipart.Part;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

/**
 * 图片客户端工具类
 * 支持从Nacos配置中心读取配置，硬编码值作为兜底配置
 * 
 * <AUTHOR>
 * @Date 2025/6/5
 * @description: 图片上传下载工具类，支持配置化管理
 */
@Component
@Slf4j
public class ImgClientUtils {

    // 静态实例，用于支持原有的静态方法调用
    private static ImgClientUtils instance;

    // 从配置中心读取，兜底配置为原硬编码值
    @Value("${tem.imgserver.host:https://dev-img-trip.lvtuguanjia.com}")
    private String pathDoMain;

    @Value("${tem.imgserver.tmpDir:temp/upload}")
    private String tmpDir;

    /**
     * 初始化静态实例
     */
    @PostConstruct
    public void init() {
        instance = this;
        log.info("🔧 ImgClientUtils 配置初始化完成:");
        log.info("  - pathDoMain: " + pathDoMain);
        log.info("  - tmpDir: " + tmpDir);
    }

    /**
     * 获取图片服务域名
     */
    public String getPathDoMain() {
        return pathDoMain;
    }

    /**
     * 获取临时目录
     */
    public String getTmpDir() {
        return tmpDir;
    }

    // ================================
    // 静态代理方法，保持原有调用方式
    // ================================

    /**
     * 上传图片 - 静态方法代理
     */
    public static UploadResult uploadImg(File file, String path) {
        return getInstance().uploadImgInstance(file, path);
    }

    /**
     * 上传图片 - 静态方法代理
     */
    public static UploadResult uploadImg2(File file, String path, String fileName, String storeType) {
        return getInstance().uploadImg2Instance(file, path, fileName, storeType);
    }

    /**
     * 上传图片 - 静态方法代理
     */
    public static UploadResult uploadImg2(InputStream in, String path, String fileName) {
        return getInstance().uploadImg2Instance(in, path, fileName);
    }

    /**
     * 上传图片（带过期时间）- 静态方法代理
     */
    public static UploadResult uploadImg(File file, String path, String fileName, int expirationDays) {
        return getInstance().uploadImgInstance(file, path, fileName, expirationDays);
    }

    /**
     * 上传图片 - 静态方法代理
     */
    public static UploadResult upload(MultipartFile multipartFile, String bizType) {
        return getInstance().uploadInstance(multipartFile, bizType);
    }

    /**
     * 上传图片 - 静态方法代理
     */
    public static UploadResult uploadImg(MultipartFile multipartFile, String path, String fileName, String storeType) {
        return getInstance().uploadImgInstance(multipartFile, path, fileName, storeType);
    }

    /**
     * 删除图片 - 静态方法代理
     */
    public static DeleteResult deleteImg(String path, String fileName) {
        return getInstance().deleteImgInstance(path, fileName);
    }

    /**
     * 获取文件MD5 - 静态方法代理
     */
    public static UploadResult getFileMd5(String bizType, String fileKey) {
        return getInstance().getFileMd5Instance(bizType, fileKey);
    }

    /**
     * 检查是否为图片文件 - 静态方法代理
     */
    public static boolean isImageFile(String fileName) {
        return getInstance().isImageFileInstance(fileName);
    }

    // ================================
    // 实例方法实现
    // ================================

    /**
     * 上传图片 - 实例方法
     */
    public UploadResult uploadImgInstance(File file, String path) {
        String fileName = UUID.randomUUID().variant() + "_" + System.currentTimeMillis() + ".jpg";
        return uploadImg2Instance(file, path, fileName, "local");
    }

    /**
     * 上传图片 - 实例方法
     */
    public UploadResult uploadImg2Instance(File file, String path, String fileName, String storeType) {
        UploadResult result = new UploadResult();
        if (file == null || path == null) {
            result.setCode(-1);
            result.setMsg("param is empty !");
            return result;
        }
        return uploadInstance(file, path, fileName);
    }

    /**
     * 核心上传方法 - 实例方法
     */
    public UploadResult uploadInstance(File file, String path, String fileName) {
        UploadResult result = new UploadResult();
        PostMethod filePost = new PostMethod(pathDoMain + "/FileUploadServlet");

        try {
            NameValuePair[] pairs = new NameValuePair[2];
            pairs[0] = new NameValuePair("path", path);
            pairs[1] = new NameValuePair("fileName", fileName);

            filePost.setQueryString(pairs);
            Part[] parts = new Part[] { new FilePart(fileName, file) };
            filePost.setRequestEntity(new MultipartRequestEntity(parts, filePost.getParams()));
            HttpClient client = new HttpClient();
            client.getHttpConnectionManager().getParams().setConnectionTimeout(200000);
            client.getHttpConnectionManager().getParams().setSoTimeout(200000);
            int status = client.executeMethod(filePost);
            String fileKey = filePost.getResponseBodyAsString();

            if (status == HttpStatus.SC_OK) {
                log.info("上传成功");
                log.info("🔍 调试信息 - 服务器返回的fileKey: [" + fileKey + "]");
                log.info("🔍 调试信息 - fileKey长度: " + (fileKey != null ? fileKey.length() : "null"));
                result.setCode(0);
                result.setFileKey(fileKey);
                result.setMsg("success");
                
                // 服务器返回的fileKey应该是完整的相对路径
                if (fileKey != null && !fileKey.trim().isEmpty()) {
                    String trimmedFileKey = fileKey.trim();
                    log.info("🔍 调试信息 - 处理后的fileKey: [" + trimmedFileKey + "]");
                    if (!trimmedFileKey.startsWith("/")) {
                        trimmedFileKey = "/" + trimmedFileKey;
                    }
                    log.info("🔍 调试信息 - 最终的urlPath: [" + trimmedFileKey + "]");
                    result.setUrlPath(trimmedFileKey);
                } else {
                    log.info("🔍 调试信息 - fileKey为空，使用默认路径构建");
                    java.util.Calendar cal = java.util.Calendar.getInstance();
                    int year = cal.get(java.util.Calendar.YEAR);
                    int month = cal.get(java.util.Calendar.MONTH) + 1;
                    int day = cal.get(java.util.Calendar.DAY_OF_MONTH);
                    
                    String urlPath = path;
                    if (!urlPath.startsWith("/")) {
                        urlPath = "/" + urlPath;
                    }
                    if (!urlPath.endsWith("/")) {
                        urlPath = urlPath + "/";
                    }
                    urlPath = urlPath + year + "/" + month + "/" + day + "/" + fileName;

                    log.info("🔍 调试信息 - 默认构建的urlPath（带日期目录）: [" + urlPath + "]");
                    result.setUrlPath(urlPath);
                }
            } else {
                log.info("上传失败  error :" + fileKey);
                result.setCode(-1);
                result.setMsg("error");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.setCode(-1);
            result.setMsg("图片服务器异常: " + ex.getMessage());
        } finally {
            filePost.releaseConnection();
        }
        return result;
    }

    /**
     * 上传图片（InputStream） - 实例方法
     */
    public UploadResult uploadImg2Instance(InputStream in, String path, String fileName) {
        FileUtils.mkdir(tmpDir);
        File file = FileUtils.createFile(in, tmpDir + "/" + fileName);
        UploadResult result = uploadInstance(file, path, fileName);
        FileUtils.del(tmpDir);
        return result;
    }

    /**
     * 上传图片（带过期时间） - 实例方法
     */
    public UploadResult uploadImgInstance(File file, String path, String fileName, int expirationDays) {
        UploadResult result = new UploadResult();
        if (file == null || path == null) {
            result.setCode(-1);
            result.setMsg("param is empty !");
            return result;
        }
        return uploadInstance(file, path, fileName, expirationDays);
    }

    /**
     * 上传（带过期时间） - 实例方法
     */
    public UploadResult uploadInstance(File file, String path, String fileName, int expirationDays) {
        UploadResult result = new UploadResult();
        PostMethod filePost = new PostMethod(pathDoMain + "/FileUploadServlet");

        try {
            NameValuePair[] pairs = new NameValuePair[3];
            pairs[0] = new NameValuePair("path", path);
            pairs[1] = new NameValuePair("fileName", fileName);
            pairs[2] = new NameValuePair("expirationDays", expirationDays + "");

            filePost.setQueryString(pairs);
            Part[] parts = new Part[] { new FilePart(fileName, file) };
            filePost.setRequestEntity(new MultipartRequestEntity(parts, filePost.getParams()));
            HttpClient client = new HttpClient();
            client.getHttpConnectionManager().getParams().setConnectionTimeout(200000);
            client.getHttpConnectionManager().getParams().setSoTimeout(200000);
            int status = client.executeMethod(filePost);
            String fileKey = filePost.getResponseBodyAsString();

            if (status == HttpStatus.SC_OK) {
                log.info("上传成功");
                log.info("🔍 调试信息 - 服务器返回的fileKey: [" + fileKey + "]");
                log.info("🔍 调试信息 - fileKey长度: " + (fileKey != null ? fileKey.length() : "null"));
                result.setCode(0);
                result.setFileKey(fileKey);
                result.setSize(file.length());
                result.setMsg("success");
                
                if (fileKey != null && !fileKey.trim().isEmpty()) {
                    String trimmedFileKey = fileKey.trim();
                    log.info("🔍 调试信息 - 处理后的fileKey: [" + trimmedFileKey + "]");
                    if (!trimmedFileKey.startsWith("/")) {
                        trimmedFileKey = "/" + trimmedFileKey;
                    }
                    log.info("🔍 调试信息 - 最终的urlPath: [" + trimmedFileKey + "]");
                    result.setUrlPath(trimmedFileKey);
                } else {
                    log.info("🔍 调试信息 - fileKey为空，使用默认路径构建");
                    java.util.Calendar cal = java.util.Calendar.getInstance();
                    int year = cal.get(java.util.Calendar.YEAR);
                    int month = cal.get(java.util.Calendar.MONTH) + 1;
                    int day = cal.get(java.util.Calendar.DAY_OF_MONTH);
                    
                    String urlPath = path;
                    if (!urlPath.startsWith("/")) {
                        urlPath = "/" + urlPath;
                    }
                    if (!urlPath.endsWith("/")) {
                        urlPath = urlPath + "/";
                    }
                    urlPath = urlPath + year + "/" + month + "/" + day + "/" + fileName;

                    log.info("🔍 调试信息 - 默认构建的urlPath（带日期目录）: [" + urlPath + "]");
                    result.setUrlPath(urlPath);
                }
            } else {
                log.info("上传失败  error :" + fileKey);
                result.setCode(-1);
                result.setMsg("error");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.setCode(-1);
            result.setMsg("图片服务器异常: " + ex.getMessage());
        } finally {
            filePost.releaseConnection();
        }
        return result;
    }

    /**
     * 删除图片 - 实例方法
     */
    public DeleteResult deleteImgInstance(String path, String fileName) {
        UploadResult result = new UploadResult();
        if (fileName == null || path == null) {
            result.setCode(-1);
            result.setMsg("param is empty !");
        }
        return ImgService.delete(path, fileName);
    }

    /**
     * 上传图片（MultipartFile） - 实例方法
     */
    public UploadResult uploadInstance(MultipartFile multipartFile, String bizType) {
        String originalFilename = multipartFile.getOriginalFilename();
        String fileType = getFileTypeInstance(multipartFile.getOriginalFilename());
        long size = multipartFile.getSize();
        String uid = UUID.randomUUID().toString();
        
        // 创建项目相对路径的临时目录
        String tempDirPath = System.getProperty("user.dir") + File.separator + tmpDir;
        File tempDir = new File(tempDirPath);
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        
        File tempFile = null;
        try {
            // 将MultipartFile转换为临时文件
            String tempFileName = uid + "." + fileType;
            tempFile = new File(tempDir, tempFileName);
            multipartFile.transferTo(tempFile);
            
            System.out.println("🔍 调试信息 - 原始文件名: [" + originalFilename + "]");
            System.out.println("🔍 调试信息 - 原始业务类型: [" + bizType + "]");
            
            // 修复业务类型：确保没有前导斜杠，和测试方法保持一致
            String normalizedBizType = bizType;
            if (normalizedBizType != null && normalizedBizType.startsWith("/")) {
                normalizedBizType = normalizedBizType.substring(1);
            }

            log.info("🔍 调试信息 - 标准化业务类型: [" + normalizedBizType + "]");
            log.info("🔍 调试信息 - 临时文件路径: [" + tempFile.getAbsolutePath() + "]");
            log.info("🔍 调试信息 - 临时文件大小: [" + tempFile.length() + "] bytes");
            
            // 完全按照 uploadImg(File, String) 的方式调用
            UploadResult result = uploadImgInstance(tempFile, normalizedBizType);
            
            // 设置额外的信息
            if (result != null) {
                result.setFileName(originalFilename);
                result.setFileType(fileType);
                result.setBizType(bizType);
                result.setSize(size);
            }
            
            return result;
            
        } catch (Exception e) {
            e.printStackTrace();
            UploadResult errorResult = new UploadResult();
            errorResult.setCode(-1);
            errorResult.setMsg("上传失败: " + e.getMessage());
            return errorResult;
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                System.out.println("🔍 调试信息 - 临时文件清理: [" + (deleted ? "成功" : "失败") + "]");
            }
            // 清理临时目录（如果为空）
            if (tempDir.exists() && tempDir.list().length == 0) {
                tempDir.delete();
            }
        }
    }

    /**
     * 获取文件类型 - 实例方法
     */
    private String getFileTypeInstance(String fileName) {
        int index = fileName.lastIndexOf(".");
        if (index == -1) {
            return null;
        }
        return fileName.substring(index + 1);
    }

    /**
     * 检查是否为图片文件 - 实例方法
     */
    public boolean isImageFileInstance(String fileName) {
        return fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") ||
                fileName.endsWith(".png") || fileName.endsWith(".gif") ||
                fileName.endsWith(".bmp");
    }

    /**
     * 上传图片（MultipartFile完整参数） - 实例方法
     */
    public UploadResult uploadImgInstance(MultipartFile multipartFile, String path, String fileName, String storeType) {
        FileUtils.mkdir(tmpDir);
        UploadResult result = null;
        try {
            File file = FileUtils.createFile(multipartFile.getInputStream(), tmpDir + "/" + fileName);
            result = uploadImg2Instance(file, path, fileName, storeType);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            FileUtils.del(tmpDir);
        }
        return result;
    }

    /**
     * 获取文件MD5 - 实例方法
     */
    public UploadResult getFileMd5Instance(String bizType, String fileKey) {
        UploadResult result = new UploadResult();
        if (StringUtils.isBlank(bizType) || StringUtils.isBlank(fileKey)) {
            result.setCode(-1);
            result.setMsg("bizType or fileKey is empty !");
            return result;
        }
        return ImgService.getFileMd5(bizType, fileKey);
    }

    // ================================
    // 工具方法
    // ================================

    /**
     * 获取单例实例
     */
    private static ImgClientUtils getInstance() {
        if (instance == null) {
            throw new IllegalStateException("ImgClientUtils未初始化，请确保Spring容器已启动");
        }
        return instance;
    }

    /**
     * 获取配置的图片服务域名 - 静态方法（用于测试和兼容性）
     */
    public static String getPathDoMainStatic() {
        return getInstance().getPathDoMain();
    }

    /**
     * 获取配置的临时目录 - 静态方法（用于测试和兼容性）
     */
    public static String getTmpDirStatic() {
        return getInstance().getTmpDir();
    }

    /**
     * 获取文件类型 - 静态方法（保持兼容性）
     */
    private static String getFileType(String fileName) {
        return getInstance().getFileTypeInstance(fileName);
    }
}
