package com.ltgj.common.utils.file;

import com.tem.imgserver.client.DeleteResult;
import com.tem.imgserver.client.ImgService;
import com.tem.imgserver.client.UploadResult;
import com.tem.imgserver.util.FileUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.multipart.FilePart;
import org.apache.commons.httpclient.methods.multipart.MultipartRequestEntity;
import org.apache.commons.httpclient.methods.multipart.Part;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2025/6/5
 * @description:
 */
public class ImgClientUtils {

    public static String pathDoMain="https://dev-img-trip.lvtuguanjia.com";

    public static String tmpDir="/test/hotel/images";

    // 上传图片
    public static UploadResult uploadImg(File file, String path) {
        String fileName = UUID.randomUUID().variant() + "_" + System.currentTimeMillis() + ".jpg";
        return uploadImg2(file, path, fileName,"local");
    }

    // 上传图片
    public static UploadResult uploadImg2(File file, String path, String filaName,String storeType) {
        UploadResult result = new UploadResult();
        if (file == null || path == null) {
            result.setCode(-1);
            result.setMsg("param is empty !");
        }
        return upload(file, path, filaName);
    }

    public static UploadResult upload(File file, String path, String fileName) {
        UploadResult result = new UploadResult();
        PostMethod filePost = new PostMethod(pathDoMain + "/FileUploadServlet");

        try {
            NameValuePair[] pairs = new NameValuePair[2];
            pairs[0] = new NameValuePair("path", path);
            pairs[1] = new NameValuePair("fileName", fileName);
//			if (storeType != null) {
//				pairs[2] = new NameValuePair("storeType", storeType);
//			}
            filePost.setQueryString(pairs);
            Part[] parts = new Part[] { new FilePart(file.getName(), file) };
            filePost.setRequestEntity(new MultipartRequestEntity(parts, filePost.getParams()));
            HttpClient client = new HttpClient();
            client.getHttpConnectionManager().getParams().setConnectionTimeout(200000);
            client.getHttpConnectionManager().getParams().setSoTimeout(200000);
            int status = client.executeMethod(filePost);
            String fileKey = filePost.getResponseBodyAsString();

            if (status == HttpStatus.SC_OK) {
                System.out.println("上传成功");
                result.setCode(0);
                result.setFileKey(fileKey);
                result.setMsg("success");
            } else {
                System.out.println("上传失败  error :" + fileKey);
                result.setCode(-1);
                result.setMsg("error");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.setCode(-1);
            result.setMsg("图片服务器异常: " + ex.getMessage());
        } finally {
            filePost.releaseConnection();
        }
        return result;
    }


    public static UploadResult uploadImg2(InputStream in, String path, String fileName) {
        FileUtils.mkdir(tmpDir);
        File file = FileUtils.createFile(in, tmpDir + "/" + fileName);
        UploadResult result = upload(file, path, fileName);
        FileUtils.del(tmpDir);
        return result;
    }

    /**
     * 上传文件
     * @param file
     * @param path
     * @param filaName
     * @param expirationDays 过期时间
     * @return
     */
    public static UploadResult uploadImg(File file, String path, String filaName, int expirationDays) {
        UploadResult result = new UploadResult();
        if (file == null || path == null) {
            result.setCode(-1);
            result.setMsg("param is empty !");
        }
        return upload(file, path, filaName, expirationDays);
    }

    /**
     * 上传
     * @param file
     * @param path
     * @param fileName
     * @param expirationDays 过期时间
     * @return
     */
    public static UploadResult upload(File file, String path, String fileName ,int expirationDays) {
        UploadResult result = new UploadResult();
        PostMethod filePost = new PostMethod(pathDoMain + "/FileUploadServlet");

        try {
            NameValuePair[] pairs = new NameValuePair[3];
            pairs[0] = new NameValuePair("path", path);
            pairs[1] = new NameValuePair("fileName", fileName);
            pairs[2] = new NameValuePair("expirationDays", expirationDays+"");

//			if (storeType != null) {
//				pairs[2] = new NameValuePair("storeType", storeType);
//			}
            filePost.setQueryString(pairs);
            Part[] parts = new Part[] { new FilePart(file.getName(), file) };
            filePost.setRequestEntity(new MultipartRequestEntity(parts, filePost.getParams()));
            HttpClient client = new HttpClient();
            client.getHttpConnectionManager().getParams().setConnectionTimeout(200000);
            client.getHttpConnectionManager().getParams().setSoTimeout(200000);
            int status = client.executeMethod(filePost);
            String fileKey = filePost.getResponseBodyAsString();

            if (status == HttpStatus.SC_OK) {
                System.out.println("上传成功");
                result.setCode(0);
                result.setFileKey(fileKey);
                result.setSize(file.length());
                result.setMsg("success");
            } else {
                System.out.println("上传失败  error :" + fileKey);
                result.setCode(-1);
                result.setMsg("error");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.setCode(-1);
            result.setMsg("图片服务器异常: " + ex.getMessage());
        } finally {
            filePost.releaseConnection();
        }
        return result;
    }


    public static DeleteResult deleteImg(String path, String fileName) {
        UploadResult result = new UploadResult();
        if (fileName == null || path == null) {
            result.setCode(-1);
            result.setMsg("param is empty !");
        }
        return ImgService.delete(path, fileName);
    }

    // 上传图片
    public static UploadResult upload(MultipartFile multipartFile, String bizType) {
        String originalFilename = multipartFile.getOriginalFilename();
        String fileType = getFileType(multipartFile.getOriginalFilename());
        long size = multipartFile.getSize();
        String uid = UUID.randomUUID().toString();
        UploadResult result = uploadImg(multipartFile, bizType, uid + "." + fileType, null);
        result.setFileId(uid);
        result.setBizType(bizType);
        result.setFileName(originalFilename);
        result.setFileType(fileType);
        result.setSize(size);
        return result;
    }

    // 获取文件类型
    private static String getFileType(String fileName) {
        int index = fileName.lastIndexOf(".");
        if (index == -1) {
            return null;
        }
        return fileName.substring(index + 1);
    }

    /**
     * 辅助方法：检查是否为图片文件
     * @param fileName 文件名
     * @return 是否为图片文件
     */
    public static boolean isImageFile(String fileName) {
        return fileName.endsWith(".jpg") || fileName.endsWith(".jpeg") ||
                fileName.endsWith(".png") || fileName.endsWith(".gif") ||
                fileName.endsWith(".bmp");
    }

    // 上传图片
    public static UploadResult uploadImg(MultipartFile multipartFile, String path, String fileName, String storeType) {
        FileUtils.mkdir(tmpDir);
        UploadResult result = null;
        try {
            File file = FileUtils.createFile(multipartFile.getInputStream(), tmpDir + "/" + fileName);
            result = uploadImg2(file, path, fileName,storeType);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            FileUtils.del(tmpDir);
        }
        return result;
    }


    public static UploadResult getFileMd5(String bizType, String fileKey) {
        UploadResult result = new UploadResult();
        if (StringUtils.isBlank(bizType) || StringUtils.isBlank(fileKey)) {
            result.setCode(-1);
            result.setMsg("bizType or fileKey is empty !");
            return result;
        }
        return ImgService.getFileMd5(bizType, fileKey);
    }
}
