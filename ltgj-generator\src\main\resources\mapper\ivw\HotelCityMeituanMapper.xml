<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.HotelCityMeituanMapper">
  <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.meituan.HotelCityMeituan">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="province_id" jdbcType="INTEGER" property="provinceId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="location_id" jdbcType="INTEGER" property="locationId" />
    <result column="location_name" jdbcType="VARCHAR" property="locationName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, province_id, province_name, city_id, city_name, location_id, location_name
  </sql>
  <select id="selectByExample" parameterType="com.ltgj.ivw.domain.meituan.HotelCityMeituanExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hotel_city_meituan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.ltgj.ivw.domain.meituan.HotelCityMeituanExample">
    delete from hotel_city_meituan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ltgj.ivw.domain.meituan.HotelCityMeituan">
    insert into hotel_city_meituan (id, province_id, province_name, 
      city_id, city_name, location_id, 
      location_name)
    values (#{id,jdbcType=INTEGER}, #{provinceId,jdbcType=INTEGER}, #{provinceName,jdbcType=VARCHAR}, 
      #{cityId,jdbcType=INTEGER}, #{cityName,jdbcType=VARCHAR}, #{locationId,jdbcType=INTEGER}, 
      #{locationName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ltgj.ivw.domain.meituan.HotelCityMeituan">
    insert into hotel_city_meituan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="locationId != null">
        location_id,
      </if>
      <if test="locationName != null">
        location_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="locationId != null">
        #{locationId,jdbcType=INTEGER},
      </if>
      <if test="locationName != null">
        #{locationName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ltgj.ivw.domain.meituan.HotelCityMeituanExample" resultType="java.lang.Long">
    select count(*) from hotel_city_meituan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hotel_city_meituan
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.provinceId != null">
        province_id = #{record.provinceId,jdbcType=INTEGER},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=INTEGER},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.locationId != null">
        location_id = #{record.locationId,jdbcType=INTEGER},
      </if>
      <if test="record.locationName != null">
        location_name = #{record.locationName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hotel_city_meituan
    set id = #{record.id,jdbcType=INTEGER},
      province_id = #{record.provinceId,jdbcType=INTEGER},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      city_id = #{record.cityId,jdbcType=INTEGER},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      location_id = #{record.locationId,jdbcType=INTEGER},
      location_name = #{record.locationName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>