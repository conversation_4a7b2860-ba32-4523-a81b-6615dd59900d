package com.ltgj.ivw.service;

import com.alibaba.fastjson2.JSONObject;
import com.ltgj.ivw.domain.HotelInfoHsjl;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnHsjl;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * 红色加力酒店数据Service接口
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
public interface IHotelInfoHsjlService extends BaseHotelService<HotelGnBase, HotelGnHsjl>
{
    /**
     * 查询红色加力酒店数据
     *
     * @param id 红色加力酒店数据主键
     * @return 红色加力酒店数据
     */
    public HotelInfoHsjl selectHotelInfoHsjlById(String id);

    /**
     * 查询红色加力酒店数据列表
     *
     * @param hotelInfoHsjl 红色加力酒店数据
     * @return 红色加力酒店数据集合
     */
    public List<HotelInfoHsjl> selectHotelInfoHsjlList(HotelInfoHsjl hotelInfoHsjl);

    public List<HotelInfoHsjl> selectHotelInfoHsjlList2(HotelInfoHsjl hotelInfoHsjl);

    /**
     * 新增红色加力酒店数据
     *
     * @param hotelInfoHsjl 红色加力酒店数据
     * @return 结果
     */
    public int insertHotelInfoHsjl(HotelInfoHsjl hotelInfoHsjl);

    /**
     * 修改红色加力酒店数据
     *
     * @param hotelInfoHsjl 红色加力酒店数据
     * @return 结果
     */
    public int updateHotelInfoHsjl(HotelInfoHsjl hotelInfoHsjl);

    /**
     * 批量删除红色加力酒店数据
     *
     * @param ids 需要删除的红色加力酒店数据主键集合
     * @return 结果
     */
    public int deleteHotelInfoHsjlByIds(String[] ids);

    /**
     * 删除红色加力酒店数据信息
     *
     * @param id 红色加力酒店数据主键
     * @return 结果
     */
    public int deleteHotelInfoHsjlById(String id);

    void updateSuccess(List<String> idList);

    List<HotelInfoHsjl> selectHotelInfoNotMapping(List<String> idList, int flag);

    List<String> selectNotMappingIdList();

    List<HotelInfoHsjl> selectListByIdList(List<String> idList);

    public int insertOrUpdateHotelInfoHsjl(HotelInfoHsjl hotelInfoHsjl);

    HotelGnHsjl buildHotelGnHsjl(PlatEnum platEnum, JSONObject jsonObject);

    Map<String, Object> getStatus(PlatEnum platEnum);

    void resetStatus();
    void resetStatus(PlatEnum platEnum);

    void updateHsjlAll(String corpId, String userName) throws Exception;

    void updateHsjlAll(String corpId, String userName, PlatEnum platEnum, String partnerCode, String secureKey, ExecutorService threadPool) throws Exception;
}
