package com.ltgj.sdk.qiantao.base;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class QiantaoBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    
    /**
     * 是否成功
     */
    private Boolean isSuccess;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    public Boolean success() {
        return Objects.equals(Boolean.TRUE, this.isSuccess);
    }
} 