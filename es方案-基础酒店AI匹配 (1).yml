app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: workflow
  name: es方案-基础酒店AI匹配
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: package
  value:
    plugin_unique_identifier: langgenius/tongyi:0.0.27@4bb3f3eb6149b01f92aa6038a6bb074cc4224b8c015f54c888e5c5a30fc1ab50
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: code
      id: 1730856295697-source-1747719953446-target
      selected: false
      source: '1730856295697'
      sourceHandle: source
      target: '1747719953446'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: iteration
      id: 1747719953446-source-1747720103485-target
      selected: false
      source: '1747719953446'
      sourceHandle: source
      target: '1747720103485'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        iteration_id: '1747720103485'
        sourceType: iteration-start
        targetType: code
      id: 1747720103485start-source-1747723429138-target
      selected: false
      source: 1747720103485start
      sourceHandle: source
      target: '1747723429138'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1747720103485'
        sourceType: llm
        targetType: code
      id: 1752050793475-source-1752052407664-target
      source: '1752050793475'
      sourceHandle: source
      target: '1752052407664'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: iteration
        targetType: code
      id: 1747720103485-source-1752116561554-target
      source: '1747720103485'
      sourceHandle: source
      target: '1752116561554'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: end
      id: 1752116561554-source-1747724473498-target
      source: '1752116561554'
      sourceHandle: source
      target: '1747724473498'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        isInLoop: false
        iteration_id: '1747720103485'
        sourceType: code
        targetType: llm
      id: 1747723429138-source-1752050793475-target
      source: '1747723429138'
      sourceHandle: source
      target: '1752050793475'
      targetHandle: target
      type: custom
      zIndex: 1002
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 酒店列表
          max_length: 48000
          options: []
          required: true
          type: paragraph
          variable: hotelList
        - label: requirements
          max_length: 48
          options: []
          required: false
          type: text-input
          variable: requirements
      height: 116
      id: '1730856295697'
      position:
        x: 30
        y: 393.5
      positionAbsolute:
        x: 30
        y: 393.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "\nfunction main({arg1}) {\n    var hotetListJson = JSON.parse(arg1);\n\
          \    return {\n        result: hotetListJson\n    }\n}\n"
        code_language: javascript
        desc: ''
        outputs:
          result:
            children: null
            type: array[object]
        selected: false
        title: 解析json
        type: code
        variables:
        - value_selector:
          - '1730856295697'
          - hotelList
          variable: arg1
      height: 54
      id: '1747719953446'
      position:
        x: 334
        y: 393.5
      positionAbsolute:
        x: 334
        y: 393.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_handle_mode: remove-abnormal-output
        height: 387
        is_parallel: true
        iterator_selector:
        - '1747719953446'
        - result
        output_selector:
        - '1752052407664'
        - result
        output_type: array[string]
        parallel_nums: 5
        selected: false
        start_node_id: 1747720103485start
        title: 循环执行
        type: iteration
        width: 1420
      height: 387
      id: '1747720103485'
      position:
        x: 638
        y: 393.5
      positionAbsolute:
        x: 638
        y: 393.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1420
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1747720103485start
      parentId: '1747720103485'
      position:
        x: 60
        y: 82
      positionAbsolute:
        x: 698
        y: 475.5
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        code: "function main({ arg1 }) {\n    // 直接提取单个字段到顶层\n    const { id, name,\
          \ city, phone, address, mult } = arg1;\n\n    // 从 mult 提取 list 数据并转换为 JSON\
          \ 字符串\n    const list = Array.isArray(mult) ? mult.map(item => ({\n    \
          \    id: item.id,\n        name: item.name,\n        city: item.city,\n\
          \        phone: item.phone,\n        address: item.address\n    })) : [];\n\
          \n    const listJsonString = JSON.stringify(list);\n\n    return {\n   \
          \     id,\n        name,\n        city,\n        phone,\n        address,\n\
          \        list: listJsonString\n    };\n}\n"
        code_language: javascript
        desc: ''
        error_strategy: fail-branch
        isInIteration: true
        iteration_id: '1747720103485'
        outputs:
          address:
            children: null
            type: string
          city:
            children: null
            type: string
          id:
            children: null
            type: string
          list:
            children: null
            type: string
          name:
            children: null
            type: string
          phone:
            children: null
            type: string
        selected: false
        title: 解析单个酒店
        type: code
        variables:
        - value_selector:
          - '1747720103485'
          - item
          variable: arg1
      height: 90
      id: '1747723429138'
      parentId: '1747720103485'
      position:
        x: 204
        y: 61
      positionAbsolute:
        x: 842
        y: 454.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1752116561554'
          - result
          variable: output
        selected: false
        title: End 2
        type: end
      height: 90
      id: '1747724473498'
      position:
        x: 2422
        y: 393.5
      positionAbsolute:
        x: 2422
        y: 393.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1747720103485'
        model:
          completion_params: {}
          mode: chat
          name: qwen-plus
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: a0dea368-2476-465c-9e9f-3fb72aade61c
          role: system
          text: 你是一位酒店匹配专家
        - id: 6b803958-00fe-42de-abdb-cc6e7497d028
          role: user
          text: ';;;; 酒店数据完全匹配系统 - LISP实现

            ;;;; 严格按照匹配规则进行打分


            ;;; 辅助函数

            (defun string-trim-all (str)

            "去除字符串前后空格"

            (if str

            (string-trim ''(#\Space #\Tab #\Newline #\Return) str)

            ""))


            (defun normalize-city-name (city)

            "标准化城市名称，去除''市''后缀"

            (let ((trimmed (string-trim-all city)))

            (if (and (> (length trimmed) 1)

            (string= (subseq trimmed (1- (length trimmed))) "市"))

            (subseq trimmed 0 (1- (length trimmed)))

            trimmed)))


            (defun extract-phone-numbers (phone-str)

            "提取并标准化电话号码，过滤区号和国际前缀"

            (let ((phones ''()))

            (when phone-str

            (dolist (part (split-string phone-str ","))

            (let ((cleaned (string-trim-all part)))

            ;; 去除086前缀

            (when (and (>= (length cleaned) 3)

            (string= (subseq cleaned 0 3) "086"))

            (setf cleaned (subseq cleaned 3)))

            ;; 去除区号（格式：0xxx-）

            (let ((dash-pos (position #\- cleaned)))

            (when (and dash-pos (> dash-pos 0)

            (char= (char cleaned 0) #\0))

            (setf cleaned (subseq cleaned (1+ dash-pos)))))

            ;; 只保留数字

            (setf cleaned (remove-if-not #''digit-char-p cleaned))

            (when (> (length cleaned) 0)

            (push cleaned phones)))))

            (remove-duplicates phones :test #''string=)))


            (defun split-string (str delimiter)

            "按分隔符分割字符串"

            (let ((parts ''())

            (start 0))

            (loop for i from 0 below (length str)

            when (char= (char str i) delimiter)

            do (push (subseq str start i) parts)

            (setf start (1+ i))

            finally (push (subseq str start) parts))

            (remove-if (lambda (s) (= (length s) 0)) (nreverse parts))))


            ;;; 核心匹配函数


            (defun match-hotel-name (target-name candidate-name)

            "匹配酒店名称，完全一致得30分"

            (let ((name1 (string-trim-all target-name))

            (name2 (string-trim-all candidate-name)))

            (if (string= name1 name2)

            30

            0)))


            (defun match-hotel-address (target-addr candidate-addr)

            "匹配酒店地址，完全一致得30分"

            (let ((addr1 (string-trim-all target-addr))

            (addr2 (string-trim-all candidate-addr)))

            (if (string= addr1 addr2)

            30

            0)))


            (defun match-hotel-phone (target-phone candidate-phone)

            "匹配酒店电话，任一号码匹配得30分"

            (let ((phones1 (extract-phone-numbers target-phone))

            (phones2 (extract-phone-numbers candidate-phone)))

            (if (some (lambda (p1)

            (member p1 phones2 :test #''string=))

            phones1)

            30

            0)))


            (defun match-hotel-city (target-city candidate-city)

            "匹配城市，完全一致得10分"

            (let ((city1 (normalize-city-name target-city))

            (city2 (normalize-city-name candidate-city)))

            (if (string= city1 city2)

            10

            0)))


            (defun calculate-total-score (name-score addr-score phone-score city-score)

            "计算总分"

            (+ name-score addr-score phone-score city-score))


            (defun generate-reason (name-score addr-score phone-score city-score)

            "生成匹配原因"

            (let ((reasons ''()))

            (when (= name-score 0) (push "名称不一致" reasons))

            (when (= addr-score 0) (push "地址不一致" reasons))

            (when (= phone-score 0) (push "电话不匹配" reasons))

            (when (= city-score 0) (push "城市不一致" reasons))

            (cond

            ((null reasons) "完全匹配")

            ((= (length reasons) 4) "不匹配")

            (t (format nil "~{~A~^,~}" reasons)))))


            (defun find-best-match (target-hotel candidate-list)

            "从候选列表中找到最佳匹配"

            (let ((best-match nil)

            (best-score -1)

            (best-details nil))

            (dolist (candidate candidate-list)

            (let* ((name-score (match-hotel-name

            (cdr (assoc ''name target-hotel))

            (cdr (assoc ''name candidate))))

            (addr-score (match-hotel-address

            (cdr (assoc ''address target-hotel))

            (cdr (assoc ''address candidate))))

            (phone-score (match-hotel-phone

            (cdr (assoc ''phone target-hotel))

            (cdr (assoc ''phone candidate))))

            (city-score (match-hotel-city

            (cdr (assoc ''city target-hotel))

            (cdr (assoc ''city candidate))))

            (total-score (calculate-total-score name-score addr-score

            phone-score city-score)))

            (when (> total-score best-score)

            (setf best-score total-score)

            (setf best-match candidate)

            (setf best-details (list name-score addr-score phone-score

            city-score total-score)))))

            (values best-match best-details)))


            (defun create-match-result (target-id target-hotel candidate-list)

            "创建匹配结果"

            (multiple-value-bind (best-match scores)

            (find-best-match target-hotel candidate-list)

            (if best-match

            (let ((name-score (first scores))

            (addr-score (second scores))

            (phone-score (third scores))

            (city-score (fourth scores))

            (total-score (fifth scores)))

            `((id . ,target-id)

            (targetId . ,(cdr (assoc ''id best-match)))

            (score . ,total-score)

            (nameScore . ,name-score)

            (addressScore . ,addr-score)

            (phoneScore . ,phone-score)

            (cityScore . ,city-score)

            (reason . ,(generate-reason name-score addr-score

            phone-score city-score))

            (remark . "")))

            ;; 未找到匹配

            `((id . ,target-id)

            (targetId . "")

            (score . 0)

            (nameScore . 0)

            (addressScore . 0)

            (phoneScore . 0)

            (cityScore . 0)

            (reason . "不匹配")

            (remark . "")))))


            (defun match-hotel-data (target-hotel candidate-list)

            "匹配单个目标酒店与候选列表"

            (let ((target-id (cdr (assoc ''id target-hotel))))

            (create-match-result target-id target-hotel candidate-list)))


            (defun batch-match-hotels (target-hotels candidate-list)

            "批量匹配酒店数据"

            (let ((results ''())

            (used-target-ids ''()))

            (dolist (target target-hotels)

            (let ((target-id (cdr (assoc ''id target))))

            (unless (member target-id used-target-ids :test #''string=)

            (push target-id used-target-ids)

            (push (match-hotel-data target candidate-list) results))))

            (nreverse results)))


            ;;; JSON输出函数


            (defun escape-json-string (str)

            "转义JSON字符串中的特殊字符"

            (if (null str) ""

            (substitute #\" #\''

            (substitute #\\ #\\ str))))


            (defun alist-to-json (alist)

            "将关联列表转换为JSON对象"

            (format nil "{~{\"~A\":~A~^,~}}"

            (mapcan (lambda (pair)

            (list (car pair)

            (if (stringp (cdr pair))

            (format nil "\"~A\"" (escape-json-string (cdr pair)))

            (cdr pair))))

            alist)))


            (defun results-to-json (results)

            "将结果列表转换为JSON数组"

            (if (= (length results) 1)

            (alist-to-json (first results))

            (format nil "[~{~A~^,~}]"

            (mapcar #''alist-to-json results))))


            ;;; 主程序入口


            (defun process-hotel-matching (target-data candidate-list)

            "处理酒店匹配请求"

            (let ((target-hotel `((id . ,(cdr (assoc ''id target-data)))

            (name . ,(cdr (assoc ''name target-data)))

            (address . ,(cdr (assoc ''address target-data)))

            (phone . ,(cdr (assoc ''phone target-data)))

            (city . ,(cdr (assoc ''city target-data))))))

            (let ((result (match-hotel-data target-hotel candidate-list)))

            (alist-to-json result))))


            ;;; 使用示例


            (defun test-hotel-matching ()

            "测试酒店匹配功能"

            (let ((target-data ''((id . "1")

            (name . "如家酒店(西安西京医院兴庆路店)")

            (address . "大学西路9号中梁柏仕公馆1号楼712室")

            (phone . "0771-3388885,08613876589809")

            (city . "西安市")))

            (candidate-list

            ''(((id . "abc")

            (name . "如家酒店(西安西京医院兴庆路店)")

            (address . "大学西路9号中梁柏仕公馆1号楼712室")

            (phone . "3388885")

            (city . "西安"))

            ((id . "def")

            (name . "如家酒店(火车站店)")

            (address . "火车站路1号")

            (phone . "5350777")

            (city . "北京")))))

            (process-hotel-matching target-data candidate-list)))


            ;;; 程序说明：

            ;;; 1. 严格按照匹配规则进行完全匹配评分

            ;;; 2. 酒店名称完全一致得30分，否则0分

            ;;; 3. 酒店地址完全一致得30分，否则0分

            ;;; 4. 酒店电话任一号码匹配得30分，否则0分（已过滤区号）

            ;;; 5. 城市完全一致得10分，否则0分

            ;;; 6. 总分为各项得分之和，不额外加分

            ;;; 7. 返回标准JSON格式，不重复输出id

            ;;; 8. 未匹配时targetId返回空字符串'
        selected: false
        structured_output:
          schema:
            additionalProperties: false
            properties:
              id:
                type: string
              reason:
                type: string
              score:
                type: string
              targetId:
                type: string
            required:
            - id
            - score
            - reason
            type: object
        structured_output_enabled: true
        title: LLM 2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1752050793475'
      parentId: '1747720103485'
      position:
        x: 810.1124318114946
        y: 65
      positionAbsolute:
        x: 1448.1124318114946
        y: 458.5
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        code: "\nfunction main({arg1}) {\n    return {\n        result: arg1\n   \
          \ }\n}\n"
        code_language: javascript
        desc: ''
        isInIteration: true
        isInLoop: false
        iteration_id: '1747720103485'
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码执行 4
        type: code
        variables:
        - value_selector:
          - '1752050793475'
          - text
          variable: arg1
      height: 54
      id: '1752052407664'
      parentId: '1747720103485'
      position:
        x: 1116
        y: 79
      positionAbsolute:
        x: 1754
        y: 472.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        code: "\nfunction main({arg1}) {\n    return {\n        result: JSON.stringify(arg1)\n\
          \    }\n}\n"
        code_language: javascript
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 代码执行 5
        type: code
        variables:
        - value_selector:
          - '1747720103485'
          - output
          variable: arg1
      height: 54
      id: '1752116561554'
      position:
        x: 2118
        y: 393.5
      positionAbsolute:
        x: 2118
        y: 393.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -644.0000000000005
      y: 221.2499999999999
      zoom: 0.5000000000000002
