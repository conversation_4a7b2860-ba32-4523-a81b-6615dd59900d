package com.ltgj.ivw.service.idempotent.dto;

import com.ltgj.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/5/28
 * @description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotelCityMappingDTO {

    /** 平台城市ID */
    @Excel(name = "平台城市ID")
    private String platNum;

    /** 供应平台标识，对应InterfacePlatEnum */
    @Excel(name = "供应平台标识，对应InterfacePlatEnum")
    private String interfacePlat;

    /** 本地城市ID */
    @Excel(name = "本地城市ID")
    private String localId;

    //重写toString
    @Override
    public String toString() {
        return platNum + "_" + interfacePlat + "_" + localId;
    }
}
