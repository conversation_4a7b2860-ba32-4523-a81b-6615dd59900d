package com.ltgj.ivw.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * hotel_city_geo
 */
@Data
public class HotelCityGeo implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 平台编码
     */
    private String interfacePlatId;

    /**
     * 城市id
     */
    private String cityId;

    /**
     * 商圈id
     */
    private String poiId;

    /**
     * 商圈名称
     */
    private String poiName;

    /**
     * 商圈英文名称
     */
    private String poiNameCn;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createdBy;

    private static final long serialVersionUID = 1L;
}