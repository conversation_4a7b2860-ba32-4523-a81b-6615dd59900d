package com.ltgj.ivw.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: HotelGnSupplier
 * @Description: 酒店-国内
 * @Author: nxy
 * @CreateDate: 2025/6/6
 * @UpdateUser: nxy
 * @UpdateDate: 2025/6/6
 */
@Data
public class HotelGnSupplier implements Serializable {
    /**
     * Database Column Remarks:
     *   ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String id;

    /**
     * Database Column Remarks:
     *   名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.name
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String name;

    /**
     * Database Column Remarks:
     *   英文名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.name_en
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String nameEn;

    /**
     * Database Column Remarks:
     *   酒店类型
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.type_id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private Integer typeId;

    /**
     * Database Column Remarks:
     *   开业时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.open_date
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String openDate;

    /**
     * Database Column Remarks:
     *   最后装修时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.decoration_date
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String decorationDate;

    /**
     * Database Column Remarks:
     *   电话
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.phone
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String phone;

    /**
     * Database Column Remarks:
     *   国家id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.country_id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String countryId;

    /**
     * Database Column Remarks:
     *   国家名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.country_name
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String countryName;

    /**
     * Database Column Remarks:
     *   省份id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.province_id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String provinceId;

    /**
     * Database Column Remarks:
     *   省份名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.province_name
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String provinceName;

    /**
     * Database Column Remarks:
     *   城市id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.city_id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String cityId;

    /**
     * Database Column Remarks:
     *   城市名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.city_name
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String cityName;

    /**
     * Database Column Remarks:
     *   区县id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.area_id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String areaId;

    /**
     * Database Column Remarks:
     *   区县名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.area_name
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String areaName;

    /**
     * Database Column Remarks:
     *   商圈id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.business_district_id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String businessDistrictId;

    /**
     * Database Column Remarks:
     *   商圈名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.business_district_name
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String businessDistrictName;

    /**
     * Database Column Remarks:
     *   地址
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.address
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String address;

    /**
     * Database Column Remarks:
     *   地址
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.address_en
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String addressEn;

    /**
     * Database Column Remarks:
     *   经度谷歌
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.lon_google
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private BigDecimal lonGoogle;

    /**
     * Database Column Remarks:
     *   纬度谷歌
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.lat_google
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private BigDecimal latGoogle;

    /**
     * Database Column Remarks:
     *   经度百度
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.lon_baidu
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private BigDecimal lonBaidu;

    /**
     * Database Column Remarks:
     *   纬度百度
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.lat_baidu
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private BigDecimal latBaidu;

    /**
     * Database Column Remarks:
     *   经度高德
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.lon_gaode
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private BigDecimal lonGaode;

    /**
     * Database Column Remarks:
     *   纬度高德
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.lat_gaode
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private BigDecimal latGaode;

    /**
     * Database Column Remarks:
     *   所属集团id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.group_id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String groupId;

    /**
     * Database Column Remarks:
     *   所属集团名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.group_name
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String groupName;

    /**
     * Database Column Remarks:
     *   品牌信息
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.brand_id
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String brandId;

    /**
     * Database Column Remarks:
     *   品牌信息
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.brand_name
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String brandName;

    /**
     * Database Column Remarks:
     *   星级
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.star
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String star;

    /**
     * Database Column Remarks:
     *   酒店首图
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.image
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String image;

    /**
     * Database Column Remarks:
     *   星级
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.score
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String score;

    /**
     * Database Column Remarks:
     *   简介
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.synopsis
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String synopsis;

    /**
     * Database Column Remarks:
     *   亮点
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.sparkle
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String sparkle;

    /**
     * Database Column Remarks:
     *   政策信息，包括：入住须知、入离政策、外宾政策、儿童政策、宠物政策等
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.policy_info
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String policyInfo;

    /**
     * Database Column Remarks:
     *   设施信息，包括：停车场、充电桩、电梯、行李寄存、餐厅、会议室、公共区域wifi、健身房、洗衣房等
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.facilities_info
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String facilitiesInfo;

    /**
     * Database Column Remarks:
     *   周边信息，包括：车站机场、景点、美食、购物
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.round_info
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String roundInfo;

    /**
     * Database Column Remarks:
     *   状态，0：无详情；1：未映射；7：验证无效；8：映射成功；9：映射失败；
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.status
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private Byte status;

    /**
     * Database Column Remarks:
     *   删除标识，0：未删除；1：删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.is_delete
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private Boolean isDelete;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.remark
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String remark;

    /**
     * Database Column Remarks:
     *   增量状态 0初始化 1 新增未处理 2 新增已处理
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.increment_status
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private Boolean incrementStatus;

    /**
     * Database Column Remarks:
     *   增量时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.increment_time
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private Date incrementTime;

    /**
     * Database Column Remarks:
     *   增量类型 0初始化 1上架 2更新 3下架
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.increment_type
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private Boolean incrementType;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.create_time
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   最后更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.update_time
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   创建人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.create_by
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String createBy;

    /**
     * Database Column Remarks:
     *   更新人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.update_by
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String updateBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve0
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve0;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve1
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve1;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve2
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve2;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve3
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve3;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve4
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve4;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve5
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve5;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve6
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve6;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve7
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve7;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve8
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve8;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_ketan.reserve9
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private String reserve9;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_gn_ketan
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private static final long serialVersionUID = 1L;

}
