package com.ltgj.ivw.service.idempotent.dto;

import com.ltgj.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2025/5/28
 * @description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotelCityDTO {
    /**
     * 市ID
     */
    @Excel(name = "城市ID")
    private String cityId;

    /**
     * $column.columnComment
     */
    @Excel(name = "平台编号")
    private String reserve1;

    //重写toString方法
    @Override
    public String toString() {
        return cityId + "_" + reserve1;
    }
}
