package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.domain.HotelInfo;
import com.ltgj.ivw.domain.HotelInfoQiantao;
import com.ltgj.ivw.domain.dto.HotelDetailDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnBaseCommonMapper;
import com.ltgj.ivw.mapper.HotelInfoQiantaoMapper;
import com.ltgj.ivw.service.IHotelGnBaseService;
import com.ltgj.ivw.service.IHotelInfoQiantaoService;
import com.ltgj.ivw.service.IHotelInfoService;
import com.ltgj.ivw.utils.BigUtils;
import com.ltgj.ivw.utils.hotelApi.ChailvgjApi;
import com.ltgj.ivw.utils.hotelApi.QiantaoApi;
import com.ltgj.supplier.common.domain.FacilitiesInfo;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import com.ltgj.supplier.common.gn.domain.HotelGnQiantao;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.common.gn.service.HotelGnIdMappingService;
import com.ltgj.supplier.common.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 千淘酒店数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Service("hotelInfoQiantaoService")
@Slf4j
public class HotelInfoQiantaoServiceImpl extends BaseHotelServiceImpl<HotelGnBase, HotelGnQiantao> implements IHotelInfoQiantaoService, IHotelInfoService, IHotelGnBaseService {
    @Autowired
    private HotelInfoQiantaoMapper hotelInfoQiantaoMapper;
    @Autowired
    private HotelGnBaseService hotelGnBaseService;
    @Autowired
    private HotelCityServiceImpl hotelCityServiceImpl;
    @Autowired
    private HotelGnBaseCommonMapper<HotelGnQiantao> mapper;
    @Autowired
    private HotelGnIdMappingService hotelGnIdMappingService;

    @Override
    public HotelInfo selectHotelInfoById(String id) {
        return this.hotelInfoQiantaoMapper.selectHotelInfoQiantaoById(id);
    }

    @Override
    public int updateHotelInfo(HotelInfo hotelInfo) {
        return this.hotelInfoQiantaoMapper.updateHotelInfoQiantao((HotelInfoQiantao) hotelInfo);
    }

    /**
     * 查询千淘酒店数据
     *
     * @param id 千淘酒店数据主键
     * @return 千淘酒店数据
     */
    @Override
    public HotelInfoQiantao selectHotelInfoQiantaoById(String id) {
        return this.hotelInfoQiantaoMapper.selectHotelInfoQiantaoById(id);
    }

    /**
     * 查询千淘酒店数据列表
     *
     * @param hotelInfoQiantao 千淘酒店数据
     * @return 千淘酒店数据
     */
    @Override
    public List<HotelInfoQiantao> selectHotelInfoQiantaoList(HotelInfoQiantao hotelInfoQiantao) {
        return this.hotelInfoQiantaoMapper.selectHotelInfoQiantaoList(hotelInfoQiantao);
    }

    @Override
    public List<HotelInfoQiantao> selectHotelInfoQiantaoList2(HotelInfoQiantao hotelInfoQiantao) {
        return this.hotelInfoQiantaoMapper.selectHotelInfoQiantaoList2(hotelInfoQiantao);
    }

    /**
     * 新增千淘酒店数据
     *
     * @param hotelInfoQiantao 千淘酒店数据
     * @return 结果
     */
    @Override
    public int insertHotelInfoQiantao(HotelInfoQiantao hotelInfoQiantao) {
        hotelInfoQiantao.setCreateTime(DateUtils.getNowDate());
        return this.hotelInfoQiantaoMapper.insertHotelInfoQiantao(hotelInfoQiantao);
    }

    /**
     * 修改千淘酒店数据
     *
     * @param hotelInfoQiantao 千淘酒店数据
     * @return 结果
     */
    @Override
    public int updateHotelInfoQiantao(HotelInfoQiantao hotelInfoQiantao) {
        hotelInfoQiantao.setUpdateTime(DateUtils.getNowDate());
        return this.hotelInfoQiantaoMapper.updateHotelInfoQiantao(hotelInfoQiantao);
    }

    /**
     * 批量删除千淘酒店数据
     *
     * @param ids 需要删除的千淘酒店数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoQiantaoByIds(String[] ids) {
        return this.hotelInfoQiantaoMapper.deleteHotelInfoQiantaoByIds(ids);
    }

    /**
     * 删除千淘酒店数据信息
     *
     * @param id 千淘酒店数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoQiantaoById(String id) {
        return this.hotelInfoQiantaoMapper.deleteHotelInfoQiantaoById(id);
    }

    @Override
    public void updateSuccess(List<String> updateSuccessList) {
        this.hotelInfoQiantaoMapper.updateSuccess(updateSuccessList);
    }

    @Override
    public List<HotelInfoQiantao> selectHotelInfoNotMapping(List<String> idList, int flag) {
        return this.hotelInfoQiantaoMapper.selectHotelInfoNotMapping(idList, flag);
    }

    @Override
    public List<String> selectNotMappingIdList() {
        return this.hotelInfoQiantaoMapper.selectNotMappingIdList();
    }

    @Override
    public List<HotelInfoQiantao> selectListByIdList(List<String> idList) {
        return this.hotelInfoQiantaoMapper.selectListByIdList(idList);
    }

    @Override
    public int insertOrUpdateHotelInfoQiantao(HotelInfoQiantao hotelInfoQiantao) {
        hotelInfoQiantao.setCreateTime(DateUtils.getNowDate());
        return this.hotelInfoQiantaoMapper.insertOrUpdateHotelInfoQiantao(hotelInfoQiantao);
    }

    @Override
    public int saveOrUpdateHotelGnQianTao(JSONObject json) {
        try {

            if(!json.containsKey("GovZone") || StringUtils.isBlank(json.getString("GovZone"))){
                log.info("处理千淘酒店国内信息, 没有区县id 不处理 json:{}", json.toString());
                return 0;
            }

            log.info("处理千淘酒店国内信息,开始 json:{}", json.toString());
            HotelGnQiantao qiantao = new HotelGnQiantao();

            HotelCity city = this.hotelCityServiceImpl.getByCityIdAndPlatIdForCache(PlatEnum.PLAT_QT, json.getString("CityCode"));
            if (city == null) {
                city = new HotelCity();
            }

            qiantao.setReserve0(json.toString());
            qiantao.setId(json.getString("HotelCode"));
            qiantao.setName(json.getString("CnName"));
            qiantao.setNameEn(json.getString("EnName"));
            // qiantao.setTypeId();
            qiantao.setOpenDate(json.getString("OpeningTime"));
            qiantao.setDecorationDate(json.getString("FixTime"));
            qiantao.setPhone(json.getString("PhoneNum"));
            qiantao.setCountryId(city.getCountryId());
            qiantao.setCountryName(city.getCountryName());
            qiantao.setProvinceId(city.getProvinceId());
            qiantao.setProvinceName(city.getProvinceName());
            qiantao.setCityId(json.getString("CityCode"));
            qiantao.setCityName(city.getCityName());
            qiantao.setAreaId(city.getLocationId());
            qiantao.setAreaName(json.getString("GovZone"));
//            qiantao.setBusinessDistrictId();
            qiantao.setBusinessDistrictName(json.getString("BusinessZone"));
//            String address = json.getString("AddrCN");
//            if (address.length() > 40) {
//                address = address.substring(0, 40);
//            }
            qiantao.setAddress(json.getString("AddrCN"));
//            qiantao.setAddressEn();
//            qiantao.setLonGoogle();
//            qiantao.setLatGoogle();
//            qiantao.setLonBaidu();
//            qiantao.setLatBaidu();

            try {
                BigDecimal lonGd = json.getBigDecimal("GDLongitude");
                lonGd = BigUtils.getBigDecimal(lonGd, 999);
                BigDecimal latGd = json.getBigDecimal("GDLatitude");
                latGd = BigUtils.getBigDecimal(latGd, 99);
                qiantao.setLonGaode(lonGd);
                qiantao.setLatGaode(latGd);
            } catch (Exception e) {
            }
            try {
                BigDecimal lonGd = json.getBigDecimal("Longitude");
                lonGd = BigUtils.getBigDecimal(lonGd, 999);
                BigDecimal latGd = json.getBigDecimal("Latitude");
                latGd = BigUtils.getBigDecimal(latGd, 99);
                qiantao.setLonBaidu(lonGd);
                qiantao.setLatBaidu(latGd);
            } catch (Exception e) {
            }

            //qiantao.setGroupId();
            qiantao.setGroupName(json.getString("HotelGroup"));
            qiantao.setBrandId(json.getString("BrandCode"));
            //qiantao.setBrandName();
            qiantao.setStar(json.getString("StarCode"));
            qiantao.setImage(json.getString("MainPicture"));
            qiantao.setScore(json.getString("CommentPoint"));
            qiantao.setSynopsis(json.getString("ShortDescription"));
//            qiantao.setSparkle();
//            qiantao.setPolicyInfo();
            qiantao.setPolicyInfo(JSON.toJSONString( new PolicyInfo()));
            JSONArray facilitiesArray = json.getJSONArray("HotelFacilities");
            FacilitiesInfo facilitiesInfo = new FacilitiesInfo();


            // 停车
            FacilitiesInfo.Parking parking = new FacilitiesInfo.Parking();
            parking.setIsHave(Boolean.FALSE);
            parking.setIsCharge(Boolean.FALSE);
            if (facilitiesArray != null && facilitiesArray.size() > 0) {

                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value != null && value.contains("停车")) {
                        parking.setIsHave(Boolean.TRUE);
                        break;
                    }

                }
            }
            // 收费
            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value != null && value.contains("停车收费")) {
                        parking.setIsCharge(Boolean.TRUE);
                        break;
                    }

                }
            }
            facilitiesInfo.setParking(parking);

            //充电
            FacilitiesInfo.Charging charging = new FacilitiesInfo.Charging();
            charging.setIsHave(Boolean.FALSE);
            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value != null && value.contains("充电桩")) {
                        charging.setIsHave(Boolean.TRUE);
                        break;
                    }

                }
            }
            facilitiesInfo.setCharging(charging);

            //电梯
            FacilitiesInfo.Lift lift = new FacilitiesInfo.Lift();
            lift.setIsHave(Boolean.FALSE);
            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value != null && value.contains("电梯")) {
                        lift.setIsHave(Boolean.TRUE);
                        break;
                    }
                }
            }
            facilitiesInfo.setLift(lift);

            //行李寄存
            FacilitiesInfo.Baggage baggage = new FacilitiesInfo.Baggage();
            baggage.setIsHave(Boolean.FALSE);
            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value != null && value.contains("行李寄存")) {
                        baggage.setIsHave(Boolean.TRUE);
                        break;
                    }
                }
            }
            facilitiesInfo.setBaggage(baggage);

            //餐厅
            FacilitiesInfo.Restaurant restaurant = new FacilitiesInfo.Restaurant();
            restaurant.setIsHave(Boolean.FALSE);
            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value != null && value.contains("餐厅")) {
                        restaurant.setIsHave(Boolean.TRUE);
                        break;
                    }
                }
            }
            facilitiesInfo.setRestaurant(restaurant);

            // 会议室
            FacilitiesInfo.MeetingRoom meetingRoom = new FacilitiesInfo.MeetingRoom();
            meetingRoom.setIsHave(Boolean.FALSE);
            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value != null && value.contains("会议")) {
                        meetingRoom.setIsHave(Boolean.TRUE);
                        break;
                    }
                }
            }
            facilitiesInfo.setMeetingRoom(meetingRoom);

            // 公共区域WIFI
            FacilitiesInfo.PublicWifi publicWifi = new FacilitiesInfo.PublicWifi();
            publicWifi.setIsHave(Boolean.FALSE);

            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value != null && value.contains("公用区wifi")) {
                        publicWifi.setIsHave(Boolean.TRUE);
                        break;
                    }
                }
            }
            facilitiesInfo.setPublicWifi(publicWifi);


            //健身房
            FacilitiesInfo.Gym gym = new FacilitiesInfo.Gym();
            gym.setIsHave(Boolean.FALSE);
            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value !=null && value.contains("健身")) {
                        gym.setIsHave(Boolean.TRUE);
                        break;
                    }
                }
            }
            facilitiesInfo.setGym(gym);

            //洗衣房
            FacilitiesInfo.Laundry laundry = new FacilitiesInfo.Laundry();
            laundry.setIsHave(Boolean.FALSE);
            if (facilitiesArray != null && facilitiesArray.size() > 0) {
                for (Object o : facilitiesArray) {
                    JSONObject facility = (JSONObject) o;
                    String value = facility.getString("Value");
                    if (value !=null && value.contains("洗衣")) {
                        laundry.setIsHave(Boolean.TRUE);
                        break;
                    }
                }
            }
            facilitiesInfo.setLaundry(laundry);

            qiantao.setFacilitiesInfo(JSON.toJSONString(facilitiesInfo));


            qiantao.setStatus(1);
            qiantao.setIsDelete(0);
            qiantao.setIncrementStatus(0);
            qiantao.setIncrementTime(new Date());
            qiantao.setCreateTime(new Date());
            qiantao.setUpdateTime(new Date());
            // 如果是定时任务会报错
            String userName = "admin";
            try {
                userName = SecurityUtils.getUsername();
            } catch (Exception e) {

            }
            qiantao.setCreateBy(userName);
            qiantao.setUpdateBy(userName);


            return this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_QT, Lists.newArrayList(qiantao));
        } catch (Exception e) {
            log.error("处理千淘酒店国内信息,异常 json:{}", json.toString(), e);
            return 0;
        }


    }

    @Override
    protected Class<HotelGnQiantao> getEntityClass() {
        return HotelGnQiantao.class;
    }

    @Override
    protected PlatEnum getPlatEnum() {
        return PlatEnum.PLAT_QT;
    }

    @Override
    protected List<HotelGnBase> doSelectListWithParams(HotelGnBase entity, Map<String, Object> queryParams) {
        return mapper.selectHotelGnBaseCommonList2(entity,getEntityClass());
    }

    @Override
    protected HotelGnBase doSelectById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    protected int doInsert(HotelGnBase entity) {
        try {
            String res = QiantaoApi.QueryHotel(entity.getId());
            JSONObject resJson = JSONObject.parseObject(res);
            log.info("通过id:{}获取千淘数据:{}", entity.getId(), res);
            if(!resJson.containsKey("ReturnJson")){
                log.error("通过id:{}获取千淘数据失败:{}", entity.getId(), res);
                return 0 ;
            }
            this.insertNew(resJson.getJSONObject("ReturnJson"));
        } catch (NumberFormatException e) {
            log.error("查询酒店详情异常", e);
            return 0;
        }
        return 1;
    }


    public void insertNew(JSONObject jsonObject) {
        int saveResult = this.saveOrUpdateHotelGnQianTao(jsonObject);
        // 处理艺龙对照
        if (saveResult > 0) {
            try {

                String hotelGnId = jsonObject.getString("HotelCode");
                String elongHotelId = "";
                String elongHotelsfileName = "QiantaoElongHotels";
                QiantaoApi.getElongHotels(elongHotelsfileName);
                List<String> fileNameList = QiantaoApi.unzipElongHotels(elongHotelsfileName);
                // 读取千淘映射text数据
                if (CollectionUtils.isEmpty(fileNameList)) {
                    throw new Exception("解压文件失败");
                }
                for (String fileName : fileNameList) {
                    List<String> qiantaoElongIdList = QiantaoApi.readElongHotels(fileName);
                    // 循环批处理映射关系
                    if (!CollectionUtils.isEmpty(qiantaoElongIdList)) {
                        for (String line : qiantaoElongIdList) {
                            if (StringUtils.isBlank(line)) {
                                continue;
                            }
                            String[] s = line.split(" ");
                            if (s.length < 2) {
                                continue;
                            }
                            if (hotelGnId.equals(s[1])) {
                                elongHotelId = s[0];
                                break;
                            }
                        }

                        if (org.apache.commons.lang3.StringUtils.isNotBlank(elongHotelId)) {
                            HotelGnIdMapping hotelGnIdMapping = new HotelGnIdMapping();
                            hotelGnIdMapping.setPlatformId(PlatEnum.PLAT_QT.getValue());
                            hotelGnIdMapping.setPlatformHotelId(hotelGnId);
                            hotelGnIdMapping.setMappingPlatformId(PlatEnum.PLAT_EL.getValue());
                            hotelGnIdMapping.setMappingHotelId(elongHotelId);
                            log.info("处理科坦国内酒店千淘id映射,插入数据, hotelGnIdMapping:{}", hotelGnId, com.alibaba.fastjson.JSON.toJSONString(hotelGnIdMapping));
                            this.hotelGnIdMappingService.addOrUpdateBatch(Lists.newArrayList(hotelGnIdMapping));
                        }

                    }
                }
            } catch (Exception e) {
                log.error("新增千淘酒店数据, 处理艺龙对照失败, jsonStr:{} ", jsonObject.toString(), e);
            }
        }
    }

    @Override
    protected int doUpdate(HotelGnBase entity) {
        return mapper.updateHotelGnBaseCommon(entity,getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        return mapper.deleteHotelGnBaseCommonByIds(ids,getEntityClass());
    }

    @Override
    public List<?> exportData(HotelGnBase entity, HotelGnQiantao searchParams) {
        return doSelectListWithParams(entity, null);
    }

    @Override
    public Class<?> getExportEntityClass() {
        return HotelGnBase.class;
    }

    @Override
    public String getExportFileName(HotelGnQiantao searchParams) {
        return "hotel_qiantao_data";
    }

    @Override
    public HotelGnBase selectHotelGnBaseById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    public void updateHotelGnBase(HotelGnBase hotelGnBase) {
        this.update(hotelGnBase);
    }

}
