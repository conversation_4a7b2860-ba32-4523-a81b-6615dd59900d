package com.ltgj.ivw.dto;

import lombok.Data;

/**
 * 上级行政区划查询请求DTO
 * 用于根据子级行政区划查询上级信息
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@Data
public class ParentLookupReq {
    
    /**
     * 平台ID(可选)
     */
    private String platformId;
    
    /**
     * 国家编码(必须)
     */
    private String countryId;
    
    /**
     * 子级ID(必须)
     * 可以是城市ID或区县ID
     */
    private String childId;
    
    /**
     * 子级类型(必须)
     * city: 通过城市查询省份
     * district: 通过区县查询城市
     */
    private String childType;
} 