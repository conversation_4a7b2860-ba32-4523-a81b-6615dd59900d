package com.ltgj.ivw.service;

import com.ltgj.common.core.domain.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 酒店服务基础接口
 * 提供通用的酒店业务操作方法，支持泛型扩展
 *
 * @param <T> 实体类型（如 HotelInfo）
 * @param <S> 查询条件类型（如 HotelInfoKlyx）
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface BaseHotelService<T, S> {

    /**
     * 根据ID查询实体
     *
     * @param id 实体ID
     * @return 实体对象
     */
    T selectById(String id);

    /**
     * 根据参数查询实体列表
     *
     * @param entity 实体对象
     * @param queryParams 查询参数
     * @return 实体列表
     */
    List<T> selectListWithParams(T entity, Map<String, Object> queryParams);

    /**
     * 插入实体
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    int insert(T entity);

    /**
     * 更新实体
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    int update(T entity);

    /**
     * 根据ID数组删除实体
     *
     * @param ids ID数组
     * @return 影响行数
     */
    int deleteByIds(String[] ids);

    // ==================== 导出相关方法 ====================

    /**
     * 获取导出数据列表
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @return 导出数据列表
     */
    List<?> exportData(T entity, S searchParams);

    /**
     * 获取导出实体类型
     *
     * @return 导出实体类型
     */
    Class<?> getExportEntityClass();

    /**
     * 获取导出文件名
     *
     * @param searchParams 查询参数
     * @return 导出文件名
     */
    String getExportFileName(S searchParams);

    // ==================== 扩展点方法 ====================

    /**
     * 构建默认查询参数
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @return 查询参数Map
     */
    Map<String, Object> buildDefaultQueryParams(T entity, S searchParams);

    /**
     * 实体验证
     *
     * @param entity 实体对象
     * @param isInsert 是否为新增操作
     * @return 验证错误信息，为空表示验证通过
     */
    String validateEntity(T entity, boolean isInsert);

    /**
     * 预处理实体
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @param isInsert 是否为新增操作
     * @return 处理后的实体
     */
    T preprocessEntity(T entity, S searchParams, boolean isInsert);

    /**
     * 删除前验证
     *
     * @param ids ID数组
     * @param searchParams 查询参数
     * @return 验证错误信息，为空表示验证通过
     */
    String validateBeforeDelete(String[] ids, S searchParams);

    /**
     * 插入后处理
     *
     * @param entity 实体对象
     * @param result 插入结果
     */
    void afterInsert(T entity, int result);

    /**
     * 更新后处理
     *
     * @param entity 实体对象
     * @param result 更新结果
     */
    void afterUpdate(T entity, int result);

    /**
     * 删除后处理
     *
     * @param ids ID数组
     * @param result 删除结果
     */
    void afterDelete(String[] ids, int result);

    /**
     * 获取表名（通过注解自动获取）
     * 
     * @return 表名
     */
    String getTableName();

    /**
     * 获取业务标题（通过注解自动获取）
     * 
     * @return 业务标题
     */
    String getBusinessTitle();

    /**
     * 根据ID数组将平台数据同步到本地
     *
     * @param ids
     * @return
     * @throws Exception
     */
    AjaxResult platToLocal(String[] ids) throws Exception;
}