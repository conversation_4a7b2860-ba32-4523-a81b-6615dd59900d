package com.ltgj.ivw.controller;

import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.bean.BeanUtils;
import com.ltgj.common.utils.poi.ExcelUtil;
import com.ltgj.ivw.request.hotelGnStatistics.GnStatisticsRequest;
import com.ltgj.ivw.response.GnStatisticsResponse;
import com.ltgj.ivw.response.HotelGnStatisticsDto;
import com.ltgj.ivw.response.HotelGnStatisticsRes;
import com.ltgj.ivw.service.HotelGnStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/hotelGnStatistics")
public class HotelGnStatisticsController extends BaseController {

    @Resource
    private HotelGnStatisticsService hotelGnStatisticsService;

    /**
     * 列表查询
     *
     * @param request
     * @return
     */
//    @PreAuthorize("@ss.hasPermi('ivw:hotelGnStatistics:list')")
    @PostMapping("/list")
    public GnStatisticsResponse list(@RequestBody GnStatisticsRequest request) {
        log.info("op=listRequest={}", request);
        return hotelGnStatisticsService.selectList(request);
    }

    /**
     * 导出信息
     */
//    @PreAuthorize("@ss.hasPermi('ivw:hotelGnStatistics:export')")
    @Log(title = "映射数据统计导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,@RequestBody GnStatisticsRequest request) {
        GnStatisticsResponse hotelGnStatisticsResponse = hotelGnStatisticsService.selectList(request);
        List<HotelGnStatisticsDto> hotelGnStatisticsList = hotelGnStatisticsResponse.getHotelGnStatisticsRes().stream().map(m -> {
            HotelGnStatisticsDto hotelGnStatisticsDto = new HotelGnStatisticsDto();
            BeanUtils.copyProperties(m, hotelGnStatisticsDto);
            hotelGnStatisticsDto.setNotNum(m.getFailNum());
            return hotelGnStatisticsDto;
        }).collect(Collectors.toList());
        ExcelUtil<HotelGnStatisticsDto> util = new ExcelUtil<>(HotelGnStatisticsDto.class);
        util.exportExcel(response, hotelGnStatisticsList, "映射数据统计");
    }
}
