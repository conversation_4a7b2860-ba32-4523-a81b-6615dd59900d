package com.ltgj.ivw.controller;

import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.poi.ExcelUtil;
import com.ltgj.ivw.domain.SupplierElongCityMapping;
import com.ltgj.ivw.service.ISupplierElongCityMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 供应商与艺龙城市映射关系Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/ivw/supplierCityMapping")
public class SupplierElongCityMappingController extends BaseController
{
    @Autowired
    private ISupplierElongCityMappingService supplierElongCityMappingService;

    /**
     * 查询供应商与艺龙城市映射关系列表
     */
    @PreAuthorize("@ss.hasPermi('ivw:supplier_city_mapping:list')")
    @GetMapping("/list")
    public TableDataInfo list(SupplierElongCityMapping supplierElongCityMapping)
    {
        startPage();
        List<SupplierElongCityMapping> list = supplierElongCityMappingService.selectSupplierElongCityMappingList(supplierElongCityMapping);
        return getDataTable(list);
    }

    /**
     * 导出供应商与艺龙城市映射关系列表
     */
    @PreAuthorize("@ss.hasPermi('ivw:supplier_city_mapping:export')")
    @Log(title = "供应商与艺龙城市映射关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SupplierElongCityMapping supplierElongCityMapping)
    {
        List<SupplierElongCityMapping> list = supplierElongCityMappingService.selectSupplierElongCityMappingList(supplierElongCityMapping);
        ExcelUtil<SupplierElongCityMapping> util = new ExcelUtil<SupplierElongCityMapping>(SupplierElongCityMapping.class);
        util.exportExcel(response, list, "供应商与艺龙城市映射关系数据");
    }

    /**
     * 获取供应商与艺龙城市映射关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('ivw:supplier_city_mapping:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(supplierElongCityMappingService.selectSupplierElongCityMappingById(id));
    }

    /**
     * 新增供应商与艺龙城市映射关系
     */
    @PreAuthorize("@ss.hasPermi('ivw:supplier_city_mapping:add')")
    @Log(title = "供应商与艺龙城市映射关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SupplierElongCityMapping supplierElongCityMapping)
    {
        return toAjax(supplierElongCityMappingService.insertSupplierElongCityMapping(supplierElongCityMapping));
    }

    /**
     * 修改供应商与艺龙城市映射关系
     */
    @PreAuthorize("@ss.hasPermi('ivw:supplier_city_mapping:edit')")
    @Log(title = "供应商与艺龙城市映射关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SupplierElongCityMapping supplierElongCityMapping)
    {
        return toAjax(supplierElongCityMappingService.updateSupplierElongCityMapping(supplierElongCityMapping));
    }

    /**
     * 删除供应商与艺龙城市映射关系
     */
    @PreAuthorize("@ss.hasPermi('ivw:supplier_city_mapping:remove')")
    @Log(title = "供应商与艺龙城市映射关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(supplierElongCityMappingService.deleteSupplierElongCityMappingByIds(ids));
    }
    
    /**
     * 导入供应商与艺龙城市映射关系
     */
    @PreAuthorize("@ss.hasPermi('ivw:supplier_city_mapping:import')")
    @Log(title = "供应商与艺龙城市映射关系", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        String operName = getUsername();
        String message = supplierElongCityMappingService.importExcel(file, updateSupport, operName);
        return success(message);
    }
    
    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<SupplierElongCityMapping> util = new ExcelUtil<SupplierElongCityMapping>(SupplierElongCityMapping.class);
        util.importTemplateExcel(response, "供应商与艺龙城市映射关系数据");
    }
} 