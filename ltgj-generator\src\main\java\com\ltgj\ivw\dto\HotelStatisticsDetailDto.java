package com.ltgj.ivw.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class HotelStatisticsDetailDto implements Serializable {

    private Integer type;

    private Long interfacePlat;

    private String interfaceName;

    private String hotelName;

    private String subtype;

//    private String localId;

    private String platId;

    private String reason;

    private Integer isFailure;

    private String createTime;

    private String originalRequest;

    private String originalResponse;
}
