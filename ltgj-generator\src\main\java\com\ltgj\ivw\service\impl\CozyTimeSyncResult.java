package com.ltgj.ivw.service.impl;

import com.ltgj.ivw.domain.CityMapping;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.service.idempotent.IdempotentResult;
import lombok.Data;
import lombok.Builder;

/**
 * 科坦综合同步结果
 */
@Data
@Builder
public class CozyTimeSyncResult {
    
    /**
     * HotelCity同步结果
     */
    private IdempotentResult<HotelCity> hotelCityResult;
    
    /**
     * CityMapping同步结果
     */
    private IdempotentResult<CityMapping> cityMappingResult;
    
    /**
     * HotelCityMapping同步结果
     */
    private IdempotentResult<HotelCityMapping> hotelCityMappingResult;
    
    /**
     * 总处理耗时（毫秒）
     */
    private long totalProcessingTimeMs;
    
    /**
     * 是否全部成功
     */
    private boolean allSuccess;
    
    /**
     * 获取综合统计信息
     */
    public String getOverallSummary() {
        int totalCount = 0;
        int totalInsert = 0;
        int totalUpdate = 0;
        int totalSkip = 0;
        int totalError = 0;
        
        if (hotelCityResult != null) {
            totalCount += hotelCityResult.getTotalCount();
            totalInsert += hotelCityResult.getInsertCount();
            totalUpdate += hotelCityResult.getUpdateCount();
            totalSkip += hotelCityResult.getSkipCount();
            totalError += hotelCityResult.getErrorCount();
        }
        
        if (cityMappingResult != null) {
            totalCount += cityMappingResult.getTotalCount();
            totalInsert += cityMappingResult.getInsertCount();
            totalUpdate += cityMappingResult.getUpdateCount();
            totalSkip += cityMappingResult.getSkipCount();
            totalError += cityMappingResult.getErrorCount();
        }
        
        if (hotelCityMappingResult != null) {
            totalCount += hotelCityMappingResult.getTotalCount();
            totalInsert += hotelCityMappingResult.getInsertCount();
            totalUpdate += hotelCityMappingResult.getUpdateCount();
            totalSkip += hotelCityMappingResult.getSkipCount();
            totalError += hotelCityMappingResult.getErrorCount();
        }
        
        double successRate = totalCount == 0 ? 0.0 : 
            (double) (totalInsert + totalUpdate + totalSkip) / totalCount * 100;
        
        return String.format(
            "科坦数据同步完成 - 总数:%d, 新增:%d, 更新:%d, 跳过:%d, 错误:%d, 成功率:%.2f%%, 总耗时:%dms",
            totalCount, totalInsert, totalUpdate, totalSkip, totalError, successRate, totalProcessingTimeMs
        );
    }
    
    /**
     * 获取详细报告
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 科坦数据同步详细报告 ===\n");
        
        if (hotelCityResult != null) {
            report.append("HotelCity: ").append(hotelCityResult.getSummary()).append("\n");
        }
        
        if (cityMappingResult != null) {
            report.append("CityMapping: ").append(cityMappingResult.getSummary()).append("\n");
        }
        
        if (hotelCityMappingResult != null) {
            report.append("HotelCityMapping: ").append(hotelCityMappingResult.getSummary()).append("\n");
        }
        
        report.append("总体: ").append(getOverallSummary());
        
        return report.toString();
    }
} 