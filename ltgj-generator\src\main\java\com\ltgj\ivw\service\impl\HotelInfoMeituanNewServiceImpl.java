package com.ltgj.ivw.service.impl;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.HotelStatusEnum;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelInfoCommonBaseMapper;
import com.ltgj.ivw.service.IHotelCityMappingService;
import com.ltgj.ivw.service.IHotelInfoMeituanService;
import com.ltgj.ivw.service.IJdJdbService;
import com.ltgj.ivw.service.ZhJdJdbMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 美团新酒店信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
@Slf4j
public class HotelInfoMeituanNewServiceImpl extends BaseHotelServiceImpl<HotelInfo, HotelInfoMeituanNew> {

    @Autowired
    private HotelInfoCommonBaseMapper<HotelInfoMeituanNew> mapper;

    @Autowired
    private IHotelInfoMeituanService hotelInfoMeituanService;

    @Autowired
    private IJdJdbService jdJdbService;

    @Autowired
    private IHotelCityMappingService hotelCityMappingService;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;

    @Override
    protected Class<HotelInfoMeituanNew> getEntityClass() {
        return HotelInfoMeituanNew.class;
    }

    @Override
    protected PlatEnum getPlatEnum() {
        return null;
    }

    // ==================== 实现抽象的数据访问方法 ====================

    @Override
    protected List<HotelInfo> doSelectListWithParams(HotelInfo entity, Map<String, Object> queryParams) {
        log.debug("美团新酒店查询列表，参数：{}", queryParams);
        return mapper.selectHotelInfoCommonListXml(queryParams);
    }

    @Override
    protected HotelInfo doSelectById(String id) {
        log.debug("美团新酒店根据ID查询：{}", id);
        return mapper.selectHotelInfoCommonById(id, getEntityClass());
    }

    @Override
    protected int doInsert(HotelInfo entity) {
        log.debug("美团新酒店插入：{}", entity.getName());
        return mapper.insertHotelInfoCommon(entity, getEntityClass());
    }

    @Override
    protected int doUpdate(HotelInfo entity) {
        log.debug("美团新酒店更新：{}", entity.getName());
        return mapper.updateHotelInfoCommon(entity, getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        log.debug("美团新酒店删除，数量：{}", ids.length);
        return mapper.deleteHotelInfoCommonByIds(ids, getEntityClass());
    }

    // ==================== 业务逻辑定制 ====================

    @Override
    public Map<String, Object> buildDefaultQueryParams(HotelInfo entity, HotelInfoMeituanNew searchParams) {
        Map<String, Object> params = super.buildDefaultQueryParams(entity, searchParams);
        
        // 添加美团新酒店特有的查询参数处理逻辑
        log.debug("美团新酒店特有参数处理：{}", params);
        
        return params;
    }

    @Override
    public String validateEntity(HotelInfo entity, boolean isInsert) {
        // 先调用父类的基础验证
        String baseValidation = super.validateEntity(entity, isInsert);
        if (baseValidation != null && !baseValidation.isEmpty()) {
            return baseValidation;
        }
        
        // 美团新酒店特有的验证逻辑
        if (entity.getName() == null || entity.getName().trim().isEmpty()) {
            return "美团新酒店名称不能为空";
        }
        
        return null;
    }

    @Override
    public HotelInfo preprocessEntity(HotelInfo entity, HotelInfoMeituanNew searchParams, boolean isInsert) {
        // 美团新酒店特有的预处理逻辑
        if (isInsert) {
            log.info("美团新酒店新增预处理：{}", entity.getName());
        } else {
            log.info("美团新酒店更新预处理：{}", entity.getName());
        }
        
        return super.preprocessEntity(entity, searchParams, isInsert);
    }

    @Override
    public String validateBeforeDelete(String[] ids, HotelInfoMeituanNew searchParams) {
        if (ids == null || ids.length == 0) {
            return "请选择要删除的美团新酒店数据";
        }
        
        return null; // 验证通过
    }

    @Override
    public void afterInsert(HotelInfo entity, int result) {
        super.afterInsert(entity, result);
        if (result > 0) {
            log.info("美团新酒店新增成功：{}", entity.getName());
        }
    }

    @Override
    public void afterUpdate(HotelInfo entity, int result) {
        super.afterUpdate(entity, result);
        if (result > 0) {
            log.info("美团新酒店更新成功：{}", entity.getName());
        }
    }

    @Override
    public void afterDelete(String[] ids, int result) {
        if (result > 0) {
            log.info("美团新酒店删除成功，数量：{}", result);
        }
    }

    /**
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @Override
    public AjaxResult platToLocal(String[] ids) throws Exception {
        HotelInfo hotelInfo = hotelInfoMeituanService.selectHotelInfoMeituanById(ids[0]);
        JdJdb jdLocal = new JdJdb();
        jdLocal.setId(PlatEnum.PLAT_MT.getValue()+ hotelInfo.getId());
        jdLocal.setInterfacePlat(PlatEnum.PLAT_MT.getValue());
        jdLocal.setCreatedate(new Date());
        jdLocal.setJdmc(hotelInfo.getName());
        jdLocal.setJdmcEn(hotelInfo.getNameEn());
        jdLocal.setJdlx(hotelInfo.getTypeId());
        jdLocal.setJddz(hotelInfo.getAddressLine());
        jdLocal.setJddh(hotelInfo.getPhone());
        jdLocal.setImgUrl(hotelInfo.getImage());
        jdLocal.setLonGoogle(hotelInfo.getLonGg());
        jdLocal.setLatGoogle(hotelInfo.getLatGg());
        jdLocal.setLonBaidu(hotelInfo.getLonBd());
        jdLocal.setLatBaidu(hotelInfo.getLatBd());
        jdLocal.setLonGaode(hotelInfo.getLonGd());
        jdLocal.setLatGaode(hotelInfo.getLatGd());

        String cityId = hotelInfo.getCityId();
        if(StringUtils.isEmpty(cityId)){
            throw new Exception("酒店所在城市为空，请先维护正确以避免后续BUG！");
        }
        HotelCityMapping hotelCityMapping = new HotelCityMapping();
        hotelCityMapping.setPlatNum(cityId);
        hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
        List<HotelCityMapping> hotelCityMappingList = hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
        if(hotelCityMappingList.size() == 0) {
            throw new Exception("该酒店所在城市映射关系不存在，请先维护正确以避免后续BUG！");
        }
        hotelCityMapping = hotelCityMappingList.get(0);
        jdLocal.setCityId(hotelCityMapping.getLocalId());
        jdLocal.setCityName(hotelCityMapping.getCityName());
        jdLocal.setBrandName(hotelInfo.getBrandId());

        jdLocal.setBusinessZoneName(hotelInfo.getBusinessDistricts());
        if(StringUtils.isNotEmpty(hotelInfo.getReserve2())) {
            jdLocal.setScore(BigDecimal.valueOf(Double.valueOf(hotelInfo.getReserve2())).divide(BigDecimal.valueOf(10)));
        }
        jdLocal.setJtmc(hotelInfo.getGroupId());
        jdLocal.setKysj(hotelInfo.getOpenDate());
        jdLocal.setZhzxsj(hotelInfo.getDecorationDate());
        if(StringUtils.isNotEmpty(hotelInfo.getReserve1())) {
            jdLocal.setMinPrice(BigDecimal.valueOf(Double.valueOf(hotelInfo.getReserve1())));
        }
        jdLocal.setReserve1(hotelInfo.getDescription());
        jdLocal.setNoticeInfo(hotelInfo.getNoticeInfo());
        jdLocal.setPolicyInfo(hotelInfo.getPolicyInfo());
        jdLocal.setFacilitiesInfo(hotelInfo.getFacilitiesInfo());
        jdLocal.setStatus(HotelStatusEnum.getStatusByCode(hotelInfo.getStatus()));
        jdJdbService.insertJdJdb(jdLocal);

        List<ZhJdJdbMapping> dbMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(hotelInfo.getId(), Long.valueOf(PlatEnum.PLAT_MT.getValue()));
        if (!ObjectUtils.isEmpty(dbMappings)){
            throw new Exception("酒店映射已存在，确认要导入请先删除！");
        }

        //添加映射关系
        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
        zhJdJdbMapping.setLocalId(jdLocal.getId());
        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
        zhJdJdbMapping.setPlatId(hotelInfo.getId());
        zhJdJdbMapping.setJdName(jdLocal.getJdmc());
        zhJdJdbMapping.setPlatJdName(hotelInfo.getName());
        zhJdJdbMapping.setStatus(0);
        zhJdJdbMapping.setSaveDate(new Date());
        zhJdJdbMapping.setIsGnGj(1);
        log.info("ZhJdJdbMapping酒店映射表数据为空ID {},开始插入数据", jdLocal.getId());
        int insert = zhJdJdbMappingService.insert(zhJdJdbMapping);
        log.info("ZhJdJdbMapping酒店映射表数据插入完成 localId:{} count： {},开始插入数据", jdLocal.getId(),insert);

        // todo 预映射
        return AjaxResult.success();
    }

    // ==================== 导出相关方法实现 ====================

    @Override
    public List<?> exportData(HotelInfo entity, HotelInfoMeituanNew searchParams) {
        log.info("开始获取美团新酒店导出数据，实体：{}，查询参数：{}", entity, searchParams);
        
        // 使用现有的查询方法获取数据，这里不进行分页
        List<HotelInfo> result = this.selectListWithParams(entity, null);
        
        log.info("获取美团新酒店导出数据完成，数据量：{}", result.size());
        return result;
    }

    @Override
    public Class<?> getExportEntityClass() {
        return HotelInfoMeituanNew.class;
    }

    @Override
    public String getExportFileName(HotelInfoMeituanNew searchParams) {
        return "美团新酒店信息数据";
    }
} 