package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnBaseCommonMapper;
import com.ltgj.ivw.mapper.HotelInfoHsjlxyMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.HsjlApi;
import com.ltgj.ivw.utils.hotelApi.HsjlxyApi;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnHsjl;
import com.ltgj.supplier.common.gn.domain.HotelGnHsjlXy;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.common.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 红色加力酒店数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Slf4j
@Service("hotelInfoHsjlxyService")
public class HotelInfoHsjlxyServiceImpl extends BaseHotelServiceImpl<HotelGnBase, HotelGnHsjlXy> implements IHotelInfoHsjlxyService, IHotelInfoService,IHotelGnBaseService {
    @Autowired
    private HotelInfoHsjlxyMapper hotelInfoHsjlxyMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private HotelGnBaseService hotelGnBaseService;
    @Autowired
    private IJdJdbHsjlCorpService jdJdbHsjlCorpService;
    @Autowired
    private IHotelUpdateRecodeService hotelUpdateRecodeService;
    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;
    @Autowired
    private HotelGnBaseCommonMapper<HotelGnHsjlXy> mapper;

    //红色加力酒店数据  请求100次/分钟
    private static int maxCountDetailHsjlxy = 10;

    @Value("${hsjlxy.clientid}")
    private String partnerCode;
    @Value("${hsjlxy.licensekey}")
    private String secureKey;
    private ExecutorService hsjlxyThreadPool = newExecutorService(3, 5);

    @Override
    public HotelInfo selectHotelInfoById(String id)
    {
        return hotelInfoHsjlxyMapper.selectHotelInfoHsjlxyById(id);
    }

    @Override
    public int updateHotelInfo(HotelInfo hotelInfo) {
        return hotelInfoHsjlxyMapper.updateHotelInfoHsjlxy((HotelInfoHsjlxy) hotelInfo);
    }

    /**
     * 查询红色加力酒店数据
     *
     * @param id 红色加力酒店数据主键
     * @return 红色加力酒店数据
     */
    @Override
    public HotelInfoHsjlxy selectHotelInfoHsjlxyById(String id)
    {
        return hotelInfoHsjlxyMapper.selectHotelInfoHsjlxyById(id);
    }

    /**
     * 查询红色加力酒店数据列表
     *
     * @param hotelInfoHsjl 红色加力酒店数据
     * @return 红色加力酒店数据
     */
    @Override
    public List<HotelInfoHsjlxy> selectHotelInfoHsjlxyList(HotelInfoHsjlxy hotelInfoHsjl) {
        return hotelInfoHsjlxyMapper.selectHotelInfoHsjlxyList(hotelInfoHsjl);
    }

    @Override
    public List<HotelInfoHsjlxy> selectHotelInfoHsjlxyList2(HotelInfoHsjlxy hotelInfoHsjlxy)
    {
        return hotelInfoHsjlxyMapper.selectHotelInfoHsjlxyList2(hotelInfoHsjlxy);
    }

    /**
     * 新增红色加力酒店数据
     *
     * @param hotelInfoHsjl 红色加力酒店数据
     * @return 结果
     */
    @Override
    public int insertHotelInfoHsjlxy(HotelInfoHsjlxy hotelInfoHsjl)
    {
        hotelInfoHsjl.setCreateTime(DateUtils.getNowDate());
        return hotelInfoHsjlxyMapper.insertHotelInfoHsjlxy(hotelInfoHsjl);
    }

    /**
     * 修改红色加力酒店数据
     *
     * @param hotelInfoHsjlxy 红色加力酒店数据
     * @return 结果
     */
    @Override
    public int updateHotelInfoHsjlxy(HotelInfoHsjlxy hotelInfoHsjlxy)
    {
        hotelInfoHsjlxy.setUpdateTime(DateUtils.getNowDate());
        return hotelInfoHsjlxyMapper.updateHotelInfoHsjlxy(hotelInfoHsjlxy);
    }

    /**
     * 批量删除红色加力酒店数据
     *
     * @param ids 需要删除的红色加力酒店数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoHsjlxyByIds(String[] ids)
    {
        return hotelInfoHsjlxyMapper.deleteHotelInfoHsjlxyByIds(ids);
    }

    /**
     * 删除红色加力酒店数据信息
     *
     * @param id 红色加力酒店数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoHsjlxyById(String id)
    {
        return hotelInfoHsjlxyMapper.deleteHotelInfoHsjlxyById(id);
    }

    @Override
    public void updateSuccess(List<String> updateSuccessList) {
        hotelInfoHsjlxyMapper.updateSuccess(updateSuccessList);
    }

    @Override
    public List<HotelInfoHsjlxy> selectHotelInfoxyNotMapping(List<String> idList, int flag) {
        return hotelInfoHsjlxyMapper.selectHotelInfoxyNotMapping(idList, flag);
    }

    @Override
    public List<HotelInfoHsjlxy> selectListByIdList(List<String> idList) {
        return hotelInfoHsjlxyMapper.selectListByIdList(idList);
    }

    @Override
    public int insertOrUpdateHotelInfoHsjlxy(HotelInfoHsjlxy hotelInfoHsjlxy) {
        hotelInfoHsjlxy.setCreateTime(DateUtils.getNowDate());
        hotelInfoHsjlxy.setIsDelete(0);
        return hotelInfoHsjlxyMapper.insertOrUpdateHotelInfoHsjlxy(hotelInfoHsjlxy);
    }

    @Override
    public List<HotelInfoHsjlxy> selectAllId(int start, int pageSize) {
        return hotelInfoHsjlxyMapper.selectAllId(start, pageSize);
    }

    public void resetStatus() {
        hotelInfoHsjlService.resetStatus(PlatEnum.PLAT_HSJL_XY);
    }

    public void updateHsjlxyAll(String corpId, String userName) throws Exception {
        hotelInfoHsjlService.updateHsjlAll(corpId, userName, PlatEnum.PLAT_HSJL_XY, partnerCode, secureKey, hsjlxyThreadPool);
    }

    @Override
    protected Class<HotelGnHsjlXy> getEntityClass() {
        return HotelGnHsjlXy.class;
    }

    @Override
    protected PlatEnum getPlatEnum() {
        return PlatEnum.PLAT_HSJL_XY;
    }

    @Override
    protected List<HotelGnBase> doSelectListWithParams(HotelGnBase entity, Map<String, Object> queryParams) {
        return mapper.selectHotelGnBaseCommonList2(entity,getEntityClass());
    }

    @Override
    protected HotelGnBase doSelectById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    protected int doInsert(HotelGnBase entity) {
        try {
            List<Long> ids = new ArrayList<>();
            ids.add(Long.valueOf(entity.getId()));
            String infoText = HsjlxyApi.queryHotelInfo(ids, null, null);
            JSONObject resJson = JSONObject.parseObject(infoText);
            insertChild(entity, resJson.getJSONObject("bussinessResponse").getJSONArray("hotelInfos").getJSONObject(0));
        } catch (RuntimeException e) {
            log.error("获取红色加力详情进异常: {}", e);
            return 0;
        }
        return 1;
    }


    public void insertChild(HotelGnBase entity, JSONObject jsonObject) {
        HotelGnHsjl hotelGnHsjl = hotelInfoHsjlService.buildHotelGnHsjl(PlatEnum.PLAT_HSJL, jsonObject);
        hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL_XY, Lists.newArrayList(hotelGnHsjl));
    }

    @Override
    protected int doUpdate(HotelGnBase entity) {
        return mapper.updateHotelGnBaseCommon(entity,getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        return mapper.deleteHotelGnBaseCommonByIds(ids,getEntityClass());
    }

    @Override
    public List<?> exportData(HotelGnBase entity, HotelGnHsjlXy searchParams) {
        return doSelectListWithParams(entity, null);
    }

    @Override
    public Class<?> getExportEntityClass() {
        return HotelGnBase.class;
    }

    @Override
    public String getExportFileName(HotelGnHsjlXy searchParams) {
        return "hotel_hsjlxy_data";
    }

    @Override
    public HotelGnBase selectHotelGnBaseById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    public void updateHotelGnBase(HotelGnBase hotelGnBase) {
        this.update(hotelGnBase);
    }

}
