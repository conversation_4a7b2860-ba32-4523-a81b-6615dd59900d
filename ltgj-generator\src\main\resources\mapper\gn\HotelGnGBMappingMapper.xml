<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.supplier.common.gn.mapper.HotelGnGBMappingMapper">
    <insert id="add" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGBMapping">
        INSERT INTO `hotel_gn_gb_mapping` (`id`, `platform_id`, `platform_gb_id`, `platform_gb_name`, `mapping_platform_id`, `mapping_gb_id`, `mapping_gb_name`
                                          , `type`, `remark`, `create_time`, `update_time`, `create_by`, `update_by`)
        VALUES (#{id}, #{platformId}, #{platformGbId}, #{platformGbName}, #{mappingPlatfromId}, #{mappingGbId}, #{mappingGbName}
                , #{type}, #{remark}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy})
    </insert>

    <select id="selectByPlatformIdAndType" resultType="com.ltgj.supplier.common.gn.domain.HotelGnGBMapping">
        select * from hotel_gn_gb_mapping where `platform_id` = #{platformId} and `type` = #{type}
    </select>

    <select id="selectByPlatformIdAndTypeAndGBId" resultType="com.ltgj.supplier.common.gn.domain.HotelGnGBMapping">
        select * from hotel_gn_gb_mapping where `platform_id` = #{platformId} and `type` = #{type} and `platform_gb_id` = #{gbId}
    </select>
</mapper>
