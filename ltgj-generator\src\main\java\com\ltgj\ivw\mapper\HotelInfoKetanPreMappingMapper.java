package com.ltgj.ivw.mapper;


import com.ltgj.ivw.domain.HotelInfoCozytime;
import com.ltgj.ivw.domain.HotelInfoKetanPreMapping;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

public interface HotelInfoKetanPreMappingMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(HotelInfoKetanPreMapping row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(HotelInfoKetanPreMapping row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    HotelInfoKetanPreMapping selectByPrimaryKey(Long id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(HotelInfoKetanPreMapping row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(HotelInfoKetanPreMapping row);

    /**
     * 分页查询
     * @param start 起始行数
     * @param size 查询条数
     * @return
     */
    List<Long> selectIdsByPage(@Param("start") int start, @Param("size") int size);

    /**
     * 批量插入
     * @param list
     * @return
     */
    int insertBatch(@Param("list") List<HotelInfoKetanPreMapping> list);


    /**
     * 分页查询需要处理的数据
     * @return
     */
    List<HotelInfoKetanPreMapping> selectNeedProcessByPage(@Param("start") int start, @Param("size") int size);

    /**
     * 查询总条数
     * @return
     */
    int selectCount();


    /**
     * 根据源id批量查询
     * @param ids
     * @return
     */
    List<HotelInfoKetanPreMapping> selectBySourceIds(@Param("ids") List<Long> ids);

    /**
     * 批量更新
     * @param toUpdateList
     */
    void batchUpdate(@Param("list") List<HotelInfoKetanPreMapping> toUpdateList);
}