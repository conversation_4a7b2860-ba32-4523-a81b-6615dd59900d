package com.ltgj.quartz.task;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.dto.HotelDetailDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.service.impl.HotelCityServiceImpl;
import com.ltgj.ivw.utils.BigUtils;
import com.ltgj.ivw.utils.ConstantList;
import com.ltgj.ivw.utils.ResumeDownload;
import com.ltgj.ivw.utils.hotelApi.*;
import com.ltgj.limiter.LtgjRateLimiter;
import com.ltgj.supplier.common.domain.FacilitiesInfo;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.gn.domain.*;
import com.ltgj.supplier.common.gn.enums.HotelGnCustomerTypeEnum;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.redisson.api.RRateLimiter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Component("hotelIncrementTask")
@Slf4j
public class HotelIncrementTask extends BaseController {

    @Autowired
    private LtgjRateLimiter limiter;
    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;
    @Autowired
    private IHotelCityService hotelCityService;
    @Autowired
    private IHotelInfoHsjlxyService hotelInfoHsjlxyService;
    @Autowired
    private IHotelInfoQiantaoService hotelInfoQiantaoService;
    @Autowired
    private IHotelInfoChailvgjService hotelInfoChailvgjService;
    @Autowired
    private IHotelInfoMeituanService hotelInfoMeituanService;
    @Autowired
    private HotelGnBaseService hotelGnBaseService;
    @Autowired
    private HotelCityServiceImpl hotelCityServiceImpl;

    private static final int maxCountDetailHsjl = 10;
    private static final int maxCountDetailHsjlxy = 10;
    private static final int maxCountDetailMeituan = 20;

    public boolean handle(PlatEnum plat) {
        long start = System.currentTimeMillis();
        switch (plat) {
            case PLAT_HSJL:
                log.info("处理红色加力酒店增量数据start");
                this.handleHSJLIds();
                this.handleHSJLInfo();
                this.handleHSJLXYInfo();
                log.info("处理红色加力酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
                break;
            case PLAT_HSJL_XY:
                log.info("处理红色加力协议酒店增量数据start");
//                handleHSJLXYIds();
//                handleHSJLXYInfo();
                log.info("处理红色加力协议酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
                break;
            case PLAT_CLGJ:
                log.info("处理差旅管家酒店增量数据start");
                this.handleCLGJIds();
                this.handleCLGJInfo();
                log.info("处理差旅管家酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
                break;
            case PLAT_QT:
                log.info("处理千陶酒店增量数据start");
                //this.handleQTInfo();
                this.handleQTInfoNew();
                log.info("处理千陶酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
                break;
            case PLAT_MT:
                log.info("处理美团酒店增量数据start");
                this.handleMTInfoNew();
                log.info("处理美团酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
                break;
            default:

        }
        return Boolean.TRUE;
    }

    public boolean handleHSJLIncremen() {
        long start = System.currentTimeMillis();
        log.info("处理红色加力酒店增量数据start");
        this.handleHSJLIds();
        this.handleHSJLInfo();
        log.info("处理红色加力酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
        return Boolean.TRUE;
    }

    public boolean handleHSJLXYIncremen() {
        long start = System.currentTimeMillis();
        log.info("处理红色加力协议酒店增量数据start");
        this.handleHSJLXYIds();
        this.handleHSJLXYInfo();
        log.info("处理红色加力协议酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
        return Boolean.TRUE;
    }

    public boolean handleCLGJIncremen() {
        long start = System.currentTimeMillis();
        log.info("处理差旅管家酒店增量数据start");
        this.handleCLGJIds();
        this.handleCLGJInfo();
        log.info("处理差旅管家酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
        return Boolean.TRUE;
    }

    public boolean handleQTIncremen() {
        long start = System.currentTimeMillis();
        log.info("处理千陶酒店增量数据start");
        this.handleQTInfo();
        log.info("处理千陶酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
        return Boolean.TRUE;
    }

    public boolean handleMTIncremen() {
        long start = System.currentTimeMillis();
        log.info("处理美团酒店增量数据start");
        this.handleMTInfo();
        log.info("处理美团酒店增量数据end 耗时：{}毫秒", (System.currentTimeMillis() - start));
        return Boolean.TRUE;
    }


    private void handleMTInfo() {
        HotelInfoMeituan mt = new HotelInfoMeituan();
        mt.setIncrementStatus(3);
        List<HotelInfoMeituan> meituanList = this.hotelInfoMeituanService.selectHotelInfoMeituanList2(mt);
        List<Long> idList = meituanList.stream().map(HotelInfoMeituan::getId).map(Long::valueOf).collect(Collectors.toList());
        List<List<Long>> idsList = ListUtils.partition(idList, maxCountDetailMeituan);
        idsList.stream().forEach(ids0 -> {
            log.info("抓取美团酒店数据: ids={}", ids0);
            String infoText = (String) this.fetchDataFunction(PlatEnum.PLAT_MT.getValue() + "_resource_increment_mt_detail", 1, 18, (result) -> MeituanApi.hotelDetail(ids0));
            log.info("抓取美团酒店数据: result={}", infoText);
            JSONObject jsonObject = JSONObject.parseObject(infoText);
            if (jsonObject.getInteger("code") != 0) {
                return;
            }
            JSONArray infos = jsonObject.getJSONObject("result").getJSONArray("hotelContents");
            if (infos == null) {
                return;
            }
            infos.stream().forEach(element -> {
                try {
                    JSONObject hotelInfo = (JSONObject) element;
                    HotelInfoMeituan meituan = this.getHotelInfoMeituan(hotelInfo);
                    meituan.setIncrementStatus(2);
                    this.hotelInfoMeituanService.updateHotelInfoMeituan(meituan);
                } catch (Exception e) {
                    log.error("抓取美团酒店详情进异常: {}, infoText:{}", e, infoText);
                }
            });
        });
    }

    private void handleMTInfoNew() {
        HotelGnMeituan mt = new HotelGnMeituan();
        mt.setIncrementStatus(3);
        List<HotelGnBase> meituanList = this.hotelGnBaseService.selectHotelGnBaseList(PlatEnum.PLAT_MT, mt);
        List<Long> idList = meituanList.stream().map(HotelGnBase::getId).map(Long::valueOf).collect(Collectors.toList());
        List<List<Long>> idsList = ListUtils.partition(idList, maxCountDetailMeituan);
        idsList.stream().forEach(ids0 -> {
            log.info("抓取美团酒店数据: ids={}", ids0);
            String infoText = (String) this.fetchDataFunction(PlatEnum.PLAT_MT.getValue() + "_resource_increment_mt_detail", 1, 18, (result) -> MeituanApi.hotelDetail(ids0));
            log.info("抓取美团酒店数据: result={}", infoText);
            JSONObject jsonObject = JSONObject.parseObject(infoText);
            if (jsonObject.getInteger("code") != 0) {
                return;
            }
            JSONArray infos = jsonObject.getJSONObject("result").getJSONArray("hotelContents");
            if (infos == null) {
                return;
            }
            infos.stream().forEach(element -> {
                try {
                    JSONObject hotelInfo = (JSONObject) element;
                    log.info("增量美团酒店信息,hotelInfo:{}", hotelInfo.toString());
                    HotelGnMeituan meituan = this.hotelInfoMeituanService.buildHotelGnMeituanFromJsonObject(hotelInfo);
                    meituan.setIncrementStatus(2);
                    this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_MT, Lists.newArrayList(meituan));
                } catch (Exception e) {
                    log.error("抓取美团酒店详情进异常: {}, infoText:{}", e, infoText);
                }
            });
        });
    }

    private void handleCLGJInfo() {
        // 补充酒店基础数据
        HotelInfoChailvgj hotelInfoChailvgj = new HotelInfoChailvgj();
        hotelInfoChailvgj.setIncrementStatus(3);
        List<HotelInfoChailvgj> hotelInfoList = this.hotelInfoChailvgjService.selectHotelInfoChailvgjList(hotelInfoChailvgj);
        List<Long> ids = hotelInfoList.stream().map(HotelInfoChailvgj::getId).map(Long::valueOf).collect(Collectors.toList());
        HotelInfoChailvgj hotelInfo = new HotelInfoChailvgj();
        ids.stream().forEach(ids0 -> {
            String infoText = null;
            try {
                log.info("抓取差旅管家酒店数据: ids={}", ids0);
                infoText = (String) this.fetchDataFunction(PlatEnum.PLAT_CLGJ.getValue() + "_resource_increment_clgj_detail", 1, 7, (result) -> ChailvgjApi.getHotelInfo(Long.valueOf(ids0), 3));
                log.info("抓取差旅管家酒店数据: result={}", infoText);
                JSONObject info = JSONObject.parseObject(infoText);
                if (!info.getString("Code").equals("200")) {
                    return;
                }
                JSONObject jsonObject = info.getJSONObject("Data");
                if (jsonObject == null) {
                    return;
                }
                HotelDetailDTO hotel = this.hotelInfoChailvgjService.buildHotelDetailDTO(jsonObject);
                this.hotelInfoChailvgjService.saveHotelInfo(hotel);
            } catch (Exception e) {
                log.error("抓取差旅管家酒店详情进异常: {}, infoText:{}", e, infoText);
            }
        });
    }

    private void handleQTInfo() {
        // 拉取增量数据
        String startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -1));
        String endDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -1));
        String infoText = QiantaoApi.queryIncrementalUpdate(startDate, endDate, false);
        log.info("千陶查询酒店增量接口返回报文infoText:{}", infoText);
        JSONObject obj = JSONObject.parseObject(infoText);
        if (Objects.isNull(obj) || !obj.getBoolean("IsSuccess")) {
            log.error("千陶查询酒店增量接口供应商失败:{}", infoText);
            return;
        }
        JSONArray files = obj.getJSONArray("ReturnJson");
        files.stream().forEach(file -> {
            String fileName = String.valueOf(file).substring(0, String.valueOf(file).indexOf(".zip"));
            ResumeDownload.downFile(QiantaoApi.downIncrementalUrl + file, "QTIncremental" + fileName);
            unzipAllHotelQT("QTIncremental" + fileName + ".zip");
            if (fileName.endsWith(".update")) {
                String pricehotels = QiantaoApi.pricehotels();
                Set<String> priceHotelSet = new HashSet<>();
                if (StringUtils.isNotBlank(pricehotels)) {
                    String[] priceHotelArr = pricehotels.split("\n");
                    priceHotelSet = Arrays.stream(priceHotelArr).map(String::trim).collect(Collectors.toSet());
                }
                this.handleQTHotelInfo(ConstantList.DOWN_PATH + "QTIncremental" + fileName + ".txt", priceHotelSet);
            } else {
                this.handleQTHotelInfo(ConstantList.DOWN_PATH + "QTIncremental" + fileName + ".txt", null);
            }
        });
    }
    private void handleQTInfoNew() {
        // 拉取增量数据
        String startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -1));
        String endDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -1));
        String infoText = QiantaoApi.queryIncrementalUpdate(startDate, endDate, false);
        log.info("千陶查询酒店增量接口返回报文infoText:{}", infoText);
        JSONObject obj = JSONObject.parseObject(infoText);
        if (Objects.isNull(obj) || !obj.getBoolean("IsSuccess")) {
            log.error("千陶查询酒店增量接口供应商失败:{}", infoText);
            return;
        }
        JSONArray files = obj.getJSONArray("ReturnJson");
        files.stream().forEach(file -> {
            String fileName = String.valueOf(file).substring(0, String.valueOf(file).indexOf(".zip"));
            ResumeDownload.downFile(QiantaoApi.downIncrementalUrl + file, "QTIncremental" + fileName);
            unzipAllHotelQT("QTIncremental" + fileName + ".zip");
            if (fileName.endsWith(".update")) {
                String pricehotels = QiantaoApi.pricehotels();
                Set<String> priceHotelSet = new HashSet<>();
                if (StringUtils.isNotBlank(pricehotels)) {
                    String[] priceHotelArr = pricehotels.split("\n");
                    priceHotelSet = Arrays.stream(priceHotelArr).map(String::trim).collect(Collectors.toSet());
                }
                this.handleQTHotelInfoNew(ConstantList.DOWN_PATH + "QTIncremental" + fileName + ".txt", priceHotelSet);
            } else {
                this.handleQTHotelInfoNew(ConstantList.DOWN_PATH + "QTIncremental" + fileName + ".txt", null);
            }
        });
    }

    private void handleQTHotelInfo(String fileName, Set<String> priceHotelSet) {
        LineIterator it = null;
        try {
            File file = new File(fileName);
            if (!file.exists()) {
                return;
            }
            it = FileUtils.lineIterator(file, "utf8");
            String line = null;
            while (it.hasNext()) {
                try {
                    line = it.nextLine();
                    HotelInfoQiantao hotelInfoQiantao = new HotelInfoQiantao();
                    if (fileName.endsWith(".delete.txt")) {
                        hotelInfoQiantao.setId(line);
                        hotelInfoQiantao.setIncrementTime(DateUtils.getNowDate());
                        hotelInfoQiantao.setIncrementStatus(1);
                        hotelInfoQiantao.setIncrementType(3);
                        this.hotelInfoQiantaoService.updateHotelInfoQiantao(hotelInfoQiantao);
                    } else {
                        JSONObject json = JSONObject.parseObject(line);
                        String areaId = json.getString("GovZone");
                        if (StringUtils.isEmpty(areaId)) {
                            continue;
                        }

                        hotelInfoQiantao.setId(json.getString("HotelCode"));
                        hotelInfoQiantao.setName(json.getString("CnName"));
                        hotelInfoQiantao.setBrandId(json.getString("BrandCode"));
                        hotelInfoQiantao.setCityId(json.getString("CityCode"));
                        hotelInfoQiantao.setStarInfo(json.getString("StarCode"));
                        hotelInfoQiantao.setAreaId(areaId);
                        hotelInfoQiantao.setNoticeInfo(json.getString("HotelTips"));
                        hotelInfoQiantao.setImage(json.getString("MainPicture"));
                        String address = json.getString("AddrCN");
                        hotelInfoQiantao.setAddressLine(address.length() > 40 ? address.substring(0, 40) : address);
                        hotelInfoQiantao.setBusinessDistricts(json.getString("BusinessZone"));
                        hotelInfoQiantao.setPhone(json.getString("PhoneNum"));
                        hotelInfoQiantao.setOpenDate(json.getString("OpeningTime"));
                        hotelInfoQiantao.setDecorationDate(json.getString("FixTime"));
                        hotelInfoQiantao.setFacilitiesInfo(json.getString("HotelFacilities"));
                        hotelInfoQiantao.setDescription(json.getString("Description"));
                        hotelInfoQiantao.setGroupId(json.getString("HotelGroup"));
                        BigDecimal lonGd = json.getBigDecimal("GDLongitude");
                        lonGd = BigUtils.getBigDecimal(lonGd, 999);
                        BigDecimal latGd = json.getBigDecimal("GDLatitude");
                        latGd = BigUtils.getBigDecimal(latGd, 99);
                        hotelInfoQiantao.setLonGd(lonGd);
                        hotelInfoQiantao.setLatGd(latGd);
                        hotelInfoQiantao.setIncrementTime(DateUtils.getNowDate());
                        hotelInfoQiantao.setIncrementStatus(1);
                        if (fileName.endsWith(".add.txt")) {
                            hotelInfoQiantao.setStatus(1);
                            hotelInfoQiantao.setIncrementType(1);
                            hotelInfoQiantao.setReserve9("1");
                            this.hotelInfoQiantaoService.insertOrUpdateHotelInfoQiantao(hotelInfoQiantao);
                        } else if (fileName.endsWith(".update.txt") && priceHotelSet.contains(hotelInfoQiantao.getId())) {
                            hotelInfoQiantao.setIncrementType(2);
                            this.hotelInfoQiantaoService.updateHotelInfoQiantao(hotelInfoQiantao);
                        }
                    }
                } catch (Exception e) {
                    log.error("千陶查询酒店增量处理酒店数据异常 e:{}", e);
                    log.error("千陶查询酒店增量处理酒店数据异常信息", line);
                    continue;
                }
            }
        } catch (Exception e) {
            log.error("千陶查询酒店增量处理酒店数据异常 e:{}", e);
        } finally {
            LineIterator.closeQuietly(it);
        }
    }

    private void handleQTHotelInfoNew(String fileName, Set<String> priceHotelSet) {
        LineIterator it = null;
        try {
            File file = new File(fileName);
            if (!file.exists()) {
                return;
            }
            it = FileUtils.lineIterator(file, "utf8");
            String line = null;
            while (it.hasNext()) {
                try {
                    line = it.nextLine();
                    HotelGnQiantao hotelInfoQiantao = new HotelGnQiantao();
                    if (fileName.endsWith(".delete.txt")) {
                        hotelInfoQiantao.setId(line);
                        hotelInfoQiantao.setIncrementTime(DateUtils.getNowDate());
                        hotelInfoQiantao.setIncrementStatus(1);
                        hotelInfoQiantao.setIncrementType(3);
                        hotelInfoQiantao.setUpdateTime(new Date());
                        this.hotelGnBaseService.updateByPrimaryKeySelective(PlatEnum.PLAT_QT, hotelInfoQiantao);
                    } else {
                        JSONObject json = JSONObject.parseObject(line);
                        String areaId = json.getString("GovZone");
                        if (StringUtils.isEmpty(areaId)) {
                            continue;
                        }

                        ////

                        HotelCity city = this.hotelCityServiceImpl.getByCityIdAndPlatIdForCache(PlatEnum.PLAT_QT, json.getString("CityCode"));
                        if (city == null) {
                            city = new HotelCity();
                        }

                        hotelInfoQiantao.setReserve0(json.toString());
                        hotelInfoQiantao.setId(json.getString("HotelCode"));
                        hotelInfoQiantao.setName(json.getString("CnName"));
                        hotelInfoQiantao.setNameEn(json.getString("EnName"));
                        hotelInfoQiantao.setOpenDate(json.getString("OpeningTime"));
                        hotelInfoQiantao.setDecorationDate(json.getString("FixTime"));
                        hotelInfoQiantao.setPhone(json.getString("PhoneNum"));
                        hotelInfoQiantao.setCountryId(city.getCountryId());
                        hotelInfoQiantao.setCountryName(city.getCountryName());
                        hotelInfoQiantao.setProvinceId(city.getProvinceId());
                        hotelInfoQiantao.setProvinceName(city.getProvinceName());
                        hotelInfoQiantao.setCityId(json.getString("CityCode"));
                        hotelInfoQiantao.setCityName(city.getCityName());
                        hotelInfoQiantao.setAreaId(city.getLocationId());
                        hotelInfoQiantao.setAreaName(json.getString("GovZone"));

                        hotelInfoQiantao.setBusinessDistrictName(json.getString("BusinessZone"));

                        hotelInfoQiantao.setAddress(json.getString("AddrCN"));

                        try {
                            BigDecimal lonGd = json.getBigDecimal("GDLongitude");
                            lonGd = BigUtils.getBigDecimal(lonGd, 999);
                            BigDecimal latGd = json.getBigDecimal("GDLatitude");
                            latGd = BigUtils.getBigDecimal(latGd, 99);
                            hotelInfoQiantao.setLonGaode(lonGd);
                            hotelInfoQiantao.setLatGaode(latGd);
                        } catch (Exception e) {
                        }
                        try {
                            BigDecimal lonGd = json.getBigDecimal("Longitude");
                            lonGd = BigUtils.getBigDecimal(lonGd, 999);
                            BigDecimal latGd = json.getBigDecimal("Latitude");
                            latGd = BigUtils.getBigDecimal(latGd, 99);
                            hotelInfoQiantao.setLonBaidu(lonGd);
                            hotelInfoQiantao.setLatBaidu(latGd);
                        } catch (Exception e) {
                        }

                        hotelInfoQiantao.setGroupName(json.getString("HotelGroup"));
                        hotelInfoQiantao.setBrandId(json.getString("BrandCode"));
                        hotelInfoQiantao.setStar(json.getString("StarCode"));
                        hotelInfoQiantao.setImage(json.getString("MainPicture"));
                        hotelInfoQiantao.setScore(json.getString("CommentPoint"));
                        hotelInfoQiantao.setSynopsis(json.getString("ShortDescription"));
                        hotelInfoQiantao.setPolicyInfo(JSON.toJSONString( new PolicyInfo()));

                        JSONArray facilitiesArray = json.getJSONArray("HotelFacilities");
                        FacilitiesInfo facilitiesInfo = new FacilitiesInfo();


                        // 停车
                        FacilitiesInfo.Parking parking = new FacilitiesInfo.Parking();
                        parking.setIsHave(Boolean.FALSE);
                        parking.setIsCharge(Boolean.FALSE);
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {

                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value != null && value.contains("停车")) {
                                    parking.setIsHave(Boolean.TRUE);
                                    break;
                                }

                            }
                        }
                        // 收费
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value != null && value.contains("停车收费")) {
                                    parking.setIsCharge(Boolean.TRUE);
                                    break;
                                }

                            }
                        }
                        facilitiesInfo.setParking(parking);

                        //充电
                        FacilitiesInfo.Charging charging = new FacilitiesInfo.Charging();
                        charging.setIsHave(Boolean.FALSE);
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value != null && value.contains("充电桩")) {
                                    charging.setIsHave(Boolean.TRUE);
                                    break;
                                }

                            }
                        }
                        facilitiesInfo.setCharging(charging);

                        //电梯
                        FacilitiesInfo.Lift lift = new FacilitiesInfo.Lift();
                        lift.setIsHave(Boolean.FALSE);
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value != null && value.contains("电梯")) {
                                    lift.setIsHave(Boolean.TRUE);
                                    break;
                                }
                            }
                        }
                        facilitiesInfo.setLift(lift);

                        //行李寄存
                        FacilitiesInfo.Baggage baggage = new FacilitiesInfo.Baggage();
                        baggage.setIsHave(Boolean.FALSE);
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value != null && value.contains("行李寄存")) {
                                    baggage.setIsHave(Boolean.TRUE);
                                    break;
                                }
                            }
                        }
                        facilitiesInfo.setBaggage(baggage);

                        //餐厅
                        FacilitiesInfo.Restaurant restaurant = new FacilitiesInfo.Restaurant();
                        restaurant.setIsHave(Boolean.FALSE);
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value != null && value.contains("餐厅")) {
                                    restaurant.setIsHave(Boolean.TRUE);
                                    break;
                                }
                            }
                        }
                        facilitiesInfo.setRestaurant(restaurant);

                        // 会议室
                        FacilitiesInfo.MeetingRoom meetingRoom = new FacilitiesInfo.MeetingRoom();
                        meetingRoom.setIsHave(Boolean.FALSE);
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value != null && value.contains("会议")) {
                                    meetingRoom.setIsHave(Boolean.TRUE);
                                    break;
                                }
                            }
                        }
                        facilitiesInfo.setMeetingRoom(meetingRoom);

                        // 公共区域WIFI
                        FacilitiesInfo.PublicWifi publicWifi = new FacilitiesInfo.PublicWifi();
                        publicWifi.setIsHave(Boolean.FALSE);

                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value != null && value.contains("公用区wifi")) {
                                    publicWifi.setIsHave(Boolean.TRUE);
                                    break;
                                }
                            }
                        }
                        facilitiesInfo.setPublicWifi(publicWifi);


                        //健身房
                        FacilitiesInfo.Gym gym = new FacilitiesInfo.Gym();
                        gym.setIsHave(Boolean.FALSE);
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value !=null && value.contains("健身")) {
                                    gym.setIsHave(Boolean.TRUE);
                                    break;
                                }
                            }
                        }
                        facilitiesInfo.setGym(gym);

                        //洗衣房
                        FacilitiesInfo.Laundry laundry = new FacilitiesInfo.Laundry();
                        laundry.setIsHave(Boolean.FALSE);
                        if (facilitiesArray != null && facilitiesArray.size() > 0) {
                            for (Object o : facilitiesArray) {
                                JSONObject facility = (JSONObject) o;
                                String value = facility.getString("Value");
                                if (value !=null && value.contains("洗衣")) {
                                    laundry.setIsHave(Boolean.TRUE);
                                    break;
                                }
                            }
                        }
                        facilitiesInfo.setLaundry(laundry);

                        hotelInfoQiantao.setFacilitiesInfo(JSON.toJSONString(facilitiesInfo));


                        hotelInfoQiantao.setStatus(1);
                        hotelInfoQiantao.setIsDelete(0);
                        hotelInfoQiantao.setIncrementStatus(0);
                        hotelInfoQiantao.setIncrementTime(new Date());
                        hotelInfoQiantao.setCreateTime(new Date());
                        hotelInfoQiantao.setUpdateTime(new Date());
                        // 如果是定时任务会报错
                        String userName = "admin";
                        try {
                            userName = SecurityUtils.getUsername();
                        } catch (Exception e) {

                        }
                        hotelInfoQiantao.setCreateBy(userName);
                        hotelInfoQiantao.setUpdateBy(userName);


                        ////


                        hotelInfoQiantao.setIncrementTime(DateUtils.getNowDate());
                        hotelInfoQiantao.setIncrementStatus(1);
                        if (fileName.endsWith(".add.txt")) {
                            hotelInfoQiantao.setStatus(1);
                            hotelInfoQiantao.setIncrementType(1);
                            hotelInfoQiantao.setReserve9("1");
                        } else if (fileName.endsWith(".update.txt") && priceHotelSet.contains(hotelInfoQiantao.getId())) {
                            hotelInfoQiantao.setIncrementType(2);
                        }

                        this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_QT, Lists.newArrayList(hotelInfoQiantao));


                    }
                } catch (Exception e) {
                    log.error("千陶查询酒店增量处理酒店数据异常 e:{}", e);
                    log.error("千陶查询酒店增量处理酒店数据异常信息", line);
                    continue;
                }
            }
        } catch (Exception e) {
            log.error("千陶查询酒店增量处理酒店数据异常 e:{}", e);
        } finally {
            LineIterator.closeQuietly(it);
        }
    }

    private void handleCLGJIds() {
        // 拉取增量数据
        String startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, DateUtils.addDays(new Date(), -1));
        int reqLastRecordId = 0;
        while (true) {
            log.info("差旅管家查询酒店增量请求：{}, {}", startDate, reqLastRecordId);
            String infoText = ChailvgjApi.getHotelIncr(startDate, reqLastRecordId);
            log.info("差旅管家查询酒店增量接口返回报文infoText:{}", infoText);
            JSONObject obj = JSONObject.parseObject(infoText);
            if (Objects.isNull(obj) || !"200".equals(obj.getString("Code"))) {
                log.error("差旅管家查询酒店增量接口供应商失败:{}", infoText);
                break;
            }
            JSONObject info = obj.getJSONObject("Data");
            if (Objects.isNull(info)) {
                log.error("差旅管家查询酒店增量接口供应商失败:{}", infoText);
                break;
            }
            Integer lastRecordId = info.getInteger("LastRecordId");
            if (lastRecordId == null) {
                break;
            }
            JSONArray ids = info.getJSONArray("HotelIncrementList");
            HotelInfoChailvgj hotelInfoChailvgj = new HotelInfoChailvgj();
            ids.stream().forEach(element -> {
                JSONObject hotelInfo = (JSONObject) element;
                hotelInfoChailvgj.setId(hotelInfo.getString("HotelId"));
                hotelInfoChailvgj.setStatus(1);
                hotelInfoChailvgj.setIncrementStatus(3);
                hotelInfoChailvgj.setIncrementTime(DateUtils.getNowDate());
                hotelInfoChailvgj.setIncrementType(1);
                this.hotelInfoChailvgjService.insertOrUpdateHotelInfoChailvgj(hotelInfoChailvgj);
            });
            if (ids.isEmpty()) {
                break;
            }
            reqLastRecordId = lastRecordId;
        }
    }

    private void handleHSJLXYInfo() {
        // 补充酒店基础数据
        List<HotelGnBase> hotelInfoHsjlList = this.hotelGnBaseService.getByIncrementStatus(PlatEnum.PLAT_HSJL_XY, 3);
        List<Long> ids = hotelInfoHsjlList.stream().map(HotelGnBase::getId).map(Long::valueOf).collect(Collectors.toList());
        List<List<Long>> idsList = ListUtils.partition(ids, maxCountDetailHsjlxy);
        HotelGnHsjlXy hotelInfoHsjl1 = new HotelGnHsjlXy();
        idsList.stream().forEach(ids0 -> {
            log.info("抓取红色加力协议酒店数据: ids={}", ids0);
            String infoText = null;
            while (true) {
                try {
                    infoText = (String) this.fetchDataFunction(PlatEnum.PLAT_HSJL_XY.getValue() + "_resource_increment_hsjlxy_detail", 60, 90, (result) -> HsjlxyApi.queryHotelInfo(ids0, null, null));
                    break;
                } catch (Exception e) {
                    continue;
                }
            }
            log.info("抓取红色加力协议酒店数据: result={}", infoText);
            JSONObject info;
            try {
                info = JSONObject.parseObject(infoText);
            } catch (Exception e) {
                return;
            }
            if (!info.getString("returnCode").equals("000")) {
                return;
            }
            JSONArray hotelInfos = info.getJSONObject("bussinessResponse").getJSONArray("hotelInfos");
            if (hotelInfos == null) {
                ids0.stream().forEach(id -> {
                    try {
                        hotelInfoHsjl1.setId(String.valueOf(id));
                        hotelInfoHsjl1.setIncrementType(3);
                        this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL_XY, Collections.singletonList(hotelInfoHsjl1));
                    } catch (Exception e) {
                        log.error("抓取红色加力酒店详情更新下架异常: {}, id:{}", e, id);
                    }
                });
                return;
            }
            String finalInfoText = infoText;
            Set<Long> idSet = new HashSet<>(ids0);
            hotelInfos.stream().forEach(element -> {
                try {
                    JSONObject hotelInfo = (JSONObject) element;
                    HotelGnHsjl hotelGnHsjl = this.hotelInfoHsjlService.buildHotelGnHsjl(PlatEnum.PLAT_HSJL, hotelInfo);
                    this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL_XY, Collections.singletonList(hotelGnHsjl));
                    idSet.remove(hotelInfo.getLong("hotelId"));
                } catch (Exception e) {
                    log.error("抓取红色加力协议酒店详情进异常: {}, infoText:{}", e, finalInfoText);
                }
            });
            idSet.stream().forEach(id -> {
                try {
                    hotelInfoHsjl1.setId(String.valueOf(id));
                    hotelInfoHsjl1.setIncrementType(3);
                    this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL_XY, Collections.singletonList(hotelInfoHsjl1));
                } catch (Exception e) {
                    log.error("抓取红色加力酒店详情更新下架异常: {}, id:{}", e, id);
                }
            });
        });
    }

    private void handleHSJLXYIds() {
        // 拉取增量数据
        String startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -2));
        String endDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -1));
        int pageNo = 1;
        while (true) {
            log.info("红色加力协议请求：{}, {}, {}", startDate, endDate, pageNo);
            int finalPageNo = pageNo;
            String infoText = null;
            try {
                infoText = (String) this.fetchDataFunction(PlatEnum.PLAT_HSJL_XY.getValue() + "_resource_increment_hsjlxy_ids", 60, 90, (result) -> HsjlxyApi.queryHotelIncrement(startDate, endDate, finalPageNo));
            } catch (Exception e) {
                continue;
            }
            log.info("红色加力协议增量接口返回报文infoText:{}", infoText);
            JSONObject obj = JSONObject.parseObject(infoText);
            if (Objects.isNull(obj) || !"000".equals(obj.getString("returnCode"))) {
                log.error("调用红色加力协议增量接口供应商失败:{}", infoText);
                break;
            }
            JSONObject info = obj.getJSONObject("bussinessResponse");
            if (Objects.isNull(info)) {
                log.error("调用红色加力协议增量接口供应商失败:{}", infoText);
                break;
            }
            int pageCount = info.getInteger("pages");
            JSONArray ids = info.getJSONArray("increments");
            HotelInfoHsjlxy hotelInfoHsjl = new HotelInfoHsjlxy();
            ids.stream().forEach(id -> {
                hotelInfoHsjl.setId(String.valueOf(id));
                hotelInfoHsjl.setStatus(1);
                hotelInfoHsjl.setIncrementStatus(3);
                hotelInfoHsjl.setIncrementTime(DateUtils.getNowDate());
                hotelInfoHsjl.setIncrementType(1);
                this.hotelInfoHsjlxyService.insertOrUpdateHotelInfoHsjlxy(hotelInfoHsjl);
            });
            if (pageNo >= pageCount) {
                break;
            }
            pageNo++;
        }
    }

    private void handleHSJLInfo() {
        // 补充酒店基础数据
        List<HotelGnBase> hotelInfoHsjlList = this.hotelGnBaseService.getByIncrementStatus(PlatEnum.PLAT_HSJL, 3);
        List<Long> ids = hotelInfoHsjlList.stream().map(HotelGnBase::getId).map(Long::valueOf).collect(Collectors.toList());
        List<List<Long>> idsList = ListUtils.partition(ids, maxCountDetailHsjl);
        HotelGnHsjl hotelInfoHsjl1 = new HotelGnHsjl();
        idsList.stream().forEach(ids0 -> {
            log.info("抓取红色加力酒店数据: ids={}", ids0);
            String infoText = null;
            while (true) {
                try {
                    infoText = (String) this.fetchDataFunction(PlatEnum.PLAT_HSJL.getValue() + "_resource_increment_hsjl_detail", 60, 90, (result) -> HsjlApi.queryHotelInfo(ids0, null, null));
                    break;
                } catch (Exception e) {
                    continue;
                }
            }
            log.info("抓取红色加力酒店数据: result={}", infoText);
            JSONObject info;
            try {
                info = JSONObject.parseObject(infoText);
            } catch (Exception e) {
                return;
            }
            if (!info.getString("returnCode").equals("000")) {
                return;
            }
            JSONArray hotelInfos = info.getJSONObject("bussinessResponse").getJSONArray("hotelInfos");
            if (hotelInfos == null) {
                log.info("抓取红色加力酒店数据: hotelInfos=null");
                ids0.stream().forEach(id -> {
                    try {
                        hotelInfoHsjl1.setId(String.valueOf(id));
                        hotelInfoHsjl1.setIncrementType(3);
                        this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Arrays.asList(hotelInfoHsjl1));
                    } catch (Exception e) {
                        log.error("抓取红色加力酒店详情更新下架异常: {}, id:{}", e, id);
                    }
                });
                return;
            }
            String finalInfoText = infoText;
            Set<Long> idSet = new HashSet<>(ids0);
            log.info("抓取红色加力酒店数据 first: idSet:{}", idSet);
            hotelInfos.stream().forEach(element -> {
                try {
                    JSONObject hotelInfo = (JSONObject) element;
                    HotelGnHsjl hotelGnHsjl = this.hotelInfoHsjlService.buildHotelGnHsjl(PlatEnum.PLAT_HSJL, hotelInfo);
                    this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Collections.singletonList(hotelGnHsjl));
                    idSet.remove(hotelInfo.getLong("hotelId"));
                    log.info("抓取红色加力酒店数据 second: idSet:{}", idSet);
                } catch (Exception e) {
                    log.error("抓取红色加力酒店详情进异常: {}, infoText:{}", e, finalInfoText);
                }
            });
            log.info("抓取红色加力酒店数据 third: idSet:{}", idSet);
            idSet.stream().forEach(id -> {
                try {
                    hotelInfoHsjl1.setId(String.valueOf(id));
                    hotelInfoHsjl1.setIncrementType(3);
                    this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Collections.singletonList(hotelInfoHsjl1));
                } catch (Exception e) {
                    log.error("抓取红色加力酒店详情更新下架异常: {}, id:{}", e, id);
                }
            });
        });
    }

    public void handleHSJLIds() {
        // 拉取协议酒店
        Set<Long> xyIdsSet = new HashSet<>();
        int xyPageNo = 1;
        while (true) {
            log.info("红色加力协议酒店列表请求：{}", xyPageNo);
            int finalPageNo = xyPageNo;
            String infoText = null;
            try {
                infoText = (String) this.fetchDataFunction(PlatEnum.PLAT_HSJL.getValue() + "_resource_increment_hsjlxy_hotel_ids", 60, 90, (result) -> HsjlxyApi.queryHotelIdList(null, finalPageNo, null, null));
            } catch (Exception e) {
                continue;
            }
            log.info("红色加力协议酒店列表返回报文infoText:{}", infoText);
            JSONObject obj = JSONObject.parseObject(infoText);
            if (Objects.isNull(obj) || !"000".equals(obj.getString("returnCode"))) {
                log.error("调用红色加力协议酒店列表接口供应商失败:{}", infoText);
                break;
            }
            JSONObject info = obj.getJSONObject("bussinessResponse");
            if (Objects.isNull(info)) {
                log.error("调用红色加力协议酒店列表接口供应商失败:{}", infoText);
                break;
            }
            int pageCount = info.getInteger("totalPage");
            JSONArray ids = info.getJSONArray("hotelIds");
            xyIdsSet.addAll(IntStream.range(0, ids.size())
                    .mapToLong(ids::getLong)
                    .boxed()
                    .collect(Collectors.toSet()));
            if (xyPageNo >= pageCount) {
                break;
            }
            xyPageNo++;
        }
        log.info("红色加力协议酒店列表数量：{}", xyIdsSet.size());
        // 拉取增量数据
        String startDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -2));
        String endDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(new Date(), -1));
        int pageNo = 1;
        while (true) {
            log.info("红色加力请求：{}, {}, {}", startDate, endDate, pageNo);
            int finalPageNo = pageNo;
            String infoText = null;
            try {
                infoText = (String) this.fetchDataFunction(PlatEnum.PLAT_HSJL.getValue() + "_resource_increment_hsjl_ids", 60, 90, (result) -> HsjlApi.queryHotelIncrement(startDate, endDate, finalPageNo));
            } catch (Exception e) {
                continue;
            }
            log.info("红色加力增量接口返回报文infoText:{}", infoText);
            JSONObject obj = JSONObject.parseObject(infoText);
            if (Objects.isNull(obj) || !"000".equals(obj.getString("returnCode"))) {
                log.error("调用红色加力增量接口供应商失败:{}", infoText);
                break;
            }
            JSONObject info = obj.getJSONObject("bussinessResponse");
            if (Objects.isNull(info)) {
                log.error("调用红色加力增量接口供应商失败:{}", infoText);
                break;
            }
            int pageCount = info.getInteger("pages");
            JSONArray ids = info.getJSONArray("increments");
            HotelInfoHsjl hotelInfoHsjl = new HotelInfoHsjl();
            HotelInfoHsjlxy hotelInfoHsjlxy = new HotelInfoHsjlxy();
            ids.stream().forEach(id -> {
                if (xyIdsSet.contains(Long.valueOf(String.valueOf(id)))) {
                    hotelInfoHsjlxy.setId(String.valueOf(id));
                    hotelInfoHsjlxy.setStatus(1);
                    hotelInfoHsjlxy.setIncrementStatus(3);
                    hotelInfoHsjlxy.setIncrementTime(DateUtils.getNowDate());
                    hotelInfoHsjlxy.setIncrementType(1);
                    this.hotelInfoHsjlxyService.insertOrUpdateHotelInfoHsjlxy(hotelInfoHsjlxy);
                } else {
                    hotelInfoHsjl.setId(String.valueOf(id));
                    hotelInfoHsjl.setStatus(1);
                    hotelInfoHsjl.setIncrementStatus(3);
                    hotelInfoHsjl.setIncrementTime(DateUtils.getNowDate());
                    hotelInfoHsjl.setIncrementType(1);
                    this.hotelInfoHsjlService.insertOrUpdateHotelInfoHsjl(hotelInfoHsjl);
                }
            });
            if (pageNo >= pageCount) {
                break;
            }
            pageNo++;
        }

        // 使用数据库协议酒店id和红色返回的协议id比较，补充数据库没有的协议id
        boolean flag = true;
        int page = 1;
        int pageSize = 1000;
        Set<String> xyDBIdList = new HashSet<>();
        while (flag) {
            try {
                List<HotelInfoHsjlxy> hsjlxyList = this.hotelInfoHsjlxyService.selectAllId((page - 1) * pageSize, pageSize);
                if (CollectionUtils.isEmpty(hsjlxyList)) {
                    break;
                }
                xyDBIdList.addAll(hsjlxyList.stream().map(item -> item.getId()).collect(Collectors.toSet()));
            } catch (Exception e) {
                this.logger.error("获取协议酒店id异常:{}", e);
                flag = false;
            }

            page++;
        }
        HotelInfoHsjlxy hotelInfoHsjlxy = new HotelInfoHsjlxy();
        xyIdsSet.stream().forEach(id -> {
            if (!xyDBIdList.contains(String.valueOf(id))) {
                log.info("数据库不存在的协议酒店id:{}", id);
                // 协议酒店id, 数据库没有
                hotelInfoHsjlxy.setId(String.valueOf(id));
                hotelInfoHsjlxy.setStatus(1);
                hotelInfoHsjlxy.setIncrementStatus(3);
                hotelInfoHsjlxy.setIncrementTime(DateUtils.getNowDate());
                hotelInfoHsjlxy.setIncrementType(1);
                this.hotelInfoHsjlxyService.insertOrUpdateHotelInfoHsjlxy(hotelInfoHsjlxy);
            }
        });
    }

    public Object fetchDataFunction(String key, long second, long num, Function<Integer, Object> function) {
        int tryNum = 1;
        RRateLimiter rRateLimiter = this.limiter.getLimiter(key, second, num);
        boolean tryAcquire = rRateLimiter.tryAcquire(1);
        while (!tryAcquire) {
            this.logger.info("当前供应商渠道【{}】限流.......睡眠500ms", key);
            try {
                Thread.sleep(500);
            } catch (InterruptedException ex) {
                this.logger.info("睡眠异常。。。。。。。。。。");
            }
            tryNum++;
            tryAcquire = rRateLimiter.tryAcquire(1);
        }
        return function.apply(tryNum);
    }

    public static void unzipAllHotelQT(String fileName) {
        try {
            FileInputStream fis = new FileInputStream(ConstantList.DOWN_PATH + fileName);
            ZipInputStream zis = new ZipInputStream(fis);
            ZipEntry entry = zis.getNextEntry();
            while (entry != null) {
                String newFileName = entry.getName();
                File newFile = new File(ConstantList.DOWN_PATH + "QTIncremental" + newFileName);
                // 写入文件到目录中
                FileOutputStream fos = new FileOutputStream(newFile);
                byte[] buffer = new byte[1024];
                int len;
                long i = 0;
                while ((len = zis.read(buffer)) > 0) {
                    i++;
                    fos.write(buffer, 0, len);
                }
                fos.close();
                zis.closeEntry(); // 关闭ZipEntry并准备读取下一个条目
                entry = zis.getNextEntry();
            }
            zis.close(); // 关闭ZipInputStream以释放任何系统资源。
        } catch (Exception e) {
            log.error("e:{}", e);
        }
    }

    public HotelInfoMeituan getHotelInfoMeituan(JSONObject meituanJson) {
        HotelInfoMeituan hotelInfo = new HotelInfoMeituan();
        try {
            JSONObject mtBase = meituanJson.getJSONObject("baseInfo");
            JSONObject mtDetail = meituanJson.getJSONObject("detailInfo");
            JSONArray mtNotices = meituanJson.getJSONArray("notices");
            JSONArray mtImages = meituanJson.getJSONArray("images");

            hotelInfo.setId(meituanJson.getString("hotelId"));
            hotelInfo.setName(mtBase.getString("name"));
            hotelInfo.setNameEn(mtBase.getString("nameEn"));
            hotelInfo.setPhone(mtBase.getString("phone"));
            JSONObject mtAddr = mtBase.getJSONObject("address");
            hotelInfo.setReserve3(mtBase.getString("bookable"));
            if (mtAddr.getJSONObject("province") != null) {
                hotelInfo.setProvinceId(mtAddr.getJSONObject("province").getString("code"));
            }
            hotelInfo.setCityId(mtAddr.getJSONObject("city").getString("code"));
            hotelInfo.setCityName(mtAddr.getJSONObject("city").getString("name"));
            if (mtAddr.getJSONObject("area") != null) {
                hotelInfo.setAreaId(mtAddr.getJSONObject("area").getString("code"));
            }
            hotelInfo.setBusinessDistricts(mtAddr.getJSONArray("businessDistricts")
                    .getJSONObject(0).getString("name"));
            hotelInfo.setAddressLine(mtAddr.getJSONObject("addressLine").getString("name"));
            JSONArray lonlats = mtAddr.getJSONArray("coordinates");
            for (int i = 0; i < lonlats.size(); i++) {
                JSONObject lonlat = lonlats.getJSONObject(i);
                String name = lonlat.getString("provider");
                BigDecimal lon = BigDecimal.valueOf(lonlat.getLong("longitude")).divide(BigDecimal.valueOf(1000000));
                BigDecimal lat = BigDecimal.valueOf(lonlat.getLong("latitude")).divide(BigDecimal.valueOf(1000000));
                lat = lat.setScale(11, BigDecimal.ROUND_HALF_UP);
                lon = lon.setScale(11, BigDecimal.ROUND_HALF_UP);
                switch (name) {
                    case "GAODE":
                        hotelInfo.setLonGd(lon);
                        hotelInfo.setLatGd(lat);
                        break;
                    case "BAIDU":
                        hotelInfo.setLonBd(lon);
                        hotelInfo.setLatBd(lat);
                        break;
                    case "GOOGLE":
                        hotelInfo.setLonGg(lon);
                        hotelInfo.setLatGg(lat);
                }
            }
            hotelInfo.setOpenDate(mtDetail.getString("openDate"));
            hotelInfo.setDecorationDate(mtDetail.getString("decorationDate"));
            hotelInfo.setDescription(mtDetail.getString("description"));
            //设置楼层
            JSONArray statics = mtDetail.getJSONArray("statics");
            for (int j = 0; j < statics.size(); j++) {
                JSONObject floor = statics.getJSONObject(j);
                if (floor.getString("type").equals("FLOOR_COUNT")) {
                    hotelInfo.setFloorCount(floor.getString("value"));
                }
            }
            //设置评分
            JSONArray scores = mtDetail.getJSONArray("ratings");
            for (int k = 0; k < scores.size(); k++) {
                JSONObject score = scores.getJSONObject(k);
                if (score.getString("type").equals("AVG_SCORE")) {
                    hotelInfo.setReserve2(score.getString("value"));
                }
            }
            JSONObject starInfo = mtDetail.getJSONObject("star");
            if (starInfo != null) {
                hotelInfo.setStarInfo(starInfo.toString());
            }
            JSONObject typeInfo = mtDetail.getJSONObject("type");
            if (typeInfo != null) {
                hotelInfo.setTypeId(typeInfo.toString());
            }
            JSONObject brandInfo = mtDetail.getJSONObject("brand");
            if (brandInfo != null) {
                hotelInfo.setBrandId(brandInfo.getString("name"));
            }
            JSONObject groupInfo = mtDetail.getJSONObject("group");
            if (groupInfo != null) {
                hotelInfo.setGroupId(groupInfo.getString("name"));
            }
            JSONObject themesInfo = mtDetail.getJSONObject("themes");
            if (themesInfo != null) {
                hotelInfo.setThemeId(themesInfo.toString());
            }
            JSONObject typesInfo = mtDetail.getJSONObject("types");
            if (themesInfo != null) {
                hotelInfo.setTypeId(typesInfo.toString());
            }
            //设置注意事项
            String noticeText = "";
            if (mtNotices != null) {
                for (int m = 0; m < mtNotices.size(); m++) {
                    JSONObject notice = mtNotices.getJSONObject(m);
                    noticeText += "<br>\\n" + notice.getString("value");
                }
                hotelInfo.setNoticeInfo(noticeText);
            }

            //设置首图
            for (int n = 0; n < mtImages.size(); n++) {
                JSONObject image = mtImages.getJSONObject(n);
                if (image.getString("title").equals("酒店首图")) {
                    hotelInfo.setImage(image.getJSONArray("links").getJSONObject(0).getString("url"));
                }
            }
            JSONObject mtPolicies = meituanJson.getJSONObject("policy");
            hotelInfo.setPolicyInfo(mtPolicies.toString());
            JSONArray mtFacilities = meituanJson.getJSONArray("facilities");
            hotelInfo.setFacilitiesInfo(mtFacilities.toString());
        } catch (Exception e) {
            this.logger.error("设置hotel进异常了： " + e);
        }
        return hotelInfo;
    }


}
