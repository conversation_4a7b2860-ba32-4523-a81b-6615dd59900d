import os
from openai import OpenAI

# ==============================================================================
# 1. 配置与初始化 (Configuration & Initialization)
# ==============================================================================

# 为了安全，API密钥从环境变量 'DEEPSEEK_API_KEY' 中获取
# 您需要在您的运行环境中设置这个环境变量
api_key = os.getenv("DEEPSEEK_API_KEY")
if not api_key:
    # 如果未找到API密钥，则引发错误并提示用户
    raise ValueError("未找到API密钥。请设置 DEEPSEEK_API_KEY 环境变量。")

# 初始化 DeepSeek 大模型客户端
client = OpenAI(
    api_key=api_key, 
    base_url="https://api.deepseek.com"
)

# ==============================================================================
# 2. 数据定义 (Data Definitions)
# ==============================================================================

# 为不同场景类型提供匹配的 Few-shots 示例，以提高生成质量
few_shots = {
    "战斗": """
问题：描述一个《黑神话 悟空》中的场景，玩家正天空中战斗。
答案：让我们一步步描绘这个过程：
1. 一步跨越千里云端, 
2. 悟空与金翅大鹏在九霄对峙, 
3. 金箍棒与利爪相交, 
4. 震碎了一轮圆月。 
""",
    "探索": """
问题：描述一个《黑神话 悟空》中的场景，玩家正在探索一个古老的、被遗忘的寺庙。
答案：让我们一步步描绘这个过程：
1. 拨开千年藤蔓, 露出斑驳的庙门,
2. 踏入尘封的大殿, 佛像垂目, 蛛网遍布,
3. 悟空在残破的壁画上发现隐藏的符文,
4. 触碰符文, 暗门缓缓开启, 露出通往地下的秘道。
""",
    "剧情": """
问题：描述一个《黑神话 悟空》中的场景，悟空与一个老僧对话。
答案：让我们一步步描绘这个过程：
1. 菩提树下, 枯坐的老僧缓缓睁眼,
2. 他问悟空: "大圣此行, 是为求真, 还是为寻仇？",
3. 悟空默然, 握紧的金箍棒又松开,
4. 风吹过, 卷起一地落叶, 仿佛是无数逝去的执念。
"""
}

# 场景要素定义保持不变
scene_types = {
    "战斗": ["对手", "武器", "环境", "技能", "结果"],
    "探索": ["地点", "发现", "障碍", "解决", "奖励"],
    "剧情": ["人物", "冲突", "对话", "情感", "转折"]
}

# ==============================================================================
# 3. 核心逻辑 (封装为函数以复用)
# ==============================================================================

def generate_scene_description(scene_type: str, scene_prompt: str, model: str = "deepseek-chat", max_tokens: int = 1024, temperature: float = 0.7) -> str:
    """
    使用 DeepSeek API 生成《黑神话：悟空》风格的场景描述。

    Args:
        scene_type (str): 场景类型 ("战斗", "探索", "剧情").
        scene_prompt (str): 描述新场景的简短提示.
        model (str): 用于生成的模型名称.
        max_tokens (int): 生成内容的最大 token 数.
        temperature (float): 采样温度.

    Returns:
        str: 生成的场景描述文本.

    Raises:
        ValueError: 如果场景类型无效.
        Exception: API 调用出错时.
    """
    if scene_type not in scene_types:
        raise ValueError(f"无效的场景类型: {scene_type}. 可选类型: {list(scene_types.keys())}")

    # 构建提示信息
    messages = [
        {
            "role": "system", 
            "content": f"""你是一位熟悉《黑神话 悟空》的{scene_type}场景设计助手，
                          善于用细节描述画面。
                          请根据以下场景要素来构建描述：
                          - {scene_types[scene_type]}"""
        },
        {
            "role": "user", 
            "content": f"""{few_shots.get(scene_type, "请开始描述。")}
                          请继续描述一个新的场景：{scene_prompt}"""
        }
    ]

    # ==============================================================================
    # 4. API 调用与错误处理
    # ==============================================================================
    try:
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            stream=False
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"调用 API 时发生错误: {e}")
        # 将异常重新抛出，以便上层调用者可以捕获
        raise

# ==============================================================================
# 5. 示例用法 (Example Usage)
# ==============================================================================

if __name__ == "__main__":
    try:
        # 您可以在这里轻松更改参数
        selected_scene_type = "探索"
        user_scene_prompt = "水帘洞"

        print(f"正在生成【{selected_scene_type}】场景：{user_scene_prompt}...\n")
        
        # 调用重构后的函数
        description = generate_scene_description(
            scene_type=selected_scene_type,
            scene_prompt=user_scene_prompt
        )

        # 输出生成结果
        print("--- 生成的场景描述 ---")
        print(description)
        print("--------------------")

    except ValueError as ve:
        # 捕获因输入参数错误（如无效的场景类型）引发的异常
        print(f"输入错误: {ve}")
    except Exception as e:
        # 捕获在函数调用过程中可能发生的其他异常（如API错误）
        print(f"程序运行出错: {e}") 