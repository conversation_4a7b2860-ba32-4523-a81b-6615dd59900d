package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.domain.ZhJdJdbMapping;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnPreMappingMapper;
import com.ltgj.ivw.mapper.JdJdbMapper;
import com.ltgj.ivw.mapper.ZhJdJdbMapper;
import com.ltgj.ivw.request.hotelGnPreMapping.ListRequest;
import com.ltgj.ivw.request.hotelGnPreMapping.ListResponse;
import com.ltgj.ivw.service.HotelGnPreMappingService;
import com.ltgj.ivw.service.IJdJdbService;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.util.BeanCovertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 酒店-国内-预映射ServiceImpl
 */
@Slf4j
@Service
@RefreshScope
public class HotelGnPreMappingServiceImpl implements HotelGnPreMappingService {

    @Autowired
    private HotelGnPreMappingService hotelGnPreMappingService;

    @Autowired
    private HotelGnPreMappingMapper hotelGnPreMappingMapper;

    @Autowired
    private JdJdbMapper jdJdbMapper;

    @Autowired
    private IJdJdbService jdJdbService;

    @Autowired
    private HotelGnBaseService hotelGnBaseService;

    @Autowired
    private ZhJdJdbMapper zhJdJdbMapper;

    @Autowired
    private BeanCovertUtil beanCovertUtil;

    @Value("${hotelGnPreMapping.highScore:100}")
    private Integer highScore;

    @Value("${hotelGnPreMapping.lowScore:0}")
    private Integer lowScore;

    /**
     * 一次查询条数
     */
    @Value("${hotelGnPreMapping.batchSize:100}")
    private Integer batchSize;

    /**
     * 处理任务批次大小
     */
    @Value("${hotelGnPreMapping.processBatchSize:1000}")
    private Integer processBatchSize;

    /**
     * 是否循环执行
     */
    @Value("${hotelGnPreMapping.isLoop:false}")
    private Boolean isLoop;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Resource
    @Qualifier(value = "asyncExecutorPreMapping")
    private ThreadPoolTaskExecutor asyncExecutor;


    @Override
    public List<ListResponse> selectList(ListRequest listRequest) {
        return hotelGnPreMappingMapper.selectList(listRequest);
    }

    @Override
    public HotelGnPreMapping selectById(String id) {
        return hotelGnPreMappingMapper.selectByPrimaryKey(id);
    }

    @Override
    public int deleteByIds(String[] ids) {
        return hotelGnPreMappingMapper.deleteByIds(ids);
    }

    @Override
    public int addHotelGnPreMapping(HotelGnPreMapping hotelGnPreMapping) {
        return hotelGnPreMappingMapper.insertSelective(hotelGnPreMapping);
    }

    private String buildTargetHotelId(HotelGnPreMapping mapping) {
        String targetHotelId = null;
        String targetPlatformId = mapping.getTargetPlatformId();
        if (ObjectUtils.isEmpty(targetPlatformId)) {
            return targetHotelId;
        } else {
            if ("-1".equals(targetPlatformId)) {
                targetHotelId = mapping.getTargetHotelId();
            } else {
                targetHotelId = targetPlatformId + mapping.getTargetHotelId();
            }
        }
        return targetHotelId;
    }

    private void updateMappingSuccess(HotelGnPreMapping mapping, String reason, String userName) {
        mapping.setStatus((byte) 1);
        mapping.setHandlerReason(reason);
        mapping.setUpdateTime(new Date());
        mapping.setUpdateBy(userName);
        hotelGnPreMappingMapper.updateByPrimaryKey(mapping);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateMappingError(HotelGnPreMapping mapping, String reason) {
        mapping.setStatus((byte) 2);
        mapping.setHandlerReason(reason);
        mapping.setUpdateTime(new Date());
        mapping.setUpdateBy("system");
        hotelGnPreMappingMapper.updateByPrimaryKey(mapping);
    }

    public void processHighScoreMappingAsync(String s) {
        log.info("开始执行 processHighScoreMapping，参数: {}", s);
        JSONObject param = JSONObject.parseObject(s);
        JSONArray hotelIdArray = param.getJSONArray("hotelIds");
        JSONArray interfacePlatArray = param.getJSONArray("interfacePlats");

        List<String> hotelIds = hotelIdArray.isEmpty() ? null : hotelIdArray.toJavaList(String.class);
        List<String> interfacePlats = interfacePlatArray.isEmpty() ? null : interfacePlatArray.toJavaList(String.class);

        List<HotelGnPreMapping> list;
        do {
            list = hotelGnPreMappingMapper.selectNeedProcess(hotelIds, interfacePlats, highScore, null, processBatchSize);
            if (CollectionUtils.isEmpty(list)) {
                log.info("没有需要处理的数据");
                return;
            }

            log.info("本次共获取到 {} 条预映射数据进行处理", list.size());

            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (int i = 0; i < list.size(); i += batchSize) {
                int end = Math.min(i + batchSize, list.size());
                List<HotelGnPreMapping> subList = list.subList(i, end);

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> processHighBatch(subList), asyncExecutor);
                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

            try {
                allFutures.get(); // 阻塞主线程直到全部完成
                log.info("所有异步任务已完成");
            } catch (Exception e) {
                log.error("异步任务执行异常", e);
            }

            if (!isLoop) {
                log.info("标记isLoop = " + isLoop + "，仅执行一次");
                break;
            }

            if (hotelIds != null && !hotelIds.isEmpty()) {
                break;
            }

        } while (!list.isEmpty());
    }


    private void processHighBatch(List<HotelGnPreMapping> batch) {
        batch.forEach(mapping -> transactionTemplate.execute(status -> {
            try {
                hotelGnPreMappingService.processHighScoreMapping(mapping, "system");
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("处理预映射数据失败，ID: {}, {}", mapping.getId(), e.getStackTrace());
                log.error("处理预映射数据失败，ID: {}", mapping.getId(), e);
                hotelGnPreMappingService.updateMappingError(mapping, e.getMessage());
                return null;
            }
        }));
    }

    @Override
    public void processLowScoreMappingAsync(String s) {
        log.info("开始执行 processLowScoreMapping，参数: {}", s);
        JSONObject param = JSONObject.parseObject(s);
        JSONArray hotelIdArray = param.getJSONArray("hotelIds");
        JSONArray interfacePlatArray = param.getJSONArray("interfacePlats");

        List<String> hotelIds = hotelIdArray.isEmpty() ? null : hotelIdArray.toJavaList(String.class);
        List<String> interfacePlats = interfacePlatArray.isEmpty() ? null : interfacePlatArray.toJavaList(String.class);

        List<HotelGnPreMapping> list;
        do {
            list = hotelGnPreMappingMapper.selectNeedProcess(hotelIds, interfacePlats, null, lowScore, processBatchSize);
            if (CollectionUtils.isEmpty(list)) {
                log.info("没有需要处理的数据");
                return;
            }

            log.info("本次共获取到 {} 条预映射数据进行处理", list.size());

            // 创建 futures 列表用于等待
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (int i = 0; i < list.size(); i += batchSize) {
                int end = Math.min(i + batchSize, list.size());
                List<HotelGnPreMapping> subList = list.subList(i, end);

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> processLowBatch(subList), asyncExecutor);
                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            try {
                allFutures.get(); // 阻塞直到全部完成
                log.info("所有异步任务已完成");
            } catch (Exception e) {
                log.error("异步任务执行异常", e);
            }

            if (!isLoop) {
                log.info("标记isLoop = " + isLoop + "，仅执行一次");
                break;
            }

            if (hotelIds != null && !hotelIds.isEmpty()) {
                break;
            }

        } while (!list.isEmpty());
    }

    private void processLowBatch(List<HotelGnPreMapping> batch) {
        batch.forEach(mapping -> transactionTemplate.execute(status -> {
            try {
                hotelGnPreMappingService.processLowScoreMapping(mapping, "system", "2");
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("处理预映射数据失败，ID: {},{}", mapping.getId(), e.getStackTrace());
                log.error("处理预映射数据失败，ID: {}", mapping.getId(), e);
                hotelGnPreMappingService.updateMappingError(mapping, e.getMessage());
                return null;
            }
        }));
    }

    private HotelGnBase validateSourceHotel(HotelGnPreMapping mapping) {
        PlatEnum platEnum = PlatEnum.getplatEnum(mapping.getSourePlatformId());
        HotelGnBase hotelGnBase = hotelGnBaseService.getById(platEnum, mapping.getSourceHotelId());
        if (ObjectUtils.isEmpty(hotelGnBase)) {
            throw new RuntimeException("源酒店不存在,映射失败");
        }
        return hotelGnBase;
    }

    private ZhJdJdbMapping checkExistingMapping(HotelGnPreMapping mapping) {
        List<ZhJdJdbMapping> existingMappings = zhJdJdbMapper.findByInterfacePlatAndPlatId(
                Long.parseLong(mapping.getSourePlatformId()), mapping.getSourceHotelId());

        if (CollectionUtils.isNotEmpty(existingMappings)) {
            if (existingMappings.size() > 1) {
                int size = (int) existingMappings.stream()
                        .map(ZhJdJdbMapping::getLocalId)
                        .distinct()
                        .count();
                if (size > 1) {
                    throw new RuntimeException("源酒店映射关系异常，存在多个映射关系");
                }
            }

            ZhJdJdbMapping existMap = existingMappings.get(0);
            String targetHotelId = buildTargetHotelId(mapping);
            if (!existMap.getLocalId().equals(targetHotelId)) {
                throw new RuntimeException("源酒店已存在映射且与目标酒店不一致");
            }
            return existMap;
        }
        return null;
    }

    private void createNewMapping(HotelGnPreMapping mapping, String targetHotelId) {

        //查询本地酒店
        JdJdb jdJdb = jdJdbMapper.selectJdJdbById(targetHotelId);

        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
        zhJdJdbMapping.setInterfacePlat(Long.parseLong(mapping.getSourePlatformId()));
        zhJdJdbMapping.setLocalId(targetHotelId);
        zhJdJdbMapping.setJdName(jdJdb.getJdmc());
        zhJdJdbMapping.setPlatId(mapping.getSourceHotelId());
        zhJdJdbMapping.setPlatJdName(mapping.getSourceHotelName());
        zhJdJdbMapping.setStatus(0);
        zhJdJdbMapping.setSaveDate(new Date());
        zhJdJdbMapping.setIsGnGj(1);
        zhJdJdbMapper.insert(zhJdJdbMapping);
    }

    private void updateHotelStatusAndMapping(HotelGnBase hotelGnBase, PlatEnum platEnum, HotelGnPreMapping mapping, String userName, String targetHotelId) {
        hotelGnBaseService.updateStatusById(platEnum, hotelGnBase.getId(), 8, userName, new Date());
        updateMappingSuccess(mapping, "映射成功, 平台酒店id = " + hotelGnBase.getId() + ",打底酒店id = " + targetHotelId, userName);
    }


    private void handleKetanPlatform(HotelGnPreMapping mapping, HotelGnBase hotelGnBase, String targetHotelId) {
        //判断是否为ketan平台
        if (PlatEnum.PLAT_KT.getValue().equals(mapping.getSourePlatformId())) {
            //将hotelGnBase转换为JdJdb
            JdJdb update = beanCovertUtil.platCovertToJdJdb(PlatEnum.PLAT_KT, hotelGnBase, targetHotelId);
            //清空创建日期
            update.setCreatedate(null);
            //清空商圈 地理位置是四级联动必须保持一致，如果只改某一部分,剩下的部分需要清空
//            update.setDistrict(null);
//            update.setDistrictName(null);
//            update.setBusinessZone(null);
//            update.setBusinessZone(null);
            //更新JdJdb
            jdJdbMapper.updateById(update);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMapping(String id) {
        HotelGnPreMapping mapping = hotelGnPreMappingMapper.selectByPrimaryKey(id);
        String targetHotelId = buildTargetHotelId(mapping);


        if (mapping.getIsDelete() || mapping.getStatus() != 0) {
            throw new RuntimeException("此酒店状态已变更，无法操作");
        }

        // 源酒店是否存在
        PlatEnum platEnum = PlatEnum.getplatEnum(mapping.getSourePlatformId());
        HotelGnBase hotelGnBase = validateSourceHotel(mapping);

        // 目标酒店是否存在
        if (ObjectUtils.isEmpty(targetHotelId)) {
            throw new RuntimeException("目标酒店不存在，映射失败");
        }
        JdJdb jdJdb = jdJdbMapper.selectJdJdbById(targetHotelId);
        if (ObjectUtils.isEmpty(jdJdb)) {
            throw new RuntimeException("目标酒店不存在，映射失败");
        }

        List<ZhJdJdbMapping> existingMappings = zhJdJdbMapper.findByInterfacePlatAndPlatId(
                Long.parseLong(mapping.getSourePlatformId()), mapping.getSourceHotelId());

        if (CollectionUtils.isNotEmpty(existingMappings)) {
            ZhJdJdbMapping existMap = existingMappings.get(0);
            if (!existMap.getLocalId().equals(targetHotelId)) {
                throw new RuntimeException("源酒店已存在映射且与目标酒店不一致,映射失败");
            }
        } else {
            List<ZhJdJdbMapping> zhJdJdbMappingList = zhJdJdbMapper.selectByLocalIdsAndInterfacePlat(
                    Arrays.asList(targetHotelId), Long.parseLong(mapping.getSourePlatformId()));

            if (CollectionUtils.isNotEmpty(zhJdJdbMappingList)) {
                throw new RuntimeException("目标酒店已存在映射且与源酒店不一致,映射失败");
            }

            // 新增映射
            createNewMapping(mapping, targetHotelId);
        }

//        handleKetanPlatform(mapping, hotelGnBase, targetHotelId);

        updateHotelStatusAndMapping(hotelGnBase, platEnum, mapping, SecurityUtils.getUsername(), targetHotelId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBasic(String id) {
        HotelGnPreMapping mapping = hotelGnPreMappingMapper.selectByPrimaryKey(id);
        //手动新增
        processLowScoreMapping(mapping, SecurityUtils.getUsername(), "1");
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void processHighScoreMapping(HotelGnPreMapping mapping, String userName) {
        if (mapping.getIsDelete() || mapping.getStatus() != 0) {
            throw new RuntimeException("此酒店状态已变更，无法操作");
        }

        Boolean isAddMapping = false;

        // 源酒店是否存在
        HotelGnBase hotelGnBase = validateSourceHotel(mapping);
        PlatEnum platEnum = PlatEnum.getplatEnum(mapping.getSourePlatformId());

        String targetHotelId = buildTargetHotelId(mapping);
        if (ObjectUtils.isEmpty(targetHotelId)) {
            targetHotelId = mapping.getSourePlatformId() + mapping.getSourceHotelId();
        }

        // 源酒店映射是否存在
        ZhJdJdbMapping existMap = checkExistingMapping(mapping);
        if (existMap == null) {
            isAddMapping = true;
//            createNewMapping(mapping, targetHotelId);
        }

        // 目标酒店是否存在
        JdJdb jdJdb = jdJdbMapper.selectJdJdbById(targetHotelId);
        if (ObjectUtils.isEmpty(jdJdb)) {
            jdJdb = beanCovertUtil.platCovertToJdJdb(platEnum, hotelGnBase, targetHotelId);
            jdJdbService.insertJdJdb(jdJdb);
        } else {
            handleKetanPlatform(mapping, hotelGnBase, targetHotelId);
        }

        if (isAddMapping) {
            createNewMapping(mapping, targetHotelId);
        }

        updateHotelStatusAndMapping(hotelGnBase, platEnum, mapping, userName, targetHotelId);
    }

    /**
     * 处理低价
     *
     * @param mapping
     * @param userName
     * @param type     手动新增 1，  定时任务 2
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void processLowScoreMapping(HotelGnPreMapping mapping, String userName, String type) {
        if (mapping.getIsDelete() || mapping.getStatus() != 0) {
            throw new RuntimeException("数据状态已变更，无法操作");
        }

        // 源酒店是否存在
        HotelGnBase hotelGnBase = validateSourceHotel(mapping);
        PlatEnum platEnum = PlatEnum.getplatEnum(mapping.getSourePlatformId());

        // 源酒店映射是否存在
        ZhJdJdbMapping existMap = checkExistingMappingLow(mapping);
        if (existMap != null) {
            JdJdb jdJdb = jdJdbMapper.selectJdJdbById(existMap.getLocalId());
            if (!Objects.isNull(jdJdb)) {
                handleKetanPlatform(mapping, hotelGnBase, jdJdb.getId());
                updateHotelStatusAndMapping(hotelGnBase, platEnum, mapping, userName, jdJdb.getId());
                return;
            }
            // 删除映射
            zhJdJdbMapper.deleteById(existMap.getId());
        }

        // 源酒店编码+源酒店id在打底酒店是否存在
        JdJdb jdJdb = jdJdbMapper.selectJdJdbById(mapping.getSourePlatformId() + mapping.getSourceHotelId());
        if (ObjectUtils.isEmpty(jdJdb)) {
            // 新增打底
            jdJdb = beanCovertUtil.platCovertToJdJdb(platEnum, hotelGnBase, mapping.getSourePlatformId() + mapping.getSourceHotelId());
            jdJdbService.insertJdJdb(jdJdb);
//            jdJdbMapper.insertJdJdb(jdJdb);

            // 新建映射
            ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
            zhJdJdbMapping.setInterfacePlat(Long.parseLong(mapping.getSourePlatformId()));
            zhJdJdbMapping.setLocalId(jdJdb.getId());
            zhJdJdbMapping.setPlatId(mapping.getSourceHotelId());
            zhJdJdbMapping.setIsGnGj(1);
            zhJdJdbMapping.setStatus(0);
            zhJdJdbMapping.setJdName(jdJdb.getJdmc());
            zhJdJdbMapping.setPlatJdName(mapping.getSourceHotelName());
            zhJdJdbMapping.setSaveDate(new Date());
            zhJdJdbMapper.insert(zhJdJdbMapping);

            updateHotelStatusAndMapping(hotelGnBase, platEnum, mapping, userName, jdJdb.getId());
            return;
        }

        // 打底酒店是否存在该平台映射
        List<ZhJdJdbMapping> zhJdJdbMappingList = zhJdJdbMapper.selectByLocalIdsAndInterfacePlat(
                Arrays.asList(jdJdb.getId()), Long.parseLong(mapping.getSourePlatformId()));
        if (CollectionUtils.isNotEmpty(zhJdJdbMappingList)) {
            throw new RuntimeException("源酒店已存在于打底酒店中且映射的平台酒店不是源酒店");
        }

        if (type.equals("2")) {
            handleKetanPlatform(mapping, hotelGnBase, jdJdb.getId());
        }

        createNewMapping(mapping, jdJdb.getId());

        updateHotelStatusAndMapping(hotelGnBase, platEnum, mapping, userName, jdJdb.getId());
    }

    @Override
    public void filterListByStatus(List<HotelGnPreMapping> aiHotelList) {
        if (CollectionUtils.isNotEmpty(aiHotelList)) {
            List<HotelGnPreMapping> hotelGnPreMappings = hotelGnPreMappingMapper.selectListByStatus(aiHotelList, aiHotelList.get(0).getSourePlatformId());
            if (CollectionUtils.isNotEmpty(hotelGnPreMappings) ) {
                List<HotelGnPreMapping> removingList = aiHotelList.stream().filter(aiHotel -> hotelGnPreMappings.stream().anyMatch(hotelGnPreMapping -> hotelGnPreMapping.getSourceHotelId().equals(aiHotel.getSourceHotelId()))).collect(Collectors.toList());
                aiHotelList.removeAll(removingList);
            }
            log.info("过滤完后，共计:{}条, aiHotelList:{}", aiHotelList.size(), aiHotelList);
        }
    }

    private ZhJdJdbMapping checkExistingMappingLow(HotelGnPreMapping mapping) {

        List<ZhJdJdbMapping> existingMappings = zhJdJdbMapper.findByInterfacePlatAndPlatId(
                Long.parseLong(mapping.getSourePlatformId()), mapping.getSourceHotelId());

        if (CollectionUtils.isNotEmpty(existingMappings)) {
            if (existingMappings.size() > 1) {
                int size = (int) existingMappings.stream()
                        .map(ZhJdJdbMapping::getLocalId)
                        .distinct()
                        .count();
                if (size > 1) {
                    throw new RuntimeException("源酒店映射关系异常，存在多个映射关系");
                }
            }
            return existingMappings.get(0);
        }
        ;
        return null;
    }

}
