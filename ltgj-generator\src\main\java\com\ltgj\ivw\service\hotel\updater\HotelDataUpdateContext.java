package com.ltgj.ivw.service.hotel.updater;

/**
 * 酒店数据更新上下文(策略模式)
 *
 * <AUTHOR>
 */
public class HotelDataUpdateContext {
    private final HotelDataUpdateStrategy updateStrategy;
    
    /**
     * 构造方法
     * @param updateStrategy 更新策略
     */
    public HotelDataUpdateContext(HotelDataUpdateStrategy updateStrategy) {
        this.updateStrategy = updateStrategy;
    }
    
    /**
     * 执行更新
     */
    public void executeUpdate(String userName) {
        updateStrategy.execute(userName);
    }
} 