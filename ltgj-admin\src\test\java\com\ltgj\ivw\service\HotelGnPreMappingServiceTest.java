package com.ltgj.ivw.service;

import com.github.pagehelper.PageInfo;
import com.ltgj.common.constant.HttpStatus;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.utils.PageUtils;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnPreMappingMapper;
import com.ltgj.ivw.request.hotelGnPreMapping.ListRequest;
import com.ltgj.ivw.request.hotelGnPreMapping.ListResponse;
import com.ltgj.ivw.service.impl.HotelGnPreMappingServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.*;

/**
 * HotelGnPreMappingServiceTest单元测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HotelGnPreMappingServiceTest {

    @Autowired
    private HotelGnPreMappingServiceImpl hotelGnPreMappingService;

    @Test
    public void testSelectList_shouldReturnEmptyList_whenNoData() {
        ListRequest request = new ListRequest();

        PageUtils.startPage();
        List<ListResponse> list = hotelGnPreMappingService.selectList(request);
        for (ListResponse item : list) {
            //平台名称转换
            if (item.getSourePlatformId() != null){
                item.setSourePlatformName(PlatEnum.getplatEnum(item.getSourePlatformId()).getName());
            }
        }
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
//        rspData.setTotal(new PageInfo(list).getTotal());
        System.out.println(new PageInfo(list).getTotal());
    }

    @Test
    public void testSelectById_shouldReturnNull_whenIdNotExists() {
        String id = "non-exist-id";

        HotelGnPreMapping result = hotelGnPreMappingService.selectById(id);

        assertNull(result);
    }
    @Test
    public void testDeleteByIds_shouldReturnZero_whenIdsInvalid() {
        String[] ids = {"invalid1", "invalid2"};

        int result = hotelGnPreMappingService.deleteByIds(ids);

        assertEquals(0, result);
    }

    @Test
    public void testAddHotelGnPreMapping_shouldInsertSuccessfully_withValidData() {
        HotelGnPreMapping mapping = new HotelGnPreMapping();
        mapping.setId("testId");
        mapping.setSourceHotelId("source_123");

        int result = hotelGnPreMappingService.addHotelGnPreMapping(mapping);

        assertTrue(result > 0);
    }



    @Test
    public void testProcessMapping_shouldNotThrowException_withNullInput() {
        assertDoesNotThrow(() -> hotelGnPreMappingService.processMapping(null));
    }

    @Test
    public void testProcessMapping_withSpecificIds_shouldNotThrowException() {
        assertDoesNotThrow(() -> hotelGnPreMappingService.processMapping("10012911"));
    }

} 