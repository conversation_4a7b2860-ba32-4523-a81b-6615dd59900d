package com.ltgj.ivw.mapper;

import java.util.List;

import com.ltgj.ivw.domain.meituan.HotelCityMeituan;
import com.ltgj.ivw.domain.meituan.HotelCityMeituanExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface HotelCityMeituanMapper {
    long countByExample(HotelCityMeituanExample example);

    int deleteByExample(HotelCityMeituanExample example);

    int insert(HotelCityMeituan record);

    int insertSelective(HotelCityMeituan record);

    List<HotelCityMeituan> selectByExample(HotelCityMeituanExample example);

    int updateByExampleSelective(@Param("record") HotelCityMeituan record, @Param("example") HotelCityMeituanExample example);

    int updateByExample(@Param("record") HotelCityMeituan record, @Param("example") HotelCityMeituanExample example);
}