package com.ltgj.ivw.service;

import com.ltgj.ivw.domain.HotelCityGeo;

import java.util.List;

/**
 * 酒店城市商圈Service接口
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface IHotelCityGeoService
{
    /**
     * 查询酒店城市商圈
     *
     * @param id 酒店城市商圈主键
     * @return 酒店城市商圈
     */
    public HotelCityGeo selectHotelCityGeoById(String id);

    /**
     * 查询酒店城市商圈列表
     *
     * @param hotelCityGeo 酒店城市商圈
     * @return 酒店城市商圈集合
     */
    public List<HotelCityGeo> selectHotelCityGeoList(HotelCityGeo hotelCityGeo);

    /**
     * 新增酒店城市商圈
     *
     * @param hotelCityGeo 酒店城市商圈
     * @return 结果
     */
    public int insertHotelCityGeo(HotelCityGeo hotelCityGeo);

    /**
     * 修改酒店城市商圈
     *
     * @param hotelCityGeo 酒店城市商圈
     * @return 结果
     */
    public int updateHotelCityGeo(HotelCityGeo hotelCityGeo);

    /**
     * 批量删除酒店城市商圈
     *
     * @param ids 需要删除的酒店城市商圈主键集合
     * @return 结果
     */
    public int deleteHotelCityGeoByIds(String[] ids);

    /**
     * 删除酒店城市商圈信息
     *
     * @param id 酒店城市商圈主键
     * @return 结果
     */
    public int deleteHotelCityGeoById(String id);

    /**
     * 批量插入酒店城市商圈数据
     *
     * @param records 商圈数据列表
     * @return 插入成功的条数
     */
    public int batchInsert(List<HotelCityGeo> records);

    /**
     * 批量插入酒店城市商圈数据 非id自增模式
     *
     * @param records 商圈数据列表
     * @return 插入成功的条数
     */
    public int batchInsertForContidion(List<HotelCityGeo> records);

    /**
     * 批量插入或更新酒店城市商圈数据
     *
     * @param records 商圈数据列表
     * @return 插入/更新成功的条数
     */
    public int batchInsertOrUpdate(List<HotelCityGeo> records);

    /**
     * 从预加载的缓存中获取地理位置层次结构
     * 功能与 IGeoHierarchyService.getGeoStructureById 相同，但从内存缓存中检索
     * 
     * @param request 查询请求参数
     * @return 地理位置层次结构
     */
    public com.ltgj.common.core.domain.AjaxResult<com.ltgj.ivw.dto.GeoHierarchyNode> getGeoStructureByIdFromCache(com.ltgj.ivw.dto.GeoStructureQueryReq request);
    
    /**
     * 预加载地理层级数据到缓存
     * 在Spring容器启动时调用，如果缓存已存在则跳过
     */
    public void preloadGeoHierarchyCache();
} 