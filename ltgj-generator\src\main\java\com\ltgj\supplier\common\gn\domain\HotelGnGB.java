package com.ltgj.supplier.common.gn.domain;

import com.ltgj.common.annotation.LogDynamicInfo;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

@Data
@LogDynamicInfo(title = "国内酒店-集团品牌")
public class HotelGnGB {

    /**
     * ID
     */
    private String id;

    /**
     * id
     */
    private String gbId;

    /**
     * 名称
     */
    private String gbName;

    /**
     * 名称-英文
     */
    private String gbNameEn;

    /**
     * 类型，集团：group；品牌：brand
     */
    private String type;

    /**
     * 父级id
     */
    private String parentGbId;

    /**
     * 平台编号：-1，基础平台；艺龙：200004
     */
    private String platformId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 映射状态，0：未映射；1：已映射
     */
    private int mappingStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 删除标识，0：未删除；1：删除
     */
    private Integer isDelete;

}
