package com.ltgj.ivw.service.impl;

import java.util.Collections;
import java.util.List;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.enums.PlatEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ltgj.ivw.mapper.HotelCityMappingMapper;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.service.IHotelCityMappingService;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 城市映射管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-28
 */
@Service
public class HotelCityMappingServiceImpl implements IHotelCityMappingService
{
    @Autowired
    private HotelCityMappingMapper hotelCityMappingMapper;

    /**
     * 查询城市映射管理
     *
     * @param id 城市映射管理主键
     * @return 城市映射管理
     */
    @Override
    public HotelCityMapping selectHotelCityMappingById(Long id)
    {
        return hotelCityMappingMapper.selectHotelCityMappingById(id);
    }

    /**
     * 查询城市映射管理列表
     *
     * @param hotelCityMapping 城市映射管理
     * @return 城市映射管理
     */
    @Override
    public List<HotelCityMapping> selectHotelCityMappingList(HotelCityMapping hotelCityMapping)
    {
        return hotelCityMappingMapper.selectHotelCityMappingList(hotelCityMapping);
    }

    @Override
    public List<HotelCityMapping> selectListLikeLocaltionName(HotelCityMapping hotelCityMapping) {
        return hotelCityMappingMapper.selectListLikeLocaltionName(hotelCityMapping);
    }

    /**
     * 新增城市映射管理
     *
     * @param hotelCityMapping 城市映射管理
     * @return 结果
     */
    @Override
    public int insertHotelCityMapping(HotelCityMapping hotelCityMapping)
    {
        return hotelCityMappingMapper.insertHotelCityMapping(hotelCityMapping);
    }

    /**
     * 修改城市映射管理
     *
     * @param hotelCityMapping 城市映射管理
     * @return 结果
     */
    @Override
    public int updateHotelCityMapping(HotelCityMapping hotelCityMapping)
    {
        return hotelCityMappingMapper.updateHotelCityMapping(hotelCityMapping);
    }

    /**
     * 批量删除城市映射管理
     *
     * @param ids 需要删除的城市映射管理主键
     * @return 结果
     */
    @Override
    public int deleteHotelCityMappingByIds(Long[] ids)
    {
        return hotelCityMappingMapper.deleteHotelCityMappingByIds(ids);
    }

    /**
     * 删除城市映射管理信息
     *
     * @param id 城市映射管理主键
     * @return 结果
     */
    @Override
    public int deleteHotelCityMappingById(Long id)
    {
        return hotelCityMappingMapper.deleteHotelCityMappingById(id);
    }

    @Override
    public List<List<HotelCityMapping>> groupByNum(List<HotelCityMapping> hotelCityMappings, int num) {
        //按照num分组
        List<List<HotelCityMapping>> result = new java.util.ArrayList<>();
        for (int i = 0; i < hotelCityMappings.size(); i += num) {
            result.add(hotelCityMappings.subList(i, Math.min(i + num, hotelCityMappings.size())));
        }
        return result;
    }

    @Override
    public HotelCityMapping getHotelCityByCondition(String platformId, String cityId) {
        if (StringUtils.isNotEmpty(platformId) && PlatEnum.PLAT_HSJL_XY.getValue().equals(platformId)){
            platformId = PlatEnum.PLAT_HSJL.getValue();
        }
        List<HotelCityMapping> list = hotelCityMappingMapper.getHotelCityByCondition(platformId, cityId);
        if (CollectionUtils.isNotEmpty(list)){
            return list.get(0);
        }
        return null;
    }
}
