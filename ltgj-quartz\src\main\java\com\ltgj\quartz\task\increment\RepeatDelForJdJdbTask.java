package com.ltgj.quartz.task.increment;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.domain.ZhJdJdbMapping;
import com.ltgj.ivw.dto.JdJdbRepeatDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelGnBaseService;
import com.ltgj.ivw.service.IJdJdbService;
import com.ltgj.ivw.service.ZhJdJdbMappingService;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2025/2/14
 * @description:
 */
@Component("repeatDelForJdJdbTask")
@Slf4j
public class RepeatDelForJdJdbTask {
    @Autowired
    private IJdJdbService jdJdbService;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;

    public void dealWithData(String params) {
        log.info("开始处理重复酒店数据，参数: {}", params);
        try {
            JdJdbRepeatDTO jdJdbRepeatDTO = JdJdbRepeatDTO.builder().build();
            if (StringUtils.isNotEmpty(params)) {
                jdJdbRepeatDTO.setJdmc(params);
            }
            List<JdJdbRepeatDTO> duplicates = findDuplicateHotels(jdJdbRepeatDTO);
            if (CollectionUtils.isEmpty(duplicates)) {
                log.info("未发现重复酒店数据，结束处理");
                return;
            }

            log.info("开始处理 {} 组重复数据", duplicates.size());
            processDuplicates(duplicates);
            log.info("重复酒店数据处理完成");
        } catch (Exception e) {
            log.error("处理重复酒店数据时发生异常，参数: {}", params, e);
            // 重新抛出异常以便上层感知
            throw new RuntimeException("处理重复酒店数据失败", e);
        }
    }

    private List<JdJdbRepeatDTO>findDuplicateHotels(JdJdbRepeatDTO jdJdbRepeatDTO) {
        List<JdJdbRepeatDTO> duplicates = jdJdbService.selectJdJdbRepeatByJdmcAndJddz(jdJdbRepeatDTO);
        log.info("发现重复酒店数据 {} 组", CollectionUtils.size(duplicates));
        return duplicates;
    }

    // 处理重复数据
    private void processDuplicates(List<JdJdbRepeatDTO> duplicates) {
        // 初始化已处理的重复数据组数
        int processedGroups = 0;
        // 初始化已删除的映射记录数
        int deletedMappings = 0;
        // 记录跳过的数据组数
        int skippedGroups = 0;

        log.info("开始逐个处理 {} 组重复数据", duplicates.size());
        
        for (int i = 0; i < duplicates.size(); i++) {
            JdJdbRepeatDTO dto = duplicates.get(i);
            log.info("处理第 {}/{} 组重复数据: jdmc='{}', jddz='{}', cityId='{}'", 
                    i + 1, duplicates.size(), dto.getJdmc(), dto.getJddz(), dto.getCityId());
            
            if (!isValidDTO(dto)) {
                log.warn("第 {} 组数据校验失败，跳过处理: jdmc='{}', jddz='{}', cityId='{}'", 
                        i + 1, dto.getJdmc(), dto.getJddz(), dto.getCityId());
                skippedGroups++;
                continue;
            }
            
            List<JdJdb> hotels = findRelatedHotels(dto);
            if (CollectionUtils.isEmpty(hotels)) {
                log.warn("第 {} 组未找到相关酒店数据，跳过处理: jdmc='{}', jddz='{}', cityId='{}'", 
                        i + 1, dto.getJdmc(), dto.getJddz(), dto.getCityId());
                skippedGroups++;
                continue;
            }
            
            log.info("第 {} 组找到 {} 个相关酒店，开始处理映射关系", i + 1, hotels.size());
            
            try {
                // 处理酒店映射并累加已删除的映射记录数
                int currentDeleted = processHotelMappings(hotels);
                deletedMappings += currentDeleted;
                // 累加已处理的重复数据组数
                processedGroups++;
                log.info("第 {} 组处理成功，删除 {} 条记录", i + 1, currentDeleted);
            } catch (Exception e) {
                log.error("第 {} 组处理失败: jdmc='{}', jddz='{}', cityId='{}', error: {}", 
                        i + 1, dto.getJdmc(), dto.getJddz(), dto.getCityId(), e.getMessage(), e);
                skippedGroups++;
            }
        }

        // 记录处理结果日志，包括处理的重复数据组数和删除的映射记录数
        log.info("处理完成。总共 {} 组，成功处理 {} 组，跳过 {} 组，删除 {} 条映射记录", 
                duplicates.size(), processedGroups, skippedGroups, deletedMappings);
    }

    private boolean isValidDTO(JdJdbRepeatDTO dto) {
        return StringUtils.isNotBlank(dto.getJdmc())
                && StringUtils.isNotBlank(dto.getJddz());
    }

    private List<JdJdb> findRelatedHotels(JdJdbRepeatDTO dto) {
        JdJdb query = new JdJdb();
        query.setJdmc(dto.getJdmc());
        query.setJddz(dto.getJddz());
        query.setCityId(dto.getCityId());
        
        log.debug("查询相关酒店: jdmc='{}', jddz='{}', cityId='{}'", dto.getJdmc(), dto.getJddz(), dto.getCityId());
        List<JdJdb> result = jdJdbService.selectJdJdbListNew(query);
        log.debug("查询结果: 找到 {} 个相关酒店", CollectionUtils.size(result));
        
        return result;
    }

    private int processHotelMappings(List<JdJdb> hotels) {

        log.info("开始处理重复酒店数据，酒店数量: {}", hotels.size());
        log.debug("酒店详情: {}", hotels.stream().map(h -> 
                String.format("id=%s,jdmc=%s", h.getId(), h.getJdmc())).collect(Collectors.toList()));
        
        List<String> hotelIds = hotels.stream()
                .map(JdJdb::getId)
                .collect(Collectors.toList());

        List<ZhJdJdbMapping> mappings = zhJdJdbMappingService.findDataByLocalIdsAndJdmc(hotelIds, hotels.get(0).getJdmc());
        log.info("查询到 {} 条映射记录", CollectionUtils.size(mappings));
        
        if (CollectionUtils.isEmpty(mappings)){
            log.info("进入情况1: 无映射记录，根据保存时间选择保留的酒店");

            Map<String, Date> hotelSaveDates = getHotelSaveDates(hotels);
            log.debug("酒店保存时间映射: {}", hotelSaveDates);
            
            //要留下的
            String maxKey = getMaxKey(hotelSaveDates, hotels);
            log.info("选择保留的酒店ID: {}", maxKey);
            
            //删除的
            List<String> deleteIds = hotelIds.stream()
                    .filter(item -> !StringUtils.equals(item, maxKey)).collect(Collectors.toList());

            log.info("准备删除的酒店ID: {}", deleteIds);
            
            if (deleteIds.isEmpty()) {
                log.info("无需删除任何酒店");
                return 0;
            }

            String[] array = deleteIds.toArray(new String[0]);

            int i = jdJdbService.deleteJdJdbByIds(array);

            log.info("情况1删除酒店数据 {} 条，deleteIds: {}", i, deleteIds);
            return i;
        }

        //mappings根据localId分组
        Map<String, List<ZhJdJdbMapping>> localIdsGroups = mappings.stream().collect(Collectors.groupingBy(ZhJdJdbMapping::getLocalId, Collectors.toList()));
        log.info("映射记录按localId分组，共 {} 个组", localIdsGroups.size());
        log.debug("分组详情: {}", localIdsGroups.keySet());

        if (localIdsGroups.size() == 1) {
            log.info("进入情况2: 只有一个localId的映射记录");
            
            String key = localIdsGroups.keySet().iterator().next();
            log.info("保留的localId: {}", key);
            
            //删除jd_jdb
            List<String> deleteIds = hotelIds.stream()
                    .filter(item -> !StringUtils.equals(item, key)).collect(Collectors.toList());

            log.info("准备删除的酒店ID: {}", deleteIds);
            
            if (deleteIds.isEmpty()) {
                log.info("无需删除任何酒店");
                return 0;
            }

            String[] array = deleteIds.toArray(new String[0]);

            int i = jdJdbService.deleteJdJdbByIds(array);

            log.info("情况2删除酒店数据 {} 条，deleteIds: {}", i, deleteIds);
            return i;
        }

        log.info("进入情况3: 多个localId的映射记录，进行复杂分组处理");
        // 根据platId和interfacePlat分组
        Map<String, Map<String, List<ZhJdJdbMapping>>> groupedMappings = groupMappings(mappings);
        return handleMappingGroups(groupedMappings);
    }

    private Map<String, Map<String, List<ZhJdJdbMapping>>> groupMappings(List<ZhJdJdbMapping> mappings) {
        return mappings.stream()
                .collect(Collectors.groupingBy(ZhJdJdbMapping::getLocalId, Collectors.groupingBy(m -> {
                            // 预分配StringBuilder大小
                            return new StringBuilder(32)
                                    .append(m.getPlatId()).append('_').append(m.getInterfacePlat())
                                    .toString();
                        },
                        Collectors.toList())));
    }

    private int handleMappingGroups(Map<String, Map<String, List<ZhJdJdbMapping>>> groups) {
        final int[] deletedCount = { 0 };

        log.info("开始处理映射分组，分组数量: {}", MapUtils.size(groups));

        if (MapUtils.isEmpty(groups)) {
            log.warn("映射分组为空，返回0");
            return 0;
        }

        //比较groups里最内层map的size是否相同
        List<Integer> sizes = groups.values().stream()
                .map(Map::size)
                .collect(Collectors.toList());
        log.debug("各组的内层map大小: {}", sizes);
        
        boolean allSameSize = sizes.stream()
                .distinct()
                .count() == 1;

        log.info("所有组大小是否相同: {}", allSameSize);

        if (!allSameSize) {
            log.info("进入分支: 组大小不同，按大小进行处理");
            
            Map<Integer, List<Map<String, List<ZhJdJdbMapping>>>> groupBySize = groups.values().stream().collect(Collectors.groupingBy(Map::size));
            log.debug("按大小分组结果: {}", groupBySize.keySet());
            
            //拿到groupBySize里key的值最大的元素
            Map.Entry<Integer, List<Map<String, List<ZhJdJdbMapping>>>> groupBySizeMaxKey = groupBySize.entrySet().stream().max(Map.Entry.comparingByKey()).orElse(null);

            if (groupBySizeMaxKey == null) {
                log.error("无法找到最大大小的分组");
                return 0;
            }
            
            log.info("最大大小的分组: 大小={}, 数量={}", groupBySizeMaxKey.getKey(), groupBySizeMaxKey.getValue().size());

            //如果长度是1说明不需要进一步判断时间
            Boolean isTimeInvoke = groupBySizeMaxKey.getValue().size() == 1;
            log.info("是否需要时间判断: {}", !isTimeInvoke);
            
            if (!isTimeInvoke) {
                //需要进一步判断时间
                log.info("多个最大组，需要进一步判断时间");
                return timeInvoke(groups, deletedCount);
            }
            
            // 被拿来当标准同步的
            ZhJdJdbMapping lastGroupItem = groupBySizeMaxKey.getValue().get(0).values().iterator().next().get(0);
            log.info("选择的标准映射记录: localId={}, platId={}", lastGroupItem.getLocalId(), lastGroupItem.getPlatId());
            
            //拿到groupBySize里key的值不是最大的元素
            Map<Integer, List<Map<String, List<ZhJdJdbMapping>>>> groupBySizeNotMaxKey = groupBySize.entrySet()
                    .stream().filter(entry -> !entry.getKey().equals(groupBySizeMaxKey.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            List<ZhJdJdbMapping> batchDelete = Lists.newArrayList();
            groupBySizeNotMaxKey.values().forEach(list -> list.forEach(map -> map.values().forEach(list1 -> list1.forEach(batchDelete::add))));

            log.info("准备批量删除的映射记录数量: {}", batchDelete.size());

            if (!batchDelete.isEmpty()) {
                deletedCount[0] = deleteMappingGroupBatch(lastGroupItem, batchDelete);
                log.info("批量删除完成，删除数量: {}", deletedCount[0]);
            }
        } else {
            log.info("进入分支: 所有组大小相同，进行时间判断");
            // 优化后的时间处理分支
            return timeInvoke(groups, deletedCount);
        }
        return deletedCount[0];
    }

    public int timeInvoke(Map<String, Map<String, List<ZhJdJdbMapping>>> groups, int[] deletedCount) {
        log.info("开始时间判断处理");
        
        //将groups中的所有数据转化为Map<String, List<ZhJdJdbMapping>> innerMap
        Map<String, List<ZhJdJdbMapping>> innerMap = groups.values().stream()
                .flatMap(map -> map.values().stream())
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(ZhJdJdbMapping::getLocalId));
        
        log.debug("转换后的innerMap包含localId: {}", innerMap.keySet());
        
        Set<String> localIds = innerMap.values().stream()
                .flatMap(list -> list.stream().map(ZhJdJdbMapping::getLocalId))
                .collect(Collectors.toCollection(HashSet::new));

        log.info("查询酒店信息，localIds数量: {}", localIds.size());
        
        List<JdJdb> jdJdbs = jdJdbService.selectJdJdbByIds(localIds.toArray(new String[0]));
        if (CollectionUtils.isEmpty(jdJdbs)) {
            log.error("根据localIds查询酒店信息为空，localIds: {}", localIds);
            return 0;
        }
        
        log.info("查询到酒店信息数量: {}", jdJdbs.size());
        
        Map<String, Date> hotelSaveDates = getHotelSaveDates(jdJdbs);
        log.debug("酒店保存时间映射: {}", hotelSaveDates);

        String maxKey = getMaxKey(hotelSaveDates, jdJdbs);
        log.info("选择保留的酒店ID: {}", maxKey);

        ZhJdJdbMapping latestRecords = innerMap.get(maxKey).get(0);
        log.info("选择的最新映射记录: localId={}, platId={}", latestRecords.getLocalId(), latestRecords.getPlatId());
        
        List<ZhJdJdbMapping> batchDelete = innerMap.entrySet().stream()
                .filter(entry -> !entry.getKey().equals(maxKey))
                .flatMap(entry -> entry.getValue().stream())
                .collect(Collectors.toList());

        log.info("准备删除的映射记录数量: {}", batchDelete.size());

        if (!batchDelete.isEmpty()) {
            deletedCount[0] = deleteMappingGroupBatch(
                    latestRecords,
                    batchDelete);
            log.info("时间判断删除完成，删除数量: {}", deletedCount[0]);
        } else {
            log.info("无需删除任何记录");
        }
        
        return deletedCount[0];
    }

    /**
     * 获取最大值对应的键
     * 如果hotelSaveDates为空，则从jdJdbs中随机选择一个ID作为maxKey
     * 否则，找到hotelSaveDates中值最大的键作为maxKey，如果无法找到最大值，则从jdJdbs中取第一个ID作为maxKey
     *
     * @param hotelSaveDates 酒店保存日期的映射，键为ID，值为日期
     * @param jdJdbs JdJdb对象的列表
     * @return 最大值对应的键
     */
    @NotNull
    private String getMaxKey(Map<String, Date> hotelSaveDates, List<JdJdb> jdJdbs) {
        String maxKey;
        if (MapUtils.isEmpty(hotelSaveDates)) {
            // 如果为空，随机选择一个jdJdb的ID作为maxKey
            maxKey = jdJdbs.stream().findAny().map(JdJdb::getId).get();
        } else {
            maxKey = hotelSaveDates.entrySet().stream()
                    .max(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .orElse(jdJdbs.stream().findFirst().map(JdJdb::getId).get());
        }
        return maxKey;
    }

    /**
     * 获取酒店保存日期映射
     * 此方法用于处理一个JdJdb对象列表，过滤并收集每个对象的ID和保存日期，
     * 并解决可能的ID冲突问题
     *
     * @param jdJdbs JdJdb对象列表，用于提取保存日期信息
     * @return 返回一个映射，其中键为JdJdb对象的ID，值为保存日期
     */
    @NotNull
    private Map<String, Date> getHotelSaveDates(List<JdJdb> jdJdbs) {
        // 使用流处理jdJdbs列表，过滤掉savedate为null的记录，并收集到一个映射中
        // 映射的键为JdJdb的id，值为savedate
        // 如果遇到id重复的情况，保留savedate较大的记录
        Map<String, Date> hotelSaveDates = jdJdbs
                .stream()
                // 过滤掉savedate为null的记录
                .filter(jdJdb -> Objects.nonNull(jdJdb.getSavedate()))
                .collect(Collectors.toMap(
                        JdJdb::getId,
                        JdJdb::getSavedate,
                        // 如果有重复的key，保留value大的一个
                        (existing, replacement) -> existing.after(replacement) ? existing : replacement,
                        HashMap::new));
        return hotelSaveDates;
    }

    // 批量删除方法,重新映射
    @Transactional(rollbackFor = Exception.class)
    public int deleteMappingGroupBatch(ZhJdJdbMapping mappingLast, List<ZhJdJdbMapping> mappings) {
        log.info("开始批量删除映射组，标准映射localId: {}, 待处理映射数量: {}", 
                mappingLast.getLocalId(), CollectionUtils.size(mappings));
        
        if (CollectionUtils.isEmpty(mappings) || Objects.isNull(mappingLast)) {
            log.warn("映射数据为空，无需处理");
            return 0;
        }

        String[] idsArray = mappings.stream()
                .map(ZhJdJdbMapping::getLocalId)
                .distinct()
                .toArray(String[]::new);

        log.info("准备删除的酒店ID数组: {}", Arrays.toString(idsArray));

        try {
            // 事务1：执行映射
            log.info("开始执行映射处理");
            this.mappingTransactional(mappingLast, mappings);
            log.info("映射处理完成");

            // 事务2：删除记录
            log.info("开始删除酒店记录，数量: {}", idsArray.length);
            int result = jdJdbService.deleteJdJdbByIds(
                    Stream.of(idsArray).toArray(String[]::new));
            log.info("删除酒店记录完成，实际删除数量: {}", result);
            return result;
        } catch (Exception e) {
            log.error("事务操作失败，标准映射localId: {}, 待删除酒店ID: {}, 错误信息: {}", 
                    mappingLast.getLocalId(), 
                    Arrays.stream(idsArray).collect(Collectors.joining(",")),
                    e.getMessage(), e);
            throw new RuntimeException("事务执行失败", e); // 触发回滚
        }
    }

    // 事务性映射方法
    public void mappingTransactional(ZhJdJdbMapping zhJdJdbMapping, List<ZhJdJdbMapping> mappings) {
        log.info("开始映射事务处理，标准映射localId: {}, 处理映射数量: {}", 
                zhJdJdbMapping.getLocalId(), mappings.size());
        
        for (int i = 0; i < mappings.size(); i++) {
            ZhJdJdbMapping mapping = mappings.get(i);
            log.debug("处理第 {}/{} 个映射: localId={}, platId={}, interfacePlat={}", 
                    i + 1, mappings.size(), mapping.getLocalId(), mapping.getPlatId(), mapping.getInterfacePlat());
            
            try {
                JdJdb jdJdb = jdJdbService.selectJdJdbById(mapping.getLocalId());
                if (jdJdb == null) {
                    log.error("本地酒店不存在，localId: {}", mapping.getLocalId());
                    continue;
                }
                
                log.debug("查询到本地酒店: id={}, jdmc={}", jdJdb.getId(), jdJdb.getJdmc());
                
                String service = PlatEnum.getplatEnum(mapping.getInterfacePlat() + "").getService();
                log.debug("获取平台服务: interfacePlat={}, service={}", mapping.getInterfacePlat(), service);
                
                IHotelGnBaseService hotelGnBaseService = SpringUtils.getBean(service);
                HotelGnBase hotelGnBase = hotelGnBaseService.selectHotelGnBaseById(mapping.getPlatId());
                boolean hotelInfoNonNull = Objects.nonNull(hotelGnBase);
                
                if (hotelInfoNonNull) {
                    log.debug("找到平台酒店: id={}, name={}, 设置状态为8", hotelGnBase.getId(), hotelGnBase.getName());
                    hotelGnBase.setStatus(8);
                    hotelGnBaseService.updateHotelGnBase(hotelGnBase);
                    log.debug("平台酒店状态更新完成");
                } else {
                    log.error("平台酒店不存在，platId: {}, interfacePlat: {}", mapping.getPlatId(), mapping.getInterfacePlat());
                }
                
                // 添加映射关系
                ZhJdJdbMapping temp = new ZhJdJdbMapping();
                temp.setLocalId(zhJdJdbMapping.getLocalId());
                temp.setInterfacePlat(mapping.getInterfacePlat());
                temp.setPlatId(mapping.getPlatId());
                temp.setJdName(jdJdb.getJdmc());
                temp.setPlatJdName(hotelInfoNonNull ? hotelGnBase.getName() : null);
                temp.setStatus(0);
                temp.setIsGnGj(1);
                temp.setSaveDate(new Date());
                
                log.debug("准备插入新映射: localId={}, platId={}, interfacePlat={}", 
                        temp.getLocalId(), temp.getPlatId(), temp.getInterfacePlat());
                
                //无需判重,之前可能就有重复的要保留
                zhJdJdbMappingService.insert(temp);
                log.info("映射插入完成");
                
            } catch (Exception e) {
                log.error("处理第 {} 个映射失败: localId={}, platId={}, error={}", 
                        i + 1, mapping.getLocalId(), mapping.getPlatId(), e.getMessage(), e);
                throw e; // 重新抛出异常以触发事务回滚
            }
        }
        
        log.info("映射事务处理完成");
    }

}
