<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.supplier.common.gn.mapper.HotelGnGBMapper">

    <insert id="addBatch" >
        INSERT INTO `hotel_gn_gb`
            (`id`, `gb_id`, `gb_name`, `gb_name_en`, `type`, `parent_gb_id`, `platform_id`, `mapping_status`
            , `remark`, `create_time`, `update_time`, `create_by`, `update_by`)
        values 
        <foreach collection="list" item="info" separator=",">
            (
            #{info.id},
            #{info.gbId},
            #{info.gbName},
            #{info.gbNameEn},
            #{info.type},
            #{info.parentGbId},
            #{info.platformId},
            #{info.mappingStatus},
            #{info.remark},
            #{info.createTime},
            #{info.updateTime},
            #{info.createBy},
            #{info.updateBy}
            )
        </foreach>
    </insert>

    <select id="selectByPlatformIdAndType" resultType="com.ltgj.supplier.common.gn.domain.HotelGnGB">
        select * from hotel_gn_gb where platform_id = #{platformId} and type = #{type}
        order by create_time asc
    </select>

    <select id="selectByPlatformIdAndTypeAndParentId" resultType="com.ltgj.supplier.common.gn.domain.HotelGnGB">
        select * from hotel_gn_gb where platform_id = #{platformId} and type = #{type} and parent_gb_id = #{parentGbId}
        order by create_time asc
    </select>

    <select id="selectByPlatformIdAndTypeAndGBName" resultType="com.ltgj.supplier.common.gn.domain.HotelGnGB">
        select  * from hotel_gn_gb where platform_id = #{platformId}
                                            and type = #{type}
                                             and gb_name = #{gbName}
        order by create_time desc limit 1
    </select>

    <select id="selectByPlatformIdAndTypeAndGBId" resultType="com.ltgj.supplier.common.gn.domain.HotelGnGB">
        select  * from hotel_gn_gb where platform_id = #{platformId} and type = #{type} and gb_id = #{gbId}
        order by create_time desc limit 1
    </select>

    <update id="updateMappingStatus" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGB">
        update hotel_gn_gb set mapping_status = #{mappingStatus}, update_by = #{updateBy}, update_time = #{updateTime}
            where platform_id = #{platformId} and type = #{type} and gb_id = #{gbId}
    </update>

    <select id="selectMaxGbId" resultType="string">
        select max(gb_id) from hotel_gn_gb where platform_id = #{platformId} and type = #{type}
    </select>

    <select id="selectAllByPlatformId" resultType="com.ltgj.supplier.common.gn.domain.HotelGnGB">
        select * from hotel_gn_gb where platform_id = #{platformId} and is_delete = 0
    </select>

    <sql id="sqlByPlatformIdAndTypeAndParam">
        where platform_id = #{platformId} and type = #{type}
        <if test="gbId != null and gbId != ''">
            and gb_id = #{gbId}
        </if>
        <if test="gbName != null and gbName != ''">
            and gb_name like concat('%', #{gbName}, '%')
        </if>

    </sql>

    <select id="countByPlatformIdAndTypeAndParam" resultType="int">
        select count(1) from hotel_gn_gb <include refid="sqlByPlatformIdAndTypeAndParam" />
    </select>

    <select id="selectByPlatformIdAndTypeAndParam" resultType="com.ltgj.supplier.common.gn.domain.HotelGnGB">
        select * from hotel_gn_gb <include refid="sqlByPlatformIdAndTypeAndParam" />
        order by create_time asc limit #{start}, #{pageSize}
    </select>
</mapper>
