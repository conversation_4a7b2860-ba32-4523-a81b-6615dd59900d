package com.ltgj.ivw.service;


import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class JdJdbCanalListenerServiceTest {

    @Autowired
    private CanalListenerService canalListenerService;

    @Test
    public void dealCanalMsgTest() {
        String msg = "{\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": \"11928423\",\n" +
                "            \"interface_plat\": \"2000054\",\n" +
                "            \"local_id\": \"20000541004384\",\n" +
                "            \"plat_id\": \"1004384\",\n" +
                "            \"is_gn_gj\": \"1\",\n" +
                "            \"status\": \"0\",\n" +
                "            \"jd_name\": \"维也纳酒店（上海虹桥国家会展中心店）\",\n" +
                "            \"savedate\": \"2025-06-26 17:22:21\",\n" +
                "            \"plat_jd_name\": \"维也纳酒店（上海虹桥国家会展中心店）\",\n" +
                "            \"mapping_score\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"database\": \"ivw_hotel\",\n" +
                "    \"es\": 1750929741000,\n" +
                "    \"gtid\": \"\",\n" +
                "    \"id\": 1344324,\n" +
                "    \"isDdl\": false,\n" +
                "    \"mysqlType\": {\n" +
                "        \"id\": \"bigint(20) unsigned\",\n" +
                "        \"interface_plat\": \"bigint(20)\",\n" +
                "        \"local_id\": \"varchar(40)\",\n" +
                "        \"plat_id\": \"varchar(20)\",\n" +
                "        \"is_gn_gj\": \"tinyint(4)\",\n" +
                "        \"status\": \"tinyint(3) unsigned\",\n" +
                "        \"jd_name\": \"varchar(128)\",\n" +
                "        \"savedate\": \"datetime\",\n" +
                "        \"plat_jd_name\": \"varchar(128)\",\n" +
                "        \"mapping_score\": \"varchar(20)\"\n" +
                "    },\n" +
                "    \"old\": null,\n" +
                "    \"pkNames\": [\n" +
                "        \"id\"\n" +
                "    ],\n" +
                "    \"sql\": \"\",\n" +
                "    \"sqlType\": {\n" +
                "        \"id\": -5,\n" +
                "        \"interface_plat\": -5,\n" +
                "        \"local_id\": 12,\n" +
                "        \"plat_id\": 12,\n" +
                "        \"is_gn_gj\": -6,\n" +
                "        \"status\": -6,\n" +
                "        \"jd_name\": 12,\n" +
                "        \"savedate\": 93,\n" +
                "        \"plat_jd_name\": 12,\n" +
                "        \"mapping_score\": 12\n" +
                "    },\n" +
                "    \"table\": \"zh_jd_jdb_mapping\",\n" +
                "    \"ts\": 1750929741918,\n" +
                "    \"type\": \"INSERT\"\n" +
                "}";
        canalListenerService.dealZhJdJdbMappingCanalMsg(msg);
    }

}
