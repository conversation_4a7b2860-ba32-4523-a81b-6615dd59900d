package com.ltgj.ivw.service;

import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import com.ltgj.ivw.domain.SupplierElongCityMapping;

/**
 * 供应商与艺龙城市映射关系Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ISupplierElongCityMappingService 
{
    /**
     * 查询供应商与艺龙城市映射关系
     * 
     * @param id 供应商与艺龙城市映射关系主键
     * @return 供应商与艺龙城市映射关系
     */
    public SupplierElongCityMapping selectSupplierElongCityMappingById(Long id);

    /**
     * 查询供应商与艺龙城市映射关系列表
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 供应商与艺龙城市映射关系集合
     */
    public List<SupplierElongCityMapping> selectSupplierElongCityMappingList(SupplierElongCityMapping supplierElongCityMapping);

    /**
     * 根据供应商编码和供应商城市编码查询映射关系
     * 
     * @param supplierCode 供应商编码
     * @param supplierCityCode 供应商城市编码
     * @return 供应商与艺龙城市映射关系
     */
    public SupplierElongCityMapping selectBySupplierAndCityCode(String supplierCode, String supplierCityCode);

    /**
     * 根据供应商编码和艺龙城市编码查询映射关系
     * 
     * @param supplierCode 供应商编码
     * @param elongCityCode 艺龙城市编码
     * @return 供应商与艺龙城市映射关系列表
     */
    public List<SupplierElongCityMapping> selectBySupplierAndElongCityCode(String supplierCode, String elongCityCode);

    /**
     * 新增供应商与艺龙城市映射关系
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 结果
     */
    public int insertSupplierElongCityMapping(SupplierElongCityMapping supplierElongCityMapping);

    /**
     * 修改供应商与艺龙城市映射关系
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 结果
     */
    public int updateSupplierElongCityMapping(SupplierElongCityMapping supplierElongCityMapping);

    /**
     * 批量删除供应商与艺龙城市映射关系
     * 
     * @param ids 需要删除的供应商与艺龙城市映射关系主键集合
     * @return 结果
     */
    public int deleteSupplierElongCityMappingByIds(Long[] ids);

    /**
     * 删除供应商与艺龙城市映射关系信息
     * 
     * @param id 供应商与艺龙城市映射关系主键
     * @return 结果
     */
    public int deleteSupplierElongCityMappingById(Long id);
    
    /**
     * 导入Excel数据
     * 
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param operName 操作人
     * @return 导入结果
     * @throws Exception 异常
     */
    public String importExcel(MultipartFile file, boolean updateSupport, String operName) throws Exception;
} 