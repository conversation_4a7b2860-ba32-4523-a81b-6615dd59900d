package com.ltgj.ivw.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FormatConfig {
    @Value("${format.hotelNameReg:[\\\\\\/@’‘’“”'()（）.。&\\#\\|\\-\\★\\s...]}")
    public String nameReg;
    @Value("${format.phoneReg:[^0-9]}")
    public String phoneReg;
    @Value("${format.addressReg:[\\\\/@|]}")
    public String addressReg;
}
