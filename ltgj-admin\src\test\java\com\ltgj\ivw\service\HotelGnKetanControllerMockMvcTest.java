package com.ltgj.ivw.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ltgj.ivw.controller.HotelGnKetanController;
import com.ltgj.sdk.cozyTime.CozyTimeSdkApi;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20CitiesResponse;
import com.ltgj.supplier.cozyTime.HotelCozyTimeSupplierService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static com.ltgj.ivw.service.HotelGnKetanControllerTest.idempotent;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 科坦酒店信息控制器MockMvc测试类
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
@AutoConfigureMockMvc
class HotelGnKetanControllerMockMvcTest {

    private MockMvc mockMvc;

    @Mock
    private HotelCozyTimeSupplierService hotelCozyTimeSupplierService;

    @Mock
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Mock
    private CozyTimeSdkApi cozyTimeSdkApi;

    @InjectMocks
    private HotelGnKetanController controller;

    private ObjectMapper objectMapper;

    private static final String mappingStrategy = "1";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testTestConnection() throws Exception {
        // 准备模拟数据
        CozyTimeHotelContentApi20CitiesResponse mockResponse = new CozyTimeHotelContentApi20CitiesResponse();
        when(cozyTimeSdkApi.getCityList(anyString())).thenReturn(mockResponse);

        // 执行测试并验证结果
        mockMvc.perform(get("/ivw/cozyTime/test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("科坦接口连接成功"));
    }

    @Test
    void testTestConnectionFailure() throws Exception {
        // 模拟异常
        when(cozyTimeSdkApi.getCityList(anyString())).thenThrow(new RuntimeException("连接失败"));

        // 执行测试并验证结果
        mockMvc.perform(get("/ivw/cozyTime/test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("科坦接口连接失败: 连接失败"));
    }

    @Test
    void testSyncCityData() throws Exception {
        // 模拟方法执行
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(threadPoolTaskExecutor).execute(any(Runnable.class));
        
        when(hotelCozyTimeSupplierService.syncCityData("1", mappingStrategy)).thenReturn("同步成功");

        // 执行测试并验证结果
        mockMvc.perform(post("/ivw/cozyTime/syncCityData"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("科坦城市数据同步任务已提交，正在后台执行"));
    }

    @Test
    void testSyncCityDataSync() throws Exception {
        // 准备模拟数据
        when(hotelCozyTimeSupplierService.syncCityData("1", mappingStrategy)).thenReturn("同步成功");

        // 执行测试并验证结果
        mockMvc.perform(post("/ivw/cozyTime/syncCityDataSync"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andDo(result -> System.out.println("Response: " + result.getResponse().getContentAsString()))
                // 验证msg字段而不是data字段
                .andExpect(jsonPath("$.msg").value("同步成功"));
    }

    @Test
    void testProcessCityData() throws Exception {
        // 准备模拟数据
        CozyTimeHotelContentApi20CitiesResponse citiesResponse = new CozyTimeHotelContentApi20CitiesResponse();
        when(hotelCozyTimeSupplierService.processCityData(any(CozyTimeHotelContentApi20CitiesResponse.class), idempotent)).thenReturn(10);

        // 执行测试并验证结果
        mockMvc.perform(post("/ivw/cozyTime/processCityData")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(citiesResponse)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("处理成功，共处理城市数据: 10条"));
    }

    @Test
    void testGetCityData() throws Exception {
        // 准备模拟数据
        CozyTimeHotelContentApi20CitiesResponse mockResponse = new CozyTimeHotelContentApi20CitiesResponse();
        when(cozyTimeSdkApi.getCityList(anyString())).thenReturn(mockResponse);

        // 执行测试并验证结果
        mockMvc.perform(get("/ivw/cozyTime/getCityData")
                .param("countryCode", "CN"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("获取科坦城市数据成功"));
    }

    @Test
    void testProcessCityMapping() throws Exception {
        // 准备模拟数据
        when(hotelCozyTimeSupplierService.processCityMapping(idempotent)).thenReturn(15);

        // 执行测试并验证结果
        mockMvc.perform(post("/ivw/cozyTime/processCityMapping"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("处理成功，共创建本地映射: 15条"));
    }

    @Test
    void testProcessCityMappingWithStrategy() throws Exception {
        // 准备模拟数据
        when(hotelCozyTimeSupplierService.processCityMapping(any(String.class),idempotent)).thenReturn(15);

        // 执行测试并验证结果
        mockMvc.perform(post("/ivw/cozyTime/processCityMappingWithStrategy")
                .param("mappingStrategy", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("处理成功，映射策略：艺龙映射，共创建本地映射: 15条"));
    }

    @Test
    void testProcessElongMapping() throws Exception {
        // 准备模拟数据
        when(hotelCozyTimeSupplierService.processElongCityMapping(idempotent)).thenReturn(20);

        // 执行测试并验证结果
        mockMvc.perform(post("/ivw/cozyTime/processElongMapping"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("处理成功，共创建艺龙映射: 20条"));
    }
} 