package com.ltgj.ivw.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.ivw.controller.HotelInfoMeituanController;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelInfoHsjl;
import com.ltgj.ivw.domain.HotelInfoMeituan;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.utils.ConstantList;
import com.ltgj.ivw.utils.TXTUtil;
import com.ltgj.ivw.utils.hotelApi.*;
import com.ltgj.quartz.task.HotelIncrementTask;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 酒店API单元测试
 *
 * <AUTHOR> SYH
 * @date 2024-10-16
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class HotelApiServiceImplTest {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private HotelIncrementTask hotelIncrementTask;

    @Resource
    private HotelInfoMeituanController hotelInfoMeituanController;

    private static ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void updateMeituanAll() {
        try {
            hotelInfoMeituanController.updateMeituanAll();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void queryHotelInfoHSJL() {
        List<Long> hotelIds = new ArrayList<>();
        hotelIds.add(561794L);
        String s = HsjlApi.queryHotelInfo(hotelIds, null, null);
        System.out.println(JSONObject.parseObject(s));
        Object hotelInfos = JSONObject.parseObject(s).getJSONObject("bussinessResponse").getJSONArray("hotelInfos").get(0);
        JSONObject hotelInfo = JSONObject.parseObject(hotelInfos.toString());
        System.out.println(hotelInfo.getInteger("hotelStar"));
    }

    @Test
    public void hotelIncrementTask() {
//        hotelIncrementTask.handleHSJLIncremen();
//        ChailvgjApi.getHotelIncr("2024-12-10 15:00:00", 0);
//        System.out.println(hotelIncrementTask.handleQTIncremen());
        List<Long> hotelIds = Arrays.asList(
                2218938L
        );
        String infoText = HsjlApi.queryHotelInfo(hotelIds, null, null);
        JSONObject info;
        try {
            info = JSONObject.parseObject(infoText);
        }catch (Exception e) {
            return;
        }
        if(!info.getString("returnCode").equals("000")) {
            return;
        }
        JSONArray hotelInfos = info.getJSONObject("bussinessResponse").getJSONArray("hotelInfos");
        hotelInfos.stream().forEach(element -> {
            JSONObject hotelInfo = (JSONObject) element;
            System.out.println(hotelInfo.getLong("hotelId"));
        });

    }

    @Test
    public void queryHotelInfoMT() {
//        List<Long> hotelIds = new ArrayList<>();
        List<Long> hotelIds = Arrays.asList(
                1188812518L, 1188902274L, 1189417102L, 1189472797L, 1189813388L,
                118986365L, 1189991840L, 1190048336L, 119016373L, 1192734236L,
                1193158929L, 1194002320L, 1194430709L, 1195671087L, 119587439L,
                1196985657L, 1197006602L, 1197595829L, 119984734L, 1200024L
        );
//        hotelIds.add(100058032L);
//        hotelIds.add(1004755596L);
        String infoText = MeituanApi.hotelDetail(hotelIds);
//        System.out.println(JSONObject.parseObject(s));
        JSONObject jsonObject = JSONObject.parseObject(infoText);
        if (jsonObject.getInteger("code") == 0){
            JSONArray infos = jsonObject.getJSONObject("result").getJSONArray("hotelContents");
            if(infos == null){
                return;
            }
            RLock lock = redissonClient.getLock("writeMTTXTLock");
            System.out.println("美团读取酒店详情size:"+ infos.size());
            for(int k = 0;k<infos.size();k++){
                JSONObject info = infos.getJSONObject(k);
                HotelInfoMeituan meituan = getHotelInfoMeituan(info);
                JSONObject mtPolicies = info.getJSONObject("policy");
                meituan.setPolicyInfo(mtPolicies.toString());
                JSONArray mtFacilities = info.getJSONArray("facilities");
                meituan.setFacilitiesInfo(mtFacilities.toString());

                try {
                    String json = objectMapper.writeValueAsString(meituan);
                    while(true) {
                        boolean isLock = lock.tryLock(1, 1, TimeUnit.SECONDS);
                        System.out.println("美团读取酒店详情lock:{}"+ isLock);
                        if (isLock) {
                            TXTUtil.writeMTTXT(JSONObject.parse(json).toString(), "D:/tianjiu/",
                                    "mtDetail1.txt");
                        }
                        break;
                    }
                } catch (Exception e) {
                    System.out.println("获取美团详情lock异常: "+e);
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }

                HotelUpdateStatus.mtDetailCountGet.incrementAndGet();
                System.out.println("write------");
            }
        } else if(jsonObject.getInteger("code") == 1200) {
//                    i++;
//                    if(i < maxReqCount){
//                        new getdetailMT(ids, i);
//                    }
            System.out.println("获取美团详情CODE=1200");
        } else{
            System.out.println("获取美团详情进ELSE了: {}");
        }
//        Object hotelInfos = JSONObject.parseObject(s).getJSONObject("bussinessResponse").getJSONArray("hotelInfos").get(0);
//        JSONObject hotelInfo = JSONObject.parseObject(hotelInfos.toString());
//        System.out.println(hotelInfo.getInteger("hotelStar"));


    }

    public static void main(String[] args) {


//        1622324,1622333,1622344,1622375,1622376,1622393,1622418,1622472,1622527,1622576
        List<Long> ids = Arrays.asList(
                1622324L,1622333L,1622344L,1622375L,1622376L,1622393L,1622418L,1622472L,1622527L,1622576L
        );
        List<List<Long>> idsList = ListUtils.partition(ids, 4);
        HotelInfoHsjl hotelInfoHsjl1 = new HotelInfoHsjl();
        idsList.stream().forEach(ids0 -> {
        String infoText = "{\"bussinessResponse\":{\"hotelInfos\":[{\"address\":\"马山口镇工贸路东段元正饭店旁\",\"allowsPet\":\"0\",\"appearancePicUrl\":\"http://dimg04.c-ctrip.com/images//0AD3t120008wpb7a28AA3_R_550_412_R5.jpg\",\"applicableGuest\":\"3\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"neixiang\",\"cityName\":\"内乡\",\"countryCode\":\"CN\",\"fitmentDate\":\"2020-06-01\",\"hotelCategory\":\"486\",\"hotelEngName\":\"Tianrunge Exquisite Hotel\",\"hotelId\":1622324,\"hotelIntroduce\":\"干净卫生，温馨舒适。 对于想要捕捉内乡城市风采的旅客来说，天润阁精致酒店是一个理想的选择。</br>酒店提供的休闲设施，旨在为旅客营造多姿多彩、奢华完美的住宿体验。\",\"hotelName\":\"天润阁精致酒店\",\"hotelPolicys\":[],\"hotelStar\":66,\"hotelSubCategory\":\"495\",\"isChargePark\":\"0\",\"isPark\":\"0\",\"latitude\":33.205733,\"longitude\":112.020324,\"petText\":\"不可携带宠物。\",\"praciceDate\":\"2020-06-01 00:00:00\",\"province\":\"HEN\",\"provinceName\":\"河南\",\"roomInfos\":[{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"BIG\",\"bedTypeName\":\"双人床\",\"bedTypecd\":\"P000000\",\"bedWidth\":\"1.5\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"A000000\",\"bedCount\":1,\"bedWidth\":\"1.5\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":2,\"roomAcreage\":\"10-12\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"双人床\",\"bedNum\":\"1\",\"bedTypeCode\":\"P000000\",\"bedWidth\":\"1.5\"}]}],\"roomName\":\"特惠单间1\"}]},\"roomFloor\":\"1-3\",\"roomId\":7798478,\"roomName\":\"特惠单间\",\"windowDetail\":0,\"windowType\":0,\"wiredBroadnet\":0,\"wirelessBroadnet\":0},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"DOUBLE\",\"bedTypeName\":\"双人床\",\"bedTypecd\":\"P000000\",\"bedWidth\":\"1.5\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"A000000\",\"bedCount\":2,\"bedWidth\":\"1.5\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":2,\"roomAcreage\":\"15-25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"双人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"P000000\",\"bedWidth\":\"1.5\"}]}],\"roomName\":\"标准间1\"}]},\"roomFloor\":\"1-3\",\"roomId\":7798479,\"roomName\":\"标准间\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":0},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"BIG\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.8\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"1000000\",\"bedCount\":1,\"bedWidth\":\"1.8\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":2,\"roomAcreage\":\"20-35\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.8\"}]}],\"roomName\":\"豪华套房1\"}]},\"roomFloor\":\"1-5\",\"roomId\":7798480,\"roomName\":\"豪华套房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":0},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"BIG\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.8\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"1000000\",\"bedCount\":1,\"bedWidth\":\"1.8\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":2,\"roomAcreage\":\"15-25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.8\"}]}],\"roomName\":\"豪华大床房1\"}]},\"roomFloor\":\"1-3\",\"roomId\":7798481,\"roomName\":\"豪华大床房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":0}],\"roomNum\":\"15\",\"telephone\":\"+86-377-65256789\"},{\"address\":\"荔城南大道万达广场万达广场 (莆田店)\",\"allowsPet\":\"1\",\"appearancePicUrl\":\"http://dimg04.c-ctrip.com/images//0202q120008jrzioi5BC7_R_550_412_R5.jpg\",\"applicableGuest\":\"3\",\"business\":\"115974913\",\"businessName\":\"城厢万达/南山广华寺\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"PUT\",\"cityName\":\"莆田\",\"countryCode\":\"CN\",\"distinct\":\"CHENGXIANGQU\",\"distinctName\":\"城厢区\",\"hotelId\":1622333,\"hotelIntroduce\":\"旅游是我的生命,只要走到了火车站,飞机场,我就会感受到莫名的兴奋.延误\\\\航班取消,任何旅途中的困惑都不会困扰我,走到哪里吃到哪里睡到哪里,我都觉得是一种幸福. 我喜欢舒适、干净、低调奢华的地方,每天睡到个大中午,然后博物馆、电影院、美食,购物,这就是我的旅游方式. 我精心布置了我的花园,每一盆花都是我亲手种下去的,每一颗石子也是我自己铺下去的,希望你也能喜欢.\",\"hotelName\":\"澜沁MaggieGrace公寓(荔城南大道分店)\",\"hotelPolicys\":[],\"hotelStar\":79,\"isChargePark\":\"0\",\"isPark\":\"1\",\"latitude\":25.424742,\"longitude\":119.004015,\"petText\":\"允许携带宠物，会收取额外费用。\",\"praciceDate\":\"2018-01-01 00:00:00\",\"province\":\"FJN\",\"provinceName\":\"福建\",\"roomInfos\":[{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"BIG\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"1000000\",\"bedCount\":1,\"bedWidth\":\"1.80\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":2,\"maxPerson\":2,\"roomAcreage\":\"70\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.80\"}]}],\"roomName\":\"精致一室一厅套房1\"}]},\"roomEngName\":\"Boutique Suite - 1-Room (1 living room)\",\"roomFloor\":\"1-20\",\"roomId\":7798504,\"roomName\":\"精致一室一厅套房\",\"windowDetail\":100,\"windowType\":3,\"wiredBroadnet\":0,\"wirelessBroadnet\":2}],\"roomNum\":\"0\",\"telephone\":\"+86-10-56320606-1005790\"},{\"address\":\"合村生仙里逸道村居精品民宿\",\"allowsPet\":\"1\",\"appearancePicUrl\":\"http://fcimage.fangcang.com/images/hotels/344/1622344/202309191695104953258.jpg\",\"applicableGuest\":\"3\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"TOU\",\"cityName\":\"桐庐\",\"countryCode\":\"CN\",\"hotelId\":1622344,\"hotelIntroduce\":\"我们精心准备，希望给您家的温暖与便利。\",\"hotelName\":\"村居民宿(合村生仙里逸道分店)\",\"hotelPolicys\":[],\"hotelStar\":79,\"isChargePark\":\"0\",\"isPark\":\"1\",\"latitude\":29.94784,\"longitude\":119.332335,\"petText\":\"允许携带宠物，会收取额外费用。\",\"province\":\"ZJG\",\"provinceName\":\"浙江\",\"roomInfos\":[{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"榻榻米\",\"bedTypecd\":\"D000000\",\"bedWidth\":\"15.00\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"榻榻米\",\"bedTypecd\":\"D000000\",\"bedWidth\":\"15.00\"},{\"bedNum\":\"2\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.20\"},{\"bedNum\":\"2\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.20\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"A000000\",\"bedCount\":10,\"bedWidth\":\"1.80\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":1,\"maxPerson\":16,\"roomAcreage\":\"345\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedName\":\"榻榻米\",\"bedNum\":\"1\",\"bedTypeCode\":\"D000000\",\"bedWidth\":\"15.00\"},{\"bedName\":\"榻榻米\",\"bedNum\":\"1\",\"bedTypeCode\":\"D000000\",\"bedWidth\":\"15.00\"},{\"bedName\":\"单人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.20\"},{\"bedName\":\"单人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.20\"}]}],\"roomName\":\"简约八室一厅套房1\"}]},\"roomEngName\":\"Simple Suite (1 living room)\",\"roomId\":7798552,\"roomName\":\"简约八室一厅套房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2}],\"roomNum\":\"0\",\"telephone\":\"+86-10-56320606-17135293\"},{\"address\":\"穿城路96号\",\"allowsPet\":\"0\",\"appearancePicUrl\":\"http://dimg04.c-ctrip.com/images//0200e120009dcuz8tFD02_R_550_412_R5.jpg\",\"applicableGuest\":\"4\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"lushui\",\"cityName\":\"泸水\",\"countryCode\":\"CN\",\"fitmentDate\":\"2022-01-01\",\"hotelCategory\":\"1302\",\"hotelEngName\":\"Shunda Boutique Hotel Lishui\",\"hotelId\":1622375,\"hotelIntroduce\":\"酒店位于穿城路96号(江东桥头），地处繁华。<br>酒店以舒适的环境，优质的服务，合理的价位，赢得您的信赖。真诚欢迎朋友们光临。\",\"hotelName\":\"泸水顺达精品酒店\",\"hotelPolicys\":[],\"hotelStar\":66,\"hotelSubCategory\":\"499\",\"isChargePark\":\"0\",\"isPark\":\"1\",\"latitude\":25.854002,\"longitude\":98.863156,\"petText\":\"不可携带宠物。\",\"praciceDate\":\"2022-01-01 00:00:00\",\"province\":\"YNN\",\"provinceName\":\"云南\",\"roomInfos\":[{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.2\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"双人床\",\"bedTypecd\":\"P000000\",\"bedWidth\":\"1.5\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"2000000\",\"bedCount\":2,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":1,\"maxPerson\":2,\"roomAcreage\":\"20-25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"双人床\",\"bedNum\":\"1\",\"bedTypeCode\":\"P000000\",\"bedWidth\":\"1.5\"},{\"bedName\":\"单人床\",\"bedNum\":\"1\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"家庭亲子房1\"}]},\"roomFloor\":\"2\",\"roomId\":7798655,\"roomName\":\"家庭亲子房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":0},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"BIG\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.8\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"1000000\",\"bedCount\":1,\"bedWidth\":\"1.8\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":1,\"maxPerson\":2,\"roomAcreage\":\"20-25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.8\"}]}],\"roomName\":\"精品大床房1\"}]},\"roomFloor\":\"2\",\"roomId\":7798656,\"roomName\":\"精品大床房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":0},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"DOUBLE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.2\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"A000000\",\"bedCount\":2,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":1,\"maxPerson\":2,\"roomAcreage\":\"20-25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"单人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"精品双床房1\"}]},\"roomFloor\":\"2\",\"roomId\":7798657,\"roomName\":\"精品双床房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":0}],\"roomNum\":\"21\",\"telephone\":\"+86-886-6668166;+86-***********\"},{\"address\":\"龙山八路翡翠山国际公寓1栋华美达酒店对面\",\"allowsPet\":\"1\",\"appearancePicUrl\":\"http://dimg04.c-ctrip.com/images//0225c12000a2xdtlbBE09_R_550_412_R5.jpg\",\"applicableGuest\":\"3\",\"business\":\"417\",\"businessName\":\"淡水/火车南站地区\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"HUI\",\"cityName\":\"惠州\",\"countryCode\":\"CN\",\"distinct\":\"HUIHYXD\",\"distinctName\":\"惠阳区\",\"hotelId\":1622376,\"hotelIntroduce\":\"温馨 卫生  舒服 安全\",\"hotelName\":\"晔叔公寓(龙山八路分店)\",\"hotelPolicys\":[],\"hotelStar\":79,\"isChargePark\":\"0\",\"isPark\":\"1\",\"latitude\":22.75664,\"longitude\":114.487247,\"petText\":\"允许携带宠物，会收取额外费用。\",\"province\":\"GDN\",\"provinceName\":\"广东\",\"roomInfos\":[{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.20\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"2000000\",\"bedCount\":2,\"bedWidth\":\"1.80\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":-100,\"maxPerson\":3,\"roomAcreage\":\"74\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.8\"}]}],\"roomName\":\"湖景二室一厅套房1\"},{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"单人床\",\"bedNum\":\"1\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"湖景二室一厅套房2\"}]},\"roomId\":7798658,\"roomName\":\"湖景二室一厅套房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":0}],\"roomNum\":\"0\",\"telephone\":\"+86-10-56320606-7003130\"},{\"address\":\"雄楚大道万科锦程光谷万科中心\",\"allowsPet\":\"1\",\"appearancePicUrl\":\"http://fcimage.fangcang.com/images/hotels/393/1622393/202309231695459393278.jpg\",\"applicableGuest\":\"3\",\"business\":\"943\",\"businessName\":\"光谷广场/武昌高校区\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"WUH\",\"cityName\":\"武汉\",\"countryCode\":\"CN\",\"distinct\":\"WUHHSQD\",\"distinctName\":\"洪山区\",\"hotelId\":1622393,\"hotelIntroduce\":\"【2019年度  中国好民宿获得者】\",\"hotelName\":\"宿逅suhome武昌一店公寓(雄楚大道4号分店)\",\"hotelPolicys\":[],\"hotelStar\":79,\"isChargePark\":\"0\",\"isPark\":\"1\",\"latitude\":30.502592,\"longitude\":114.413226,\"petText\":\"允许携带宠物，会收取额外费用。\",\"province\":\"HBI\",\"provinceName\":\"湖北\",\"roomInfos\":[{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedNum\":\"2\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"双人床\",\"bedTypecd\":\"P000000\",\"bedWidth\":\"1.50\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"3000000\",\"bedCount\":3,\"bedWidth\":\"1.80\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":2,\"maxPerson\":6,\"roomAcreage\":\"120\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedName\":\"双人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"P000000\",\"bedWidth\":\"1.50\"}]}],\"roomName\":\"三居室1\"}]},\"roomEngName\":\"3-Bedroom\",\"roomId\":7798719,\"roomName\":\"三居室\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2}],\"roomNum\":\"0\",\"telephone\":\"+86-10-56320606-1185415\"},{\"address\":\"苏溪镇人民路503号\",\"allowsPet\":\"0\",\"appearancePicUrl\":\"http://dimg04.c-ctrip.com/images//0AD4c120009f4odw0667E_R_550_412_R5.jpg\",\"applicableGuest\":\"3\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"YIW\",\"cityName\":\"义乌\",\"countryCode\":\"CN\",\"fitmentDate\":\"2021-07-01\",\"hotelCategory\":\"486\",\"hotelId\":1622418,\"hotelIntroduce\":\"对于想要捕捉义乌城市风采的旅客来说，茗柯电竞酒店是一个理想的选择。</br>旅客可以在闲暇时间去酒店的休闲区，提升健康幸福感。\",\"hotelName\":\"茗柯电竞酒店\",\"hotelPolicys\":[],\"hotelStar\":66,\"hotelSubCategory\":\"495\",\"isChargePark\":\"0\",\"isPark\":\"0\",\"latitude\":29.418032,\"longitude\":120.136547,\"petText\":\"不可携带宠物。\",\"praciceDate\":\"2021-07-01 00:00:00\",\"province\":\"ZJG\",\"provinceName\":\"浙江\",\"roomInfos\":[{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"BIG\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.8\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"1000000\",\"bedCount\":1,\"bedWidth\":\"1.8\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":2,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.8\"}]}],\"roomName\":\"网红情侣房(i712700+4070s）32g 6000mhz内存+华硕27寸2k180hz显示器1\"}]},\"roomFloor\":\"2\",\"roomId\":7798795,\"roomName\":\"网红情侣房(i712700+4070s）32g 6000mhz内存+华硕27寸2k180hz显示器\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"4\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"太空舱\",\"bedTypecd\":\"J000000\",\"bedWidth\":\"1\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"4000000\",\"bedCount\":4,\"bedWidth\":\"1\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":5,\"roomAcreage\":\"30\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"太空舱\",\"bedNum\":\"4\",\"bedTypeCode\":\"J000000\",\"bedWidth\":\"1\"}]}],\"roomName\":\"五人电竞麻将轰趴房(i712700+4070s）32g 6000mhz内存+华硕2k260hz显示器1\"}]},\"roomFloor\":\"2\",\"roomId\":7798796,\"roomName\":\"五人电竞麻将轰趴房(i712700+4070s）32g 6000mhz内存+华硕2k260hz显示器\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"上下铺\",\"bedTypecd\":\"B000000\",\"bedWidth\":\"1.2\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"B000000\",\"bedCount\":1,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":3,\"roomAcreage\":\"25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"上下铺\",\"bedNum\":\"1\",\"bedTypeCode\":\"B000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"三人电竞麻将房（i5 12400f+3070显卡）32g 6000mhz内存+240hz1\"}]},\"roomFloor\":\"20\",\"roomId\":7798797,\"roomName\":\"三人电竞麻将房（i5 12400f+3070显卡）32g 6000mhz内存+240hz\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"上下铺\",\"bedTypecd\":\"B000000\",\"bedWidth\":\"1.2\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"2000000\",\"bedCount\":2,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":-100,\"maxPerson\":4,\"roomAcreage\":\"25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"上下铺\",\"bedNum\":\"2\",\"bedTypeCode\":\"B000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"四人玩家国度电竞房(i5 12400f+3070显卡）1\"}]},\"roomFloor\":\"2\",\"roomId\":7798798,\"roomName\":\"四人玩家国度电竞房(i5 12400f+3070显卡）\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"4\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"太空舱\",\"bedTypecd\":\"J000000\",\"bedWidth\":\"1.2\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"4000000\",\"bedCount\":4,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":4,\"roomAcreage\":\"30\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"太空舱\",\"bedNum\":\"4\",\"bedTypeCode\":\"J000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"四人太空舱作战房(i712700+4070s）32g 6000mhz内存+明基25寸240hz显示器1\"}]},\"roomFloor\":\"2\",\"roomId\":7798799,\"roomName\":\"四人太空舱作战房(i712700+4070s）32g 6000mhz内存+明基25寸240hz显示器\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.2\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"双人床\",\"bedTypecd\":\"P000000\",\"bedWidth\":\"1.5\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"2000000\",\"bedCount\":2,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":3,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"单人床\",\"bedNum\":\"1\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.2\"},{\"bedName\":\"双人床\",\"bedNum\":\"1\",\"bedTypeCode\":\"P000000\",\"bedWidth\":\"1.5\"}]}],\"roomName\":\"三人至臻双床房(i712700+4070s）32g 6000mhz内存+华硕27寸2k180hz显示器1\"}]},\"roomFloor\":\"2\",\"roomId\":7798800,\"roomName\":\"三人至臻双床房(i712700+4070s）32g 6000mhz内存+华硕27寸2k180hz显示器\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"上下铺\",\"bedTypecd\":\"B000000\",\"bedWidth\":\"1.2\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"B000000\",\"bedCount\":1,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":3,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"上下铺\",\"bedNum\":\"1\",\"bedTypeCode\":\"B000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"三人男神开黑房1\"}]},\"roomFloor\":\"2\",\"roomId\":7798801,\"roomName\":\"三人男神开黑房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"上下铺\",\"bedTypecd\":\"B000000\",\"bedWidth\":\"1.2\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"B000000\",\"bedCount\":1,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":3,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"上下铺\",\"bedNum\":\"1\",\"bedTypeCode\":\"B000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"三人基情战斗电竞房1\"}]},\"roomFloor\":\"2\",\"roomId\":7798802,\"roomName\":\"三人基情战斗电竞房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"太空舱\",\"bedTypecd\":\"J000000\",\"bedWidth\":\"1.2\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"2000000\",\"bedCount\":2,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":-100,\"maxPerson\":2,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"太空舱\",\"bedNum\":\"2\",\"bedTypeCode\":\"J000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"2人太空舱漫游房(i712700+4070s）32g 6000mhz内存+华硕27寸2k180hz显示器1\"}]},\"roomFloor\":\"2\",\"roomId\":7798803,\"roomName\":\"2人太空舱漫游房(i712700+4070s）32g 6000mhz内存+华硕27寸2k180hz显示器\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"上下铺\",\"bedTypecd\":\"B000000\",\"bedWidth\":\"1.2\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"B000000\",\"bedCount\":1,\"bedWidth\":\"1.2\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":2,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"上下铺\",\"bedNum\":\"1\",\"bedTypeCode\":\"B000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"2人険定电竞房（i5 12400f+3070显卡）32g 6000mhz内存+240hz1\"}]},\"roomFloor\":\"2\",\"roomId\":7798804,\"roomName\":\"2人険定电竞房（i5 12400f+3070显卡）32g 6000mhz内存+240hz\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"addBedflag\":0,\"addCribFlag\":\"0\",\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"BIG\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.8\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"1000000\",\"bedCount\":1,\"bedWidth\":\"1.8\",\"withOr\":2}],\"broadNet\":3,\"isAllowSmoking\":2,\"maxPerson\":2,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.8\"}]}],\"roomName\":\"2人梦幻大床电竞房（i5 12400f+3070显卡）2k165hz1\"}]},\"roomFloor\":\"2\",\"roomId\":7798805,\"roomName\":\"2人梦幻大床电竞房（i5 12400f+3070显卡）2k165hz\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2}],\"roomNum\":\"20\",\"telephone\":\"+86-***********\"},{\"address\":\"甲宝山路卓越罗纳河谷\",\"allowsPet\":\"1\",\"appearancePicUrl\":\"http://fcimage.fangcang.com/images/hotels/472/1622472/202310061696607131422.jpg\",\"applicableGuest\":\"3\",\"business\":\"15110\",\"businessName\":\"天河机场/盘龙城\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"WUH\",\"cityName\":\"武汉\",\"countryCode\":\"CN\",\"distinct\":\"WUHHPQD\",\"distinctName\":\"黄陂区\",\"hotelId\":1622472,\"hotelIntroduce\":\"你要大隐于市，也要玩得尽兴。\",\"hotelName\":\"飞鸟789民宿(甲宝山路分店)\",\"hotelPolicys\":[],\"hotelStar\":79,\"isChargePark\":\"0\",\"isPark\":\"1\",\"latitude\":30.691378,\"longitude\":114.261201,\"petText\":\"允许携带宠物，会收取额外费用。\",\"province\":\"HBI\",\"provinceName\":\"湖北\",\"roomInfos\":[{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"双人床\",\"bedTypecd\":\"P000000\",\"bedWidth\":\"1.50\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"3000000\",\"bedCount\":3,\"bedWidth\":\"1.80\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":1,\"maxPerson\":6,\"roomAcreage\":\"240\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"2\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedName\":\"双人床\",\"bedNum\":\"1\",\"bedTypeCode\":\"P000000\",\"bedWidth\":\"1.50\"}]}],\"roomName\":\"花园三室一厅套房1\"}]},\"roomEngName\":\"Garden Suite - 3-Bedroom\",\"roomId\":7798964,\"roomName\":\"花园三室一厅套房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2}],\"roomNum\":\"1\",\"telephone\":\"+86-10-56320606-28413709\"},{\"address\":\"清莲路东坑村东二新村\",\"allowsPet\":\"1\",\"appearancePicUrl\":\"http://fcimage.fangcang.com/images/hotels/527/1622527/202309291695970019550.jpg\",\"applicableGuest\":\"3\",\"business\":\"54630341\",\"businessName\":\"顺盈时代广场/城市广场\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"QIN\",\"cityName\":\"清远\",\"countryCode\":\"CN\",\"distinct\":\"QINQCQD\",\"distinctName\":\"清城区\",\"hotelId\":1622527,\"hotelIntroduce\":\"大好时光，美景，美屋不可辜负！\",\"hotelName\":\"诺诺民宿(清莲路分店)\",\"hotelPolicys\":[],\"hotelStar\":79,\"isChargePark\":\"0\",\"isPark\":\"1\",\"latitude\":23.623961,\"longitude\":113.147143,\"petText\":\"允许携带宠物，会收取额外费用。\",\"province\":\"GDN\",\"provinceName\":\"广东\",\"roomInfos\":[{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"4\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.20\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"4000000\",\"bedCount\":4,\"bedWidth\":\"1.20\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":4,\"roomAcreage\":\"30\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"单人床\",\"bedNum\":\"4\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.20\"}]}],\"roomName\":\"舒适一室单床房1\"}]},\"roomEngName\":\"Cozy Room - 1-Room (1 bed)\",\"roomId\":7799146,\"roomName\":\"舒适一室单床房\",\"windowDetail\":100,\"windowType\":3,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"DOUBLE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.20\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"A000000\",\"bedCount\":2,\"bedWidth\":\"1.20\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":2,\"roomAcreage\":\"25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"单人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.20\"}]}],\"roomName\":\"标准间1\"}]},\"roomEngName\":\"Standard  Room\",\"roomId\":7799147,\"roomName\":\"标准间\",\"windowDetail\":100,\"windowType\":3,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"3\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.20\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"3000000\",\"bedCount\":3,\"bedWidth\":\"1.20\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":3,\"roomAcreage\":\"25\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"单人床\",\"bedNum\":\"3\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.20\"}]}],\"roomName\":\"一室单床房1\"}]},\"roomEngName\":\"Guestroom - 1-Room (1 bed)\",\"roomId\":7799148,\"roomName\":\"一室单床房\",\"windowDetail\":100,\"windowType\":3,\"wiredBroadnet\":0,\"wirelessBroadnet\":2}],\"roomNum\":\"0\",\"telephone\":\"+86-10-56320606-4382358\"},{\"address\":\"芦梅线富春江镇芦茨村双源二组\",\"allowsPet\":\"1\",\"appearancePicUrl\":\"http://fcimage.fangcang.com/images/hotels/576/1622576/202308201692498534308.jpg\",\"applicableGuest\":\"3\",\"business\":\"21464458\",\"businessName\":\"白云源度假区\",\"checkInTime\":\"14:00\",\"checkOutTime\":\"12:00\",\"city\":\"TOU\",\"cityName\":\"桐庐\",\"countryCode\":\"CN\",\"hotelId\":1622576,\"hotelIntroduce\":\"桐庐梅林居民宿地处美丽的白云源入口处，四季风景如画，周围有严子陵钓台、富春江小三峡、印象富春江、江南龙门湾、瑶琳仙境、垂云通天河等著名景点。 是一幢四层小楼，集住宿、餐饮、棋牌、KTV、烧烤等一体，门口设有多个停车位。山居的客房内设施齐全，空调、电视、独卫、24小时热水、无线网络等设施一应俱全。 在这里，你可以远离城市的喧嚣，静享乡村的妖娆，体验纯正的农家土菜，呼吸清新的自然气味，体验山好、水好的优美景致。\",\"hotelName\":\"桐庐梅林居民宿(芦梅线分店)\",\"hotelPolicys\":[],\"hotelStar\":79,\"isChargePark\":\"0\",\"isPark\":\"1\",\"latitude\":29.708092,\"longitude\":119.715312,\"petText\":\"允许携带宠物，会收取额外费用。\",\"province\":\"ZJG\",\"provinceName\":\"浙江\",\"roomInfos\":[{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"DOUBLE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"1000000\",\"bedCount\":2,\"bedWidth\":\"1.80\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":4,\"roomAcreage\":\"35\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"2\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.8\"}]}],\"roomName\":\"阳光一室二床房B1\"}]},\"roomId\":7799300,\"roomName\":\"阳光一室二床房B\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"榻榻米\",\"bedTypecd\":\"D000000\",\"bedWidth\":\"1.80\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"D000000\",\"bedCount\":1,\"bedWidth\":\"1.80\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":4,\"roomAcreage\":\"60\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"榻榻米\",\"bedNum\":\"1\",\"bedTypeCode\":\"D000000\",\"bedWidth\":\"1.5\"},{\"bedName\":\"榻榻米\",\"bedNum\":\"1\",\"bedTypeCode\":\"D000000\",\"bedWidth\":\"1.8\"}]}],\"roomName\":\"亲子一室二床房1\"}]},\"roomId\":7799301,\"roomName\":\"亲子一室二床房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"BIG\",\"bedTypeName\":\"特大床\",\"bedTypecd\":\"L000000\",\"bedWidth\":\"2.00\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"1000000\",\"bedCount\":1,\"bedWidth\":\"2.00\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":2,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"特大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"L000000\",\"bedWidth\":\"2.0\"}]}],\"roomName\":\"阳光一室大床房1\"}]},\"roomId\":7799302,\"roomName\":\"阳光一室大床房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"DOUBLE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.20\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"A000000\",\"bedCount\":2,\"bedWidth\":\"1.20\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":2,\"roomAcreage\":\"20\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"单人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"阳光一室二床房1\"}]},\"roomId\":7799303,\"roomName\":\"阳光一室二床房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"DOUBLE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.20\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"A000000\",\"bedCount\":2,\"bedWidth\":\"1.20\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":2,\"roomAcreage\":\"28\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"单人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.2\"}]}],\"roomName\":\"山景一室二床房1\"}]},\"roomId\":7799304,\"roomName\":\"山景一室二床房\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"2\",\"bedNumberType\":\"DOUBLE\",\"bedTypeName\":\"双人床\",\"bedTypecd\":\"P000000\",\"bedWidth\":\"1.50\"}],\"withOr\":\"2\"},\"bedTypes\":[{\"bedCode\":\"A000000\",\"bedCount\":2,\"bedWidth\":\"1.50\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":4,\"roomAcreage\":\"26\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"双人床\",\"bedNum\":\"2\",\"bedTypeCode\":\"P000000\",\"bedWidth\":\"1.5\"}]}],\"roomName\":\"阳光一室二床房A1\"}]},\"roomId\":7799305,\"roomName\":\"阳光一室二床房A\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2},{\"bedTypeDetails\":{\"bedTypesNew\":[{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"大床\",\"bedTypecd\":\"1000000\",\"bedWidth\":\"1.80\"},{\"bedNum\":\"1\",\"bedNumberType\":\"MORE\",\"bedTypeName\":\"单人床\",\"bedTypecd\":\"A000000\",\"bedWidth\":\"1.30\"}],\"withOr\":\"1\"},\"bedTypes\":[{\"bedCode\":\"2000000\",\"bedCount\":2,\"bedWidth\":\"1.80\",\"withOr\":2}],\"broadNet\":2,\"isAllowSmoking\":-100,\"maxPerson\":3,\"roomAcreage\":\"60\",\"roomBedTypeInfos\":{\"roomBeds\":[{\"bedGroups\":[{\"bedInfos\":[{\"bedName\":\"大床\",\"bedNum\":\"1\",\"bedTypeCode\":\"1000000\",\"bedWidth\":\"1.8\"},{\"bedName\":\"单人床\",\"bedNum\":\"1\",\"bedTypeCode\":\"A000000\",\"bedWidth\":\"1.3\"}]}],\"roomName\":\"亲子一室二床房A1\"}]},\"roomId\":7799306,\"roomName\":\"亲子一室二床房A\",\"windowDetail\":2,\"windowType\":1,\"wiredBroadnet\":0,\"wirelessBroadnet\":2}],\"roomNum\":\"8\",\"telephone\":\"+86-10-56320606-8108808\"}]},\"returnCode\":\"000\",\"returnMsg\":\"成功\"}";
        JSONObject info;
        try {
            info = JSONObject.parseObject(infoText);
        }catch (Exception e) {
            return;
        }
        if(!info.getString("returnCode").equals("000")) {
            return;
        }
        JSONArray hotelInfos = info.getJSONObject("bussinessResponse").getJSONArray("hotelInfos");
        if(hotelInfos == null) {
            ids0.stream().forEach(id -> {
                try {
                    System.out.println(String.valueOf(id));
                } catch (Exception e) {
                }
            });
            return;
        }
        String finalInfoText = infoText;
        Set<Long> idSet = new HashSet<>(ids0);
        hotelInfos.stream().forEach(element -> {
            try {
                JSONObject hotelInfo = (JSONObject) element;
                hotelInfoHsjl1.setId(hotelInfo.getString("hotelId"));
                hotelInfoHsjl1.setAddressLine(hotelInfo.getString("address"));
                hotelInfoHsjl1.setBusinessDistricts(hotelInfo.getString("businessName"));
                String cityId = hotelInfo.getString("city");
                hotelInfoHsjl1.setLonBd(hotelInfo.getBigDecimal("longitude"));
                hotelInfoHsjl1.setLatBd(hotelInfo.getBigDecimal("latitude"));
                hotelInfoHsjl1.setOpenDate(hotelInfo.getString("praciceDate"));
                hotelInfoHsjl1.setDecorationDate(hotelInfo.getString("fitmentDate"));
                hotelInfoHsjl1.setPhone(hotelInfo.getString("telephone"));
                hotelInfoHsjl1.setName(hotelInfo.getString("hotelName"));
                hotelInfoHsjl1.setNameEn(hotelInfo.getString("hotelEngName"));
                hotelInfoHsjl1.setDescription(hotelInfo.getString("hotelIntroduce"));
                hotelInfoHsjl1.setStarInfo(hotelInfo.getString("StringhotelStar"));
                hotelInfoHsjl1.setGroupId(hotelInfo.getString("parentHotelGroupName"));
                hotelInfoHsjl1.setBrandId(hotelInfo.getString("plateName"));
                JSONObject policyInfo = new JSONObject();
                policyInfo.put("inOutTime", hotelInfo.getString("checkInTim") + "-" + hotelInfo.getString("checkOutTime"));
                hotelInfoHsjl1.setPolicyInfo(policyInfo.toString());
                hotelInfoHsjl1.setImage(hotelInfo.getString("appearancePicUrl"));
                hotelInfoHsjl1.setFacilitiesInfo(hotelInfo.getString("hotelFacilityNew"));
                hotelInfoHsjl1.setIncrementStatus(1);
                idSet.remove(hotelInfo.getLong("hotelId"));
            } catch (Exception e) {
            }
        });
        idSet.stream().forEach(id -> {
            try {
                System.out.println(String.valueOf(id));
            } catch (Exception e) {
            }
        });
        });
//        System.out.println("BTC 精品度假酒店(BTC Boutique Resort Private Pool Villas Hua Hin บ้านทะเลจีน บูติค รีสอร์ท หัวหิน)+BTC Boutique Resort Private Pool Villas Hua Hin บ้านทะเลจีน บูติค รีสอร์ท หัวหิน".length());
//        LineIterator it = null;
//        try {
//            File file = new File("D:/tianjiu/mtDetail.txt");
//            if (file.exists()) {
//                it = FileUtils.lineIterator(file, "utf8");
//                ObjectMapper objectMapper = new ObjectMapper();
//                String line = null;
//                while (it.hasNext()) {
//                    try {
//                        line = it.nextLine();
//                        HotelInfoMeituan meituan = objectMapper.readValue(line, HotelInfoMeituan.class);
//                        if(meituan.getId().equals("1037383373")) {
//                            System.out.println("mtDetail信息:"+line);
//                        }
//                    }catch (Exception e) {
//                        System.out.println("mtDetail异常信息"+line);
//                        continue;
//                    }
//                }
//            }
//        }catch (Exception e){
//        } finally {
//            LineIterator.closeQuietly(it);
//        }
//        try {
//            for(int i=0; i<3; i++) {
//                while (true) {
//                    System.out.println(2222);
//                    break;
//                }
//            }
//            String str = "{\"id\":\"1135462865\",\"name\":\"品睿酒店（徐州睢宁高铁站店）\",\"phone\":\"0516-88090555/***********\",\"provinceId\":\"320000\",\"cityId\":\"320300\",\"cityName\":\"徐州市\",\"areaId\":\"320324\",\"businessDistricts\":\"无合适商圈\",\"addressLine\":\"505省道与刘粱路交叉处\",\"openDate\":\"2024/06\",\"decorationDate\":\"2024/01\",\"description\":\"徐州睢宁高铁站品睿酒店坐落于睢宁县梁集镇505省道与刘梁路交叉口，距睢宁高铁站仅3公里。我们拥有81间客房，以及餐饮包厢，自助餐厅，会议为一体的酒店商业综合体，通过简约年轻感设计、智慧化场景体验以及茶理事特色服务 ，打造新一代年轻人喜爱的多元旅宿生活。\\n品睿酒店聚焦新一代年轻人喜爱的生活方式 ，我们要让所有人在任何城镇都能住上好酒店。\",\"floorCount\":\"5\",\"brandId\":\"品睿酒店\",\"groupId\":\"尚美生活集团\",\"starInfo\":\"{\\\"code\\\":3,\\\"name\\\":\\\"高档（按四星级标准建造）\\\"}\",\"image\":\"http://p1.meituan.net/tdchoteldark/e003de736af368c6af8519d3878e294220127659.jpg\",\"policyInfo\":\"{\\\"checkinPolicy\\\":{\\\"start\\\":\\\"12:00\\\",\\\"end\\\":\\\"0\\\"},\\\"checkOutPolicy\\\":{\\\"start\\\":\\\"0\\\",\\\"end\\\":\\\"12:00\\\"},\\\"guestPolicy\\\":{\\\"guestType\\\":{\\\"code\\\":3,\\\"name\\\":\\\"酒店仅接待大陆和港澳台客人\\\"},\\\"ageLimit\\\":{\\\"isLimited\\\":1,\\\"maxAge\\\":0,\\\"minAge\\\":18}},\\\"breakfastPolicy\\\":{\\\"hasBreakfast\\\":1,\\\"businessHours\\\":[{\\\"times\\\":[\\\"07:00-09:00\\\"]}]},\\\"petPolicy\\\":{\\\"allowed\\\":0},\\\"parkingPolicies\\\":[{\\\"policyItems\\\":[{\\\"type\\\":\\\"HAS_PARKING\\\",\\\"name\\\":\\\"是否有停车场\\\",\\\"value\\\":\\\"1\\\",\\\"description\\\":\\\"有停车场\\\"},{\\\"type\\\":\\\"PARKING_TYPE\\\",\\\"name\\\":\\\"停车场类型\\\",\\\"value\\\":\\\"0\\\",\\\"description\\\":\\\"私人停车场\\\"},{\\\"type\\\":\\\"PARKING_BOOK_TYPE\\\",\\\"name\\\":\\\"停车场预订\\\",\\\"value\\\":\\\"2\\\",\\\"description\\\":\\\"无法提前预订\\\"},{\\\"type\\\":\\\"PARKING_LOCATION\\\",\\\"name\\\":\\\"停车场位置\\\",\\\"value\\\":\\\"0\\\",\\\"description\\\":\\\"酒店内\\\"},{\\\"type\\\":\\\"PARKING_FEE_TYPE\\\",\\\"name\\\":\\\"停车场费用类型\\\",\\\"value\\\":\\\"0\\\",\\\"description\\\":\\\"免费\\\"}]}],\\\"chargePointPolicies\\\":[{\\\"policyItems\\\":[{\\\"type\\\":\\\"HAS_CHARGING_POINT\\\",\\\"name\\\":\\\"是否有充电车位\\\",\\\"value\\\":\\\"1\\\",\\\"description\\\":\\\"有充电车位\\\"},{\\\"type\\\":\\\"CHARGING_POINT_TYPE\\\",\\\"name\\\":\\\"充电车位类型\\\",\\\"value\\\":\\\"0\\\",\\\"description\\\":\\\"一般充电桩\\\"},{\\\"type\\\":\\\"CHARGING_POINT_LOCATION\\\",\\\"name\\\":\\\"充电车位位置\\\",\\\"value\\\":\\\"0\\\",\\\"description\\\":\\\"酒店内\\\"},{\\\"type\\\":\\\"CHARGING_POINT_COUNT\\\",\\\"name\\\":\\\"充电车位数量\\\",\\\"value\\\":\\\"8\\\",\\\"description\\\":\\\"充电车位数量：8\\\"},{\\\"type\\\":\\\"CHARGING_POINT_DISTANCE\\\",\\\"name\\\":\\\"停车后步行距离\\\",\\\"value\\\":\\\"0\\\",\\\"description\\\":\\\"100米以内\\\"}]}],\\\"childPolicy\\\":{\\\"allowedChild\\\":\\\"1\\\",\\\"minAge\\\":\\\"0\\\",\\\"maxAmount\\\":\\\"1\\\",\\\"fees\\\":[{\\\"type\\\":\\\"1\\\",\\\"ageRange\\\":\\\"0-17\\\",\\\"pattern\\\":\\\"0\\\",\\\"hasBreakfast\\\":\\\"0\\\"}],\\\"breakfastFees\\\":[{\\\"type\\\":\\\"2\\\",\\\"pattern\\\":\\\"1\\\",\\\"amount\\\":\\\"38\\\",\\\"heightRange\\\":\\\"1.2-99\\\"}],\\\"specialTips\\\":\\\"不接受18岁以下客人在无监护人陪同的情况下入住;加床及儿童政策取决于您所选的房型，若超过房型限定人数，或携带儿童年龄不在政策描述范围内，可能需收取额外费用，提出的任何要求均需获得酒店的确认\\\",\\\"extraBedFees\\\":\\\"0\\\"}}\",\"facilitiesInfo\":\"[{\\\"category\\\":\\\"HOTEL_SERVICE\\\",\\\"facilityItems\\\":[{\\\"id\\\":101883,\\\"name\\\":\\\"行李寄存是否收费\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101882,\\\"name\\\":\\\"接站服务是否收费\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101896,\\\"name\\\":\\\"送站服务是否收费\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101901,\\\"name\\\":\\\"代客泊车是否收费\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101931,\\\"name\\\":\\\"公用区wifi是否收费\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101007,\\\"name\\\":\\\"信用卡结算服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102031,\\\"name\\\":\\\"是否有停车场\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102033,\\\"name\\\":\\\"接机服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102032,\\\"name\\\":\\\"公用区wifi\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102035,\\\"name\\\":\\\"行李寄存\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102034,\\\"name\\\":\\\"接站服务\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102037,\\\"name\\\":\\\"送站服务\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102036,\\\"name\\\":\\\"送机服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102041,\\\"name\\\":\\\"代客泊车\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102040,\\\"name\\\":\\\"自行车租赁服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101039,\\\"name\\\":\\\"洗衣服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101038,\\\"name\\\":\\\"叫醒服务\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101041,\\\"name\\\":\\\"24小时前台接待服务\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101040,\\\"name\\\":\\\"送餐服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101044,\\\"name\\\":\\\"擦鞋服务\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101047,\\\"name\\\":\\\"外币兑换服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101048,\\\"name\\\":\\\"专职行李员\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101052,\\\"name\\\":\\\"多语种服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101101,\\\"name\\\":\\\"租车服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101102,\\\"name\\\":\\\"叫车服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101106,\\\"name\\\":\\\"儿童看护\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101175,\\\"name\\\":\\\"婚宴服务\\\",\\\"value\\\":\\\"3\\\"},{\\\"id\\\":101174,\\\"name\\\":\\\"快速办理入住/退房手续\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101176,\\\"name\\\":\\\"儿童俱乐部\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101181,\\\"name\\\":\\\"吸烟区\\\",\\\"value\\\":\\\"3\\\"},{\\\"id\\\":101182,\\\"name\\\":\\\"支付方式\\\",\\\"value\\\":\\\"[12,13]\\\"},{\\\"id\\\":101198,\\\"name\\\":\\\"可加婴儿床\\\",\\\"value\\\":\\\"0\\\"}]},{\\\"category\\\":\\\"HOTEL_FACILITY\\\",\\\"facilityItems\\\":[{\\\"id\\\":101905,\\\"name\\\":\\\"会议厅是否收费\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101009,\\\"name\\\":\\\"前台贵重物品保险柜\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101011,\\\"name\\\":\\\"酒吧\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101013,\\\"name\\\":\\\"温泉\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101014,\\\"name\\\":\\\"钓鱼\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101019,\\\"name\\\":\\\"棋牌室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102043,\\\"name\\\":\\\"会议厅\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101018,\\\"name\\\":\\\"儿童乐园\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101020,\\\"name\\\":\\\"舞厅\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101023,\\\"name\\\":\\\"SPA\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101022,\\\"name\\\":\\\"桑拿浴室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101025,\\\"name\\\":\\\"按摩室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101024,\\\"name\\\":\\\"理发美容中心\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101027,\\\"name\\\":\\\"自动取款机\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101026,\\\"name\\\":\\\"足浴\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101028,\\\"name\\\":\\\"健身室\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101030,\\\"name\\\":\\\"台球/桌球室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101032,\\\"name\\\":\\\"乒乓球室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101035,\\\"name\\\":\\\"保龄球场\\\",\\\"value\\\":\\\"0\\\"}{\"id\":\"592752573\",\"name\":\"云溪里民宿\",\"phone\":\"***********/***********\",\"provinceId\":\"330000\",\"cityId\":\"330100\",\"cityName\":\"杭州市\",\"areaId\":\"330106\",\"businessDistricts\":\"小和山\",\"addressLine\":\"石马社区105号楼\",\"openDate\":\"2021/07\",\"decorationDate\":\"2021/03\",\"description\":\"民宿客房带有独立阳台、配备超清投影，让你享受电影院般的观影体验。民宿精选床上用品，所有房间均配备22-26cm厚的椰棕乳胶床垫，睡感舒适。民宿卫生间干净清爽、装修风格简约时尚，配备高端卫浴，为您带来美好的洗漱体验。民宿周边美食众多，娱乐设施丰富，诚挚欢迎大家的到来。\",\"floorCount\":\"5\",\"starInfo\":\"{\\\"code\\\":6,\\\"name\\\":\\\"经济型\\\"}\",\"image\":\"http://p0.meituan.net/tdchoteldark/6cd5ae1fdeb0f41cb159bc3c60d3c3c41385917.jpg\",\"policyInfo\":\"{\\\"checkinPolicy\\\":{\\\"start\\\":\\\"14:00\\\",\\\"end\\\":\\\"0\\\"},\\\"checkOutPolicy\\\":{\\\"end\\\":\\\"12:00\\\"},\\\"guestPolicy\\\":{\\\"guestType\\\":{\\\"code\\\":2,\\\"name\\\":\\\"酒店仅接待大陆客人\\\"},\\\"ageLimit\\\":{\\\"isLimited\\\":0}},\\\"breakfastPolicy\\\":{\\\"hasBreakfast\\\":0},\\\"petPolicy\\\":{\\\"allowed\\\":0},\\\"parkingPolicies\\\":[{\\\"policyItems\\\":[{\\\"type\\\":\\\"HAS_PARKING\\\",\\\"name\\\":\\\"是否有停车场\\\",\\\"value\\\":\\\"0\\\",\\\"description\\\":\\\"没有停车场\\\"}]}],\\\"chargePointPolicies\\\":[{\\\"policyItems\\\":[{\\\"type\\\":\\\"HAS_CHARGING_POINT\\\",\\\"name\\\":\\\"是否有充电车位\\\",\\\"value\\\":\\\"0\\\",\\\"description\\\":\\\"没有充电车位\\\"}]}],\\\"childPolicy\\\":{\\\"allowedChild\\\":\\\"1\\\",\\\"minAge\\\":\\\"0\\\",\\\"maxAmount\\\":\\\"1\\\",\\\"fees\\\":[{\\\"type\\\":\\\"1\\\",\\\"ageRange\\\":\\\"0-17\\\",\\\"pattern\\\":\\\"0\\\",\\\"hasBreakfast\\\":\\\"0\\\"}],\\\"breakfastFees\\\":[]}}\",\"facilitiesInfo\":\"[{\\\"category\\\":\\\"HOTEL_SERVICE\\\",\\\"facilityItems\\\":[{\\\"id\\\":101931,\\\"name\\\":\\\"公用区wifi是否收费\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101174,\\\"name\\\":\\\"快速办理入住/退房手续\\\",\\\"value\\\":\\\"3\\\"},{\\\"id\\\":101176,\\\"name\\\":\\\"儿童俱乐部\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101182,\\\"name\\\":\\\"支付方式\\\",\\\"value\\\":\\\"[12,13,11]\\\"},{\\\"id\\\":101198,\\\"name\\\":\\\"可加婴儿床\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101007,\\\"name\\\":\\\"信用卡结算服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102031,\\\"name\\\":\\\"是否有停车场\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102033,\\\"name\\\":\\\"接机服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102032,\\\"name\\\":\\\"公用区wifi\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102035,\\\"name\\\":\\\"行李寄存\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":102034,\\\"name\\\":\\\"接站服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102037,\\\"name\\\":\\\"送站服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102036,\\\"name\\\":\\\"送机服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101039,\\\"name\\\":\\\"洗衣服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101038,\\\"name\\\":\\\"叫醒服务\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101041,\\\"name\\\":\\\"24小时前台接待服务\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101040,\\\"name\\\":\\\"送餐服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101047,\\\"name\\\":\\\"外币兑换服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101101,\\\"name\\\":\\\"租车服务\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101106,\\\"name\\\":\\\"儿童看护\\\",\\\"value\\\":\\\"0\\\"}]},{\\\"category\\\":\\\"HOTEL_FACILITY\\\",\\\"facilityItems\\\":[{\\\"id\\\":101137,\\\"name\\\":\\\"游戏室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101147,\\\"name\\\":\\\"高尔夫球场\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101153,\\\"name\\\":\\\"滑雪\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101152,\\\"name\\\":\\\"专享海滩区\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101167,\\\"name\\\":\\\"夜总会\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101009,\\\"name\\\":\\\"前台贵重物品保险柜\\\",\\\"value\\\":\\\"1\\\"},{\\\"id\\\":101011,\\\"name\\\":\\\"酒吧\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101013,\\\"name\\\":\\\"温泉\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101019,\\\"name\\\":\\\"棋牌室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":102043,\\\"name\\\":\\\"会议厅\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101018,\\\"name\\\":\\\"儿童乐园\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101023,\\\"name\\\":\\\"SPA\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101022,\\\"name\\\":\\\"桑拿浴室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101025,\\\"name\\\":\\\"按摩室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101024,\\\"name\\\":\\\"理发美容中心\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101026,\\\"name\\\":\\\"足浴\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101028,\\\"name\\\":\\\"健身室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101030,\\\"name\\\":\\\"台球/桌球室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101032,\\\"name\\\":\\\"乒乓球室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101035,\\\"name\\\":\\\"保龄球场\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101034,\\\"name\\\":\\\"网球场\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101036,\\\"name\\\":\\\"壁球室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101093,\\\"name\\\":\\\"西式餐厅\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101092,\\\"name\\\":\\\"中式餐厅\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101095,\\\"name\\\":\\\"茶室\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101094,\\\"name\\\":\\\"日式餐厅\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101096,\\\"name\\\":\\\"咖啡厅\\\",\\\"value\\\":\\\"0\\\"},{\\\"id\\\":101100,\\\"name\\\":\\\"商务中心\\\",\\\"value\\\":\\\"0\\\"}]}]\",\"lonBd\":120.03053600000,\"latBd\":30.21632800000,\"lonGd\":120.02408400000,\"latGd\":30.21022400000,\"lonGg\":120.01934500000,\"latGg\":30.21261400000,\"reserve2\":\"49\",\"reserve3\":\"true\"}\n";
//            ObjectMapper objectMapper = new ObjectMapper();
//            HotelInfoMeituan meituan = objectMapper.readValue(str, HotelInfoMeituan.class);
//            System.out.println(JSONObject.toJSONString(meituan));
//        }catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    class getdetailMT extends Thread {
        private int i;

        getdetailMT(int i) {
            this.i = i;
        }

        public void run() {
//            TXTUtil.writeMTTXT(redissonClient);
        }
    }


    public HotelInfoMeituan getHotelInfoMeituan(JSONObject meituanJson){
        HotelInfoMeituan hotelInfo = new HotelInfoMeituan();
        try {
            JSONObject mtBase = meituanJson.getJSONObject("baseInfo");
            JSONObject mtDetail = meituanJson.getJSONObject("detailInfo");
            JSONArray mtNotices = meituanJson.getJSONArray("notices");
            JSONArray mtImages = meituanJson.getJSONArray("images");

            hotelInfo.setId(meituanJson.getString("hotelId"));
            hotelInfo.setName(mtBase.getString("name"));
            hotelInfo.setNameEn(mtBase.getString("nameEn"));
            hotelInfo.setPhone(mtBase.getString("phone"));
            JSONObject mtAddr = mtBase.getJSONObject("address");
            hotelInfo.setReserve3(mtBase.getString("bookable"));
            if (mtAddr.getJSONObject("province") != null) {
                hotelInfo.setProvinceId(mtAddr.getJSONObject("province").getString("code"));
            }
            hotelInfo.setCityId(mtAddr.getJSONObject("city").getString("code"));
            hotelInfo.setCityName(mtAddr.getJSONObject("city").getString("name"));
            if (mtAddr.getJSONObject("area") != null) {
                hotelInfo.setAreaId(mtAddr.getJSONObject("area").getString("code"));
            }
            hotelInfo.setBusinessDistricts(mtAddr.getJSONArray("businessDistricts")
                    .getJSONObject(0).getString("name"));
            hotelInfo.setAddressLine(mtAddr.getJSONObject("addressLine").getString("name"));
            JSONArray lonlats = mtAddr.getJSONArray("coordinates");
            for (int i = 0; i < lonlats.size(); i++) {
                JSONObject lonlat = lonlats.getJSONObject(i);
                String name = lonlat.getString("provider");
                BigDecimal lon = BigDecimal.valueOf(lonlat.getLong("longitude")).divide(BigDecimal.valueOf(1000000));
                BigDecimal lat = BigDecimal.valueOf(lonlat.getLong("latitude")).divide(BigDecimal.valueOf(1000000));
                lat = lat.setScale(11, BigDecimal.ROUND_HALF_UP);
                lon = lon.setScale(11, BigDecimal.ROUND_HALF_UP);
                switch (name) {
                    case "GAODE":
                        hotelInfo.setLonGd(lon);
                        hotelInfo.setLatGd(lat);
                        break;
                    case "BAIDU":
                        hotelInfo.setLonBd(lon);
                        hotelInfo.setLatBd(lat);
                        break;
                    case "GOOGLE":
                        hotelInfo.setLonGg(lon);
                        hotelInfo.setLatGg(lat);
                }
            }
            hotelInfo.setOpenDate(mtDetail.getString("openDate"));
            hotelInfo.setDecorationDate(mtDetail.getString("decorationDate"));
            hotelInfo.setDescription(mtDetail.getString("description"));
            //设置楼层
            JSONArray statics = mtDetail.getJSONArray("statics");
            for (int j = 0; j < statics.size(); j++) {
                JSONObject floor = statics.getJSONObject(j);
                if (floor.getString("type").equals("FLOOR_COUNT")) {
                    hotelInfo.setFloorCount(floor.getString("value"));
                }
            }
            //设置评分
            JSONArray scores = mtDetail.getJSONArray("ratings");
            for (int k = 0; k < scores.size(); k++) {
                JSONObject score = scores.getJSONObject(k);
                if (score.getString("type").equals("AVG_SCORE")) {
                    hotelInfo.setReserve2(score.getString("value"));
                }
            }
            JSONObject starInfo = mtDetail.getJSONObject("star");
            if (starInfo != null) {
                hotelInfo.setStarInfo(starInfo.toString());
            }
            JSONObject typeInfo = mtDetail.getJSONObject("type");
            if (typeInfo != null) {
                hotelInfo.setTypeId(typeInfo.toString());
            }
            JSONObject brandInfo = mtDetail.getJSONObject("brand");
            if (brandInfo != null) {
                hotelInfo.setBrandId(brandInfo.getString("name"));
            }
            JSONObject groupInfo = mtDetail.getJSONObject("group");
            if (groupInfo != null) {
                hotelInfo.setGroupId(groupInfo.getString("name"));
            }
            JSONObject themesInfo = mtDetail.getJSONObject("themes");
            if (themesInfo != null) {
                hotelInfo.setThemeId(themesInfo.toString());
            }
            JSONObject typesInfo = mtDetail.getJSONObject("types");
            if (themesInfo != null) {
                hotelInfo.setTypeId(typesInfo.toString());
            }
            //设置注意事项
            String noticeText = "";
            if (mtNotices != null) {
                for (int m = 0; m < mtNotices.size(); m++) {
                    JSONObject notice = mtNotices.getJSONObject(m);
                    noticeText += "<br>\\n" + notice.getString("value");
                }
                hotelInfo.setNoticeInfo(noticeText);
            }

            //设置首图
            for (int n = 0; n < mtImages.size(); n++) {
                JSONObject image = mtImages.getJSONObject(n);
                if (image.getString("title").equals("酒店首图")) {
                    hotelInfo.setImage(image.getJSONArray("links").getJSONObject(0).getString("url"));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            TXTUtil.writeTXT(new Date() +" 设置hotel进异常了： " + e + "----------"+ meituanJson
                    , ConstantList.LOG_PATH, "log.txt");
        }
        return hotelInfo;
    }

}