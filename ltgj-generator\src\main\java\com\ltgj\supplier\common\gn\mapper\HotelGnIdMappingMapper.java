package com.ltgj.supplier.common.gn.mapper;

import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: hp.long
 * @create: 2024-12-23 11:45
 */
public interface HotelGnIdMappingMapper {

    long addOrUpdateBatch(@Param("list") List<HotelGnIdMapping> list);

    HotelGnIdMapping queryByPlatformIdAndHotelId(@Param("platformId") String platformId, @Param("platformHotelId") String platformHotelId);
}
