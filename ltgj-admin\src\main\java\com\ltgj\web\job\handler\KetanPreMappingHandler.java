package com.ltgj.web.job.handler;

import com.ltgj.ivw.enums.InterfacePlatEnum;
import com.ltgj.ivw.service.IHotelInfoKetanPreMappingService;
import com.ltgj.supplier.common.dto.HotelBaseRequest;
import com.ltgj.supplier.klyx.gn.HotelKlyxManager;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "ketanHotelMappingHandler")
public class KetanPreMappingHandler extends IJobHandler {

    @Autowired
    private IHotelInfoKetanPreMappingService ketanPreMappingService;

    /**
     * 定时任务处理预映射
     * @param s 标记： 1：请求接口获取与艺龙酒店关系存入预映射表  2：根据预映射表更新酒店映射关系表
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        long startTime = System.currentTimeMillis();

        if ("1".equals(s)){
            XxlJobLogger.log("----科坦酒店存储预映射表数据----开始");
            try {
                log.info("----科坦酒店存储预映射表数据----开始");
                ketanPreMappingService.addMapping();
                log.info("----科坦酒店存储预映射表数据----完成");
                XxlJobLogger.log("----科坦酒店存储预映射表数据----完成");
                return ReturnT.SUCCESS;
            } catch (Exception e) {
                log.error("---科坦酒店存储预映射表数据---失败：" + e);
            }
        } else if ("2".equals(s)){
            try {
                XxlJobLogger.log("----科坦酒店处理预映射----开始");
                log.info("----科坦酒店处理预映射----开始");
                ketanPreMappingService.processMapping();
                XxlJobLogger.log("----科坦酒店处理预映射----完成");
                return ReturnT.SUCCESS;
            } catch (Exception e) {
                log.error("---科坦酒店处理预映射---失败：" + e);
            }
        }else{
            XxlJobLogger.log("----科坦酒店存储预映射表数据----开始");
            try {
                log.info("----科坦酒店存储预映射表数据----开始");
                ketanPreMappingService.addMapping();
                log.info("----科坦酒店存储预映射表数据----完成");
                XxlJobLogger.log("----科坦酒店存储预映射表数据----完成");
            } catch (Exception e) {
                log.error("---科坦酒店存储预映射表数据---失败：" + e);
            }

            try {
                XxlJobLogger.log("----科坦酒店处理预映射----开始");
                log.info("----科坦酒店处理预映射----开始");
                ketanPreMappingService.processMapping();
                XxlJobLogger.log("----科坦酒店处理预映射----完成");
                return ReturnT.SUCCESS;
            } catch (Exception e) {
                log.error("---科坦酒店处理预映射---失败：" + e);
            }

        }


        log.info("---科坦酒店处理预映射---结束 执行结果：失败");
        XxlJobLogger.log( "科坦任务处理 执行用时：" + (System.currentTimeMillis() - startTime) / 1000 + "秒");
        return ReturnT.FAIL;
    }
}
