package com.ltgj.ivw.service.hotel.updater;

import com.ltgj.ivw.domain.HotelUpdateRecode;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelUpdateRecodeService;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 默认进度观察者实现
 * 
 * <AUTHOR>
 */
@Slf4j
public class DefaultProgressObserver implements ProgressObserver {
    private final HotelUpdateRecode updateRecode;
    private final IHotelUpdateRecodeService hotelUpdateRecodeService;
    
    public DefaultProgressObserver(HotelUpdateRecode updateRecode, IHotelUpdateRecodeService hotelUpdateRecodeService) {
        this.updateRecode = updateRecode;
        this.hotelUpdateRecodeService = hotelUpdateRecodeService;
    }
    
    @Override
    public void onProgressUpdated(int processed, int total, int add, int update) {
        HotelUpdateStatus.clgjDetailCountGet = processed;
        HotelUpdateStatus.clgjDetailCountUpdate = update;
        log.info("酒店数据更新进度: 总数:{}, 已处理:{}, 新增:{}, 更新:{},进度:{}%",
                total, processed, add, update, total > 0 ? (processed * 100 / total) : 0);
    }
    
    @Override
    public void onCompleted(int processed, int add, int update) {
        HotelUpdateStatus.expStatusCLGJ = 3;
        updateRecode.setCountTotal(Long.valueOf(processed));
        updateRecode.setCountAdd(Long.valueOf(add));
        updateRecode.setCountFail(Long.valueOf(processed-add-update));
        updateRecode.setYl2(PlatEnum.PLAT_CLGJ.getValue());
        updateRecode.setTimeEnd(new Date());
        hotelUpdateRecodeService.insertHotelUpdateRecode(updateRecode);
        log.info("酒店数据更新完成: 总处理:{}, 成功:{}", processed, add+update);
    }
    
    @Override
    public void onError(Exception e) {
        HotelUpdateStatus.expStatusCLGJ = 3;
        updateRecode.setYl2(PlatEnum.PLAT_CLGJ.getValue());
        updateRecode.setTimeEnd(new Date());
        hotelUpdateRecodeService.insertHotelUpdateRecode(updateRecode);
        log.error("酒店数据更新异常", e);
    }
} 