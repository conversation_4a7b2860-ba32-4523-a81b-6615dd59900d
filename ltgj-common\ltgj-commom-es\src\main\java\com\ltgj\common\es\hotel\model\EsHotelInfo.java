package com.ltgj.common.es.hotel.model;

import com.tem.search.annotation.ESID;
import com.tem.search.annotation.ESMapping;
import com.tem.search.annotation.ESMetaData;
import com.tem.search.enums.Analyzer;
import com.tem.search.enums.DataType;
import com.tem.search.pojo.BaseSearchInfo;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@ESMetaData(indexName = "hotel-jd",indexType = "info", number_of_shards = 3,number_of_replicas = 1,printLog = true)
public class EsHotelInfo extends BaseSearchInfo implements Serializable {

	private static final long serialVersionUID = -4922855477301354574L;

	/**
	 * 酒店ID
	 */
	@ESID
	@ESMapping(datatype = DataType.keyword_type)
    private String hotelId;

	/**
	 * 国别类型 国内：1 default 国际：2
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private Integer nationalType;

	/**
	 * 酒店中文名
	 */
	@ESMapping(datatype = DataType.text_type, norms = false, keyword = false, analyzer = Analyzer.ik_max_word, search_analyzer = Analyzer.ik_max_word, analyzers = {Analyzer.pinyin, Analyzer.ik_max_word})
	private String name;

	/**
	 * 酒店名称（英文）
	 */
	@ESMapping(datatype = DataType.text_type, norms = false, keyword = false, analyzer = Analyzer.standard, search_analyzer = Analyzer.standard, analyzers = {Analyzer.english, Analyzer.simple})
	private String nameEn;

	/**
	 * 搜索关键字（包含中文名、英文名、名称全拼、名称简拼、地址、品牌）
	 */
	@ESMapping(datatype = DataType.text_type, norms = false, keyword = false, analyzer = Analyzer.ik_max_word, search_analyzer = Analyzer.ik_max_word, analyzers = {Analyzer.pinyin, Analyzer.ik_max_word})
	private String keyWord;

	/**
	 * 酒店首图
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private String indexPicture;

	/**
	 * 行政区编码
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private String districtId;

	/**
	 * 行政区名称
	 */
	@ESMapping(suggest = true, datatype = DataType.keyword_type, norms = false, analyzers = {Analyzer.pinyin})
	private String districtName;

	/**
	 * 城市编码code、城市ID
	 */
	@ESMapping(datatype = DataType.keyword_type)
    private String cityCode;

	/**
	 * 城市名称
	 */
	@ESMapping(suggest = true, datatype = DataType.keyword_type, norms = false, analyzers = {Analyzer.pinyin})
    private String cityName;

	/**
	 * 区县编码code、区县ID
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private String areaCode;

	/**
	 * 区县名称
	 */
	@ESMapping(suggest = true, datatype = DataType.keyword_type, norms = false, analyzers = {Analyzer.pinyin})
	private String areaName;

	/**
	 * 酒店评分
	 */
	@ESMapping(datatype = DataType.float_type)
	private Float score;;

	/**
	 * 酒店星级
	 */
	@ESMapping(datatype = DataType.integer_type)
	private Integer star;

	/**
	 * 品牌ID
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private String brandId;

	/**
	 * 品牌名称
	 */
	@ESMapping(datatype = DataType.text_type)
	private String brandName;

	/**
	 * 餐食类型（1.有餐食 2.无餐食）
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private Integer mealsType;

	/**
	 * 窗户类型（1.有窗 2.无窗）
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private Integer windowType;

	/**
	 * 协议code（如果需要筛选协议酒店，需要把协议号传过来）
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private List<String> agreementCodeList;

	/**
	 * 地址名称
	 */
	@ESMapping(datatype = DataType.text_type, keyword = false, norms = false, analyzer = Analyzer.ik_max_word, search_analyzer = Analyzer.ik_max_word, analyzers = {Analyzer.pinyin, Analyzer.ik_max_word})
    private String address;

	/**
	 * 经纬度-百度
	 */
	@ESMapping(datatype = DataType.geo_point_type)
    private Location location;

	/**
	 * 价格json([{"date":2024-10-24,"minPrice":100.25},{"date":2024-10-25,"minPrice":200.25}])
	 * 方案一：确保各个资源方底价任务跑完，循环时间最长的底价，获取到最低价保存
	 * 方案二：确保酒店基础数据跑完，在跑底价的时候，同步更新es酒店底价数据
	 */
	@ESMapping(datatype = DataType.nested_type, nested_class = DatePrice.class)
	private List<DatePrice> datePriceList;

	/**
	 * 酒店类型
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private List<String> hotelType;

	/**
	 * 地址名称英文
	 */
	@ESMapping(datatype = DataType.text_type)
	private String addressEn;

	/**
	 * 酒店联系电话
	 */
	@ESMapping(datatype = DataType.text_type)
	private String linkPhone;

	/**
	 * 商圈编码
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private String businessZone;

	/**
	 * 商圈名称
	 */
	@ESMapping(suggest = true, datatype = DataType.keyword_type, norms = false, analyzers = {Analyzer.pinyin})
	private String businessZoneName;

	/**
	 * 映射平台标志集合
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private List<String> interfacePlatList;

	/**
	 * 映射平台标志集合
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private List<String> interfacePlatListNew;

	/**
	 * 酒店集团ID
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private String hotelGroupId;

	/**
	 * 酒店集团名称
	 */
	@ESMapping(suggest = true, datatype = DataType.keyword_type, norms = false, analyzers = {Analyzer.pinyin})
	private String hotelGroupName;

	/**
	 * 酒店开业时间
	 */
	@ESMapping(datatype = DataType.text_type)
	private String openingDate;

	/**
	 * 最后装修时间
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private String finalDecorationTime;

	/**
	 * 酒店推荐度
	 */
	@ESMapping(datatype = DataType.float_type)
	private Float rank;

	/**
	 * 简介
	 */
	@ESMapping(datatype = DataType.text_type)
	private String briefIntroduction;

	/**
	 * 告知信息
	 */
	@ESMapping(datatype = DataType.text_type)
	private String noticeInfo;

	/**
	 * 政策信息
	 */
	@ESMapping(datatype = DataType.text_type)
	private String policyInfo;

	/**
	 * 设施信息
	 */
	@ESMapping(datatype = DataType.text_type)
	private String facilitiesInfo;

	/**
	 * 车站机场信息
	 */
	@ESMapping(datatype = DataType.text_type)
	private String stationAirportInfo;

	/**
	 * 周边
	 */
	@ESMapping(datatype = DataType.text_type)
	private String surroundings;

	/**
	 * 酒店卖点
	 */
	@ESMapping(datatype = DataType.text_type)
	private String sellingPoints;

	/**
	 * 1:主推 2:次推
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private Integer recommendLevel;

	/**
	 * 酒店可用状态 0--可用，1--不可用
	 */
	@ESMapping(datatype = DataType.keyword_type)
	private Integer status;
}
