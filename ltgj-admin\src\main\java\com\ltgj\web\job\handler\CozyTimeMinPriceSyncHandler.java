package com.ltgj.web.job.handler;

import com.ltgj.ivw.domain.MinPriceReq;
import com.ltgj.quartz.task.MinPriceTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/5/26
 * @Description： hotel-科坦最低价同步任务
 */
@Slf4j
@Component
@JobHandler(value = "cozyTimeMinPriceSyncHandler")
public class CozyTimeMinPriceSyncHandler extends IJobHandler {

    @Resource
    private MinPriceTask minPriceTask;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        long startTime = System.currentTimeMillis();
        XxlJobLogger.log("----hotel-科坦最低价同步任务----开始");
        log.info("----hotel-科坦最低价同步任务----开始");

        Boolean result = true;
        try {
            MinPriceReq minPriceReq = new MinPriceReq();
            minPriceReq.setUserName("admin");
            this.minPriceTask.updateLowPriceOfCozyTime(minPriceReq);
        } catch (Exception e) {
            log.error("科坦最低价同步任务异常：" + e);
            result = false;
        }

        log.info("---hotel-科坦最低价同步任务---结束 执行结果：" + (result ? "成功" : "失败"));
        XxlJobLogger.log("-----hotel-科坦最低价同步任务-----务结束 执行结果：" + (result ? "成功" : "失败") + " 执行用时：" + (System.currentTimeMillis() - startTime) / 1000 + "秒");

        return result ? ReturnT.SUCCESS : ReturnT.FAIL;
    }
}
