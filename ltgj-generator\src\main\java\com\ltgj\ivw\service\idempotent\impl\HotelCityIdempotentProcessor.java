package com.ltgj.ivw.service.idempotent.impl;

import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.mapper.HotelCityMapper;
import com.ltgj.ivw.service.idempotent.AbstractIdempotentProcessor;
import com.ltgj.ivw.service.idempotent.dto.HotelCityDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * HotelCity表幂等性处理器
 */
@Slf4j
@Component
public class HotelCityIdempotentProcessor extends AbstractIdempotentProcessor<HotelCity, Integer, HotelCityDTO> {
    
    @Autowired
    private HotelCityMapper hotelCityMapper;
    
    @Override
    protected String getProcessorName() {
        return "HotelCity幂等性处理器";
    }
    
    @Override
    public Integer getEntityKey(HotelCity entity) {
        return entity.getId();
    }

    @Override
    public HotelCityDTO getEntityAttributeKey(HotelCity entity) {
        return HotelCityDTO.builder().cityId(entity.getCityId()).reserve1(entity.getReserve1()).build() ;
    }

    @Override
    public boolean isEntityEqual(HotelCity existing, HotelCity incoming) {
        if (existing == null || incoming == null) {
            return false;
        }
        
        try {
            // 使用反射比较所有字段（排除时间字段）
            Field[] fields = HotelCity.class.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                
                // 跳过时间字段和序列化字段
                String fieldName = field.getName();
                if (fieldName.equals("serialVersionUID") || 
                    fieldName.equals("createTime") || 
                    fieldName.equals("updateTime") ||
                        fieldName.equals("id")) {
                    continue;
                }
                
                Object existingValue = field.get(existing);
                Object incomingValue = field.get(incoming);
                
                if (!Objects.equals(existingValue, incomingValue)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("比较HotelCity实体时发生错误", e);
            return false;
        }
    }
    
    @Override
    public String getChangedFields(HotelCity existing, HotelCity incoming) {
        if (existing == null || incoming == null) {
            return "实体为空";
        }
        
        List<String> changedFields = new ArrayList<>();
        
        try {
            Field[] fields = HotelCity.class.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                
                String fieldName = field.getName();
                if (fieldName.equals("serialVersionUID") || 
                    fieldName.equals("createTime") || 
                    fieldName.equals("updateTime") ||
                    fieldName.equals("id")) {
                    continue;
                }
                
                Object existingValue = field.get(existing);
                Object incomingValue = field.get(incoming);
                
                if (!Objects.equals(existingValue, incomingValue)) {
                    changedFields.add(String.format("%s: [%s] -> [%s]", 
                        fieldName, existingValue, incomingValue));
                }
            }
        } catch (Exception e) {
            log.error("获取HotelCity变更字段时发生错误", e);
            return "获取变更字段失败: " + e.getMessage();
        }
        
        return changedFields.isEmpty() ? "无变更" : String.join(", ", changedFields);
    }
    
    @Override
    public List<HotelCity> batchQueryExisting(List<Integer> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return new ArrayList<>();
        }
        
        try {
            // 分批查询，避免IN子句过长
            List<HotelCity> result = new ArrayList<>();
            int batchSize = 1000;
            
            for (int i = 0; i < keys.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, keys.size());
                List<Integer> batchKeys = keys.subList(i, endIndex);
                
                // 这里需要在HotelCityMapper中添加批量查询方法
                List<HotelCity> batchResult = hotelCityMapper.selectByIds(batchKeys);
                result.addAll(batchResult);
            }
            
            return result;
        } catch (Exception e) {
            log.error("批量查询HotelCity时发生错误，keys: {}", keys, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<HotelCity> batchQueryExistingByIdempotentKey(List<HotelCityDTO> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return new ArrayList<>();
        }

        try {
            // 分批查询，避免IN子句过长
            List<HotelCity> result = new ArrayList<>();
            int batchSize = 1000;

            for (int i = 0; i < keys.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, keys.size());
                List<HotelCityDTO> batchKeys = keys.subList(i, endIndex);

                // 这里需要在HotelCityMapper中添加批量查询方法
                List<HotelCity> batchResult = hotelCityMapper.selectByAttribute(batchKeys);
                result.addAll(batchResult);
            }

            return result;
        } catch (Exception e) {
            log.error("批量查询HotelCity时发生错误，keys: {}", keys, e);
            return new ArrayList<>();
        }
    }

    @Override
    public int batchInsert(List<HotelCity> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        
        try {
            // 设置创建时间
            Date now = new Date();
            insertList.forEach(entity -> {
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
            });
            
            return hotelCityMapper.batchInsertHotelCity(insertList);
        } catch (Exception e) {
            log.error("批量插入HotelCity时发生错误，数量: {}", insertList.size(), e);
            return 0;
        }
    }
    
    @Override
    public int batchUpdate(List<HotelCity> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        
        try {
            // 设置更新时间
            Date now = new Date();
            updateList.forEach(entity -> entity.setUpdateTime(now));
            
            // 逐个更新（如果需要批量更新，需要在Mapper中添加相应方法）
            int updateCount = 0;
            for (HotelCity entity : updateList) {
                int result = hotelCityMapper.updateHotelCityByAttribute(entity);
                updateCount += result;
            }
            
            return updateCount;
        } catch (Exception e) {
            log.error("批量更新HotelCity时发生错误，数量: {}", updateList.size(), e);
            return 0;
        }
    }
} 