{"mcpServers": {"puppeteer": {"command": "node", "args": ["D:\\tools\\nvm\\nvm\\node_cache\\node_modules\\@modelcontextprotocol\\server-puppeteer\\dist\\index.js"]}, "sequential-thinking": {"command": "cmd", "args": ["-y", "/c npx -y @modelcontextprotocol/server-sequential-thinking"]}, "filesystem": {"command": "npx -y @modelcontextprotocol/server-filesystem", "args": ["C:\\Users\\<USER>\\Desktop", "C:\\Users\\<USER>\\Documents", "C:\\Users\\<USER>\\Downloads", "D:\\tianjiugongxiang_gaoyu"], "env": {"MCP_SERVER_FILESYSTEM_ROOT": "C:\\Users\\<USER>\\Desktop", "MCP_SERVER_FILESYSTEM_MOUNTS": "C:\\Users\\<USER>\\Documents,C:\\Users\\<USER>\\Downloads"}}, "fetch": {"command": "npx", "args": ["-y", "@tokenizin/mcp-npx-fetch"]}, "browser-tools": {"command": "cmd", "args": ["-y", "/c npx -y @agentdeskai/browser-tools-mcp@latest"], "enabled": true}, "context7": {"command": "cmd", "args": ["-y", "/c npx -y @upstash/context7-mcp@latest"]}, "playwright": {"command": "cmd", "args": ["-y", "/c npx -y @executeautomation/playwright-mcp-server"]}, "github": {"command": "cmd", "args": ["-y", "/c npx -y @modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "redis": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-redis", "redis://localhost:6379"]}, "@dpflucas/mysql-mcp-server": {"command": "npx", "args": ["mysql-mcp-server"], "env": {"MYSQL_DATABASE": "ivw_hotel", "MYSQL_HOST": "rm-2ze0m6aw8n0u117oj.mysql.rds.aliyuncs.com", "MYSQL_PASSWORD": "yQMtQr#R*afHt&Ps", "MYSQL_PORT": "3306", "MYSQL_USER": "dev_scrm_trip"}}, "interactive-feedback-mcp": {"command": "uv", "args": ["--directory", "D:\\data\\dify\\mcp-test-server\\interactive-feedback-mcp", "run", "server.py"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "terminal": {"command": "node", "args": ["D:\\data\\dify\\mcp-test-server\\terminal-mcp-server\\terminal\\dist\\index.js"], "env": {"PERMISSION_REQUIRED": "true"}}, "software-planning-tool": {"command": "cmd", "args": ["-y", "/c npx -y NightTrek/Software-planning-mcp"], "disabled": false, "autoApprove": []}, "addFunction": {"name": "MCP 服务器", "type": "stdio", "description": "", "isActive": true, "registryUrl": "", "command": "uv", "args": ["--directory", "D:\\data\\dify\\mcp-test-server\\mcp-server", "run", "main.py"]}, "LeetCode": {"command": "cmd", "args": ["-y", "/c -y @jinzcdev/leetcode-mcp-server --site cn"]}, "sqlite": {"command": "uv", "args": ["--directory", "parent_of_servers_repo/servers/src/sqlite", "run", "mcp-server-sqlite", "--db-path", "~/test.db"]}, "fetcher": {"command": "npx", "args": ["-y", "fetcher-mcp"]}, "天九共享 - API 文档": {"command": "cmd", "args": ["/c", "npx", "-y", "apifox-mcp-server@latest", "--site-id=5447262"]}, "edgeone-pages-mcp-server": {"url": "https://mcp-on-edge.edgeone.site/mcp-server"}}}