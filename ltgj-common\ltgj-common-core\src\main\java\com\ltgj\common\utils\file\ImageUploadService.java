package com.ltgj.common.utils.file;

import com.tem.imgserver.client.UploadResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 图片上传服务类
 * 提供多种方式的图片上传功能，统一封装ImgClientUtils的调用逻辑
 * 
 * <AUTHOR>
 * @Date 2025/6/5
 * @description: 图片上传服务API，支持File、MultipartFile和自定义文件名三种上传方式
 */
@Service
@Slf4j
public class ImageUploadService {

    @Autowired
    private ImgClientUtils imgClientUtils;

    /**
     * 使用File对象上传图片
     * 适用于本地文件上传场景，自动生成UUID文件名
     * 
     * @param localImagePath 本地图片文件路径
     * @param bizType 业务类型（相当于文件目录），如：test/hotel/images
     * @return ImageUploadResult 上传结果，包含完整访问URL和上传详情
     */
    public ImageUploadResult uploadImageWithFile(String localImagePath, String bizType) {
        log.info("=== 开始上传图片（File方式）===");
        log.info("📁 文件路径：{}", localImagePath);
        log.info("📂 业务类型：{}", bizType);
        
        ImageUploadResult result = new ImageUploadResult();
        
        try {
            // 1. 验证文件是否存在
            File imageFile = new File(localImagePath);
            if (!imageFile.exists()) {
                String errorMsg = "指定的图片文件不存在！路径：" + localImagePath;
                log.error("❌ 错误：{}", errorMsg);
                result.setSuccess(false);
                result.setErrorMessage(errorMsg);
                return result;
            }

            // 2. 验证是否为图片文件
            String fileName = imageFile.getName().toLowerCase();
            if (!ImgClientUtils.isImageFile(fileName)) {
                String errorMsg = "文件格式不支持！仅支持jpg、jpeg、png、gif、bmp格式，当前文件：" + fileName;
                log.error("❌ 错误：{}", errorMsg);
                result.setSuccess(false);
                result.setErrorMessage(errorMsg);
                return result;
            }

            // 3. 记录文件信息
            long fileSize = imageFile.length();
            log.info("📄 文件名称：{}", imageFile.getName());
            log.info("📊 文件大小：{} bytes ({} MB)", fileSize, String.format("%.2f", fileSize / 1024.0 / 1024.0));

            // 4. 调用ImgClientUtils静态方法上传图片
            log.info("🚀 正在上传图片...");
            UploadResult uploadResult = ImgClientUtils.uploadImg(imageFile, bizType);

            // 5. 处理上传结果
            return processUploadResult(uploadResult, "File方式上传");

        } catch (Exception e) {
            String errorMsg = "图片上传过程中发生异常：" + e.getMessage();
            log.error("❌ {}", errorMsg, e);
            result.setSuccess(false);
            result.setErrorMessage(errorMsg);
            return result;
        }
    }


    /**
     * 使用MultipartFile上传图片（Web接口专用 - 推荐方式）
     * 适用于Web控制器直接接收MultipartFile的场景
     *
     * @param multipartFile Web上传的MultipartFile对象
     * @param bizType 业务类型（相当于文件目录），如：test/hotel/images
     * @return ImageUploadResult 上传结果，包含完整访问URL和详细文件信息
     */
    public ImageUploadResult uploadImageWithMultipartFile(MultipartFile multipartFile, String bizType) {
        log.info("=== 开始上传图片（MultipartFile方式）===");
        log.info("📄 文件名称：{}", multipartFile.getOriginalFilename());
        log.info("📂 业务类型：{}", bizType);

        ImageUploadResult result = new ImageUploadResult();

        try {
            // 1. 验证MultipartFile是否有效
            if (multipartFile == null || multipartFile.isEmpty()) {
                String errorMsg = "上传文件为空或无效";
                log.error("❌ 错误：{}", errorMsg);
                result.setSuccess(false);
                result.setErrorMessage(errorMsg);
                return result;
            }

            // 2. 验证是否为图片文件
            String fileName = multipartFile.getOriginalFilename();
            if (fileName == null || !ImgClientUtils.isImageFile(fileName.toLowerCase())) {
                String errorMsg = "文件格式不支持！仅支持jpg、jpeg、png、gif、bmp格式，当前文件：" + fileName;
                log.error("❌ 错误：{}", errorMsg);
                result.setSuccess(false);
                result.setErrorMessage(errorMsg);
                return result;
            }

            // 3. 记录文件信息
            log.info("📊 文件大小：{} bytes ({} MB)", multipartFile.getSize(), String.format("%.2f", multipartFile.getSize() / 1024.0 / 1024.0));
            log.info("📋 内容类型：{}", multipartFile.getContentType());

            // 4. 调用ImgClientUtils静态方法上传图片
            log.info("🚀 正在上传图片...");
            UploadResult uploadResult = ImgClientUtils.upload(multipartFile, bizType);

            // 5. 处理上传结果
            return processUploadResult(uploadResult, "MultipartFile方式上传");

        } catch (Exception e) {
            String errorMsg = "图片上传过程中发生异常：" + e.getMessage();
            log.error("❌ {}", errorMsg, e);
            result.setSuccess(false);
            result.setErrorMessage(errorMsg);
            return result;
        }
    }


    /**
     * 批量上传多个图片文件（Web接口专用 - 批量处理推荐方式）
     * 适用于需要同时上传多个图片文件的场景，如相册上传、批量处理等
     *
     * @param multipartFiles Web上传的MultipartFile对象列表
     * @param bizType 业务类型（相当于文件目录），如：test/hotel/images
     * @return List&lt;ImageUploadResult&gt; 批量上传结果列表，每个元素对应一个文件的上传结果
     */
    public List<ImageUploadResult> uploadImagesWithMultipartFiles(List<MultipartFile> multipartFiles, String bizType) {
        log.info("=== 开始批量上传图片（MultipartFile批量方式）===");
        log.info("📦 文件数量：{}", multipartFiles != null ? multipartFiles.size() : 0);
        log.info("📂 业务类型：{}", bizType);

        List<ImageUploadResult> results = new ArrayList<>();

        // 1. 验证输入参数
        if (multipartFiles == null || multipartFiles.isEmpty()) {
            String errorMsg = "上传文件列表为空或无效";
            log.error("❌ 错误：{}", errorMsg);
            
            ImageUploadResult errorResult = new ImageUploadResult();
            errorResult.setSuccess(false);
            errorResult.setErrorMessage(errorMsg);
            results.add(errorResult);
            return results;
        }

        // 2. 统计信息
        int totalFiles = multipartFiles.size();
        int successCount = 0;
        int failureCount = 0;

        log.info("🚀 开始处理 {} 个文件的批量上传...", totalFiles);

        // 3. 逐个处理每个文件
        for (int i = 0; i < multipartFiles.size(); i++) {
            MultipartFile file = multipartFiles.get(i);
            
            log.info("📄 正在处理第 {}/{} 个文件：{}", i + 1, totalFiles, 
                    file != null ? file.getOriginalFilename() : "null");

            try {
                // 调用现有的单文件上传方法
                ImageUploadResult singleResult = uploadImageWithMultipartFile(file, bizType);
                results.add(singleResult);

                // 统计结果
                if (singleResult.isSuccess()) {
                    successCount++;
                    log.info("✅ 第 {}/{} 个文件上传成功：{}", i + 1, totalFiles, singleResult.getFullAccessUrl());
                } else {
                    failureCount++;
                    log.warn("❌ 第 {}/{} 个文件上传失败：{}", i + 1, totalFiles, singleResult.getErrorMessage());
                }

            } catch (Exception e) {
                // 处理单个文件上传异常，不影响其他文件
                failureCount++;
                String errorMsg = "第 " + (i + 1) + " 个文件上传过程中发生异常：" + e.getMessage();
                log.error("❌ {}", errorMsg, e);
                
                ImageUploadResult errorResult = new ImageUploadResult();
                errorResult.setSuccess(false);
                errorResult.setErrorMessage(errorMsg);
                results.add(errorResult);
            }
        }

        // 4. 输出批量上传统计结果
        log.info("=== 批量上传完成统计 ===");
        log.info("📊 总文件数：{}", totalFiles);
        log.info("✅ 成功数量：{}", successCount);
        log.info("❌ 失败数量：{}", failureCount);
        log.info("📈 成功率：{} %", totalFiles > 0 ? String.format("%.2f", (successCount * 100.0) / totalFiles) : "0.00");

        // 5. 输出成功文件的访问地址汇总
        if (successCount > 0) {
            log.info("🌐 成功上传的文件访问地址：");
            results.stream()
                    .filter(ImageUploadResult::isSuccess)
                    .forEach(result -> log.info("  - {}", result.getFullAccessUrl()));
        }

        return results;
    }

    /**
     * 使用自定义文件名上传图片（高级用法）
     * 适用于需要控制文件命名规则的场景
     * 
     * @param localImagePath 本地图片文件路径
     * @param businessPath 业务路径，如：test/hotel/custom
     * @param customFileName 自定义文件名，如：hotel_image_20250605.jpg
     * @param storeType 存储类型，如：local、oss等
     * @return ImageUploadResult 上传结果，包含完整访问URL和上传详情
     */
    public ImageUploadResult uploadImageWithCustomName(String localImagePath, String businessPath, String customFileName, String storeType) {
        log.info("=== 开始上传图片（自定义文件名方式）===");
        log.info("📁 源文件路径：{}", localImagePath);
        log.info("📂 业务路径：{}", businessPath);
        log.info("🎯 自定义文件名：{}", customFileName);
        log.info("💾 存储类型：{}", storeType);
        
        ImageUploadResult result = new ImageUploadResult();
        
        try {
            // 1. 验证文件是否存在
            File imageFile = new File(localImagePath);
            if (!imageFile.exists()) {
                String errorMsg = "指定的图片文件不存在！路径：" + localImagePath;
                log.error("❌ 错误：{}", errorMsg);
                result.setSuccess(false);
                result.setErrorMessage(errorMsg);
                return result;
            }

            // 2. 验证自定义文件名格式
            if (customFileName == null || customFileName.trim().isEmpty()) {
                String errorMsg = "自定义文件名不能为空";
                log.error("❌ 错误：{}", errorMsg);
                result.setSuccess(false);
                result.setErrorMessage(errorMsg);
                return result;
            }

            if (!ImgClientUtils.isImageFile(customFileName.toLowerCase())) {
                String errorMsg = "自定义文件名格式不支持！仅支持jpg、jpeg、png、gif、bmp格式，当前文件名：" + customFileName;
                log.error("❌ 错误：{}", errorMsg);
                result.setSuccess(false);
                result.setErrorMessage(errorMsg);
                return result;
            }

            // 3. 记录文件信息
            log.info("📄 原始文件名：{}", imageFile.getName());
            log.info("📊 文件大小：{} bytes", imageFile.length());

            // 4. 调用ImgClientUtils静态方法上传图片
            log.info("🚀 正在上传图片...");
            UploadResult uploadResult = ImgClientUtils.uploadImg2(imageFile, businessPath, customFileName, storeType);

            // 5. 处理上传结果
            return processUploadResult(uploadResult, "自定义文件名方式上传");

        } catch (Exception e) {
            String errorMsg = "图片上传过程中发生异常：" + e.getMessage();
            log.error("❌ {}", errorMsg, e);
            result.setSuccess(false);
            result.setErrorMessage(errorMsg);
            return result;
        }
    }

    /**
     * 处理上传结果的通用方法
     * 统一处理UploadResult，构建完整的访问URL和详细信息
     * 
     * @param uploadResult ImgClientUtils返回的上传结果
     * @param uploadMethod 上传方式描述（用于日志记录）
     * @return ImageUploadResult 统一格式的上传结果
     */
    private ImageUploadResult processUploadResult(UploadResult uploadResult, String uploadMethod) {
        ImageUploadResult result = new ImageUploadResult();
        
        if (uploadResult == null) {
            String errorMsg = "上传失败：返回结果为空";
            log.error("❌ {}", errorMsg);
            result.setSuccess(false);
            result.setErrorMessage(errorMsg);
            return result;
        }

        log.info("📊 上传结果详情：");
        log.info("  - 返回码：{}", uploadResult.getCode());
        log.info("  - 消息：{}", uploadResult.getMsg());

        if (uploadResult.getCode() == 0) {
            log.info("✅ 图片{}成功！", uploadMethod);
            
            // 构建完整的可访问URL地址
            String fullAccessUrl = buildFullImageUrl(uploadResult);
            
            // 设置成功结果
            result.setSuccess(true);
            result.setFullAccessUrl(fullAccessUrl);
            result.setUploadResult(uploadResult);
            
            // 记录详细信息
            log.info("🌐 完整访问地址：{}", fullAccessUrl);
            log.info("📋 可直接复制到浏览器访问：{}", fullAccessUrl);
            
            // 显示详细信息
            if (uploadResult.getFileId() != null) {
                log.info("  - 文件ID：{}", uploadResult.getFileId());
            }
            if (uploadResult.getFileName() != null) {
                log.info("  - 文件名：{}", uploadResult.getFileName());
            }
            if (uploadResult.getFileType() != null) {
                log.info("  - 文件类型：{}", uploadResult.getFileType());
            }
            if (uploadResult.getBizType() != null) {
                log.info("  - 业务类型：{}", uploadResult.getBizType());
            }
            if (uploadResult.getSize() != null) {
                log.info("  - 文件大小：{} bytes", uploadResult.getSize());
            }
            if (uploadResult.getUrlPath() != null) {
                log.info("  - 相对路径：{}", uploadResult.getUrlPath());
            }
            
        } else {
            String errorMsg = "图片上传失败！错误码：" + uploadResult.getCode() + "，错误信息：" + uploadResult.getMsg();
            log.error("❌ {}", errorMsg);
            result.setSuccess(false);
            result.setErrorMessage(errorMsg);
            result.setUploadResult(uploadResult);
        }
        
        return result;
    }

    /**
     * 构建完整的图片访问URL地址
     * 从UploadResult中提取路径信息，构建可直接访问的完整URL
     * 
     * @param uploadResult 上传结果对象
     * @return 完整的可访问URL地址
     */
    private String buildFullImageUrl(UploadResult uploadResult) {
        if (uploadResult == null) {
            return null;
        }
        
        // 尝试获取图片路径信息
        String urlPath = null;
        
        // 方式1：优先使用getUrlPath()方法
        if (uploadResult.getUrlPath() != null && !uploadResult.getUrlPath().isEmpty()) {
            urlPath = uploadResult.getUrlPath();
        }
        // 方式2：如果urlPath为空，尝试使用getFileKey()
        else if (uploadResult.getFileKey() != null && !uploadResult.getFileKey().isEmpty()) {
            urlPath = uploadResult.getFileKey();
        }
        
        if (urlPath == null || urlPath.isEmpty()) {
            log.warn("⚠️ 警告：无法获取图片路径信息");
            log.warn("  - getUrlPath(): {}", uploadResult.getUrlPath());
            log.warn("  - getFileKey(): {}", uploadResult.getFileKey());
            return "无法构建访问URL - 缺少路径信息";
        }
        
        // 检查是否已经是完整的URL
        if (urlPath.startsWith("http://") || urlPath.startsWith("https://")) {
            return urlPath;
        }
        
        // 构建完整URL - 使用注入的实例获取配置
        String baseUrl = imgClientUtils.getPathDoMain();
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        
        if (urlPath.startsWith("/")) {
            urlPath = urlPath.substring(1);
        }
        
        String fullUrl = baseUrl + urlPath;
        
        // 输出调试信息
        log.debug("🔧 URL构建详情：");
        log.debug("  - 基础URL：{}", imgClientUtils.getPathDoMain());
        log.debug("  - 相对路径：{}", uploadResult.getUrlPath());
        log.debug("  - FileKey：{}", uploadResult.getFileKey());
        log.debug("  - 构建结果：{}", fullUrl);
        
        return fullUrl;
    }

    /**
     * 图片上传结果封装类
     * 统一封装上传结果，提供更友好的API接口
     */
    public static class ImageUploadResult {
        /** 是否上传成功 */
        private boolean success;
        
        /** 完整的可访问URL地址 */
        private String fullAccessUrl;
        
        /** 错误信息（失败时） */
        private String errorMessage;
        
        /** 原始上传结果对象 */
        private UploadResult uploadResult;

        // Getter和Setter方法
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getFullAccessUrl() {
            return fullAccessUrl;
        }

        public void setFullAccessUrl(String fullAccessUrl) {
            this.fullAccessUrl = fullAccessUrl;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public UploadResult getUploadResult() {
            return uploadResult;
        }

        public void setUploadResult(UploadResult uploadResult) {
            this.uploadResult = uploadResult;
        }

        @Override
        public String toString() {
            return "ImageUploadResult{" +
                    "success=" + success +
                    ", fullAccessUrl='" + fullAccessUrl + '\'' +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", uploadResult=" + uploadResult +
                    '}';
        }
    }
} 