package com.ltgj.ivw.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;

@RestController
@RequestMapping("/api/ivw/meituan")
public class HotelMeiTuanCallbackController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(HotelMeiTuanCallbackController.class);
    @Autowired
    private HotelGnBaseService hotelGnBaseService;

    @ResponseBody
    @PostMapping("/hotel.poi.info.change.callback")
    public String hotelInfoChangeCallback(@RequestBody String s) {
        log.info("收到美团[酒店信息变更通知]: {}", s);
        try {
            JSONObject jsonObject1 = JSONObject.parseObject(s);
            JSONObject jsonObject = jsonObject1.getJSONObject("data");

//            String changeTime = jsonObject.getString("changeTime");
            Date incrementTime = DateUtils.getNowDate();
            JSONArray changeInfos = jsonObject.getJSONArray("changeInfos");

            changeInfos.stream().forEach(element -> {
                JSONObject changeInfo = (JSONObject) element;
                Long hotelId = changeInfo.getLong("hotelId");
                //HotelInfoMeituan hotelInfoMeituan = hotelInfoMeituanService.selectHotelInfoMeituanById(String.valueOf(hotelId));
                HotelGnBase hotelInfoMeituan = hotelGnBaseService.getById(PlatEnum.PLAT_MT, String.valueOf(hotelId));
                JSONArray changeTypes = changeInfo.getJSONArray("changeTypes");
                changeTypes.stream().forEach(changeType -> {
                    switch (String.valueOf(changeType)) {
                        case "1" :
                            if(Objects.isNull(hotelInfoMeituan)) {
                                HotelGnBase hotelInfo = new HotelGnBase();
                                hotelInfo.setId(String.valueOf(hotelId));
                                hotelInfo.setStatus(0);
                                hotelInfo.setIncrementStatus(3);
                                hotelInfo.setIncrementTime(incrementTime);
                                hotelInfo.setIncrementType(1);
                                hotelInfo.setUpdateTime(new Date());
                                hotelInfo.setUpdateBy("callback");
                                hotelInfo.setCreateTime(new Date());
                                hotelInfo.setCreateBy("callback");
                                hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_MT, Collections.singletonList(hotelInfo));
                            }else {
                                HotelGnBase ihotelInfo = new HotelGnBase();
                                ihotelInfo.setId(hotelInfoMeituan.getId());
                                ihotelInfo.setIncrementStatus(3);
                                ihotelInfo.setIncrementTime(incrementTime);
                                ihotelInfo.setIncrementType(2);
                                ihotelInfo.setUpdateTime(new Date());
                                ihotelInfo.setUpdateBy("callback");
                                hotelGnBaseService.updateByPrimaryKeySelective(PlatEnum.PLAT_MT,ihotelInfo);
                            }
                            break;
                        case "2" :
                            if(Objects.nonNull(hotelInfoMeituan)) {
                                HotelGnBase uhotelInfo = new HotelGnBase();
                                uhotelInfo.setId(hotelInfoMeituan.getId());
                                uhotelInfo.setIncrementStatus(1);
                                uhotelInfo.setIncrementTime(incrementTime);
                                uhotelInfo.setIncrementType(3);
                                hotelGnBaseService.updateByPrimaryKeySelective(PlatEnum.PLAT_MT,uhotelInfo);
                            }
                            break;
                    }
                });
            });
        } catch (Exception e) {
            log.error("处理美团酒店信息变更回调失败: ", e);
        }

        return "{\"code\":0,\"message\":\"成功\"}";
    }
}
