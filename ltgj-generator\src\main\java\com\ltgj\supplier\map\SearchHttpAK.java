/**
  * 默认：
  */

package com.ltgj.supplier.map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ltgj.supplier.map.request.BaiduMapPlaceSearchRequest;
import com.ltgj.supplier.map.response.BaiduMapApiResponse;
import com.ltgj.supplier.map.response.BaiduMapPlaceSearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class SearchHttpAK {

    public static String URL = "https://api.map.baidu.com/place/v2/suggestion?";

    public static String URL2 = "https://api.map.baidu.com/place/v2/search?";

    public static String AK = "6ABi9n7CGHu21KRHl523I4lpSpoTHarl";

    public static void main(String[] args) throws Exception {

        SearchHttpAK snCal = new SearchHttpAK();
//
//        Map<String, String> params = new LinkedHashMap<>();
//        params.put("query", "天安门");
//        params.put("region", "北京");
//        params.put("city_limit", "true");
//        params.put("output", "json");
//        params.put("ak", AK);
//
//
//

        BaiduMapPlaceSearchRequest request = new BaiduMapPlaceSearchRequest();
        request.setQuery("度假");
        request.setRegion("乐山");
        System.out.println(JSONObject.toJSONString(snCal.searchPlace2(request)));
    }

    /**
     * 默认ak
     * 选择了ak，使用IP白名单校验：
     * 根据您选择的AK已为您生成调用代码
     * 检测到您当前的ak设置了IP白名单校验
     * 您的IP白名单中的IP非公网IP，请设置为公网IP，否则将请求失败
     * 请在IP地址为xxxxxxx的计算发起请求，否则将请求失败
     */
    public String requestGetAK(String strUrl, Map<String, String> param) throws Exception {
        if (strUrl == null || strUrl.length() <= 0 || param == null || param.size() <= 0) {
            return null;
        }

        StringBuilder queryString = new StringBuilder();
        queryString.append(strUrl);
        for (Map.Entry<?, ?> pair : param.entrySet()) {
            queryString.append(pair.getKey()).append("=");
            //    第一种方式使用的 jdk 自带的转码方式  第二种方式使用的 spring 的转码方法 两种均可
            //    queryString.append(URLEncoder.encode((String) pair.getValue(), "UTF-8").replace("+", "%20") + "&");
            queryString.append(UriUtils.encode((String) pair.getValue(), "UTF-8")).append("&");
        }

        if (queryString.length() > 0) {
            queryString.deleteCharAt(queryString.length() - 1);
        }

        java.net.URL url = new URL(queryString.toString());
        System.out.println(queryString);
        URLConnection httpConnection = url.openConnection();
        httpConnection.connect();

        InputStreamReader isr = new InputStreamReader(httpConnection.getInputStream());
        BufferedReader reader = new BufferedReader(isr);
        StringBuilder buffer = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            buffer.append(line);
        }
        reader.close();
        isr.close();
        //System.out.println("AK: " + buffer);
        return buffer.toString();
    }

    /**
     * 搜索地点
     *
     * @param request 要求
     * @return {@link BaiduMapApiResponse }<{@link List }<{@link BaiduMapPlaceSearchResponse }>>
     */
    public BaiduMapApiResponse<List<BaiduMapPlaceSearchResponse>> searchPlace(BaiduMapPlaceSearchRequest request) {
        try {
            // 1. 构建请求参数
            String jsonResponse = handleBaiduMapApiRequest(request);
            // 3. 解析响应为通用响应对象
            return JSON.parseObject(jsonResponse, new TypeReference<BaiduMapApiResponse<List<BaiduMapPlaceSearchResponse>>>() {});
        } catch (Exception e) {
            log.error("请求百度地图API异常：",e);
            BaiduMapApiResponse<List<BaiduMapPlaceSearchResponse>> errorResponse = new BaiduMapApiResponse<>();
            errorResponse.setStatus(-1);
            errorResponse.setMessage("请求失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 搜索地点
     *
     * @param request 要求
     * @return {@link BaiduMapApiResponse }<{@link List }<{@link BaiduMapPlaceSearchResponse }>>
     */
    public BaiduMapApiResponse<List<BaiduMapPlaceSearchResponse>> searchPlace2(BaiduMapPlaceSearchRequest request) {
        try {
            // 1. 构建请求参数
            String jsonResponse = handleBaiduMapSearchApiRequest(request);
            // 3. 解析响应为通用响应对象
            return JSON.parseObject(jsonResponse, new TypeReference<BaiduMapApiResponse<List<BaiduMapPlaceSearchResponse>>>() {});
        } catch (Exception e) {
            log.error("请求百度地图API异常：",e);
            BaiduMapApiResponse<List<BaiduMapPlaceSearchResponse>> errorResponse = new BaiduMapApiResponse<>();
            errorResponse.setStatus(-1);
            errorResponse.setMessage("请求失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 处理百度地图api请求
     *
     * @param request 要求
     * @return {@link String }
     * @throws Exception 例外
     */
    private static String handleBaiduMapApiRequest(BaiduMapPlaceSearchRequest request) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("query", request.getQuery());
        params.put("region", request.getRegion());
        params.put("city_limit", "true");
        if (request.getTag() != null) {
            params.put("tag", request.getTag());
        }
        params.put("output", "json");
        params.put("ak", SearchHttpAK.AK); // 使用已定义的 AK

        // 2. 发起请求
        SearchHttpAK httpClient = new SearchHttpAK();
        return httpClient.requestGetAK(URL, params);
    }

    /**
     * 处理百度地图api请求
     *
     * @param request 要求
     * @return {@link String }
     * @throws Exception 例外
     */
    private static String handleBaiduMapSearchApiRequest(BaiduMapPlaceSearchRequest request) throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("query", request.getQuery());
        params.put("region", request.getRegion());
        params.put("city_limit", "true");
        if (request.getTag() != null) {
            params.put("tag", request.getTag());
        }
        params.put("output", "json");
        params.put("ak", SearchHttpAK.AK); // 使用已定义的 AK

        // 2. 发起请求
        SearchHttpAK httpClient = new SearchHttpAK();
        return httpClient.requestGetAK(URL2, params);
    }

}