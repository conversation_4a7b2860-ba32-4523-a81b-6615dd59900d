package com.ltgj.sdk.cozyTime.model.book;

import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import lombok.Data;

import java.util.List;

/**
 * 合作方调用该接口创建订单生成科坦订单号
 */
@Data
public class CozyTimeHotelApi20BookingRequest implements CozyTimeRequestInterface<CozyTimeHotelApi20BookingResponse> {
    @Override
    public String getRequestMethod() {
        return "POST";
    }

    @Override
    public String getEndpoint() {
        return "/hotel_api/2.0/booking";
    }

    @Override
    public Class<CozyTimeHotelApi20BookingResponse> getResponseClass() {
        return CozyTimeHotelApi20BookingResponse.class;
    }


    // 合作方订单号
    // 必填 true
    //合作方订单号，请确保唯一
    private String clientReference;

    // 联系人姓名
    // 必填 true
    private String contactName;

    // 联系人电话
    // 必填 true
    private String contactPhone;

    // 联系人邮箱
    // 必填 false
    private String contactEmail;

    // 预定房间数
    // 必填 true
    private Integer roomCount;

    // 下单凭证
    // 必填 true
    //下单凭证，从可订检查接口报文里面获取
    private String rateKey;

    // 特殊需求
    // 必填 false
    // 发送特殊要求至酒店(不保证能满足，以酒店安排为准)。请勿使用此字段传递任何敏感个人或财务信息。
    private String specialRequest;

    // 套餐id
    // 必填 false
    // 从房型可订检查接口报文里面获取
    private String pkgProductId;

    // 房间入住人列表
    // 必填 true
    private List<CozyTimeHotelApi20BookingRequestGuestRoomInfo> guestRoomList;

    @Data
    class CozyTimeHotelApi20BookingRequestGuestRoomInfo {
        // 入住人信息列表
        // 必填 true
        private List<CozyTimeHotelApi20BookingRequestGuestInfo> guests;

        // 房间号
        // 必填 true
        private Integer roomNumber;


    }

    @Data
    class CozyTimeHotelApi20BookingRequestGuestInfo {
        // 入住人类型 (AD:成人; CH:儿童，默认成人)
        // 必填 false

        private String type;

        // 入住人名 (海外订单传拼音或英文名)
        // 必填 true
        private String givenName;

        // 入住人姓名 (全名)
        // 必填 true
        // 举例：
        //中文：张志东
        //非中文：zhidong/zhang
        private String fullName;

        // 入住人姓 (海外订单传拼音或英文姓氏)
        // 必填 true
        // 海外订单，传姓氏拼音或者英文姓氏
        private String familyName;


    }


}
