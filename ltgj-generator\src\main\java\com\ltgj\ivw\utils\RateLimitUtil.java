package com.ltgj.ivw.utils;

import com.ltgj.limiter.LtgjRateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.stereotype.Component;

import java.util.function.Function;

/**
 * 限流工具类
 */
@Component
@Slf4j
public class RateLimitUtil {


    @Autowired
    private LtgjRateLimiter limiter;

    public Object fetchDataFunction(String key, long second, long num, Function<Integer, Object> function) {
        int tryNum = 1;
        RRateLimiter rRateLimiter = limiter.getLimiter(key, second, num);
        boolean tryAcquire = rRateLimiter.tryAcquire(1);
        while (!tryAcquire) {
            log.info("当前供应商渠道【{}】限流.......睡眠500ms", key);
            try {
                Thread.sleep(500);
            } catch (InterruptedException ex) {
                log.info("睡眠异常。。。。。。。。。。");
            }
            tryNum++;
            tryAcquire = rRateLimiter.tryAcquire(1);
        }
        return function.apply(tryNum);
    }
}
