package com.ltgj.ivw.service;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.ltgj.sdk.cozyTime.CozyTimeSdkApi;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20CitiesResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.Method;

/**
 * CozyTimeSdkApi测试类
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CozyTimeSdkApiTest {

    @Autowired
    private CozyTimeSdkApi cozyTimeSdkApi;
    
    @Value("${cozyTime.url}")
    private String apiUrl;

    @Value("${cozyTime.partnerCode}")
    private String partnerCode;
    
    @Value("${cozyTime.connectTimeout:60000}")
    private int connectTimeout;
    
    @Value("${cozyTime.readTimeout:60000}")
    private int readTimeout;
    
    private final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    
    // 通过反射获取HttpMethod枚举
    private Class<?> httpMethodClass;
    private Object httpMethodGet;
    private Object httpMethodPost;

    @Before
    public void setup() {
        // 在测试前打印关键配置信息，帮助诊断问题
        System.out.println("===== 科坦SDK配置信息 =====");
        System.out.println("API地址: " + apiUrl);
        System.out.println("合作商编码: " + partnerCode);
        System.out.println("连接超时(ms): " + connectTimeout);
        System.out.println("读取超时(ms): " + readTimeout);
        System.out.println("===========================\n");
        
        try {
            // 通过反射获取HttpMethod枚举类
            httpMethodClass = Class.forName("com.ltgj.sdk.cozyTime.CozyTimeSdkApi$HttpMethod");
            httpMethodGet = Enum.valueOf((Class<Enum>) httpMethodClass, "GET");
            httpMethodPost = Enum.valueOf((Class<Enum>) httpMethodClass, "POST");
            System.out.println("反射获取HttpMethod枚举成功：" + httpMethodGet + ", " + httpMethodPost);
        } catch (Exception e) {
            System.err.println("反射获取HttpMethod枚举失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试获取科坦城市列表（自动选择HTTP方法）
     */
    @Test
    public void testGetCityList() {
        try {
            // 中国的国家编码
            String countryCode = "CN";
            
            System.out.println("开始获取科坦城市列表，国家编码: " + countryCode);
            
            // 调用城市列表接口，自动选择HTTP方法
            CozyTimeHotelContentApi20CitiesResponse cityResponse = cozyTimeSdkApi.getCityList(countryCode);
            
            // 打印结果
            if (cityResponse != null) {
                printCityResult(cityResponse, "自动选择方法");
            } else {
                System.out.println("获取科坦城市列表失败，返回为空");
            }
        } catch (Exception e) {
            System.err.println("测试获取城市列表时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试使用GET方法获取科坦城市列表
     */
    @Test
    public void testGetCityListWithGet() {
        try {
            // 中国的国家编码
            String countryCode = "CN";
            
            System.out.println("开始使用GET方法获取科坦城市列表，国家编码: " + countryCode);
            
            // 使用反射调用带HttpMethod参数的getCityList方法
            Method getCityListMethod = cozyTimeSdkApi.getClass().getMethod("getCityList", String.class, httpMethodClass);
            CozyTimeHotelContentApi20CitiesResponse cityResponse = 
                (CozyTimeHotelContentApi20CitiesResponse) getCityListMethod.invoke(cozyTimeSdkApi, countryCode, httpMethodGet);
            
            // 打印结果
            if (cityResponse != null) {
                printCityResult(cityResponse, "GET方法");
            } else {
                System.out.println("使用GET方法获取科坦城市列表失败，返回为空");
            }
        } catch (Exception e) {
            System.err.println("测试使用GET方法获取城市列表时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试使用POST方法获取科坦城市列表
     */
    @Test
    public void testGetCityListWithPost() {
        try {
            // 中国的国家编码
            String countryCode = "CN";
            
            System.out.println("开始使用POST方法获取科坦城市列表，国家编码: " + countryCode);
            
            // 使用反射调用带HttpMethod参数的getCityList方法
            Method getCityListMethod = cozyTimeSdkApi.getClass().getMethod("getCityList", String.class, httpMethodClass);
            CozyTimeHotelContentApi20CitiesResponse cityResponse = 
                (CozyTimeHotelContentApi20CitiesResponse) getCityListMethod.invoke(cozyTimeSdkApi, countryCode, httpMethodPost);
            
            // 打印结果
            if (cityResponse != null) {
                printCityResult(cityResponse, "POST方法");
            } else {
                System.out.println("使用POST方法获取科坦城市列表失败，返回为空");
            }
        } catch (Exception e) {
            System.err.println("测试使用POST方法获取城市列表时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 打印城市列表结果
     * 
     * @param response 城市响应
     * @param methodDesc 方法描述
     */
    private void printCityResult(CozyTimeHotelContentApi20CitiesResponse response, String methodDesc) {
        System.out.println("===== 科坦城市列表获取成功 (" + methodDesc + ") =====");
        System.out.println("国家编码: " + response.getCountryCode());
        
        int cityCount = (response.getCities() != null) ? response.getCities().size() : 0;
        System.out.println("城市数量: " + cityCount);
        
        // 打印城市详情
        if (response.getCities() != null && !response.getCities().isEmpty()) {
            System.out.println("\n===== 城市详情示例（前5个） =====");
            int count = 0;
            for (CozyTimeHotelContentApi20CitiesResponse.City city : response.getCities()) {
                System.out.println("城市编码: " + city.getCityCode());
                System.out.println("城市名称: " + city.getName());
                System.out.println("城市中文名: " + city.getNameCN());
                System.out.println("省份编码: " + city.getProvinceCode());
                System.out.println("省份名称: " + city.getProvinceNameCN());
                
                int districtCount = (city.getDistricts() != null) ? city.getDistricts().size() : 0;
                System.out.println("区县数量: " + districtCount);
                System.out.println("------------------------");
                
                if (++count >= 5) {
                    break;
                }
            }
        }
        
        // 输出完整JSON
        System.out.println("\n===== 完整JSON数据 =====");
        System.out.println(gson.toJson(response));
    }
    
    /**
     * 测试签名生成
     */
    @Test
    public void testGenerateSignature() {
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            
            // 使用反射获取generateSignature方法
            Method generateSignatureMethod = null;
            try {
                // 先尝试获取公共方法
                generateSignatureMethod = cozyTimeSdkApi.getClass().getMethod("generateSignature", String.class, long.class);
            } catch (NoSuchMethodException e) {
                // 如果公共方法不存在，尝试获取私有方法
                generateSignatureMethod = cozyTimeSdkApi.getClass().getDeclaredMethod("generateSignature", String.class, long.class);
                generateSignatureMethod.setAccessible(true);
            }
            
            String signature = (String) generateSignatureMethod.invoke(cozyTimeSdkApi, partnerCode, timestamp);
            System.out.println("===== 测试签名生成 =====");
            System.out.println("合作商编码: " + partnerCode);
            System.out.println("时间戳: " + timestamp);
            System.out.println("生成的签名: " + signature);
        } catch (Exception e) {
            System.err.println("测试签名生成时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
}