package com.ltgj.ivw.service.impl;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.domain.HotelInfoGj;
import com.ltgj.ivw.domain.HotelInfoGjDaolvNew;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelInfoGjCommonBaseMapper;
import com.ltgj.ivw.service.IHotelInfoGjDaoLvNewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 道旅国际酒店信息Service业务层处理（新架构）
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service("hotelInfoGjDaolvNewService")
@Slf4j
public class HotelInfoGjDaolvNewServiceImpl extends BaseHotelServiceImpl<HotelInfoGj, HotelInfoGjDaolvNew> implements IHotelInfoGjDaoLvNewService {

    @Autowired
    private HotelInfoGjCommonBaseMapper<HotelInfoGjDaolvNew> gjMapper;

    @Override
    protected Class<HotelInfoGjDaolvNew> getEntityClass() {
        return HotelInfoGjDaolvNew.class;
    }

    protected PlatEnum getPlatEnum(){
        return PlatEnum.PLAT_DL;
    };

    // ==================== 实现抽象的数据访问方法 ====================

    @Override
    protected List<HotelInfoGj> doSelectListWithParams(HotelInfoGj entity, Map<String, Object> queryParams) {
        log.debug("道旅国际酒店查询列表，参数：{}", queryParams);
        // 使用国际酒店专用的Mapper
        return gjMapper.selectHotelInfoGjCommonList(entity, getEntityClass());
    }

    @Override
    protected HotelInfoGj doSelectById(String id) {
        log.debug("道旅国际酒店根据ID查询：{}", id);
        return gjMapper.selectHotelInfoGjCommonById(id, getEntityClass());
    }

    @Override
    protected int doInsert(HotelInfoGj entity) {
        log.debug("道旅国际酒店插入：{}", entity.getName());
        return gjMapper.insertHotelInfoGjCommon(entity, getEntityClass());
    }

    @Override
    protected int doUpdate(HotelInfoGj entity) {
        log.debug("道旅国际酒店更新：{}", entity.getName());
        return gjMapper.updateHotelInfoGjCommon(entity, getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        log.debug("道旅国际酒店删除，数量：{}", ids.length);
        return gjMapper.deleteHotelInfoGJCommon(ids, getEntityClass());
    }

    // ==================== 业务逻辑定制 ====================

    @Override
    public List<?> exportData(HotelInfoGj entity, HotelInfoGjDaolvNew searchParams) {
        return Collections.emptyList();
    }

    @Override
    public Class<?> getExportEntityClass() {
        return null;
    }

    @Override
    public String getExportFileName(HotelInfoGjDaolvNew searchParams) {
        return "";
    }

    @Override
    public Map<String, Object> buildDefaultQueryParams(HotelInfoGj entity, HotelInfoGjDaolvNew searchParams) {
        Map<String, Object> params = new HashMap<>();
        
        // 道旅国际特有的查询参数构建逻辑
        // 添加表名
        params.put("tableName", getTableName());
        
        // 如果有特定的查询条件，可以在这里添加
        if (entity != null && entity.getName() != null) {
            params.put("hotelName", entity.getName());
        }
        
        log.debug("道旅国际酒店查询参数：{}", params);
        return params;
    }

    @Override
    public String validateEntity(HotelInfoGj entity, boolean isInsert) {
        // 道旅国际特有的验证逻辑
        if (entity == null) {
            return "酒店信息不能为空";
        }
        
        if (entity.getName() == null || entity.getName().trim().isEmpty()) {
            return "酒店名称不能为空";
        }
        
        // 道旅国际特有的验证规则
        log.debug("道旅国际酒店信息验证通过：{}", entity.getName());
        
        return null; // 验证通过
    }

    @Override
    public HotelInfoGj preprocessEntity(HotelInfoGj entity, HotelInfoGjDaolvNew searchParams, boolean isInsert) {
        // 道旅国际特有的预处理逻辑
        if (isInsert) {
            log.debug("道旅国际酒店信息新增预处理：{}", entity.getName());
            // 设置默认状态等
            if (entity.getStatus() == null) {
                entity.setStatus(1);
            }
        } else {
            log.debug("道旅国际酒店信息更新预处理：{}", entity.getName());
        }
        
        return entity;
    }

    @Override
    public String validateBeforeDelete(String[] ids, HotelInfoGjDaolvNew searchParams) {
        // 道旅国际删除前验证
        if (ids == null || ids.length == 0) {
            return "请选择要删除的数据";
        }
        
        log.debug("道旅国际酒店删除前验证通过，数量：{}", ids.length);
        return null;
    }

    @Override
    public void afterInsert(HotelInfoGj entity, int result) {
        log.info("道旅国际酒店新增完成：{}，结果：{}", entity.getName(), result);
    }

    @Override
    public void afterUpdate(HotelInfoGj entity, int result) {
        log.info("道旅国际酒店更新完成：{}，结果：{}", entity.getName(), result);
    }

    @Override
    public void afterDelete(String[] ids, int result) {
        log.info("道旅国际酒店删除完成，数量：{}，结果：{}", ids.length, result);
    }

    @Override
    public String getTableName() {
        return "hotel_gj_dl"; // 道旅国际酒店表名
    }

    @Override
    public String getBusinessTitle() {
        return "道旅国际酒店信息";
    }

    /**
     * 道旅国际特有的业务方法：平台映射到本地
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @Override
    public AjaxResult platToLocal(String[] ids) throws Exception {
        log.info("道旅国际平台映射到本地处理：{}", ids);
        // 实现道旅国际特有的平台映射逻辑
        // 这里可以调用现有的业务逻辑
        return null;
    }

}