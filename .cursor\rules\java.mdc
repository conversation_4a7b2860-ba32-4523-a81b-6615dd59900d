---
description: 
globs: 
alwaysApply: false
---
---
description: Enforces best practices for Java development, covering code style, performance, security, and testing. Provides guidelines for writing clean, maintainable, and efficient Java code.
globs: *.java
---
- # Java Best Practices

  This document outlines comprehensive best practices for Java development, covering code organization, common patterns, performance considerations, security, testing, common pitfalls, and tooling. Adhering to these guidelines will help you write clean, maintainable, efficient, and secure Java code.

- ## 1. Code Organization and Structure

  - ### 1.1 Directory Structure

    - **Maven Standard Layout:**  Use the standard Maven directory structure for most projects.  This provides a consistent and predictable layout that's easily understood by other developers and tools.
      
      src/
        main/
          java/
            com/example/  <- Your package structure starts here
          resources/
        test/
          java/
            com/example/
          resources/
        pom.xml  <- Maven project file
      
    - **Gradle Layout:** Gradle supports the Maven layout and provides other ways to configure the source directories. Choose a layout that best fits your project's needs.

    - **Package by Feature:** Organize packages by feature rather than by layer (e.g., controllers, services, repositories).  This improves cohesion and reduces dependencies between features.
      
      src/
        main/
          java/
            com/example/
              user/
                UserController.java
                UserService.java
                UserRepository.java
              product/
                ProductController.java
                ProductService.java
                ProductRepository.java
      
    - **Modularization:**  For large projects, consider using Java modules (Jigsaw, introduced in Java 9) to improve encapsulation and reduce dependencies.

  - ### 1.2 File Naming Conventions

    - **Classes and Interfaces:** Use `PascalCase` (e.g., `UserController`, `UserService`).
    - **Methods and Variables:** Use `camelCase` (e.g., `getUserById`, `userName`).
    - **Constants:** Use `UPPER_SNAKE_CASE` (e.g., `MAX_RETRIES`, `DEFAULT_TIMEOUT`).
    - **Packages:** Use all lowercase (e.g., `com.example.user`).
    - **Avoid abbreviations:** Use meaningful and descriptive names.

  - ### 1.3 Module Organization

    - **`module-info.java`:**  Use `module-info.java` to define module dependencies and exported packages.  This allows for strong encapsulation and controlled access to internal APIs.
    - **Explicit Dependencies:**  Declare all module dependencies explicitly in `module-info.java`.  Avoid relying on transitive dependencies.
    - **Minimize Exports:** Only export the packages that are intended for public use.  Keep internal packages hidden from other modules.

  - ### 1.4 Component Architecture

    - **Dependency Injection:** Use dependency injection (DI) to manage component dependencies. Frameworks like Spring and Guice simplify DI.
    - **Inversion of Control (IoC):**  Apply IoC to decouple components and improve testability.
    - **Layered Architecture:**  Structure your application into layers (e.g., presentation, business logic, data access).  This promotes separation of concerns and maintainability.
    - **Microservices:** For large, complex applications, consider a microservices architecture. This allows for independent development, deployment, and scaling of individual services.

  - ### 1.5 Code Splitting

    - **Feature Toggles:** Use feature toggles to enable or disable features at runtime. This allows for incremental deployment and testing of new features.
    - **Dynamic Loading:**  Use dynamic class loading to load modules or components on demand. This can reduce the initial startup time and memory footprint of your application.
    - **Conditional Compilation:** Use conditional compilation (e.g., with Maven profiles) to include or exclude code based on the environment. This allows for different configurations for development, testing, and production.

- ## 2. Common Patterns and Anti-patterns

  - ### 2.1 Design Patterns

    - **Singleton:**  Use the Singleton pattern sparingly and only when a single instance of a class is truly required. Consider dependency injection as an alternative.
    - **Factory:**  Use the Factory pattern to create objects without specifying their concrete classes.  This promotes loose coupling and allows for easy substitution of different implementations.
    - **Strategy:** Use the Strategy pattern to encapsulate different algorithms or behaviors. This allows you to switch between algorithms at runtime.
    - **Observer:**  Use the Observer pattern to define a one-to-many dependency between objects. This allows for loose coupling and easy addition of new observers.

    - **Template Method:** Use the Template Method pattern to define the skeleton of an algorithm in a base class, allowing subclasses 