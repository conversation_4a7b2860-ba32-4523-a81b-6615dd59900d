package com.ltgj.ivw.service;

import com.ltgj.common.es.hotel.model.DatePrice;
import com.ltgj.common.es.hotel.model.EsHotelInfo;
import com.ltgj.common.es.hotel.service.EsHotelInfoManager;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.service.impl.EsHotelInfoServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import org.testng.collections.Lists;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;


/**
 * 酒店基础数据查询单元测试
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class EsHotelInfoServiceImplTest {

    @Autowired
    private EsHotelInfoService esHotelInfoService;

    @Autowired
    private EsHotelInfoManager esHotelInfoManager;

    @Test
    public void testUpdateMinPrice()  {
        EsHotelInfo esHotelInfo = new EsHotelInfo();
        esHotelInfo.setHotelId("200000400101185");
        List datePriceDtos = Lists.newArrayList(new DatePrice("2024-10-31", new BigDecimal(87), "2000077"));
        esHotelInfo.setDatePriceList(datePriceDtos);
        esHotelInfoService.updateMinPrice(esHotelInfo);
    }

    @Test
    public void testIndex() throws Exception {
        esHotelInfoManager.createEsHotelInfoIndex();
    }

    @Test
    public void syncHotelToEs() {
        esHotelInfoService.syncHotelToEs("2");
    }

    @Test
    public void syncHotelJdJdbToEs() {
        esHotelInfoService.syncHotelToEs();
    }

    @Test
    public void queryHotelInfo() {
        // int size = esHotelInfoService.getInterfacePlats("0", 1, 100).size();
        // System.out.println("酒店基础表zh_jd_jdb_0初始查询数据量:"+size);

//        List<JdJdb> interfacePlats = esHotelInfoService.getInterfacePlats("0", 0, 10);
//        interfacePlats.forEach(jdJdb -> System.out.println(jdJdb.getId()));
    }

    @Test
    public void findByBeanCount() {
        System.out.println("esHotelInfoService.findByBeanCount(\"0\") = " + esHotelInfoService.findByBeanCount("0"));
    }


    @Test
    public void testParse() {
        String date = new EsHotelInfoServiceImpl().convertToDateFormat("2024-06-28 00:00:00");
        System.out.println("date = " + date);
        String date1 = new EsHotelInfoServiceImpl().convertToDateFormat("2024/02");
        System.out.println("date1 = " + date1);
        String date2 = new EsHotelInfoServiceImpl().convertToDateFormat("1713888000000");
        System.out.println("date2 = " + date2);
        String date3 = new EsHotelInfoServiceImpl().convertToDateFormat("2023-06-01");
        System.out.println("date3 = " + date3);

    }

    @Test
    public void testFindByIds() {
        List<JdJdb> jdJdbByIds = esHotelInfoService.getJdJdbByIds(Arrays.asList("200000400101185", "200000400101186"));
        jdJdbByIds.forEach(jdJdb -> System.out.println(jdJdb.getId()));


    }

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testRedis() {
        stringRedisTemplate.opsForValue().set("aa", "10");
        stringRedisTemplate.opsForValue().increment("aa");
        String s = stringRedisTemplate.opsForValue().get("aa");
        System.out.println(s);
        String s1 = stringRedisTemplate.opsForValue().get("bb");
        System.out.println(s1);
    }


}