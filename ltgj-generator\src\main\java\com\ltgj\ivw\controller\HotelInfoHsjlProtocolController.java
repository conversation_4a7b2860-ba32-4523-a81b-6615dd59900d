package com.ltgj.ivw.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.*;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.HsjlxyApi;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnHsjlXy;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 红色加力酒店数据Controller
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@RestController
@RequestMapping("/ivw/hsjlxy")
@Slf4j
public class HotelInfoHsjlProtocolController extends AbstractIvwController<HotelGnBase, HotelGnHsjlXy, IHotelInfoHsjlxyService> {
    @Autowired
    private IHotelInfoHsjlxyService hotelInfoHsjlxyService;
    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private IHotelCityMappingService hotelCityMappingService;
    @Autowired
    private IJdJdbMappingService jdJdbMappingService;
    @Autowired
    private IHotelUpdateRecodeService hotelUpdateRecodeService;
    @Autowired
    private IJdJdbHsjlCorpService jdJdbHsjlCorpService;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;

    @Autowired
    private IHotelCityService hotelCityService;
    @Autowired
    private HotelInfoHsjlElongIdService hotelInfoHsjlElongIdService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private HotelGnBaseService hotelGnBaseService;
    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;
    @Autowired
    private HotelGnPreMappingService hotelGnPreMappingService;

    //红色加力酒店数据  请求100次/分钟
    private static int maxCountDetailHsjlxy = 10;

    //获取酒店更新状态信息
    @PreAuthorize("@ss.hasPermi('ivw:hsjlxy:query')")
    @GetMapping("/getInfoStatus")
    public AjaxResult getInfoStatus() {
        Map map = hotelInfoHsjlService.getStatus(PlatEnum.PLAT_HSJL_XY);
        log.info("状态查询:{}", JSON.toJSONString(map));
        return AjaxResult.success(map);
    }

    @PreAuthorize("@ss.hasPermi('ivw:hsjlxy:query')")
    @GetMapping("/updateData")
    public void updateData(String partnerCode, String secureKey, String corpId) throws Exception {
        hotelInfoHsjlxyService.updateHsjlxyAll(corpId, getUsername());
    }

    @PreAuthorize("@ss.hasPermi('ivw:hsjlxy:query')")
    @GetMapping("/resetStatus")
    public void resetStatus() {
        hotelInfoHsjlxyService.resetStatus();
    }

    public HotelCityMapping getMapping(String platId) {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
        mapping.setPlatNum(platId);
        List<HotelCityMapping> list = hotelCityMappingService.selectHotelCityMappingList(mapping);
        if(list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    /**
     * 平台酒店导入本地
     */
    @PreAuthorize("@ss.hasPermi('ivw:hsjlxy:remove')")
    @Log(title = "红色加力酒店数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult platToLocal(@PathVariable String[] ids) throws Exception {
        return hotelService.platToLocal(ids);
    }

    /**
     * 导入本地酒店
     * 1.insert jd_jdb_mapping
     * 2.insert zh_jd_jdb_mapping
     */
    @PreAuthorize("@ss.hasPermi('ivw:hsjlxy:remove')")
    @Log(title = "红色加力酒店数据", businessType = BusinessType.UPDATE)
	@PostMapping("/impt/list")
    public AjaxResult platToLocals(@RequestBody String body) {
        if (StringUtils.isBlank(body)) {
            return AjaxResult.success();
        }
        new Thread(() -> {
            String[] ids = body.split(",");
            if (ids.length <= 0) {
                return ;
            }
            for (String id : ids) {
                if (StringUtils.isBlank(id)) {
                    continue;
                }
                HotelInfo hotelInfo;
                JdJdb jdLocal;
                try {
                    hotelInfo = hotelInfoHsjlxyService.selectHotelInfoHsjlxyById(id);
                    jdLocal = new JdJdb();
                    jdLocal.setId(PlatEnum.PLAT_HSJL_XY.getValue()+hotelInfo.getId());
                    jdLocal.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                    jdLocal.setCreatedate(new Date());
                    jdLocal.setJdmc(hotelInfo.getName());
                    jdLocal.setJdmcEn(hotelInfo.getNameEn());
                    jdLocal.setJdlx(hotelInfo.getTypeId());
                    jdLocal.setJddz(hotelInfo.getAddressLine());
                    jdLocal.setJddh(hotelInfo.getPhone());
                    jdLocal.setImgUrl(hotelInfo.getImage());
                    jdLocal.setLonGoogle(hotelInfo.getLonGg());
                    jdLocal.setLatGoogle(hotelInfo.getLatGg());
                    jdLocal.setLonBaidu(hotelInfo.getLonBd());
                    jdLocal.setLatBaidu(hotelInfo.getLatBd());
                    jdLocal.setLonGaode(hotelInfo.getLonGd());
                    jdLocal.setLatGaode(hotelInfo.getLatGd());

                    String cityId = hotelInfo.getCityId();
                    if(StringUtils.isEmpty(cityId)){
                        log.info("酒店城市为空: {}", JsonUtil.toStr(hotelInfo));
                        throw new Exception("酒店所在城市为空，请先维护正确以避免后续BUG！");
                    }
                    HotelCityMapping hotelCityMapping = new HotelCityMapping();
                    hotelCityMapping.setPlatNum(cityId);
                    hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
                    List<HotelCityMapping> hotelCityMappingList = hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
                    if(hotelCityMappingList.size() == 0) {
                        log.info("酒店城市映射为空: {}", JsonUtil.toStr(hotelInfo));
                        log.info("酒店城市映射为空: {}", JsonUtil.toStr(hotelCityMapping));
                        log.info("酒店城市映射为空: {}", JsonUtil.toStr(hotelCityMappingList));
                        throw new Exception("该酒店所在城市映射关系不存在，请先维护正确以避免后续BUG！");
                    }
                    hotelCityMapping = hotelCityMappingList.get(0);
                    jdLocal.setCityId(hotelCityMapping.getLocalId());
                    jdLocal.setCityName(hotelCityMapping.getCityName());

//                    JdJdbMapping jdJdbMapping = new JdJdbMapping();
//                    jdJdbMapping.setPlatId(hotelInfo.getId());
//                    jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
//                    if(jdJdbMappingService.selectJdJdbMappingList(jdJdbMapping).size() > 0) {
//                        throw new Exception("酒店映射已存在，确认要导入请先删除！");
//                    }

                    jdLocal.setBrandName(hotelInfo.getBrandId());

                    jdLocal.setBusinessZoneName(hotelInfo.getBusinessDistricts());
                    jdLocal.setJdxj(hotelInfo.getStarInfo());
                    if(StringUtils.isNotEmpty(hotelInfo.getReserve2())) {
                        jdLocal.setScore(BigDecimal.valueOf(Double.valueOf(hotelInfo.getReserve2())).divide(BigDecimal.valueOf(10)));
                    }
                    jdLocal.setJtmc(hotelInfo.getGroupId());
                    jdLocal.setKysj(hotelInfo.getOpenDate());
                    jdLocal.setZhzxsj(hotelInfo.getDecorationDate());
                    if(StringUtils.isNotEmpty(hotelInfo.getReserve1())) {
                        jdLocal.setMinPrice(BigDecimal.valueOf(Double.valueOf(hotelInfo.getReserve1())));
                    }
                    jdLocal.setReserve1(hotelInfo.getDescription());
                    jdLocal.setNoticeInfo(hotelInfo.getNoticeInfo());
                    jdLocal.setPolicyInfo(hotelInfo.getPolicyInfo());
                    jdLocal.setFacilitiesInfo(hotelInfo.getFacilitiesInfo());
                    jdJdbService.insertJdJdb(jdLocal);
                    String localId = PlatEnum.PLAT_HSJL_XY.getValue() + hotelInfo.getId();
//                    jdJdbMapping.setLocalId(localId);
//                    jdJdbMapping.setJdName(jdLocal.getJdmc());
//                    jdJdbMappingService.insertJdJdbMapping(jdJdbMapping);

                    ZhJdJdbMapping dbMapping = zhJdJdbMappingService.findByLocalIdAndInterfacePlat(localId,Long.parseLong(PlatEnum.PLAT_HSJL_XY.getValue()));
                    if(null == dbMapping){
                        //添加映射关系
                        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                        zhJdJdbMapping.setLocalId(localId);
                        zhJdJdbMapping.setInterfacePlat(Long.parseLong(PlatEnum.PLAT_HSJL_XY.getValue()));
                        zhJdJdbMapping.setPlatId(hotelInfo.getId());
                        zhJdJdbMapping.setJdName(jdLocal.getJdmc());
                        zhJdJdbMapping.setPlatJdName(hotelInfo.getName());
                        zhJdJdbMapping.setStatus(0);
                        zhJdJdbMapping.setSaveDate(new Date());
                        zhJdJdbMapping.setIsGnGj(1);
                        log.info("ZhJdJdbMapping酒店映射表数据为空ID {},开始插入数据", localId);
                        int insert = zhJdJdbMappingService.insert(zhJdJdbMapping);
                        log.info("ZhJdJdbMapping酒店映射表数据插入完成 localId:{} count： {},开始插入数据",localId,insert);
                    }

                    //更新映射状态
                    HotelInfoHsjlxy hi = new HotelInfoHsjlxy();
                    hi.setId(hotelInfo.getId());
                    hi.setStatus(8);
                    hotelInfoHsjlxyService.updateHotelInfoHsjlxy(hi);
                    log.info("开始处理红色加力协议最低价与上传ES");

//                    for (int i = 0; i < 10; i++) {
//                        try {
//                            log.info("准备插入zhJdJdb:{}", JsonUtil.toStr(jdLocal));
//                            zhJdJdbService.insertZhJdJdb(jdLocal, jdLocal.getCityId());
//                            log.info("插入zhJdJdb完成, 开始查询最低价: jdId={}, hotelId={}", jdLocal.getId(), hotelInfo.getId());
//                            jdJdbService.dealListMinPriceHSJLXYNew(jdLocal.getId(), Long.valueOf(hotelInfo.getId()));
//                            log.info("查询最低价完成, 开始推送es: zhJdJdb:{}", JsonUtil.toStr(jdLocal));
//                            esHotelInfoService.extracted(Lists.newArrayList(jdLocal));
//                            EsHotelInfo esHotelInfo = new EsHotelInfo();
//                            esHotelInfo.setHotelId(jdLocal.getId());
//                            esHotelInfo.setDatePriceList(zhJdJdbMinPriceService.getHotelMinPriceList(jdLocal.getId(), Integer.valueOf(PlatEnum.PLAT_HSJL.getValue())));
//                            log.info("开始推送es最低价: zhJdJdb:{}", JsonUtil.toStr(esHotelInfo));
//                            esHotelInfoService.batchUpdateMinPrice(esHotelInfo);
//                            log.info("处理红色加力协议最低价与上传ES结束");
//                            break;
//                        } catch (Exception e) {
//                            log.error("红色加力协议酒店同步ES失败: {}", JsonUtil.toStr(jdLocal));
//                            log.error("红色加力协议酒店同步ES失败:", e);
//                            try {
//                                Thread.sleep(10000);
//                            } catch (InterruptedException e1) {
//                                log.info("睡眠失败: ", e1);
//                            }
//                        }
//                    }
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.info("睡眠失败: ", e);
                    }
                } catch (Exception e) {
                    log.error("导入红色加力协议酒店失败: " + id + ", error: ", e);
                }
            }
            log.info("批量导入红色加力协议酒店结束: {}", body);
        }).start();
        return AjaxResult.success();
    }


    public void updateHsjlxyAll(String partnerCode, String secureKey, String corpId) throws Exception {
        if(HotelUpdateStatus.status == 1){
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        RLock rLock = redissonClient.getLock("hotel_hsjl_xy_update");
        if (!rLock.tryLock(10, TimeUnit.SECONDS)) {
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        if((StringUtils.isNotEmpty(partnerCode) && StringUtils.isEmpty(secureKey)) ||
                (StringUtils.isEmpty(partnerCode) && StringUtils.isNotEmpty(secureKey))) {
            throw new Exception("CODE和密钥参数错误！");
        }

        HotelUpdateStatus.status = 1;
        HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
        hotelUpdateRecode.setId("HSJL" + MyTools.getTimesNo());
        hotelUpdateRecode.setYl2(PlatEnum.PLAT_HSJL_XY.getValue());
        hotelUpdateRecode.setTimeStart(new Date());
        hotelUpdateRecode.setUpdateBy(getUsername());
        while (true) {
            if (HotelUpdateStatus.expStatusHSJL_XY == 0) {
                new Thread(() -> {
                    try {
                        insertHotelHSJL(hotelUpdateRecode, 1, 2, partnerCode, secureKey, corpId);
                    } catch (Exception e) {
                        log.error("插入酒店信息失败: e");
                    }
                }).start();
            }
            if (HotelUpdateStatus.expStatusHSJL_XY == 2) {
                new Thread(() -> updateHotelHSJL(hotelUpdateRecode, 3, 4, partnerCode, secureKey)).start();
                break;
            }
            Thread.sleep(10000);
            System.err.println("红色加力协议-等待10秒执行下一轮操作！当前状态"+HotelUpdateStatus.expStatusHSJL_XY);
        }
    }

    //插入红色加力酒店数据
    @RequestMapping("/insertHotelHSJL")
    public void insertHotelHSJL(HotelUpdateRecode recode, int start, int end, String partnerCode, String secureKey, String corpId) {
        boolean insertCorpFlag = false;
        if(StringUtils.isNotEmpty(partnerCode) && StringUtils.isNotEmpty(secureKey)) {
            insertCorpFlag = true;
        }
        AtomicLong hotelCount = new AtomicLong(0);

        HotelUpdateStatus.expStatusHSJL_XY = start;
        List<String> idsAll = new ArrayList<>();
        Map<String, HotelGnBase> map = new HashMap<>();
        List<HotelGnBase> list =  hotelGnBaseService.getAll(PlatEnum.PLAT_HSJL_XY);
//        RList<Object> hotelHsjlUpdateList = redissonClient.getList("hotel_hsjl_update_list");
        for (HotelGnBase hsjl : list) {
            map.put(hsjl.getId(), hsjl);
        }
        HotelCity hc = new HotelCity();
        hc.setReserve1(PlatEnum.PLAT_HSJL.getValue());
//        List<HotelCity> hcs = hotelCityService.selectHotelCityList(hc);
        List<HotelCity> hcs = new ArrayList<>();
        String cityInfoText = HsjlxyApi.queryCityList(Boolean.TRUE, partnerCode, secureKey);
        log.info("城市列表返回报文信息：{}", cityInfoText);
        if(StringUtils.isNotEmpty(cityInfoText)) {
            JSONObject bussinessResponse = JSONObject.parseObject(cityInfoText).getJSONObject("bussinessResponse");
            JSONArray provinces = bussinessResponse.getJSONArray("provinces");
            for (int i = 0; i < provinces.size(); i++) {
                JSONObject province = provinces.getJSONObject(i);
                log.info("省份：{}", province);
                JSONArray cities = province.getJSONArray("citys");
                for (int j = 0; j < cities.size(); j++){
                    JSONObject city = cities.getJSONObject(j);
                    String parentCityCode = city.getString("parentCityCode");
                    log.info("parentCityCode：{}", parentCityCode);
//                    if(StringUtils.isNotEmpty(parentCityCode)){
//                        continue;
//                    }
//                    String cityId = city.getString("cityId");
                    String cityCode = city.getString("cityCode");
//                    log.info("cityId：{},cityCode:{}", cityId,cityCode);
                    HotelCity hotelCity = new HotelCity();
                    hotelCity.setCityId(cityCode);
                    hotelCity.setCityName(city.getString("cityName"));
                    hcs.add(hotelCity);

                }
            }

        }
        log.info("红色加力协议获取城市信息数量 cityList:{}",hcs.size());
        int cityCount = 0;
        for(HotelCity hotelCity:hcs){
            cityCount++;
//            if(StringUtils.isEmpty(hotelCity.getLocationId())){
                log.info("cityId开始执行 当前执行数量：{},cityId:{},cityName:{}",cityCount,hotelCity.getCityId(),hotelCity.getCityName());
                String infoText = HsjlxyApi.queryHotelIdList(hotelCity.getCityId(), 1, partnerCode, secureKey);
                log.info("根据城市查询酒店列表,ID：{} 返回报文infoText:{}",hotelCity.getCityId(),infoText);
                JSONObject obj = JSONObject.parseObject(infoText);
                if(obj.getString("returnCode").equals("009")){
                    log.error("调用供应商失败");
                    try {
                        Thread.sleep(5000);
                        log.info("继续执行");
                        infoText = HsjlxyApi.queryHotelIdList(hotelCity.getCityId(), 1, partnerCode, secureKey);
                        log.info("根据城市查询酒店列表第二次,ID：{} 返回报文infoText:{}",hotelCity.getCityId(),infoText);
                        obj = JSONObject.parseObject(infoText);
                    } catch (InterruptedException e) {
                        log.error("重复执行失败，直接抛出异常 cityId:{} errorMessage:{}",hotelCity.getCityId(),e.getMessage(),e);
                        throw new RuntimeException(e);
                    }
                }
                JSONObject info = obj.getJSONObject("bussinessResponse");
                int pageCount = 0;
                JSONArray ids = null;
                try{
                    if (info != null) {
                        pageCount = info.getInteger("totalPage");
                        ids = info.getJSONArray("hotelIds");
                        if(pageCount <= 0){
                            log.error("页数为0，cityId:{}",hotelCity.getCityId());
                            Thread.sleep(1000);
                            continue;
                        }
                    } else {
                        log.error("info 为空，cityId: {}", hotelCity.getCityId());
                        Thread.sleep(1000);
                        continue; // 或者抛出异常
                    }
                }catch (Exception e){
                    log.error("休眠异常 errorMessage:{}",e.getMessage(),e);
                }

            HotelGnHsjlXy hotelInfoHsjlxy = new HotelGnHsjlXy();
            hotelCount.addAndGet(ids.size());
                for (int j = 0; j < ids.size(); j++) {
                    String id = ids.getString(j);
                    idsAll.add(id);
                    if (map.get(id) == null) {
                        try {
                            HotelGnBase temp = hotelGnBaseService.getById(PlatEnum.PLAT_HSJL_XY, id);
                            if (Objects.nonNull(temp)) {
                                log.info("红色加力协议数据存在 更新delete状态 hotelId:{}",id);
                                hotelInfoHsjlxy.setIsDelete(0);
                                hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL_XY, Collections.singletonList(temp));
                            } else {
                                log.info("红色加力协议数据不存在 开始插入到数据中 hotelId:{}",id);
                                hotelInfoHsjlxy.setId(id);
                                hotelInfoHsjlxy.setReserve9("1");
                                if (insertCorpFlag) {
                                    insertCorp(id, corpId);
                                    hotelInfoHsjlxy.setReserve8("1");
                                }
                                hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL_XY, Collections.singletonList(hotelInfoHsjlxy));
                                log.info("红色加力协议酒店插入成功 id:{}",hotelInfoHsjlxy.getId());
                            }
                            HotelUpdateStatus.hsjlIdsCountAdd++;
                        } catch (Exception e) {
                            log.error("插入红色加力协议酒店数据失败: ", e);
                        }
                    } else {
//                        idsAll.add(id);
                        log.info("红色加力协议数据已经存在 直接添加到缓存中 hotelId:{}",id);
                    }
                }
                log.info("首次执行城市Id：{}，酒店数量：{},pageCount：{}",hotelCity.getCityId(),ids.size(),pageCount);

                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    log.error("休眠异常 errorMessage:{}",e.getMessage(),e);
                    throw new RuntimeException(e);
                }


                for (int k = 2; k <= pageCount; k++) {
                    try {
                        infoText = HsjlxyApi.queryHotelIdList(hotelCity.getCityId(), k, partnerCode, secureKey);
                        info = JSONObject.parseObject(infoText).getJSONObject("bussinessResponse");
                        if (info == null) {
                            continue;
                        }
                        ids = info.getJSONArray("hotelIds");
                        hotelCount.addAndGet(ids.size());
                        for (int m = 0; m < ids.size(); m++) {
                            try {
                                String id = ids.getString(m);
                                idsAll.add(id);
                                if (map.get(id) == null) {
                                    HotelGnBase temp = hotelGnBaseService.getById(PlatEnum.PLAT_HSJL_XY, id);
                                    if (Objects.nonNull(temp)) {
                                        log.info("红色加力协议数据已存在, 更新删除状态: {}", JsonUtil.toStr(temp));
                                        temp.setIsDelete(0);
                                        hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL_XY, Collections.singletonList(temp));
                                    } else {
                                        log.info("红色加力协议数据不存在 开始插入到数据中 k:{},酒店ID:{}",k,id);
                                        hotelInfoHsjlxy.setId(id);
                                        hotelInfoHsjlxy.setReserve9("1");
                                        if (insertCorpFlag) {
                                            insertCorp(id, corpId);
                                            hotelInfoHsjlxy.setReserve8("1");
                                        }
                                        hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL_XY, Collections.singletonList(hotelInfoHsjlxy));
                                        log.info("红色加力协议酒店插入成功 K:{},id:{}",k,hotelInfoHsjlxy.getId());
                                    }
                                    HotelUpdateStatus.hsjlIdsCountAdd++;
                                } else {
//                                    idsAll.add(id);
                                    log.info("红色加力协议数据已经存在 直接添加到缓存中 k:{},酒店ID:{}",k,id);
                                }
                            } catch (Exception e) {
                                log.error("处理红色加力协议新增/更新失败: ", e);
                            }
                        }
                        Thread.sleep(5000);
                    } catch (Exception e) {
                        log.error("插入HSJL协议进异常了 errorMessage:", e);
                        continue;
                    }
                }
//            }
        }
        //删除已失效酒店及映射，如果是企业更新，则不删除
//        if(!insertCorpFlag) {
//            log.info("删除已失效酒店开始: {}", JsonUtil.toStr(idsAll));
//            for (HotelInfoHsjlxy hotel : list) {
//                if (!idsAll.contains(hotel.getId())) {
//                    if(hotel.getReserve8() == null) {   //非企业酒店，删除
//                        hotelInfoHsjlxyService.deleteHotelInfoHsjlxyById(hotel.getId());
//                        HotelUpdateStatus.hsjlxyDeleteJS++;
//                        JdJdbMapping queryMapping = new JdJdbMapping();
//                        queryMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
//                        queryMapping.setPlatId(hotel.getId());
//                        for (JdJdbMapping jdJdbMapping : jdJdbMappingService.selectJdJdbMappingList(queryMapping)) {
//                            jdJdbMappingService.deleteJdJdbMappingByIdV1(jdJdbMapping.getId());
//                        }
//                    }
//                }
//            }
//        }
        log.info("获取到红色加力协议总计酒店数量: {}", hotelCount.get());

        HotelUpdateStatus.expStatusHSJL_XY = end;
        recode.setCountDel(HotelUpdateStatus.hsjlxyDeleteJS);
        recode.setCountAdd(HotelUpdateStatus.hsjlxyIdsCountAdd);
        hotelUpdateRecodeService.insertHotelUpdateRecode(recode);
    }

    //更新红色加力酒店详情
    @RequestMapping("/updateHotelHSJL")
    public void updateHotelHSJL(HotelUpdateRecode recode, int start, int end, String partnerCode, String secureKey) {
        HotelUpdateStatus.expStatusHSJL_XY = start;
        HotelInfoHsjlxy hotelInfoHsjl = new HotelInfoHsjlxy();
//        hotelInfoHsjl.setStatus(0);
        List<HotelInfoHsjlxy> hotelInfoHsjlList = hotelInfoHsjlxyService.selectHotelInfoHsjlxyList2(hotelInfoHsjl);
        logger.info("红色加力协议状态为0数据：{}",hotelInfoHsjlList.size());
        HotelUpdateStatus.hsjlxyDetailCount = hotelInfoHsjlList.size();
        List<List<Long>> idsList = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (int i = 0;i<hotelInfoHsjlList.size();i++){
            HotelInfoHsjlxy hsjl = hotelInfoHsjlList.get(i);
            if(ids.size() == maxCountDetailHsjlxy){
                idsList.add(ids);
                ids = new ArrayList<>();
            }
            // 红色加力有酒店数据, 直接拷贝使用
            HotelInfoHsjl hotelInfoHsjl2 = null/*hotelInfoHsjlService.selectHotelInfoHsjlById(hsjl.getId())*/;
            if (Objects.nonNull(hotelInfoHsjl2)) {
                updateHsjlXyInfo(hotelInfoHsjl2);
                if(i == hotelInfoHsjlList.size()-1){
                    idsList.add(ids);
                }
                continue;
            } else {
                ids.add(Long.valueOf(hsjl.getId()));
            }
            if(i == hotelInfoHsjlList.size()-1){
                idsList.add(ids);
            }
        }
        for(int j = 0; j<idsList.size(); j++){
            List<Long> ids0 = idsList.get(j);
            try{
                String infoText = HsjlxyApi.queryHotelInfo(ids0, partnerCode, secureKey);
                logger.info("红色加力协议详情接口返回数据：{}",infoText);
                JSONObject info = JSONObject.parseObject(infoText);
                if(info.getString("returnCode").equals("000")){
                    JSONArray hotelInfos = info.getJSONObject("bussinessResponse").getJSONArray("hotelInfos");
                    if(hotelInfos != null) {
                        for(int k = 0; k < hotelInfos.size(); k++){
                            try {
                                HotelInfoHsjlxy hotelInfoHsjl1 = new HotelInfoHsjlxy();
                                JSONObject hotelInfo = hotelInfos.getJSONObject(k);
                                hotelInfoHsjl1.setId(hotelInfo.getString("hotelId"));
                                hotelInfoHsjl1.setAddressLine(hotelInfo.getString("address"));
                                hotelInfoHsjl1.setBusinessDistricts(hotelInfo.getString("businessName"));
                                String cityId = hotelInfo.getString("city");
                                HotelCity city = hotelCityService.selectByHsjlLocation(cityId);
                                if (Objects.isNull(city)) {
                                    hotelInfoHsjl1.setCityId(cityId);
                                    hotelInfoHsjl1.setCityName(hotelInfo.getString("cityName"));
                                    hotelInfoHsjl1.setReserve5(hotelInfo.getString("distinct"));
                                    hotelInfoHsjl1.setAreaId(hotelInfo.getString("distinctName"));
                                } else {
                                    hotelInfoHsjl1.setReserve5(cityId);
                                    hotelInfoHsjl1.setAreaId(hotelInfo.getString("cityName"));
                                    hotelInfoHsjl1.setCityId(city.getCityId());
                                    hotelInfoHsjl1.setCityName(city.getCityName());
                                }
//                                hotelInfoHsjl1.setAreaId(hotelInfo.getString("distinctName"));
                                hotelInfoHsjl1.setLonBd(hotelInfo.getBigDecimal("longitude"));
                                hotelInfoHsjl1.setLatBd(hotelInfo.getBigDecimal("latitude"));
                                hotelInfoHsjl1.setOpenDate(hotelInfo.getString("praciceDate"));
                                hotelInfoHsjl1.setDecorationDate(hotelInfo.getString("fitmentDate"));
                                hotelInfoHsjl1.setPhone(hotelInfo.getString("telephone"));
                                hotelInfoHsjl1.setName(hotelInfo.getString("hotelName"));
                                hotelInfoHsjl1.setNameEn(hotelInfo.getString("hotelEngName"));
                                hotelInfoHsjl1.setDescription(hotelInfo.getString("hotelIntroduce"));
                                hotelInfoHsjl1.setStarInfo(hotelInfo.getString("StringhotelStar"));
                                hotelInfoHsjl1.setGroupId(hotelInfo.getString("parentHotelGroupName"));
                                hotelInfoHsjl1.setBrandId(hotelInfo.getString("plateName"));
                                JSONObject policyInfo = new JSONObject();
                                policyInfo.put("inOutTime", hotelInfo.getString("checkInTim")+"-"+hotelInfo.getString("checkOutTime"));
                                hotelInfoHsjl1.setPolicyInfo(policyInfo.toString());
                                hotelInfoHsjl1.setImage(hotelInfo.getString("appearancePicUrl"));
                                hotelInfoHsjl1.setFacilitiesInfo(hotelInfo.getString("hotelFacilityNew"));
                                HotelInfoHsjlxy hotelInfoHsjlxy = hotelInfoHsjlxyService.selectHotelInfoHsjlxyById(hotelInfoHsjl1.getId());
                                if (Objects.nonNull(hotelInfoHsjlxy) && Objects.equals(0, hotelInfoHsjlxy.getStatus())) {
                                    hotelInfoHsjl1.setStatus(1);
                                }
                                hotelInfoHsjlxyService.updateHotelInfoHsjlxy(hotelInfoHsjl1);
                                HotelUpdateStatus.hsjlxyDetailCountUpdate++;
                            }catch (Exception e) {
                                logger.error("红色加力协议数据更新失败 errorMessage:{}",e.getMessage(),e);
//                                TXTUtil.writeTXT(new Date() + " 获取红色加力详情进异常了： " + infoText,
//                                        ConstantList.LOG_PATH, "log.txt");
                            }
                        }
                    }
                }else if(info.getString("returnCode").equals("009")){
                    j--;
                    System.out.println(infoText);
                    logger.error("红色加力协议数据更新失败 {}",infoText);
                }else {
                    logger.error("红色加力协议数据更新失败 {}",infoText);
//                    TXTUtil.writeTXT(new Date() +" 获取红色加力详情进else了： " + infoText,
//                            ConstantList.LOG_PATH, "log.txt");
                }
            }catch (Exception e) {
                j--;
                logger.error("红色加力协议数据更新失败 errorMessage:{}",e.getMessage(),e);

//                e.printStackTrace();
//                TXTUtil.writeTXT(new Date() + " 获取付红色加力详情进异常了： " +e,
//                        ConstantList.LOG_PATH, "log.txt");
                continue;
            }finally {
                try {
                    Thread.sleep(6000);
                } catch (InterruptedException e) {
                    log.error("红色加力协议休眠异常 {}",e.getMessage(),e);
                }
            }
        }
        HotelUpdateStatus.expStatusHSJL_XY = end;
        HotelUpdateStatus.status = 0;
        recode.setCountFail(HotelUpdateStatus.hsjlxyDetailCountUpdate);
        recode.setCountAdd(HotelUpdateStatus.hsjlxyIdsCountAdd);
        recode.setTimeEnd(new Date());
        hotelUpdateRecodeService.updateHotelUpdateRecode(recode);
        RLock rLock = redissonClient.getLock("hotel_hsjl_xy_update");
        rLock.unlock();
    }

    private void updateHsjlXyInfo(HotelInfoHsjl hotelInfoHsjl2) {
        HotelInfoHsjlxy hotelInfoHsjl1 = new HotelInfoHsjlxy();
        hotelInfoHsjl1.setId(hotelInfoHsjl2.getId());
        hotelInfoHsjl1.setAddressLine(hotelInfoHsjl2.getAddressLine());
        hotelInfoHsjl1.setBusinessDistricts(hotelInfoHsjl2.getBusinessDistricts());
        hotelInfoHsjl1.setCityId(hotelInfoHsjl2.getCityId());
        hotelInfoHsjl1.setCityName(hotelInfoHsjl2.getCityName());
        hotelInfoHsjl1.setAreaId(hotelInfoHsjl2.getAreaId());
        hotelInfoHsjl1.setLonBd(hotelInfoHsjl2.getLonBd());
        hotelInfoHsjl1.setLatBd(hotelInfoHsjl2.getLatBd());
        hotelInfoHsjl1.setOpenDate(hotelInfoHsjl2.getOpenDate());
        hotelInfoHsjl1.setDecorationDate(hotelInfoHsjl2.getDecorationDate());
        hotelInfoHsjl1.setPhone(hotelInfoHsjl2.getPhone());
        hotelInfoHsjl1.setName(hotelInfoHsjl2.getName());
        hotelInfoHsjl1.setNameEn(hotelInfoHsjl2.getNameEn());
        hotelInfoHsjl1.setDescription(hotelInfoHsjl2.getDescription());
        hotelInfoHsjl1.setStarInfo(hotelInfoHsjl2.getStarInfo());
        hotelInfoHsjl1.setGroupId(hotelInfoHsjl2.getGroupId());
        hotelInfoHsjl1.setBrandId(hotelInfoHsjl2.getBrandId());
//        JSONObject policyInfo = new JSONObject();
//        policyInfo.put("inOutTime", hotelInfo.getString("checkInTim")+"-"+hotelInfo.getString("checkOutTime"));
        hotelInfoHsjl1.setPolicyInfo(hotelInfoHsjl2.getPolicyInfo());
        hotelInfoHsjl1.setImage(hotelInfoHsjl2.getImage());
        hotelInfoHsjl1.setFacilitiesInfo(hotelInfoHsjl2.getFacilitiesInfo());
        hotelInfoHsjl1.setStatus(8);

        ZhJdJdbMapping zhJdJdbMappingxy = new ZhJdJdbMapping();
        zhJdJdbMappingxy.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
        zhJdJdbMappingxy.setPlatId(hotelInfoHsjl2.getId());
        List<ZhJdJdbMapping> zhJdJdbMappingxyList = zhJdJdbMappingService.selectZhJdJdbMappingList(zhJdJdbMappingxy);
        if (CollectionUtils.isEmpty(zhJdJdbMappingxyList)) {
            ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
            zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
            zhJdJdbMapping.setPlatId(hotelInfoHsjl2.getId());
            List<ZhJdJdbMapping> zhJdJdbMappings = zhJdJdbMappingService.selectZhJdJdbMappingList(zhJdJdbMappingxy);
            if (CollectionUtils.isEmpty(zhJdJdbMappings)) {
                // 红色加力也没匹配, 则协议无法匹配
                hotelInfoHsjl1.setStatus(1);
            } else {
                // 使用红色加力的匹配逻辑直接匹配协议酒店
                ZhJdJdbMapping zhJdJdbMapping1 = zhJdJdbMappings.get(0);
                ZhJdJdbMapping insertZhJdJdbMapping = new ZhJdJdbMapping();
                insertZhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
                insertZhJdJdbMapping.setLocalId(zhJdJdbMapping1.getLocalId());
                insertZhJdJdbMapping.setPlatId(zhJdJdbMapping1.getLocalId());
                insertZhJdJdbMapping.setIsGnGj(zhJdJdbMapping1.getIsGnGj());
                insertZhJdJdbMapping.setJdName(zhJdJdbMapping1.getJdName());
                insertZhJdJdbMapping.setStatus(0);
                insertZhJdJdbMapping.setSaveDate(new Date());
                zhJdJdbMappingService.insert(insertZhJdJdbMapping);
            }
        }

        hotelInfoHsjlxyService.updateHotelInfoHsjlxy(hotelInfoHsjl1);
    }


    public List<HotelCityMapping> getCitys2(String interfacePlat) {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setInterfacePlat(Long.valueOf(interfacePlat));
        return hotelCityMappingService.selectHotelCityMappingList(mapping);
    }

    public List<String> getCityStr() {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setLocalData(1);
        List<HotelCityMapping> mappingList = hotelCityMappingService.selectHotelCityMappingList(mapping);
        List<String> cityStr = new ArrayList<>();
        for (HotelCityMapping hotelCityMapping : mappingList) {
            cityStr.add(hotelCityMapping.getCityName());
        }
        return cityStr;
    }

    /**
     * 红色加力协议 酒店数据映射
     * 只是新增zh_jd_jdb_mapping表数据
     * @return
     * @throws Exception
     */
    @RequestMapping("/mapping")
    public AjaxResult mapping() throws Exception {
        if(HotelUpdateStatus.status == 1){
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        Map<String, JdJdbMapping> jdjdbmappingMap = new ConcurrentHashMap<>();
        HotelUpdateStatus.status = 1;
        HotelUpdateStatus.expStatusHSJL_XY = 11;
        HotelInfoHsjlxy search = new HotelInfoHsjlxy();
        search.setStatus(1);//未映射数据
        List<HotelInfoHsjlxy> list = hotelInfoHsjlxyService.selectHotelInfoHsjlxyList2(search);
        HotelUpdateStatus.hsjlxyMappingCount = list.size();
        logger.info("红色加力协议酒店映射数据 {}",HotelUpdateStatus.hsjlxyMappingCount);

        List<HotelInfoHsjlxy> newList = new ArrayList<>();
        List<List<HotelInfoHsjlxy>> lists = ListUtil.subList(list, 1000);
        for (List<HotelInfoHsjlxy> l : lists) {
            List<String> idList = l.stream().map(HotelInfoHsjlxy::getId).collect(Collectors.toList());
            //根据红色加力ID批量查询艺龙映射关系数据
            List<HotelInfoHsjlElongId> containIdList = hotelInfoHsjlElongIdService.selectIdInStatus(idList);
            List<String> jdJdbInList = jdJdbService.selectInIds(containIdList.stream().map(e->PlatEnum.PLAT_EL.getValue()+e.getElongId()).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(containIdList)) {
                Map<String, HotelInfoHsjlElongId> relationMap
                    = containIdList.stream().collect(Collectors.toMap(HotelInfoHsjlElongId::getId, Function.identity()));
                List<String> updateSuccessList = new ArrayList<>();
                for (HotelInfoHsjlxy hotelInfoHsjl : l) {
                    HotelInfoHsjlElongId hotelInfoHsjlElongId = relationMap.get(hotelInfoHsjl.getId());
                    if (Objects.nonNull(hotelInfoHsjlElongId) && jdJdbInList.contains(PlatEnum.PLAT_EL.getValue() + hotelInfoHsjlElongId.getElongId())) {
                        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                        zhJdJdbMapping.setLocalId(PlatEnum.PLAT_EL.getValue() + hotelInfoHsjlElongId.getElongId());
                        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
                        zhJdJdbMapping.setPlatId(hotelInfoHsjl.getId());
                        zhJdJdbMapping.setJdName(hotelInfoHsjl.getName());
                        zhJdJdbMapping.setStatus(0);
                        zhJdJdbMapping.setSaveDate(new Date());
                        zhJdJdbMapping.setIsGnGj(1);
                        logger.info("红色加力协议酒店zh_jd_jdb_mapping插入前数据 {}", JSONObject.toJSONString(zhJdJdbMapping));
                        //插入前校验重复
                        log.info("红色加力协议酒店zh_jd_jdb_mapping localId {},interfacePlat {}", zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat());
                        if(null == zhJdJdbMappingService.findByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(),zhJdJdbMapping.getInterfacePlat())) {
                            zhJdJdbMappingService.insert(zhJdJdbMapping);
                            updateSuccessList.add(hotelInfoHsjl.getId());
                        }
                        HotelUpdateStatus.hsjlxyMappingCounted.incrementAndGet();
                    } else {
                        newList.add(hotelInfoHsjl);
                    }
                }
                if (!CollectionUtils.isEmpty(updateSuccessList)) {
                    hotelInfoHsjlxyService.updateSuccess(updateSuccessList);
                }
            }
        }
        list = newList;

        if(HotelUpdateStatus.jdMap.get("10119") == null) {
            HotelCityMapping city = new HotelCityMapping();
            city.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_EL.getValue()));
            List<HotelCityMapping> hotelCityMappings = hotelCityMappingService.selectHotelCityMappingList(city);
            JdJdb jdJdb = new JdJdb();
            for (HotelCityMapping cityMapping : hotelCityMappings) {
                jdJdb.setCityId(cityMapping.getLocalId());//城市ID
                List<JdJdb> jdJdbs = jdJdbService.selectJdJdbList(jdJdb);
                HotelUpdateStatus.jdMap.put(cityMapping.getLocalId(), jdJdbs);
                log.info("艺龙城市酒店数据 cityId:{},酒店数量：{}", jdJdb.getCityId(),jdJdbs.size());
            }
        }
        List<String> cityStr = getCityStr();

        int tc = 10;
        AtomicInteger threadSize = new AtomicInteger(tc);

        int count = list.size();
        int tcount = count / tc;

        for(int t = 0; t < tc; t++) {
            List<HotelInfoHsjlxy> listSub0;
            if(t == tc - 1) {
                listSub0 = list.subList(t * tcount, count);
            }else {
                listSub0 = list.subList(t * tcount, (t + 1) * tcount);
            }
            List<HotelInfoHsjlxy> listSub = listSub0;
            new Thread(() -> {
                try {
                    HotelCityMapping hotelCityMapping = new HotelCityMapping();
                    hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
                    for (int j = 0; j < listSub.size(); j++) {
                        HotelInfoHsjlxy hotelInfoHsjlxy = listSub.get(j);
                        hotelCityMapping.setPlatNum(hotelInfoHsjlxy.getCityId());
                        List<HotelCityMapping> mappings = hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
                        log.info("cityId:{},mappings size {}",hotelCityMapping.getPlatNum(),mappings.size());
                        if (mappings.isEmpty()) {
                            log.info("红色加力协议hotelCityMapping未查询到 cityId:{}",hotelCityMapping.getPlatNum());
                            HotelUpdateStatus.hsjlxyMappingCountFail.incrementAndGet();
                            hotelInfoHsjlxy.setStatus(9);
                            hotelInfoHsjlxyService.updateHotelInfoHsjlxy(hotelInfoHsjlxy);
                            continue;
                        }
                        log.info("根据城市ID查询艺龙酒店信息,cityId:{}",mappings.get(0).getLocalId());
                        List<JdJdb> jdList = HotelUpdateStatus.jdMap.get(mappings.get(0).getLocalId());
                        if (jdList == null || jdList.isEmpty()) {
                            log.info("根据红色加力协议城市ID未查询到艺龙酒店数据 cityId：{}",mappings.get(0).getLocalId());
                            HotelUpdateStatus.hsjlxyMappingCountFail.incrementAndGet();
                            hotelInfoHsjlxy.setStatus(9);
                            hotelInfoHsjlxyService.updateHotelInfoHsjlxy(hotelInfoHsjlxy);
                            continue;
                        }
                        log.info("匹配开始，城市ID：{},酒店数量：{}",mappings.get(0).getLocalId(),jdList.size());
                        String namePlat = MatchRate.getOk(hotelInfoHsjlxy.getName());
                        String addressPlat = MatchRate.getOk(hotelInfoHsjlxy.getAddressLine());
                        String telPlat = MatchRate.getOk(hotelInfoHsjlxy.getPhone());
                        BigDecimal lonGDPlat = hotelInfoHsjlxy.getLonGd();
                        BigDecimal latGDPlat = hotelInfoHsjlxy.getLatGd();
                        BigDecimal lonBDPlat = hotelInfoHsjlxy.getLonBd();
                        BigDecimal latBDPlat = hotelInfoHsjlxy.getLatBd();
                        JdJdbMapping jdJdbMapping = new JdJdbMapping();
                        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
                        for (int i = 0; i < jdList.size(); i++) {
                            JdJdb jdJdb = jdList.get(i);
                            String name = MatchRate.getOk(jdJdb.getJdmc());
                            String address = MatchRate.getOk(jdJdb.getJddz());
                            String tel = MatchRate.getOk(jdJdb.getJddh());
                            BigDecimal lonGD = jdJdb.getLonGaode();
                            BigDecimal latGD = jdJdb.getLatGaode();
                            BigDecimal lonBD = jdJdb.getLonBaidu();
                            BigDecimal latBD = jdJdb.getLatBaidu();

                            int matchRateAddr = MatchRate.getMatchRate(null, address, addressPlat) * 2;
                            int matchRateName = MatchRate.getMatchRate2(cityStr, name, namePlat) * 5;
                            String name0 = name.replace("酒店", "").replace("店", "");
                            String namePlat0 = namePlat.replace("酒店", "").replace("店", "");
                            if(name0.equals(namePlat0)){
                                matchRateName = 700;
                            }
                            int matchRate = matchRateAddr + matchRateName;

                            if (StringUtils.isNotEmpty(tel) && StringUtils.isNotEmpty(telPlat)) {
                                if (tel.contains(telPlat) || telPlat.contains(tel)) {
                                    matchRate += 200;
                                }
                            } else {
                                matchRate += 100;
                            }

                            int dist = 9999;
                            if (latGDPlat != null && lonGDPlat != null && lonGD != null && latGD != null) {
                                int dist2 = GetDistance.calculateDistance(lonGD.doubleValue(), latGD.doubleValue(),
                                        lonGDPlat.doubleValue(), latGDPlat.doubleValue());
                                if (dist2 < dist) {
                                    dist = dist2;
                                }
                            }
                            if (latBDPlat != null && lonBDPlat != null && lonBD != null && latBD != null) {
                                int dist3 = GetDistance.calculateDistance(lonBD.doubleValue(), latBD.doubleValue(),
                                        lonBDPlat.doubleValue(), latBDPlat.doubleValue());
                                if (dist3 < dist) {
                                    dist = dist3;
                                }
                            }
                            if (dist < 50) {
                                matchRate += 100;
                            }

                            if (matchRate > 799) {
                                log.info("匹配成功,开始查询Jd_jdb_mapping映射,酒店ID：{}", jdJdb.getId());
                                JdJdbMapping jdJdbMapping2 = jdjdbmappingMap.get(jdJdb.getId());
                                if (jdJdbMapping2 != null) {
                                    log.info("匹配成功,Jd_jdb_mapping映射已经存在,酒店ID：{} ,匹配分：{}", jdJdb.getId(),jdJdbMapping2.getReserve0());
                                    if (Integer.valueOf(jdJdbMapping2.getReserve0()) < matchRate) {
                                        HotelInfoHsjlxy noys2 = new HotelInfoHsjlxy();
                                        noys2.setId(jdJdbMapping2.getPlatId());
                                        noys2.setStatus(1);
                                        hotelInfoHsjlxyService.updateHotelInfoHsjlxy(noys2);
                                        jdJdbMapping2.setReserve0(matchRate + "");
                                        jdJdbMapping2.setPlatId(hotelInfoHsjlxy.getId());
                                        jdjdbmappingMap.put(jdJdb.getId(), jdJdbMapping2);
                                        HotelUpdateStatus.hsjlxyMappingCountFail.incrementAndGet();
                                        break;
                                    }else {
                                        log.info("匹配失败,Jd_jdb_mapping不存在");
                                        if(i == jdList.size() - 1) {
                                            HotelUpdateStatus.hsjlxyMappingCountFail.incrementAndGet();
                                            hotelInfoHsjlxy.setStatus(9);
                                            hotelInfoHsjlxyService.updateHotelInfoHsjlxy(hotelInfoHsjlxy);
                                            log.info("匹配到最后一条失败,Jd_jdb_mapping不存在,更新酒店基础表状态 cityId:{}", hotelInfoHsjlxy.getCityId());
                                            break;
                                        }
                                        continue;
                                    }
                                }

                                jdJdbMapping.setPlatId(hotelInfoHsjlxy.getId());
                                jdJdbMapping.setLocalId(jdJdb.getId());
                                jdJdbMapping.setReserve0(matchRate + "");
                                jdJdbMapping.setJdName(jdJdb.getJdmc());
                                jdJdbMapping.setReserve3(hotelInfoHsjlxy.getName());
                                jdjdbmappingMap.put(jdJdb.getId(), jdJdbMapping);
                                HotelUpdateStatus.hsjlxyMappingCounted.incrementAndGet();
                                log.info("新增Jd_jdb_mapping映射到map中...酒店ID：{}", jdJdb.getId());
                                break;
                            }
                            log.info("匹配失败 城市ID：{},酒店ID：{}", mappings.get(0).getLocalId(), jdJdb.getId());
                            if (i == jdList.size() - 1) {
                                log.info("红色加力协议酒店最后一条匹配失败 hotelId:{}",hotelInfoHsjlxy.getId());
                                HotelUpdateStatus.hsjlxyMappingCountFail.incrementAndGet();
                                hotelInfoHsjlxy.setStatus(9);
                                hotelInfoHsjlxyService.updateHotelInfoHsjlxy(hotelInfoHsjlxy);
                            }
                        }
                    }
                }catch (Exception e) {
                    log.error("程序执行异常 errorMessage:{}",e.getMessage(),e);
                }
                threadSize.decrementAndGet();
            }).start();
        }
        while (true) {
            if(threadSize.get() == 0) {
                Set<Map.Entry<String, JdJdbMapping>> entries = jdjdbmappingMap.entrySet();
                HotelInfoHsjlxy hotel = new HotelInfoHsjlxy();
                hotel.setStatus(8);
                for (Map.Entry<String, JdJdbMapping> entry : entries) {
                    JdJdbMapping jdJdbMapping = entry.getValue();
                    //插入前校验重复
                    log.info("jdjdbmapping localId {},interfacePlat {}", jdJdbMapping.getLocalId(), jdJdbMapping.getInterfacePlat());
                    if(null == jdJdbMappingService.selectJdJdbMappingByLocalIdAndInterfacePlat(jdJdbMapping.getLocalId(), jdJdbMapping.getInterfacePlat())) {
                        jdJdbMappingService.insertJdJdbMapping(jdJdbMapping);
                        log.info("红色加力协议jd_jdb_mapping插入成功 id:{}",jdJdbMapping.getId());

                    }
                    ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                    zhJdJdbMapping.setLocalId(jdJdbMapping.getLocalId());
                    zhJdJdbMapping.setInterfacePlat(jdJdbMapping.getInterfacePlat());
                    zhJdJdbMapping.setPlatId(jdJdbMapping.getPlatId());
                    zhJdJdbMapping.setJdName(jdJdbMapping.getJdName());
                    zhJdJdbMapping.setStatus(0);
                    zhJdJdbMapping.setSaveDate(new Date());
                    zhJdJdbMapping.setIsGnGj(1);
                    logger.info("红色加力协议zh_jd_jdb_mapping插入前数据 {}", JSONObject.toJSONString(zhJdJdbMapping));
                    //插入前校验重复
                    log.info("zhjdjdbmapping localId {},interfacePlat {}", zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat());
                    if(null == zhJdJdbMappingService.findByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(),zhJdJdbMapping.getInterfacePlat())) {
                        zhJdJdbMappingService.insert(zhJdJdbMapping);
                        log.info("红色加力协议zh_jd_jdb_mapping插入成功 id:{}",zhJdJdbMapping.getId());
                    }
                    hotel.setId(jdJdbMapping.getPlatId());
                    hotelInfoHsjlxyService.updateHotelInfoHsjlxy(hotel);
                }
                HotelUpdateStatus.expStatusHSJL_XY  = 12;
                HotelUpdateStatus.status = 0;
                break;
            }
            log.info("等待中...");
            Thread.sleep(10000);
        }
        return AjaxResult.success();
    }

    @PostMapping("/listMinPriceHSJLXYNew")
    public AjaxResult listMinPriceHSJLXYNew(@RequestBody MinPriceReq minPriceReq) {
        return AjaxResult.success(HsjlxyApi.queryHotelLowestPrice(minPriceReq));
    }


    public void insertCorp(String jdidHsjl, String corpId) {
        JdJdbHsjlCorp jdJdbHsjlCorp = new JdJdbHsjlCorp();
        jdJdbHsjlCorp.setJdidHsjl(jdidHsjl);
        jdJdbHsjlCorp.setCorpId(corpId);
        List<JdJdbHsjlCorp> list = jdJdbHsjlCorpService.selectJdJdbHsjlCorpList(jdJdbHsjlCorp);
        if(list.size() == 0) {
            jdJdbHsjlCorpService.insertJdJdbHsjlCorp(jdJdbHsjlCorp);
        }
    }

    @GetMapping("/unlock")
    public void unLock() {
        RLock rLock = redissonClient.getLock("hotel_hsjl_xy_update");
        rLock.unlock();
    }

    @GetMapping("/rm/disk")
    public void rmDisk() {
        File f = new File("~/hotel/download/");
        if (f.exists()) {
            f.delete();
        }
    }
}
