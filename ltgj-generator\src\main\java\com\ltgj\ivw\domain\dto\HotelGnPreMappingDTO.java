package com.ltgj.ivw.domain.dto;

import com.ltgj.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/6/11
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HotelGnPreMappingDTO {

    /**
     * 平台标识，对应InterfacePlatEnum
     */
    @Excel(name = "平台标识，对应InterfacePlatEnum")
    private String interfacePlat;

    /**
     * 本地酒店ID
     */
    @Excel(name = "本地酒店ID")
    private String localId;

    /**
     * 平台酒店ID
     */
    @Excel(name = "平台酒店ID")
    private String platId;

}
