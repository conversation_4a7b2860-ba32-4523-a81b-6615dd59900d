package com.ltgj.web.job.handler;

import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.controller.HotelGnKetanController;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 科坦城市映射定时任务处理器
 * 
 * <AUTHOR>
 * @date 2024-09-17
 */
@Slf4j
@Component
@JobHandler(value = "ketanCityMappingHandler")
public class KetanCityMappingHandler extends IJobHandler {

    @Autowired
    private HotelGnKetanController hotelGnKetanController;
    
    /**
     * 映射策略常量，与HotelCozyTimeSupplierService中定义保持一致
     */
    private static final String MAPPING_STRATEGY_ELONG = "1"; // 艺龙映射策略
    private static final String MAPPING_STRATEGY_NAME = "2";  // 名称映射策略
    private static final String MAPPING_STRATEGY_BOTH = "3";  // 两种策略结合

    /**
     * 幂等策略：1-幂等；0-非幂等
     */
    private static final String IDEMPOTENT_ENABLED = "1";

    /**
     * 同步策略：1-同步；0-异步
     */
    private static final String SYNC_ENABLED = "1";

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("开始执行科坦城市映射定时任务，参数：{}", param);
        
        try {
            // 默认参数设置
            String mappingStrategy = MAPPING_STRATEGY_ELONG;
            String idempotent = IDEMPOTENT_ENABLED;
            String synchronousStrategy = SYNC_ENABLED;
            
            // 解析参数字符串
            if (StringUtils.isNotEmpty(param)) {
                try {
                    String[] params = param.trim().split(",");
                    
                    // 获取映射策略参数
                    if (params.length > 0 && StringUtils.isNotEmpty(params[0])) {
                        String strategy = params[0].trim();
                        if (StringUtils.containsAny(strategy, MAPPING_STRATEGY_ELONG, MAPPING_STRATEGY_NAME, MAPPING_STRATEGY_BOTH)) {
                            mappingStrategy = strategy;
                        } else {
                            log.warn("无效的映射策略参数：{}，使用默认策略：{}", strategy, MAPPING_STRATEGY_ELONG);
                        }
                    }
                    
                    // 获取幂等策略参数
                    if (params.length > 1 && StringUtils.isNotEmpty(params[1])) {
                        idempotent = params[1].trim();
                    }
                    
                    // 获取同步策略参数
                    if (params.length > 2 && StringUtils.isNotEmpty(params[2])) {
                        synchronousStrategy = params[2].trim();
                    }
                } catch (Exception e) {
                    log.warn("参数解析错误：{}，将使用默认参数", param, e);
                }
            }
            
            // 记录执行信息
            log.info("科坦城市映射任务执行参数 - 映射策略: {}, 幂等策略: {}, 同步策略: {}", 
                    getMappingStrategyName(mappingStrategy), 
                    IDEMPOTENT_ENABLED.equals(idempotent) ? "启用" : "禁用", 
                    SYNC_ENABLED.equals(synchronousStrategy) ? "同步执行" : "异步执行");

            // 根据同步策略执行相应的方法
            if (SYNC_ENABLED.equals(synchronousStrategy)) {
                log.info("开始同步执行科坦城市映射");
                hotelGnKetanController.syncCityDataSync(idempotent, mappingStrategy);
            } else {
                log.info("开始异步执行科坦城市映射");
                hotelGnKetanController.syncCityData(idempotent, mappingStrategy);
            }

            log.info("科坦城市映射定时任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("科坦城市映射定时任务执行异常: {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行异常：" + e.getMessage());
        }
    }
    
    /**
     * 获取映射策略名称
     *
     * @param mappingStrategy 映射策略代码
     * @return 映射策略名称
     */
    private String getMappingStrategyName(String mappingStrategy) {
        switch (mappingStrategy) {
            case MAPPING_STRATEGY_ELONG:
                return "艺龙映射";
            case MAPPING_STRATEGY_NAME:
                return "名称映射";
            case MAPPING_STRATEGY_BOTH:
                return "综合映射";
            default:
                return "未知策略";
        }
    }
} 