package com.ltgj.ivw.mapper;

import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.dto.HotelCheckImageDto;
import com.ltgj.ivw.dto.JdJdbRepeatDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 酒店基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
public interface JdJdbMapper
{
    /**
     * 查询酒店基础信息
     *
     * @param id 酒店基础信息主键
     * @return 酒店基础信息
     */
    public JdJdb selectJdJdbById(String id);
    /**
     * 查询酒店基础信息
     *
     * @param id 酒店基础信息主键
     * @return 酒店基础信息
     */
    public List<JdJdb> selectJdJdbByInterfacePlat(String id);

    /**
     * 查询酒店基础信息列表
     *
     * @param jdJdb 酒店基础信息
     * @return 酒店基础信息集合
     */
    public List<JdJdb> selectJdJdbList(JdJdb jdJdb);

    /**
     * 新增酒店基础信息
     *
     * @param jdJdb 酒店基础信息
     * @return 结果
     */
    public int insertJdJdb(JdJdb jdJdb);

    /**
     * 修改酒店基础信息
     *
     * @param jdJdb 酒店基础信息
     * @return 结果
     */
    public int updateJdJdb(JdJdb jdJdb);

    /**
     * 根据id更新酒店基础信息
     * @param jdJdb
     * @return
     */
    int updateById(JdJdb jdJdb);


    /**
     * 删除酒店基础信息
     *
     * @param id 酒店基础信息主键
     * @return 结果
     */
    public int deleteJdJdbById(String id);

    /**
     * 批量删除酒店基础信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJdJdbByIds(String[] ids);

    int deleteAllByCityIdYs(String cityIdYs);

    void insertJdJdbZh(JdJdb jdJdb);

    void deleteJdJdbByIdZh(String id);

    /**
     * 按页面获取jd-jdb
     *
     * @param number   数
     * @param pageSize 页面大小
     * @return {@link List }<{@link JdJdb }>
     */
    List<JdJdb> getJdJdbByPage(@Param("number") Integer number,@Param("pageSize") Integer pageSize);

    /**
     * 关联查询JdJdb，从映射表获取所有InterfacePlat
     *
     * @param id 身份证件
     * @return {@link JdJdb }
     */
    JdJdb getInterfacePlatById(@Param("id") String id);

    int findByBeanCount(String suffix);

    /**
     * JDB 计数
     *
     * @return int
     */
    int findJdJdbCount();

    List<String> getThirdHotelId(@Param("suffix") int suffix, @Param("interfacePlats") List<String> interfacePlats, @Param("number") Integer number, @Param("pageSize") Integer pageSize);

    /**
     * 获取酒店id列表
     * 查Jd_jdb
     *
     * @param number   数字
     * @param pageSize 页面大小
     * @return {@link List }<{@link String }>
     */
    List<String> getHotelIdList(@Param("number") int number, @Param("pageSize") int pageSize);

    void updateImageData(@Param("dto") HotelCheckImageDto hotelCheckImageDtoOptional);

    List<String> selectInIds(@Param("idList")List<String> idList);

    List<String> selectAllJson(@Param("start")int start, @Param("pageSize")int pageSize);

    List<JdJdb> selectAll(@Param("start")int start, @Param("pageSize")int pageSize);

    List<JdJdb> getJdJdbByIds(@Param("idList")List<String> idList);

    List<JdJdb> getJdJdbByIdsNew(@Param("idList")List<String> idList);


    List<String> selectIdList(@Param("minId") String minId, @Param("pageNumber") int pageNumber);

    List<JdJdb> selectJdJdbByPlaId(@Param("plat") String plat, @Param("platId") String platId);

    List<JdJdbRepeatDTO> selectJdJdbRepeatByJdmcAndJddz(JdJdbRepeatDTO jdJdbRepeatDTO);

    List<JdJdb> selectJdJdbListNew(JdJdb jdJdb);

    List<String> selectAllJsonForScore(@Param("start")int start, @Param("pageSize")int pageSize);

    List<JdJdb> selectBatchByIds(@Param("ids") List<String> ids);

    void updateBatch(List<JdJdb> list);

    void updateBatchJdbDocumentId(List<JdJdb> list);

    List<String> selectCityId();

    List<JdJdb> selectByCityId(@Param("cityId") String cityId, @Param("date") Date date);

    int count();

    List<JdJdb> listByIds(@Param("ids") List<String> ids);

    void updateKnowledgeIdByCityIdAndDate(@Param("cityId") String cityId, @Param("knowledgeId") String knowledgeId, @Param("date") Date date);
}
