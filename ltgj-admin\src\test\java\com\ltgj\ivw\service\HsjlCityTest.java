package com.ltgj.ivw.service;

import com.ltgj.ivw.utils.hotelApi.HsjlApi;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HsjlCityTest {
    @Test
    public void test() throws Exception {
        String cityListJson = HsjlApi.queryCityList();
        System.out.println("cityListJson = " + cityListJson);
    }

}
