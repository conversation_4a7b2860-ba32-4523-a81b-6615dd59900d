package com.ltgj.ivw.service;

import com.ltgj.ivw.dto.HotelGnGeoMappingDTO;
import com.ltgj.ivw.vo.HotelGnGeoMappingVO;
import com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/13
 * @description:
 */
public interface IHotelGnGeoMappingService {
    /**
     * 批量插入或更新酒店地理映射数据
     *
     * @param records 酒店地理映射数据列表
     * @return 插入/更新成功的条数
     */
    int batchInsertOrUpdate(List<HotelGnGeoMapping> records);

    /**
     * 创建酒店地理映射数据
     * @param hotelGnGeoMappingDTO
     * @return
     */
    HotelGnGeoMappingVO createGnGeoMapping(HotelGnGeoMappingDTO hotelGnGeoMappingDTO);

    /**
     * 查询酒店地理映射数据
     * @param hotelGnGeoMappingDTO
     * @return
     */
    HotelGnGeoMappingVO selectGnGeoMapping(HotelGnGeoMappingDTO hotelGnGeoMappingDTO);
}
