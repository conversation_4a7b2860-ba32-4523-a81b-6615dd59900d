package com.ltgj.supplier.common.gn.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 国内 - 酒店设施详细信息枚举
 * 包含设施的详细属性配置信息
 */
@Getter
public enum HotelGnFacilityDetail {
    PARKING(1, "停车场", "parking", true, true),
    CHARGING(2, "充电桩", "charging", true, false),
    LIFT(3, "电梯", "lift", true, false),
    BAGGAGE(4, "行李寄存", "baggage", true, false),
    RESTAURANT(5, "餐厅", "restaurant", true, false),
    MEETING_ROOM(6, "会议室", "meetingRoom", true, false),
    PUBLIC_WIFI(7, "公共区域WIFI", "publicWifi", true, false),
    GYM(8, "健身房", "gym", true, false),
    LAUNDRY(9, "洗衣房", "laundry", true, false),
    ;

    private final int id;
    private final String name;
    private final String key;
    private final boolean supportIsHave;     // 是否支持"是否有无"属性
    private final boolean supportIsCharge;   // 是否支持"是否收费"属性

    HotelGnFacilityDetail(int id, String name, String key, boolean supportIsHave, boolean supportIsCharge) {
        this.id = id;
        this.name = name;
        this.key = key;
        this.supportIsHave = supportIsHave;
        this.supportIsCharge = supportIsCharge;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getKey() {
        return key;
    }

    public boolean isSupportIsHave() {
        return supportIsHave;
    }

    public boolean isSupportIsCharge() {
        return supportIsCharge;
    }

    /**
     * 根据ID获取设施详细信息
     */
    public static HotelGnFacilityDetail getById(int id) {
        for (HotelGnFacilityDetail facility : HotelGnFacilityDetail.values()) {
            if (facility.getId() == id) {
                return facility;
            }
        }
        return null;
    }

    /**
     * 根据key获取设施详细信息
     */
    public static HotelGnFacilityDetail getByKey(String key) {
        for (HotelGnFacilityDetail facility : HotelGnFacilityDetail.values()) {
            if (facility.getKey().equals(key)) {
                return facility;
            }
        }
        return null;
    }

    /**
     * 根据名称获取设施详细信息
     */
    public static HotelGnFacilityDetail getByName(String name) {
        for (HotelGnFacilityDetail facility : HotelGnFacilityDetail.values()) {
            if (facility.getName().equals(name)) {
                return facility;
            }
        }
        return null;
    }
} 