package com.ltgj.web.job.handler;

import com.ltgj.ivw.service.HotelInfoQiantaoElongIdService;
import com.ltgj.ivw.service.IHotelInfoKetanPreMappingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 千淘酒店国内国内酒店艺龙映射
 */
@Slf4j
@Component
@JobHandler(value = "qiantaoHotelGnELongMappingHandler")
public class QiantaoHotelGnELongMappingHandler extends IJobHandler {

    @Autowired
    private HotelInfoQiantaoElongIdService hotelInfoQiantaoElongIdService;

    /**
     * 定时任务处理预映射
     *
     * @param s 标记： 1：请求接口获取与艺龙酒店关系存入预映射表  2：根据预映射表更新酒店映射关系表
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        long startTime = System.currentTimeMillis();

        try {
            XxlJobLogger.log("----千淘酒店国内国内酒店艺龙映射----开始");
            log.info("----千淘酒店国内国内酒店艺龙映射----开始");
            this.hotelInfoQiantaoElongIdService.batchUpdateHotelGnELongMapping();
            XxlJobLogger.log("----千淘酒店国内国内酒店艺龙映射----完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("---千淘酒店国内国内酒店艺龙映射---失败：" + e);
        }


        log.info("---千淘酒店国内国内酒店艺龙映射---结束 执行结果：失败");
        XxlJobLogger.log("千淘任务处理 执行用时：" + (System.currentTimeMillis() - startTime) / 1000 + "秒");
        return ReturnT.FAIL;
    }
}
