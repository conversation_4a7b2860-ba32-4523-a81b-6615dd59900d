# 重复酒店数据判断逻辑修改 - 添加 cityId 字段

## 修改概述

将重复酒店数据的判断逻辑从原来的基于 `jdmc`（酒店名称）和 `jddz`（酒店地址）两个字段，扩展为基于 `jdmc`、`jddz` 和 `cityId`（城市ID）三个字段共同判断。

## 修改文件列表

### 1. `ltgj-generator/src/main/java/com/ltgj/ivw/dto/JdJdbRepeatDTO.java`
**修改内容：**
- 添加 `cityId` 字段（String 类型）
- 添加相应的 JavaDoc 注释

**影响：**
- DTO 现在包含城市ID信息，用于更精确的重复数据识别

### 2. `ltgj-generator/src/main/resources/mapper/ivw/JdJdbMapper.xml`
**修改内容：**
- 更新 `JdJdbRepeatDTOResult` ResultMap，添加 `cityId` 字段映射
- 修改 `selectJdJdbRepeatByJdmcAndJddz` SQL 查询：
  - SELECT 子句添加 `city_id` 字段
  - GROUP BY 子句修改为 `GROUP BY jdmc, jddz, city_id`
  - 添加 cityId 的动态查询条件（使用 test 标签处理空值）
- 修改 `selectJdJdbListNew` SQL，添加 cityId 查询条件支持

**影响：**
- 重复数据查询现在基于三个字段进行分组
- 支持按 cityId 进行过滤查询
- 正确处理 cityId 为空的情况

### 3. `ltgj-quartz/src/main/java/com/ltgj/quartz/task/increment/RepeatDelForJdJdbTask.java`
**修改内容：**
- 更新 `findRelatedHotels()` 方法，在查询条件中添加 cityId
- 更新所有相关的日志输出，包含 cityId 信息
- 保持 `isValidDTO()` 方法不变（因为 cityId 可能为空，不需要强制验证）

**影响：**
- 查找相关酒店时会考虑 cityId 匹配
- 日志记录更加详细，便于排查问题

## 功能变化

### 修改前
- 重复判断规则：`jdmc + jddz` 相同即视为重复
- 例如：相同酒店名称和地址，但在不同城市的酒店会被视为重复

### 修改后
- 重复判断规则：`jdmc + jddz + cityId` 三者相同才视为重复
- 例如：相同酒店名称和地址，但在不同城市的酒店不再被视为重复
- 支持 cityId 为空的情况（空值会被作为一个独立的分组条件）

## 空值处理

通过 MyBatis 的 `<if test="cityId != null and cityId!=''">` 标签处理：
- 当 cityId 不为空时，加入查询条件
- 当 cityId 为空时，不加入查询条件，依靠 GROUP BY 中的 city_id 字段自然处理 NULL 值分组

## 数据库影响

- MySQL 的 GROUP BY 会将 NULL 值作为一个独立的分组，这符合我们的业务需求
- 相同 `jdmc`、`jddz` 但 `cityId` 都为 NULL 的记录会被分在一组
- 相同 `jdmc`、`jddz` 但 `cityId` 为不同非空值的记录会被分在不同组

## 兼容性

- 保持了原有的 API 接口不变
- 向后兼容：如果不传 cityId 参数，仍能正常工作
- 不影响现有的其他功能模块

## 测试建议

1. 测试 cityId 为空的情况
2. 测试 cityId 不为空的情况
3. 测试混合场景（部分记录有 cityId，部分没有）
4. 验证重复数据识别的准确性
5. 验证日志输出的完整性 