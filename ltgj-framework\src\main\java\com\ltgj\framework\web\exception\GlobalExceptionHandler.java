package com.ltgj.framework.web.exception;

import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import com.ltgj.common.constant.HttpStatus;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.exception.DemoModeException;
import com.ltgj.common.exception.ServiceException;
import com.ltgj.common.utils.StringUtils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.converter.HttpMessageNotReadableException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.fasterxml.jackson.databind.JsonMappingException;

import java.math.BigDecimal;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler implements AsyncUncaughtExceptionHandler
{
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 权限校验异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public AjaxResult handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限校验失败'{}'", requestURI, e.getMessage());
        if (requestURI.endsWith("/getInfoStatus")) {
            return AjaxResult.success(HttpStatus.SUCCESS);
        }
        return AjaxResult.error(HttpStatus.FORBIDDEN, "没有权限，请联系管理员授权");
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public AjaxResult handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
            HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestURI, e.getMethod());
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public AjaxResult handleServiceException(ServiceException e, HttpServletRequest request)
    {
        log.error(e.getMessage(), e);
        Integer code = e.getCode();
        return StringUtils.isNotNull(code) ? AjaxResult.error(code, e.getMessage()) : AjaxResult.error(e.getMessage());
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生未知异常.", requestURI, e);
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public AjaxResult handleException(Exception e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestURI, e);
        return AjaxResult.error(e.getMessage());
    }

    /**
     * 处理请求参数格式错误 @RequestBody 参数格式错误时抛出的异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public AjaxResult handleHttpMessageNotReadableException(HttpMessageNotReadableException e, HttpServletRequest request)
    {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',请求参数格式错误", requestURI, e);
        
        String message = "请求参数格式错误";
        Throwable cause = e.getCause();
        
        if (cause instanceof InvalidFormatException)
        {
            InvalidFormatException ife = (InvalidFormatException) cause;
            String fieldName = ife.getPath().stream()
                    .map(JsonMappingException.Reference::getFieldName)
                    .collect(Collectors.joining("."));
            String value = ife.getValue() != null ? ife.getValue().toString() : "null";
            
            // 针对 BigDecimal 类型的特殊处理
            if (ife.getTargetType() != null && ife.getTargetType().equals(BigDecimal.class))
            {
                message = String.format("字段 [%s] 的值 [%s] 不是有效的数字格式", fieldName, value);
                
                // 检查是否是经纬度字段
                if (fieldName.contains("lon") || fieldName.contains("lat") || 
                    fieldName.contains("经度") || fieldName.contains("纬度"))
                {
                    message = String.format("经纬度字段 [%s] 的值 [%s] 格式不正确，请输入有效的数字", fieldName, value);
                }
            }
            else
            {
                message = String.format("字段 [%s] 的值 [%s] 格式不正确", fieldName, value);
            }
        }
        else if (cause instanceof JsonMappingException)
        {
            JsonMappingException jme = (JsonMappingException) cause;
            String fieldName = jme.getPath().stream()
                    .map(JsonMappingException.Reference::getFieldName)
                    .collect(Collectors.joining("."));
            message = String.format("字段 [%s] 解析失败：%s", fieldName, jme.getMessage());
        }
        
        return AjaxResult.error(message);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(BindException.class)
    public Object handleBindException(BindException e)
    {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getAllErrors().stream()
                .map(ObjectError::getDefaultMessage)
                .collect(Collectors.joining("；"));
        return AjaxResult.error(message);
    }

    /**
     * 自定义验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e)
    {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getAllErrors().stream()
                .map(ObjectError::getDefaultMessage)
                .collect(Collectors.joining("；"));
        return AjaxResult.error(message);
    }

    /**
     * 演示模式异常
     */
    @ExceptionHandler(DemoModeException.class)
    public AjaxResult handleDemoModeException(DemoModeException e)
    {
        return AjaxResult.error("演示模式，不允许操作");
    }

    /**
     * 处理未捕获的异常
     *
     * 当某个方法执行过程中抛出了未被捕获的异常时，该方法会被调用其主要作用是记录异常信息，
     * 包括异常发生的 方法名和该方法的参数值
     *
     * @param ex 异常对象，包含异常类型和异常堆栈信息
     * @param method 发生异常的方法对象，用于获取方法名
     * @param params 发生异常的方法的参数数组，用于记录参数值
     */
    @Override
    public void handleUncaughtException(Throwable ex, Method method, Object... params) {
        // 获取日志记录器
        Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

        // 检查方法对象是否为null，如果为null，则记录错误并直接返回
        if (method == null) {
            logger.error("Method is null, cannot handle uncaught exception.");
            return;
        }

        // 记录异常信息
        logger.error("Uncaught exception in method: {}", method.getName(), ex);
        // 记录方法参数信息
        logger.error("Parameters: {}", Arrays.toString(params));
    }
}
