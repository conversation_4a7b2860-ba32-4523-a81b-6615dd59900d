package com.ltgj.ivw.mapper;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.service.idempotent.dto.HotelCityMappingDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 城市映射管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-28
 */
public interface HotelCityMappingMapper
{
    /**
     * 查询城市映射管理
     *
     * @param id 城市映射管理主键
     * @return 城市映射管理
     */
    public HotelCityMapping selectHotelCityMappingById(Long id);

    /**
     * 查询城市映射管理列表
     *
     * @param hotelCityMapping 城市映射管理
     * @return 城市映射管理集合
     */
    public List<HotelCityMapping> selectHotelCityMappingList(HotelCityMapping hotelCityMapping);

    /**
     * 新增城市映射管理
     *
     * @param hotelCityMapping 城市映射管理
     * @return 结果
     */
    public int insertHotelCityMapping(HotelCityMapping hotelCityMapping);

    /**
     * 修改城市映射管理
     *
     * @param hotelCityMapping 城市映射管理
     * @return 结果
     */
    public int updateHotelCityMapping(HotelCityMapping hotelCityMapping);

    /**
     * 修改城市映射管理(根据幂等属性修改)
     *
     * @param hotelCityMapping 城市映射管理
     * @return 结果
     */
    public int updateHotelCityMappingByAttribute(HotelCityMapping hotelCityMapping);

    /**
     * 删除城市映射管理
     *
     * @param id 城市映射管理主键
     * @return 结果
     */
    public int deleteHotelCityMappingById(Long id);

    /**
     * 批量删除城市映射管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHotelCityMappingByIds(Long[] ids);

    List<HotelCityMapping> selectListLikeLocaltionName(@Param("hotelCityMapping") HotelCityMapping hotelCityMapping);
    
    /**
     * 根据ID列表批量查询酒店城市映射数据
     * 
     * @param ids ID列表
     * @return 酒店城市映射列表
     */
    List<HotelCityMapping> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据幂等键查询酒店城市数据
     *
     * @param hotelCityMappingDTOS 幂等键列表
     * @return 酒店城市列表
     */
    List<HotelCityMapping> selectByAttribute(@Param("hotelCityMappingDTOS") List<HotelCityMappingDTO> hotelCityMappingDTOS);

    /**
     * 批量插入酒店城市映射数据
     * 
     * @param hotelCityMappingList 酒店城市映射列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<HotelCityMapping> hotelCityMappingList);

    List<HotelCityMapping> getHotelCityByCondition(@Param("platformId") String platformId, @Param("cityId") String cityId);
}
