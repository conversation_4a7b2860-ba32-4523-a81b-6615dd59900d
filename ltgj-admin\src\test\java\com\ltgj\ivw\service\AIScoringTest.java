package com.ltgj.ivw.service;

import com.alibaba.fastjson2.JSONArray;
import com.ltgj.common.utils.ExceptionUtil;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.utils.dto.HotelInfoDTO;
import com.ltgj.quartz.task.AIScoringTask;
import com.ltgj.supplier.cozyTime.HotelCozyTimeSupplierService;
import com.ltgj.web.job.handler.UploadAIKnowledgeHandlerV2;
import com.xxl.job.core.util.DateUtil;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.junit.Test;

import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 *
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class AIScoringTest {

    @Autowired
    AIScoringTask aiScoring;
    @Autowired
    private AiMappingService aiMappingService;

    @Autowired
    private HotelCozyTimeSupplierService hotelCozyTimeSupplierService;

    @Autowired
    private DifyApiService difyApiService;
    @Autowired
    @Qualifier("difyApiService2Impl")
    private DifyApiService difyApiService2;
    @Autowired
    private CanalListenerService canalListenerService;
    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private UploadAIKnowledgeHandlerV2 uploadAIKnowledgeHandlerV2;
    @Resource
    private HotelPreMappingManager hotelPreMappingManager;
    @Test
    public void mainTest() {
//        String s = "{\"platNums\":[\"2000010\"],\"repeat\":1,\"type\":2,\"threadNum\":5,\"allStatus\":0,\"multipleSize\":10,\"version\":1}";
//        hotelPreMappingManager.handleFirstPreMapping(s);
//        String msg="{\"data\":[{\"id\":\"200000423111435\",\"interfacePlat\":\"2000079\",\"jdmc\":\"曼扬光美宿\",\"jdmc_en\":\"\",\"jdlx\":\"1\",\"jddz\":\"黄冈镇七号路段新联狮头寨村猪哥溪南303号\",\"jddz_en\":null,\"jddh\":\"0768-8596986,***********,***********\",\"img_url\":\"https://hotelimages.ceekee.com/0203c12000fpdgeiv99BF_R_550_412.jpg\",\"status\":\"0\",\"lon_google\":null,\"lat_google\":null,\"lon_baidu\":null,\"lat_baidu\":null,\"lon_gaode\":\"116.991004000\",\"lat_gaode\":\"23.665292000\",\"city_id\":\"10453\",\"city_name\":\"潮州\",\"brand_id\":null,\"brand_name\":null,\"district\":\"804\",\"district_name\":\"饶平\",\"business_zone\":null,\"business_zone_name\":null,\"jdxj\":\"0\",\"score\":\"4.7\",\"jtid\":null,\"jtmc\":null,\"kysj\":\"2024\",\"zhzxsj\":\"2024\",\"rank\":\"0\",\"createdate\":\"2025-06-25 14:05:47\",\"mender\":null,\"savedate\":\"2025-06-25 14:05:47\",\"min_price\":\"0.0\",\"reserve1\":null,\"reserve2\":null,\"notice_info\":null,\"policy_info\":\"{\\\"checkInOutPolicy\\\":{\\\"checkIn\\\":\\\"14:00\\\",\\\"checkOut\\\":\\\"12:00\\\"},\\\"customerPolicy\\\":{\\\"customerDesc\\\":\\\"仅接待大陆和港澳台客人，须持港澳台身份证或大陆身份证入住\\\",\\\"customerType\\\":\\\"3\\\"}}\",\"facilities_info\":\"{}\",\"reserve6\":null,\"reserve7\":null,\"reserve8\":null,\"reserve9\":null,\"reserve0\":\"0\",\"reserve3\":null,\"reserve4\":null,\"reserve5\":null,\"origin_id\":null,\"recommend_level\":\"0\",\"country_id\":\"00002\",\"country_name\":\"中国\",\"province_id\":\"2001\",\"province_name\":\"广东\",\"sparkle\":null,\"ai_document_id\":null,\"round_info\":null,\"ai_dataset_id\":null}],\"database\":\"pre_ivw_hotel\",\"es\":1750831547000,\"gtid\":\"\",\"id\":658810,\"isDdl\":false,\"mysqlType\":{\"id\":\"varchar(40)\",\"interfacePlat\":\"varchar(10)\",\"jdmc\":\"varchar(100)\",\"jdmc_en\":\"varchar(200)\",\"jdlx\":\"varchar(500)\",\"jddz\":\"varchar(500)\",\"jddz_en\":\"varchar(500)\",\"jddh\":\"varchar(60)\",\"img_url\":\"varchar(255)\",\"status\":\"tinyint(4)\",\"lon_google\":\"decimal(15,11)\",\"lat_google\":\"decimal(15,11)\",\"lon_baidu\":\"decimal(15,11)\",\"lat_baidu\":\"decimal(15,11)\",\"lon_gaode\":\"decimal(15,11)\",\"lat_gaode\":\"decimal(15,11)\",\"city_id\":\"varchar(32)\",\"city_name\":\"varchar(20)\",\"brand_id\":\"varchar(32)\",\"brand_name\":\"varchar(100)\",\"district\":\"varchar(32)\",\"district_name\":\"varchar(32)\",\"business_zone\":\"varchar(32)\",\"business_zone_name\":\"varchar(255)\",\"jdxj\":\"tinyint(4)\",\"score\":\"decimal(5,1)\",\"jtid\":\"varchar(100)\",\"jtmc\":\"varchar(100)\",\"kysj\":\"varchar(20)\",\"zhzxsj\":\"varchar(20)\",\"rank\":\"int(11)\",\"createdate\":\"datetime\",\"mender\":\"varchar(60)\",\"savedate\":\"datetime\",\"min_price\":\"decimal(7,2)\",\"reserve1\":\"longtext\",\"reserve2\":\"varchar(100)\",\"notice_info\":\"longtext\",\"policy_info\":\"longtext\",\"facilities_info\":\"longtext\",\"reserve6\":\"varchar(100)\",\"reserve7\":\"varchar(100)\",\"reserve8\":\"varchar(100)\",\"reserve9\":\"varchar(100)\",\"reserve0\":\"varchar(100)\",\"reserve3\":\"varchar(1000)\",\"reserve4\":\"varchar(100)\",\"reserve5\":\"varchar(100)\",\"origin_id\":\"varchar(80)\",\"recommend_level\":\"int(11)\",\"country_id\":\"varchar(11)\",\"country_name\":\"varchar(128)\",\"province_id\":\"varchar(255)\",\"province_name\":\"varchar(255)\",\"sparkle\":\"varchar(1000)\",\"ai_document_id\":\"varchar(40)\",\"round_info\":\"varchar(11)\",\"ai_dataset_id\":\"varchar(40)\"},\"old\":null,\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":12,\"interfacePlat\":12,\"jdmc\":12,\"jdmc_en\":12,\"jdlx\":12,\"jddz\":12,\"jddz_en\":12,\"jddh\":12,\"img_url\":12,\"status\":-6,\"lon_google\":3,\"lat_google\":3,\"lon_baidu\":3,\"lat_baidu\":3,\"lon_gaode\":3,\"lat_gaode\":3,\"city_id\":12,\"city_name\":12,\"brand_id\":12,\"brand_name\":12,\"district\":12,\"district_name\":12,\"business_zone\":12,\"business_zone_name\":12,\"jdxj\":-6,\"score\":3,\"jtid\":12,\"jtmc\":12,\"kysj\":12,\"zhzxsj\":12,\"rank\":4,\"createdate\":93,\"mender\":12,\"savedate\":93,\"min_price\":3,\"reserve1\":2005,\"reserve2\":12,\"notice_info\":2005,\"policy_info\":2005,\"facilities_info\":2005,\"reserve6\":12,\"reserve7\":12,\"reserve8\":12,\"reserve9\":12,\"reserve0\":12,\"reserve3\":12,\"reserve4\":12,\"reserve5\":12,\"origin_id\":12,\"recommend_level\":4,\"country_id\":12,\"country_name\":12,\"province_id\":12,\"province_name\":12,\"sparkle\":12,\"ai_document_id\":12,\"round_info\":12,\"ai_dataset_id\":12},\"table\":\"jd_jdb\",\"ts\":1750831547410,\"type\":\"INSERT\"}";
//        canalListenerService.dealJdJdbCanalMsgForKnowledgeWithChunk(msg);
//        try {
//            hotelCozyTimeSupplierService.saveHotelInfosByIds(Arrays.asList(23073831L, 904191L, 6322141L));
//        } catch (Exception e) {
//            log.error(ExceptionUtils.getStackTrace(e));
//        }
//     hotelCozyTimeSupplierService.updateAll(null);
//        aiScoring.getCancel();
//      aiScoring.getFacilities();
//        hotelCozyTimeSupplierService.getFacilities();
//       aiMappingService.uploadKnowledgeByCity(null);
//        JSONArray objects = difyApiService.retrieveChunks("200000402405012");
//       String docId="304f6406-6883-4b73-9b4b-4d2c6c88aa9e";
//       String segmentId="625744dc-4b7d-4cf9-8874-3ef6b467e768";
//        difyApiService.updateSegmentByText(segmentId,docId,"{\"address\":\"关索大道关岭体育馆旁\",\"city\":\"安顺市\",\"id\":\"200000402405012\",\"name\":\"关岭万绿城柏兰廷酒店\",\"phone\":\"08513752888813123634675\",\"platform\":\"2000010\"}");
//                  aiMappingService.aiKeTanMultipleMapping();
//        aiMappingService.aiKeTanMapping();
//               aiMappingService.uploadKnowledgeByCity(null);
//        aiMappingService.formatJdjdb();
//        canalListenerService.dealJdJdbCanalMsgForKnowledge(msg);
//        List<JdJdb> jsonList = jdJdbService.selectByCityId("10355", DateUtil.parseDateTime("2023-05-01 00:00:00"));
//        while (true) {
//            List<HotelInfoDTO> list = new ArrayList<>();
//            HotelInfoDTO hotelInfoDTO = new HotelInfoDTO();
//            hotelInfoDTO.setName("关岭万绿城柏兰廷酒店");
//            hotelInfoDTO.setCity("安顺市");
//            hotelInfoDTO.setAddress("关索大道关岭体育馆旁");
//            hotelInfoDTO.setPhone("08513752888813123634675");
//            hotelInfoDTO.setId("200000402405012");
//            list.add(hotelInfoDTO);
//            difyApiService.runBatchWorkflowV2(list);
//        }

//        try {
//            uploadAIKnowledgeHandlerV2.execute("7e3a5071-4aa5-496f-9218-436b9f89d115,2024-05-01 00:00:00");
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
        HashMap<String, String> map = new HashMap<>();
        map.put("city_name","策勒");
        difyApiService.updateKnowledgeMetadata( map,"178d603b-f75c-48cb-8110-9c2ecd55f702",null);
    }

//    String msg="{\"data\":[{\"id\":\"200000400101012\",\"interfacePlat\":\"2000010\",\"jdmc\":\"非繁城品酒店北京王府井南锣鼓巷店\",\"jdmc_en\":\"Chonpines Hotel (Beijing Wangfujing Nanluoguxiang Store)\",\"jdlx\":\"酒店\",\"jddz\":\"东四北大街320号\",\"jddz_en\":null,\"jddh\":\"***********\",\"img_url\":\"https://image.qiantaohotel.com/Hotels/H569274/hotel_4FCB4986CF1ED11D6FA7833F5692F682CG.jpg\",\"status\":\"0\",\"lon_google\":\"116.417531000\",\"lat_google\":\"39.929863000\",\"lon_baidu\":\"116.***********\",\"lat_baidu\":\"39.***********\",\"lon_gaode\":\"123.000000000\",\"lat_gaode\":null,\"city_id\":\"10119\",\"city_name\":\"北京\",\"brand_id\":null,\"brand_name\":\"非繁城品\",\"district\":\"01010002\",\"district_name\":null,\"business_zone\":\"755228\",\"business_zone_name\":null,\"jdxj\":\"2\",\"score\":\"4.6\",\"jtid\":null,\"jtmc\":\"锦江酒店（中国区）\",\"kysj\":null,\"zhzxsj\":null,\"rank\":\"130\",\"createdate\":null,\"mender\":null,\"savedate\":\"2025-06-11 09:32:52\",\"min_price\":\"597.0\",\"reserve1\":null,\"reserve2\":\"2000099\",\"notice_info\":\"[{\\\"FacilityId\\\":1402,\\\"FacilityName\\\":\\\"接待大陆，港澳台及外国客人\\\",\\\"FacilityNameEn\\\":\\\"This hotel accommodates Mainland Chinese, Hong Kong, Macau, Taiwan and other foreign guests\\\"},{\\\"FacilityId\\\":721,\\\"FacilityName\\\":\\\"根据《北京市宾馆不得主动提供的一次性用品目录》相关规定，自2020年5月1日起，宾馆不得主动提供一次性用品，目录含：牙刷、梳子、浴擦、剃须刀、指甲锉、鞋擦。如您需要可联系酒店索取。;\\\",\\\"FacilityNameEn\\\":\\\"\\\"}]\",\"policy_info\":\"[{\\\"timeInOut\\\":\\\"12:00-14:00\\\"}]\",\"facilities_info\":\"{\\\"GeneralFacilities\\\":\\\"[{\\\\\\\"FacilityId\\\\\\\":1431,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"净水机\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Water purifier\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":200,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"无电梯\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"No elevator\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1433,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"火灾报警器\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Fire alarm\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1439,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"灭火器\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Fire extinguisher\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1496,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"烟雾报警器\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Smoke alarm\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1436,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"门禁系统\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Access control system\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1497,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"安全报警器\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Safety alarm\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1437,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"新风系统\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Fresh air system\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1049,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"公共区域禁烟\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Non-smoking hotel\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":221,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"无烟楼层\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Non-smoking floor\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1418,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"公用区wifi\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Public area WiFi\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":213,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"安全消防系统\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Fire protection system\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":215,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"公共区域闭路电视监控系统\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Closed Circuit Television in public area\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":196,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"无停车场\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"No parking lot\\\\\\\"}]\\\",\\\"ServiceFacilities\\\":\\\"[{\\\\\\\"FacilityId\\\\\\\":10,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"叫醒服务\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Wake-up calls\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":1498,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"保安人员\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Security personnel\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":13,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"旅游服务\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Travel service\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":17,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"行李寄存\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Luggage storage\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":21,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"快速入住退房\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Express check-in/check-out\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":2211,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"全部客   禁烟\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"All rooms non-smoking\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":24,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"24小时前台\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Front desk (24 hours)\\\\\\\"},{\\\\\\\"FacilityId\\\\\\\":2602,\\\\\\\"FacilityName\\\\\\\":\\\\\\\"楼梯扶手\\\\\\\",\\\\\\\"FacilityNameEn\\\\\\\":\\\\\\\"Stair handrail\\\\\\\"}]\\\"}\",\"reserve6\":null,\"reserve7\":null,\"reserve8\":null,\"reserve9\":null,\"reserve0\":\"6\",\"reserve3\":null,\"reserve4\":null,\"reserve5\":null,\"origin_id\":null,\"recommend_level\":null,\"country_id\":\"CN\",\"country_name\":\"中国\",\"province_id\":null,\"province_name\":null,\"sparkle\":null,\"ai_document_id\":\"805af55a-39c8-41b5-ad70-c35042364e47\",\"round_info\":null}],\"database\":\"ivw_hotel\",\"es\":1749623690000,\"gtid\":\"\",\"id\":1545013,\"isDdl\":false,\"mysqlType\":{\"id\":\"varchar(40)\",\"interfacePlat\":\"varchar(10)\",\"jdmc\":\"varchar(100)\",\"jdmc_en\":\"varchar(200)\",\"jdlx\":\"varchar(500)\",\"jddz\":\"varchar(500)\",\"jddz_en\":\"varchar(500)\",\"jddh\":\"varchar(60)\",\"img_url\":\"varchar(255)\",\"status\":\"tinyint(4)\",\"lon_google\":\"decimal(15,11)\",\"lat_google\":\"decimal(15,11)\",\"lon_baidu\":\"decimal(15,11)\",\"lat_baidu\":\"decimal(15,11)\",\"lon_gaode\":\"decimal(15,11)\",\"lat_gaode\":\"decimal(15,11)\",\"city_id\":\"varchar(32)\",\"city_name\":\"varchar(20)\",\"brand_id\":\"varchar(32)\",\"brand_name\":\"varchar(100)\",\"district\":\"varchar(32)\",\"district_name\":\"varchar(32)\",\"business_zone\":\"varchar(32)\",\"business_zone_name\":\"varchar(255)\",\"jdxj\":\"tinyint(4)\",\"score\":\"decimal(5,1)\",\"jtid\":\"varchar(100)\",\"jtmc\":\"varchar(100)\",\"kysj\":\"varchar(20)\",\"zhzxsj\":\"varchar(20)\",\"rank\":\"int(11)\",\"createdate\":\"datetime\",\"mender\":\"varchar(60)\",\"savedate\":\"datetime\",\"min_price\":\"decimal(7,2)\",\"reserve1\":\"longtext\",\"reserve2\":\"varchar(100)\",\"notice_info\":\"longtext\",\"policy_info\":\"longtext\",\"facilities_info\":\"longtext\",\"reserve6\":\"varchar(100)\",\"reserve7\":\"varchar(100)\",\"reserve8\":\"varchar(100)\",\"reserve9\":\"varchar(100)\",\"reserve0\":\"varchar(100)\",\"reserve3\":\"varchar(1000)\",\"reserve4\":\"varchar(100)\",\"reserve5\":\"varchar(100)\",\"origin_id\":\"varchar(80)\",\"recommend_level\":\"int(11)\",\"country_id\":\"varchar(11)\",\"country_name\":\"varchar(128)\",\"province_id\":\"varchar(20)\",\"province_name\":\"varchar(128)\",\"sparkle\":\"varchar(128)\",\"ai_document_id\":\"varchar(40)\",\"round_info\":\"varchar(11)\"},\"old\":[{\"lon_gaode\":null}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":12,\"interfacePlat\":12,\"jdmc\":12,\"jdmc_en\":12,\"jdlx\":12,\"jddz\":12,\"jddz_en\":12,\"jddh\":12,\"img_url\":12,\"status\":-6,\"lon_google\":3,\"lat_google\":3,\"lon_baidu\":3,\"lat_baidu\":3,\"lon_gaode\":3,\"lat_gaode\":3,\"city_id\":12,\"city_name\":12,\"brand_id\":12,\"brand_name\":12,\"district\":12,\"district_name\":12,\"business_zone\":12,\"business_zone_name\":12,\"jdxj\":-6,\"score\":3,\"jtid\":12,\"jtmc\":12,\"kysj\":12,\"zhzxsj\":12,\"rank\":4,\"createdate\":93,\"mender\":12,\"savedate\":93,\"min_price\":3,\"reserve1\":2005,\"reserve2\":12,\"notice_info\":2005,\"policy_info\":2005,\"facilities_info\":2005,\"reserve6\":12,\"reserve7\":12,\"reserve8\":12,\"reserve9\":12,\"reserve0\":12,\"reserve3\":12,\"reserve4\":12,\"reserve5\":12,\"origin_id\":12,\"recommend_level\":4,\"country_id\":12,\"country_name\":12,\"province_id\":12,\"province_name\":12,\"sparkle\":12,\"ai_document_id\":12,\"round_info\":12},\"table\":\"jd_jdb\",\"ts\":1749623690687,\"type\":\"UPDATE\"}";

}
