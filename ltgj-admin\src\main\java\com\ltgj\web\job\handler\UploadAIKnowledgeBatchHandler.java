package com.ltgj.web.job.handler;

import com.ltgj.ivw.service.AiMappingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "uploadAIKnowledgeBatchHandler")
public class UploadAIKnowledgeBatchHandler extends IJobHandler {
    @Autowired
    private AiMappingService aiMappingService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("开始上传知识库");

        try {
            aiMappingService.uploadKnowledgeForScore();
        } catch (Exception e) {
            log.error("上传知识库失败", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
