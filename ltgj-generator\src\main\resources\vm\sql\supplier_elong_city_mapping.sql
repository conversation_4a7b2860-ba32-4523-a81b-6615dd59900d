-- 供应商与艺龙城市映射关系表
drop table if exists supplier_elong_city_mapping;
create table supplier_elong_city_mapping (
  id                   bigint(20)      not null auto_increment    comment '主键ID',
  supplier_code        varchar(50)     not null                   comment '供应商编码',
  supplier_province_code varchar(50)                              comment '供应商省份编码',
  province_name        varchar(100)                              comment '省份名称',
  supplier_city_code   varchar(50)     not null                   comment '供应商城市编码',
  supplier_city_name   varchar(100)                              comment '供应商城市名称',
  elong_province_code  varchar(50)                               comment '艺龙省份编码',
  elong_city_code      varchar(50)     not null                   comment '艺龙城市编码',
  status               char(1)         default '0'                comment '状态（0正常 1停用）',
  del_flag             char(1)         default '0'                comment '删除标记（0代表存在 1代表删除）',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (id)
) engine=innodb auto_increment=1 comment = '供应商与艺龙城市映射关系表';

-- 创建唯一索引
create unique index idx_supplier_elong_city_mapping_unique on supplier_elong_city_mapping(supplier_code, supplier_city_code, elong_city_code, del_flag);

-- 创建普通索引
create index idx_supplier_elong_city_mapping_supplier_code on supplier_elong_city_mapping(supplier_code);
create index idx_supplier_elong_city_mapping_supplier_city_code on supplier_elong_city_mapping(supplier_city_code);
create index idx_supplier_elong_city_mapping_elong_city_code on supplier_elong_city_mapping(elong_city_code); 