# 企业微信自建应用精准单人消息发送技术文档

## 目录
1. [核心流程概述](#1-核心流程概述)
2. [认证凭证获取](#2-认证凭证获取)
3. [Access Token获取](#3-access-token获取)
4. [获取用户列表API](#4-获取用户列表api)
5. [获取特定用户信息API](#5-获取特定用户信息api)
6. [精准单人消息发送API](#6-精准单人消息发送api)
7. [用户查找和筛选策略](#7-用户查找和筛选策略)
8. [完整实现流程](#8-完整实现流程)
9. [错误处理和最佳实践](#9-错误处理和最佳实践)

---

## 1. 核心流程概述

### 1.1 整体流程图
```
1. 获取Access Token
    ↓
2. 获取部门用户列表 (可选：按部门筛选)
    ↓
3. 根据条件查找目标用户 (姓名/手机号/邮箱等)
    ↓
4. 获取用户详细信息 (可选：验证用户状态)
    ↓
5. 使用userid精准发送消息给特定用户
```

### 1.2 关键点说明
- **用户标识**: 企业微信使用`userid`作为用户的唯一标识，而不是微信号
- **精准发送**: 在消息发送API的`touser`参数中只指定一个`userid`
- **用户查找**: 支持通过姓名、手机号、邮箱等多种方式查找用户
- **权限范围**: 只能操作应用可见范围内的用户

---

## 2. 认证凭证获取

### 2.1 必需的认证信息
| 参数 | 获取位置 | 格式 | 用途 |
|------|----------|------|------|
| corpid | 企业微信管理后台 → "我的企业" → "企业信息" | ww开头18位字符串 | 企业身份标识 |
| corpsecret | 应用管理 → 自建应用 → "查看Secret" | 64位字符串 | 应用密钥 |
| agentid | 自建应用详情页面 | 数字 | 应用标识 |

---

## 3. Access Token获取

### 3.1 API接口
```http
GET https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}
```

### 3.2 返回结果
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "access_token": "accesstoken000001",
    "expires_in": 7200
}
```

---

## 4. 获取用户列表API

### 4.1 获取部门用户列表
**接口地址**:
```http
GET https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token={access_token}&department_id={department_id}&fetch_child={fetch_child}
```

**请求参数**:
| 参数 | 类型 | 必须 | 说明 |
|------|------|------|------|
| access_token | string | 是 | 调用接口凭证 |
| department_id | int | 是 | 获取的部门id，1为根部门 |
| fetch_child | int | 否 | 是否递归获取子部门下面的成员：1-递归获取，0-只获取本部门 |

**返回结果**:
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "userlist": [
        {
            "userid": "zhangsan",
            "name": "张三",
            "department": [1, 2],
            "position": "产品经理",
            "mobile": "13800000000",
            "gender": "1",
            "email": "<EMAIL>",
            "avatar": "http://wx.qlogo.cn/...",
            "status": 1,
            "enable": 1,
            "isleader": 0,
            "telephone": "020-123456",
            "english_name": "jackzhang",
            "address": "广州市海珠区新港中路"
        }
    ]
}
```

### 4.2 获取全量用户列表（支持分页）
**接口地址**:
```http
POST https://qyapi.weixin.qq.com/cgi-bin/user/list_id?access_token={access_token}
```

**请求参数**:
```json
{
    "cursor": "",
    "limit": 10000
}
```

---

## 5. 获取特定用户信息API

### 5.1 根据userid获取用户详情
**接口地址**:
```http
GET https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token={access_token}&userid={userid}
```

**请求参数**:
| 参数 | 类型 | 必须 | 说明 |
|------|------|------|------|
| access_token | string | 是 | 调用接口凭证 |
| userid | string | 是 | 成员userid |

**返回结果**:
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "userid": "zhangsan",
    "name": "张三",
    "department": [1, 2],
    "position": "产品经理",
    "mobile": "13800000000",
    "gender": "1",
    "email": "<EMAIL>",
    "avatar": "http://wx.qlogo.cn/...",
    "status": 1,
    "enable": 1,
    "isleader": 0,
    "telephone": "020-123456",
    "english_name": "jackzhang",
    "address": "广州市海珠区新港中路",
    "qr_code": "https://open.work.weixin.qq.com/..."
}
```

### 5.2 用户状态说明
| 字段 | 说明 |
|------|------|
| status | 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业 |
| enable | 启用状态: 1=启用，0=禁用 |

---

## 6. 精准单人消息发送API

### 6.1 发送文本消息给特定用户
**接口地址**:
```http
POST https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}
```

**请求参数（单人发送）**:
```json
{
    "touser": "zhangsan",
    "toparty": "",
    "totag": "",
    "msgtype": "text",
    "agentid": 1000002,
    "text": {
        "content": "张三，您好！这是一条专门发送给您的消息。"
    },
    "safe": 0,
    "enable_id_trans": 0,
    "enable_duplicate_check": 1,
    "duplicate_check_interval": 1800
}
```

**关键点说明**:
- `touser`: 只填写一个用户的userid，实现精准单人发送
- `toparty`和`totag`: 留空，避免群发
- `enable_duplicate_check`: 建议设为1，避免重复发送

### 6.2 发送图文消息给特定用户
```json
{
    "touser": "zhangsan",
    "msgtype": "news",
    "agentid": 1000002,
    "news": {
        "articles": [
            {
                "title": "专属通知 - 张三",
                "description": "这是专门发送给您的个人通知...",
                "url": "https://your-system.com/personal/zhangsan",
                "picurl": "https://your-system.com/images/personal-notice.png"
            }
        ]
    }
}
```

---

## 7. 用户查找和筛选策略

### 7.1 按姓名查找用户
```
实现逻辑：
1. 获取部门用户列表
2. 遍历用户列表，匹配name字段
3. 支持模糊匹配和精确匹配
```

### 7.2 按手机号查找用户
```
实现逻辑：
1. 获取用户列表
2. 匹配mobile字段
3. 注意：手机号可能为空或隐私保护
```

### 7.3 按邮箱查找用户
```
实现逻辑：
1. 获取用户列表
2. 匹配email字段
3. 支持邮箱前缀匹配
```

### 7.4 按部门+职位查找
```
实现逻辑：
1. 先筛选指定部门
2. 再根据position字段筛选
3. 支持多级部门查找
```

### 7.5 高级查找策略
```
组合条件查找：
- 部门 + 姓名
- 职位 + 邮箱
- 状态过滤（只查找已激活用户）
- 权限过滤（只查找应用可见用户）
```

---

## 8. 完整实现流程

### 8.1 基本流程示例
```
步骤1: 初始化API客户端
├── 配置corpid, corpsecret, agentid
└── 获取并缓存access_token

步骤2: 查找目标用户
├── 方式A: 根据已知userid直接获取
├── 方式B: 根据姓名在部门中查找
├── 方式C: 根据手机号查找
└── 方式D: 根据邮箱查找

步骤3: 验证用户状态
├── 检查用户是否已激活(status=1)
├── 检查用户是否启用(enable=1)
└── 检查用户是否在应用可见范围内

步骤4: 构造消息内容
├── 个性化消息内容（可使用用户姓名）
├── 选择消息类型（文本/图文/卡片等）
└── 设置消息参数

步骤5: 发送消息
├── 调用发送消息API
├── 检查发送结果
└── 记录发送日志
```

### 8.2 错误处理流程
```
用户查找失败：
├── 用户不存在 → 记录日志，返回错误信息
├── 用户未激活 → 提示用户激活企业微信
├── 用户不在可见范围 → 检查应用配置
└── 权限不足 → 检查应用权限设置

消息发送失败：
├── access_token过期 → 重新获取token
├── 消息内容违规 → 修改消息内容
├── 频率限制 → 实现重试机制
└── 网络错误 → 重试发送
```

---

## 9. 错误处理和最佳实践

### 9.1 常见错误码
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 60111 | userid不存在 | 检查用户是否在企业中，或userid拼写错误 |
| 60112 | 用户不在应用可见范围内 | 在应用管理中添加用户到可见范围 |
| 40003 | 无效的userid | 检查userid格式和有效性 |
| 301 | 用户不存在 | 用户可能已被删除或禁用 |
| 60020 | IP白名单限制 | 检查服务器IP是否在白名单中 |

### 9.2 性能优化建议
```
1. 用户信息缓存：
   - 缓存用户列表，避免频繁API调用
   - 设置合理的缓存过期时间（建议1小时）
   - 实现增量更新机制

2. 批量操作优化：
   - 一次性获取部门所有用户
   - 本地筛选目标用户
   - 避免逐个调用用户详情接口

3. 并发控制：
   - 限制API调用频率
   - 实现队列机制处理大量消息
   - 错峰发送，避开高峰期
```

### 9.3 安全性考虑
```
1. 敏感信息保护：
   - 加密存储corpsecret
   - 不在日志中记录用户敏感信息
   - 定期轮换密钥

2. 权限最小化：
   - 只获取必要的用户信息
   - 限制应用可见范围
   - 定期审核用户权限

3. 消息内容安全：
   - 过滤敏感内容
   - 验证消息来源
   - 记录消息发送日志
```

### 9.4 监控和日志
```
关键监控指标：
- API调用成功率
- 消息发送成功率
- 用户查找命中率
- 接口响应时间

日志记录内容：
- 用户查找记录（去敏感化）
- 消息发送记录
- 错误信息和重试记录
- 性能统计数据
```

---

## 10. 示例场景应用

### 10.1 生日祝福推送
```
场景：每天定时给当天生日的员工发送生日祝福

实现步骤：
1. 获取所有用户列表
2. 筛选今天生日的用户（需要用户资料中有生日信息）
3. 给每个生日用户发送个性化祝福消息
4. 记录发送结果
```

### 10.2 任务分配通知
```
场景：系统中有新任务分配给特定用户时，发送通知

实现步骤：
1. 根据系统用户ID关联企业微信userid
2. 验证用户状态和权限
3. 发送任务详情消息（包含任务链接）
4. 记录通知状态
```

### 10.3 审批流程通知
```
场景：审批流程到达特定节点时，通知对应审批人

实现步骤：
1. 根据审批人角色查找对应企业微信用户
2. 构造包含审批详情的消息
3. 发送给特定审批人
4. 跟踪审批状态
```

---

**总结**：通过以上API和流程，您可以实现精准的单人消息推送，关键是要先通过用户列表API获取到目标用户的userid，然后在发送消息时只指定这一个userid，从而避免群发效果。 