package com.ltgj.ivw.strategy.impl;

import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.mapper.GeoProvinceMapper;
import com.ltgj.ivw.strategy.GeoQueryStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 国家查询策略实现
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@Slf4j
public class CountryQueryStrategy implements GeoQueryStrategy {
    
    @Autowired
    private GeoProvinceMapper geoProvinceMapper;
    
    @Override
    public List<GeoHierarchyNode> execute(GeoLevelQueryReq request) {
        log.info("执行国家查询策略，参数：{}", request);
        
        // 查询所有有效的国家信息
        List<Map<String, Object>> countries = geoProvinceMapper.selectDistinctCountries(true);
        
        return countries.stream()
                .map(this::convertToNode)
                .collect(Collectors.toList());
    }
    
    @Override
    public String getSupportedLevel() {
        return "country";
    }
    
    /**
     * 转换为层级节点
     */
    private GeoHierarchyNode convertToNode(Map<String, Object> countryMap) {
        GeoHierarchyNode node = new GeoHierarchyNode();
        node.setId(String.valueOf(countryMap.get("nation")));
        node.setName(String.valueOf(countryMap.get("nationName")));
        node.setType("country");
        node.setLevel(1);
        return node;
    }
    
} 