package com.ltgj.ivw.controller;

import com.ltgj.common.annotation.DynamicPreAuthorize;
import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.poi.ExcelUtil;
import com.ltgj.ivw.service.BaseHotelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 抽象的IVW控制器，提供通用的CRUD操作
 * 支持实体类特有的业务逻辑扩展
 * 
 * 注解驱动设计：
 * - @Table：动态获取表名
 * - @LogDynamicInfo：动态获取业务标题
 * - @DynamicPreAuthorize：动态权限控制
 *
 * @param <T> 实体类型
 * @param <S> 查询条件类型
 * @param <V> 业务服务类型
 */
@RestController
@Slf4j
public abstract class AbstractIvwController<T, S, V extends BaseHotelService<T, S>> extends BaseController {

    @Autowired
    protected V hotelService;

    /**
     * 获取列表-通用
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @return 分页数据
     */
    @DynamicPreAuthorize("ivw:{generic}:list")
    @GetMapping("/list")
    public TableDataInfo list(T entity, S searchParams) {
        startPage();
        
        // 预处理查询参数（子类可重写）
        entity = preprocessQueryEntity(entity, searchParams);
        
        // 构建查询条件（支持特有字段）
        Map<String, Object> queryParams = buildQueryParams(entity, searchParams);
        
        List<T> list = hotelService.selectListWithParams(entity, queryParams);
        
        // 后处理结果（子类可重写）
        list = postprocessQueryResult(list, searchParams);
        
        return getDataTable(list);
    }

    /**
     * 获取详细信息
     *
     * @param id 实体ID
     * @param searchParams 查询参数
     * @return Ajax结果
     */
    @DynamicPreAuthorize("ivw:{generic}:query")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id, S searchParams) {
        if (StringUtils.isEmpty(id)) {
            return error("ID不能为空");
        }
        
        T entity = hotelService.selectById(id);
        if (entity == null) {
            return error("数据不存在");
        }
        
        // 后处理单个实体（子类可重写）
        entity = postprocessSingleEntity(entity, searchParams);
        
        return success(entity);
    }

    /**
     * 新增实体
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @return Ajax结果
     */
    @DynamicPreAuthorize("ivw:{generic}:add")
    @Log(title = "{dynamicTitle}", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody T entity, S searchParams) {
        // 数据验证（子类可重写）
        String validationResult = validateEntity(entity, true);
        if (StringUtils.isNotEmpty(validationResult)) {
            return error(validationResult);
        }
        
        // 预处理实体（子类可重写）
        entity = preprocessEntity(entity, searchParams, true);
        
        int result = hotelService.insert(entity);
        
        // 插入后处理（子类可重写）
        afterInsert(entity, result);
        
        return toAjax(result);
    }

    /**
     * 修改实体
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @return Ajax结果
     */
    @DynamicPreAuthorize("ivw:{generic}:edit")
    @Log(title = "{dynamicTitle}", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody T entity, S searchParams) {
        // 数据验证（子类可重写）
        String validationResult = validateEntity(entity, false);
        if (StringUtils.isNotEmpty(validationResult)) {
            return error(validationResult);
        }
        
        // 预处理实体（子类可重写）
        entity = preprocessEntity(entity, searchParams, false);
        
        int result = hotelService.update(entity);
        
        // 更新后处理（子类可重写）
        afterUpdate(entity, result);
        
        return toAjax(result);
    }

    /**
     * 删除实体
     *
     * @param ids 实体ID数组
     * @param searchParams 查询参数
     * @return Ajax结果
     */
    @DynamicPreAuthorize("ivw:{generic}:delete")
    @Log(title = "{dynamicTitle}", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable String[] ids, S searchParams) {
        if (ids == null || ids.length == 0) {
            return error("请选择要删除的数据");
        }
        
        // 删除前验证（子类可重写）
        String validationResult = validateBeforeDelete(ids, searchParams);
        if (StringUtils.isNotEmpty(validationResult)) {
            return error(validationResult);
        }
        
        int result = hotelService.deleteByIds(ids);
        
        // 删除后处理（子类可重写）
        afterDelete(ids, result);
        
        return toAjax(result);
    }

    /**
     * 导出数据-通用
     *
     * @param response HTTP响应对象
     * @param entity 查询实体
     * @param searchParams 查询参数
     */
    @DynamicPreAuthorize("ivw:{generic}:export")
    @Log(title = "{dynamicTitle}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, T entity, S searchParams) {
        try {
            log.info("开始导出数据，实体：{}，查询参数：{}", entity, searchParams);
            
            // 1. 通过Service获取导出数据列表
            List<?> dataList = this.hotelService.exportData(entity, searchParams);
            
            // 2. 通过Service获取导出实体类型
            Class<?> exportClass = this.hotelService.getExportEntityClass();
            
            // 3. 通过Service获取导出文件名
            String fileName = this.hotelService.getExportFileName(searchParams);
            
            // 4. 执行Excel导出
            @SuppressWarnings("unchecked")
            ExcelUtil<Object> util = new ExcelUtil<>((Class<Object>) exportClass);
            util.exportExcel(response, (List<Object>) dataList, fileName);
            
            log.info("导出完成，数据量：{}，文件名：{}", dataList.size(), fileName);
            
        } catch (Exception e) {
            log.error("导出数据失败", e);
            throw new RuntimeException("导出数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询单个实体（供子类控制器直接调用）
     * 
     * @param id 实体ID
     * @return 实体对象
     */
    protected T selectById(String id) {
        return hotelService.selectById(id);
    }

    // ==================== 扩展点方法 ====================

    /**
     * 预处理查询实体（子类可重写）
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @return 处理后的实体
     */
    protected T preprocessQueryEntity(T entity, S searchParams) {
        return entity;
    }

    /**
     * 构建查询参数（子类可重写，支持特有字段）
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @return 查询参数Map
     */
    protected Map<String, Object> buildQueryParams(T entity, S searchParams) {
        return hotelService.buildDefaultQueryParams(entity, searchParams);
    }

    /**
     * 后处理查询结果（子类可重写）
     *
     * @param list 查询结果列表
     * @param searchParams 查询参数
     * @return 处理后的列表
     */
    protected List<T> postprocessQueryResult(List<T> list, S searchParams) {
        return list;
    }

    /**
     * 后处理单个实体（子类可重写）
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @return 处理后的实体
     */
    protected T postprocessSingleEntity(T entity, S searchParams) {
        return entity;
    }

    /**
     * 实体验证（子类可重写）
     *
     * @param entity 实体对象
     * @param isInsert 是否为新增操作
     * @return 验证错误信息，为空表示验证通过
     */
    protected String validateEntity(T entity, boolean isInsert) {
        return hotelService.validateEntity(entity, isInsert);
    }

    /**
     * 预处理实体（子类可重写）
     *
     * @param entity 实体对象
     * @param searchParams 查询参数
     * @param isInsert 是否为新增操作
     * @return 处理后的实体
     */
    protected T preprocessEntity(T entity, S searchParams, boolean isInsert) {
        return hotelService.preprocessEntity(entity, searchParams, isInsert);
    }

    /**
     * 删除前验证（子类可重写）
     *
     * @param ids ID数组
     * @param searchParams 查询参数
     * @return 验证错误信息，为空表示验证通过
     */
    protected String validateBeforeDelete(String[] ids, S searchParams) {
        return hotelService.validateBeforeDelete(ids, searchParams);
    }

    /**
     * 插入后处理（子类可重写）
     *
     * @param entity 实体对象
     * @param result 插入结果
     */
    protected void afterInsert(T entity, int result) {
        hotelService.afterInsert(entity, result);
    }

    /**
     * 更新后处理（子类可重写）
     *
     * @param entity 实体对象
     * @param result 更新结果
     */
    protected void afterUpdate(T entity, int result) {
        hotelService.afterUpdate(entity, result);
    }

    /**
     * 删除后处理（子类可重写）
     *
     * @param ids ID数组
     * @param result 删除结果
     */
    protected void afterDelete(String[] ids, int result) {
        hotelService.afterDelete(ids, result);
    }

    /**
     * 获取业务标题（通过服务层自动获取）
     * 支持从 @LogDynamicInfo 注解自动获取标题
     *
     * @return 业务标题
     */
    protected String getBusinessTitle() {
        return hotelService.getBusinessTitle();
    }

    /**
     * 获取表名（通过服务层自动获取）
     * 支持从 @Table 注解自动获取表名
     *
     * @return 表名
     */
    protected String getTableName() {
        return hotelService.getTableName();
    }
}