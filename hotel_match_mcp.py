import json
import re
import openai

# DeepSeek API配置
openai.api_key = "YOUR_DEEPSEEK_API_KEY"  # 请替换为你的DeepSeek API Key
openai.base_url = "https://api.deepseek.com/v1"

# 读取英文极致严谨提示词
with open("hotel-matching-prompt-english.mdc", "r", encoding="utf-8") as f:
    strict_prompt = f.read()

def call_llm(prompt, input_data, model="deepseek-chat"):
    """调用DeepSeek大模型，返回原始输出字符串"""
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": f"Input data:\n{json.dumps(input_data, ensure_ascii=False)}"}
    ]
    response = openai.ChatCompletion.create(
        model=model,
        messages=messages,
        temperature=0
    )
    return response.choices[0].message.content.strip()

def strict_validate_llm_output(llm_output, input_data):
    """
    对大模型输出进行极致严谨校验。
    校验不通过则抛出异常或返回致命错误信息。
    """
    # 只保留第一个JSON对象
    match = re.search(r'\{[\s\S]*\}', llm_output)
    if not match:
        raise ValueError("FATAL ERROR: Output is not a valid JSON object.")

    try:
        result = json.loads(match.group())
    except Exception:
        raise ValueError("FATAL ERROR: Output is not valid JSON.")

    # 1. 必须字段
    required_fields = ["id", "targetId", "score", "nameScore", "addressScore", "phoneScore", "cityScore", "reason", "remark"]
    for field in required_fields:
        if field not in result:
            raise ValueError(f"FATAL ERROR: Missing field '{field}'.")

    # 2. 分数字段类型与范围
    for field in ["score", "nameScore", "addressScore", "phoneScore", "cityScore"]:
        if not isinstance(result[field], int):
            raise ValueError(f"FATAL ERROR: Field '{field}' must be integer.")
    if result["cityScore"] > 10:
        raise ValueError("FATAL ERROR: cityScore exceeded 10.")
    if result["score"] > 100:
        raise ValueError("FATAL ERROR: Total score exceeded 100.")
    if result["remark"] != "":
        raise ValueError("FATAL ERROR: remark must be empty string.")

    # 3. reason字段校验
    zero_reasons = []
    if result["nameScore"] == 0:
        zero_reasons.append("名称不一致")
    if result["addressScore"] == 0:
        zero_reasons.append("地址不一致")
    if result["phoneScore"] == 0:
        zero_reasons.append("电话不一致")
    if result["cityScore"] == 0:
        zero_reasons.append("城市不一致")
    if result["score"] == 100:
        expected_reason = "完全匹配"
    else:
        expected_reason = ", ".join(zero_reasons)
    if result["reason"] != expected_reason:
        raise ValueError(f"FATAL ERROR: reason field incorrect. Expected: '{expected_reason}', Got: '{result['reason']}'")

    # 4. 唯一输出校验
    if llm_output.count("{") > 1 or llm_output.count("}") > 1:
        raise ValueError("FATAL ERROR: Output contains multiple JSON objects or extra content.")

    return result

def hotel_match_mcp(input_data):
    """
    MCP主入口：输入酒店数据，返回极致严谨的匹配结果（或致命错误）。
    """
    llm_output = call_llm(strict_prompt, input_data)
    try:
        result = strict_validate_llm_output(llm_output, input_data)
        return result
    except Exception as e:
        return {"error": str(e)}

# 示例用法
if __name__ == "__main__":
    # 你的输入数据
    input_data = [
        {
            "id": "M12164524",
            "name": "海口西海驿站(免税城会展中心店)",
            "address": "海口-秀英区-滨海大道266号天利龙腾湾1号楼24层（近海南国际会展中心大厅100米）",
            "phone": "18876839108",
            "city": "海口",
            "mult": [
                {
                    "id": "abc",
                    "name": "北京大酒店",
                    "address": "海口-秀英区-滨海大道269号天利龙腾湾1号楼24层（近海南国际会展中心大厅100米）",
                    "phone": "18876839108",
                    "city": "海口"
                },
                {
                    "id": "M33628054",
                    "name": "邵东时尚精品旅馆",
                    "address": "南苑路图书城7栋8-10号",
                    "phone": "0739-2631198",
                    "city": "邵阳"
                }
            ]
        }
    ]
    result = hotel_match_mcp(input_data)
    print(json.dumps(result, ensure_ascii=False, indent=2)) 