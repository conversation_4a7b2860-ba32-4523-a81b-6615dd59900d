package com.ltgj.supplier.common.gn.domain;

import com.ltgj.common.annotation.LogDynamicInfo;
import lombok.Data;

import java.util.Date;

@Data
@LogDynamicInfo(title = "国内酒店-集团品牌-映射")
public class HotelGnGBMapping {

    /**
     * ID
     */
    private String id;

    /**
     * 平台编号，20000XX
     */
    private String platformId;

    /**
     * 平台-集团id
     */
    private String platformGbId;

    /**
     * 平台-集团名称
     */
    private String platformGbName;

    /**
     * 映射-平台编号：-1，基础平台；艺龙：200004
     */
    private String mappingPlatfromId;

    /**
     * 映射-集团id
     */
    private String mappingGbId;

    /**
     * 映射-集团名称
     */
    private String mappingGbName;

    /**
     * 类型，集团：group；品牌：brand
     */
    private String type;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 删除标识，0：未删除；1：删除
     */
    private Integer isDelete;

}
