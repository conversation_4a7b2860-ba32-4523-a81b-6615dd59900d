<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.supplier.common.gn.mapper.HotelGnGeoMappingMapper">
  <resultMap id="BaseResultMap" type="com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
    <result column="platform_city_id" jdbcType="VARCHAR" property="platformCityId" />
    <result column="platform_geo_id" jdbcType="VARCHAR" property="platformGeoId" />
    <result column="platform_geo_name" jdbcType="VARCHAR" property="platformGeoName" />
    <result column="mapping_platform_id" jdbcType="VARCHAR" property="mappingPlatformId" />
    <result column="mapping_city_id" jdbcType="VARCHAR" property="mappingCityId" />
    <result column="mapping_geo_id" jdbcType="VARCHAR" property="mappingGeoId" />
    <result column="mapping_geo_name" jdbcType="VARCHAR" property="mappingGeoName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_id, platform_city_id, platform_geo_id, platform_geo_name, mapping_platform_id, 
    mapping_city_id, mapping_geo_id, mapping_geo_name, remark, create_time, update_time, 
    create_by, update_by, is_delete
  </sql>
  <select id="selectByExample" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGeoMappingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hotel_gn_geo_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hotel_gn_geo_mapping
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hotel_gn_geo_mapping
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGeoMappingExample">
    delete from hotel_gn_geo_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping">
    insert into hotel_gn_geo_mapping (platform_id, platform_city_id, platform_geo_id, 
      platform_geo_name, mapping_platform_id, mapping_city_id, 
      mapping_geo_id, mapping_geo_name, remark, 
      create_time, update_time, create_by, 
      update_by, is_delete)
    values (#{platformId,jdbcType=VARCHAR}, #{platformCityId,jdbcType=VARCHAR}, #{platformGeoId,jdbcType=VARCHAR}, 
      #{platformGeoName,jdbcType=VARCHAR}, #{mappingPlatformId,jdbcType=VARCHAR}, #{mappingCityId,jdbcType=VARCHAR}, 
      #{mappingGeoId,jdbcType=VARCHAR}, #{mappingGeoName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{updateBy,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping" >
    insert into hotel_gn_geo_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="platformCityId != null">
        platform_city_id,
      </if>
      <if test="platformGeoId != null">
        platform_geo_id,
      </if>
      <if test="platformGeoName != null">
        platform_geo_name,
      </if>
      <if test="mappingPlatformId != null">
        mapping_platform_id,
      </if>
      <if test="mappingCityId != null">
        mapping_city_id,
      </if>
      <if test="mappingGeoId != null">
        mapping_geo_id,
      </if>
      <if test="mappingGeoName != null">
        mapping_geo_name,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="platformCityId != null">
        #{platformCityId,jdbcType=VARCHAR},
      </if>
      <if test="platformGeoId != null">
        #{platformGeoId,jdbcType=VARCHAR},
      </if>
      <if test="platformGeoName != null">
        #{platformGeoName,jdbcType=VARCHAR},
      </if>
      <if test="mappingPlatformId != null">
        #{mappingPlatformId,jdbcType=VARCHAR},
      </if>
      <if test="mappingCityId != null">
        #{mappingCityId,jdbcType=VARCHAR},
      </if>
      <if test="mappingGeoId != null">
        #{mappingGeoId,jdbcType=VARCHAR},
      </if>
      <if test="mappingGeoName != null">
        #{mappingGeoName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGeoMappingExample" resultType="java.lang.Long">
    select count(*) from hotel_gn_geo_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hotel_gn_geo_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformCityId != null">
        platform_city_id = #{record.platformCityId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformGeoId != null">
        platform_geo_id = #{record.platformGeoId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformGeoName != null">
        platform_geo_name = #{record.platformGeoName,jdbcType=VARCHAR},
      </if>
      <if test="record.mappingPlatformId != null">
        mapping_platform_id = #{record.mappingPlatformId,jdbcType=VARCHAR},
      </if>
      <if test="record.mappingCityId != null">
        mapping_city_id = #{record.mappingCityId,jdbcType=VARCHAR},
      </if>
      <if test="record.mappingGeoId != null">
        mapping_geo_id = #{record.mappingGeoId,jdbcType=VARCHAR},
      </if>
      <if test="record.mappingGeoName != null">
        mapping_geo_name = #{record.mappingGeoName,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BOOLEAN},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hotel_gn_geo_mapping
    set id = #{record.id,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      platform_city_id = #{record.platformCityId,jdbcType=VARCHAR},
      platform_geo_id = #{record.platformGeoId,jdbcType=VARCHAR},
      platform_geo_name = #{record.platformGeoName,jdbcType=VARCHAR},
      mapping_platform_id = #{record.mappingPlatformId,jdbcType=VARCHAR},
      mapping_city_id = #{record.mappingCityId,jdbcType=VARCHAR},
      mapping_geo_id = #{record.mappingGeoId,jdbcType=VARCHAR},
      mapping_geo_name = #{record.mappingGeoName,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      is_delete = #{record.isDelete,jdbcType=BOOLEAN}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping">
    update hotel_gn_geo_mapping
    <set>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="platformCityId != null">
        platform_city_id = #{platformCityId,jdbcType=VARCHAR},
      </if>
      <if test="platformGeoId != null">
        platform_geo_id = #{platformGeoId,jdbcType=VARCHAR},
      </if>
      <if test="platformGeoName != null">
        platform_geo_name = #{platformGeoName,jdbcType=VARCHAR},
      </if>
      <if test="mappingPlatformId != null">
        mapping_platform_id = #{mappingPlatformId,jdbcType=VARCHAR},
      </if>
      <if test="mappingCityId != null">
        mapping_city_id = #{mappingCityId,jdbcType=VARCHAR},
      </if>
      <if test="mappingGeoId != null">
        mapping_geo_id = #{mappingGeoId,jdbcType=VARCHAR},
      </if>
      <if test="mappingGeoName != null">
        mapping_geo_name = #{mappingGeoName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BOOLEAN},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping">
    update hotel_gn_geo_mapping
    set platform_id = #{platformId,jdbcType=VARCHAR},
      platform_city_id = #{platformCityId,jdbcType=VARCHAR},
      platform_geo_id = #{platformGeoId,jdbcType=VARCHAR},
      platform_geo_name = #{platformGeoName,jdbcType=VARCHAR},
      mapping_platform_id = #{mappingPlatformId,jdbcType=VARCHAR},
      mapping_city_id = #{mappingCityId,jdbcType=VARCHAR},
      mapping_geo_id = #{mappingGeoId,jdbcType=VARCHAR},
      mapping_geo_name = #{mappingGeoName,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by = #{updateBy,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=BOOLEAN}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsertOrUpdate" parameterType="java.util.List">
    insert into hotel_gn_geo_mapping (
        id, platform_id, platform_city_id, platform_geo_id, platform_geo_name, mapping_platform_id, 
        mapping_city_id, mapping_geo_id, mapping_geo_name, remark, create_time, update_time, 
        create_by, update_by, is_delete
    )
    values
    <foreach collection="list" item="item" separator=",">
        (
            #{item.id}, #{item.platformId}, #{item.platformCityId}, #{item.platformGeoId}, #{item.platformGeoName},
            #{item.mappingPlatformId}, #{item.mappingCityId}, #{item.mappingGeoId}, #{item.mappingGeoName},
            #{item.remark}, #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}, #{item.isDelete}
        )
    </foreach>
    on duplicate key update
        platform_id = values(platform_id),
        platform_city_id = values(platform_city_id),
        platform_geo_id = values(platform_geo_id),
        platform_geo_name = values(platform_geo_name),
        mapping_platform_id = values(mapping_platform_id),
        mapping_city_id = values(mapping_city_id),
        mapping_geo_id = values(mapping_geo_id),
        mapping_geo_name = values(mapping_geo_name),
        remark = values(remark),
        create_time = values(create_time),
        update_time = values(update_time),
        create_by = values(create_by),
        update_by = values(update_by),
        is_delete = values(is_delete)
  </insert>
</mapper>