package com.ltgj.ivw.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * HotelInfoChailvgjServiceImpl的updateAll方法单元测试
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class IHotelInfoKetanPreMappingServiceTest {
    @Autowired
    private IHotelInfoKetanPreMappingService service;

    @Test
    public void addMapping() {
        try {
            long l = System.currentTimeMillis();
            log.info("开始执行");
            service.addMapping();
            log.info("结束执行");
            log.info("耗时：{}", System.currentTimeMillis() - l);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void processMapping() {
        try {
            long l = System.currentTimeMillis();
            log.info("开始执行");
            service.processMapping();
            log.info("结束执行");
            log.info("耗时：{}", System.currentTimeMillis() - l);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

} 