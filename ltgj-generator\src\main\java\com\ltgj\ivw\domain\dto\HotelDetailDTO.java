package com.ltgj.ivw.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 酒店详情数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class HotelDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 酒店ID */
    private String hotelId;

    /** 酒店名称 */
    private String hotelName;

    /** 酒店英文名称 */
    private String hotelNameEn;

    /** 酒店头图 **/
    private String logUrl;

    /** 地级市ID */
    private String cityId;

    /** 地级市名称 */
    private String cityName;

    /** 城市ID */
    private String areaId;

    /** 城市名称 */
    private String areaName;

    /** 酒店地址 */
    private String address;

    /** 酒店星级 */
    private Integer starRating;

    /** 星级描述 */
    private String starDesc;

    /** 区域 */
    private String district;

    /** 商业区 */
    private String businessArea;

    /** 电话 */
    private String telephone;

    /** 品牌编码 */
    private String brandCode;

    /** 品牌名称 */
    private String brandName;

    /** 开业年份 */
    private String openingYear;

    /** 入离描述 */
    private String inOutDesc;

    /** 装修年份 */
    private String renovationYear;

    /** 评分 */
    private BigDecimal reviewScore;

    /** 是否可预订 */
    private Boolean isBookable;

    /** 是否稀缺资源 */
    private Boolean isScarce;

    /** 是否优势资源 */
    private Boolean isAdvantage;

    /** 是否高需求资源 */
    private Boolean isHighDemand;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 酒店图片列表 */
    private List<ImageDTO> images;

    /** 映射关系列表 */
    private List<MappingDTO> mappings;

    /**
     * 原始数据
     */
    private String originalText;

    /**
     * 宾客类型
     */
    private Integer guestType;

    /**
     * 设施列表
     */
    private List<Integer> facilityList;

    /**
     * 当前登录用户名称
     */
    private String currentUserName;
}