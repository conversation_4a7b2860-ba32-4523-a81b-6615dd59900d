package com.ltgj.ivw.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.domain.ZhJdJdbMapping;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.utils.HotelDataFormatUtil;
import com.ltgj.ivw.utils.dto.AIMappingResultDto;
import com.ltgj.ivw.utils.dto.HotelInfoDTO;
import com.ltgj.supplier.common.AbstractHotelService;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.common.gn.service.HotelGnIdMappingService;
import com.ltgj.supplier.common.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HotelPreMappingManager extends AbstractHotelService {

    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;
    @Autowired
    private HotelGnPreMappingService hotelGnPreMappingService;
    @Autowired
    private HotelGnIdMappingService hotelGnIdMappingService;
    @Autowired
    private HotelGnBaseService hotelGnBaseService;
    @Autowired
    private DifyApiService difyApiService;
    @Autowired
    @Qualifier("difyApiService2Impl")
    private DifyApiService difyApiService2;

    private IdUtil sequence = new IdUtil();

    @Value("${hotel.dify.default.score:90}")
    private Integer hotelDifyDefaultScore;

    @Autowired
    private RedissonClient redissonClient;

    public void dealHotelCanalMsg(JSONArray dataList, PlatEnum plat, String type, JSONArray oldList) {
        for (int i = 0; i < dataList.size(); i++) {
            if ("UPDATE".equals(type)) {
                JSONObject oldData = oldList.getJSONObject(i);
                if (!(oldData.containsKey("name") || oldData.containsKey("city_name") || oldData.containsKey("address") || oldData.containsKey("phone"))) {
                    continue;
                }
            }

            JSONObject data = dataList.getJSONObject(i);
            //主键id
            String id = data.getString("id");
            String name = data.getString("name");
            String cityName = data.getString("city_name");
            String addressLine = data.getString("address");
            String phone = data.getString("phone");

            //根据主键id查询酒店映射是否存在
            List<ZhJdJdbMapping> hsMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(id, Long.parseLong(plat.getValue()));
            // ai匹配
            AIMappingResultDto aiMappingResultDto = hotelDifyAi(name, cityName, addressLine, phone, id, plat);
            // ai匹配基础数据
            JdJdb aiJdJdb = Objects.isNull(hsMappings) ? null : jdJdbService.selectJdJdbById(hsMappings.get(hsMappings.size() - 1).getLocalId());
            if (CollectionUtils.isEmpty(hsMappings)) {
                // id映射
                HotelGnIdMapping hotelGnIdMapping = hotelGnIdMappingService.queryByPlatformIdAndHotelId(plat.getValue(), id);
                String targetPlatformId = Objects.isNull(hotelGnIdMapping) ? null : hotelGnIdMapping.getMappingPlatformId();
                String targetHotelId = Objects.isNull(hotelGnIdMapping) ? null : hotelGnIdMapping.getMappingHotelId();
                if (Objects.isNull(hotelGnIdMapping)) {
                    if (Objects.nonNull(aiMappingResultDto)) {
                        targetPlatformId = "-1";
                    }
                    addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "AI", aiMappingResultDto, aiJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                    return;
                }
                // id映射基础数据
                JdJdb idJdJdb = PlatEnum.PLAT_BASE.getValue().equals(targetPlatformId) ?
                        jdJdbService.selectJdJdbById(hotelGnIdMapping.getMappingHotelId()) :
                        jdJdbService.selectJdJdbById(hotelGnIdMapping.getMappingPlatformId() + hotelGnIdMapping.getMappingHotelId());
                if (Objects.isNull(idJdJdb)) {
                    addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "AI", aiMappingResultDto, idJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                    return;
                }
                if (Objects.nonNull(aiMappingResultDto) && aiMappingResultDto.getScore() == 0) {
                    aiMappingResultDto.setScore(100);
                    addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, idJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                    return;
                }
                if (Objects.nonNull(aiMappingResultDto) && aiMappingResultDto.getId().equals(idJdJdb.getId())) {
                    aiMappingResultDto.setScore(100);
                    addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, idJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                    return;
                }
                if (Objects.nonNull(aiMappingResultDto)) {
                    aiMappingResultDto.setScore(hotelDifyDefaultScore);
                    aiMappingResultDto.setReason("可能重复酒店");
                }
                addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, idJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                return;
            }
            if (Objects.nonNull(aiMappingResultDto) && aiMappingResultDto.getId().equals(hsMappings.get(hsMappings.size() - 1).getLocalId())) {
                aiMappingResultDto.setScore(100);
                addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, aiJdJdb, Byte.valueOf("0"), hsMappings.get(hsMappings.size() - 1).getLocalId(), "-1", hsMappings.get(hsMappings.size() - 1).getLocalId());
//                hotelGnBaseService.updateStatusById(plat,  id, 8);
                return;
            }
            addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, aiJdJdb, Byte.valueOf("3"), hsMappings.get(hsMappings.size() - 1).getLocalId(), "-1", hsMappings.get(hsMappings.size() - 1).getLocalId());

        }
    }

    private void addHotelGnPreMapping(String id, String name, String addressLine, String phone, String cityName, PlatEnum plat, String mappingType, AIMappingResultDto aiMappingResultDto, JdJdb aiJdJdb, Byte status, String localId, String targetPlatformId, String targetHotelId) {
        HotelGnPreMapping hotelGnPreMapping = new HotelGnPreMapping();
        hotelGnPreMapping.setId(String.valueOf(sequence.nextId()));
        hotelGnPreMapping.setSourceHotelId(id);
        hotelGnPreMapping.setSourceHotelName(name);
        hotelGnPreMapping.setSourceHotelAddress(addressLine);
        hotelGnPreMapping.setSourceHotelPhone(phone);
        hotelGnPreMapping.setSourceHotelCityName(cityName);
        hotelGnPreMapping.setSourePlatformId(plat.getValue());
        hotelGnPreMapping.setMappingType(mappingType);
        hotelGnPreMapping.setTargetHotelId(StringUtils.isNotBlank(targetHotelId) ? targetHotelId : (Objects.isNull(aiMappingResultDto) ? null : aiMappingResultDto.getId()));
        if (Objects.nonNull(aiJdJdb)) {
            hotelGnPreMapping.setTargetHotelName(aiJdJdb.getJdmc());
            hotelGnPreMapping.setTargetHotelAddress(aiJdJdb.getJddz());
            hotelGnPreMapping.setTargetHotelPhone(aiJdJdb.getJddh());
            hotelGnPreMapping.setTargetHotelCityName(aiJdJdb.getCityName());
        } else {
            hotelGnPreMapping.setTargetHotelName(Objects.isNull(aiMappingResultDto) ? null : aiMappingResultDto.getName());
            hotelGnPreMapping.setTargetHotelAddress(Objects.isNull(aiMappingResultDto) ? null : aiMappingResultDto.getAddress());
            hotelGnPreMapping.setTargetHotelPhone(Objects.isNull(aiMappingResultDto) ? null : aiMappingResultDto.getPhone());
            hotelGnPreMapping.setTargetHotelCityName(Objects.isNull(aiMappingResultDto) ? null : aiMappingResultDto.getCity());
        }
        hotelGnPreMapping.setTargetPlatformId(targetPlatformId);
        hotelGnPreMapping.setScore(Objects.isNull(aiMappingResultDto) ? hotelDifyDefaultScore : aiMappingResultDto.getScore());
        hotelGnPreMapping.setStatus(status);
        hotelGnPreMapping.setScoreReason(Objects.isNull(aiMappingResultDto) ? "" : aiMappingResultDto.getReason());
        hotelGnPreMapping.setHandlerReason("现映射id=" + localId + ";新匹配映射id=" + (Objects.isNull(aiMappingResultDto) ? "接口异常，返回null" : aiMappingResultDto.getId()));
        hotelGnPreMapping.setCreateTime(new Date());
        hotelGnPreMapping.setCreateBy("system");
        hotelGnPreMappingService.addHotelGnPreMapping(hotelGnPreMapping);
    }


    private AIMappingResultDto hotelDifyAi(String name, String cityName, String addressLine, String phone, String id, PlatEnum plat) {
        List<AIMappingResultDto> resultDtos = new ArrayList<>();
        JSONArray params = new JSONArray();
        JSONObject hotelObj = new JSONObject();
        hotelObj.put("name", HotelDataFormatUtil.formatName(name));
        hotelObj.put("city", cityName);
        hotelObj.put("address", HotelDataFormatUtil.formatAddress(addressLine));
        hotelObj.put("phone", HotelDataFormatUtil.formatPhone(phone));
        params.add(hotelObj);
        try {
            resultDtos = difyApiService.runBatchWorkflow(params);
            if (CollectionUtils.isEmpty(resultDtos)) {
                return null;
            }
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        AIMappingResultDto aiMappingResultDto = resultDtos.get(0);
        aiMappingResultDto.setInterfacePlat(plat.getValue());
        aiMappingResultDto.setPlatId(id);
        return aiMappingResultDto;
    }

    private List<AIMappingResultDto> hotelDifyAiV1(String name, String cityName, String addressLine, String phone, String id, PlatEnum plat, String hotelId) {
        List<AIMappingResultDto> resultDtos = new ArrayList<>();
        JSONArray params = new JSONArray();
        JSONObject hotelObj = new JSONObject();
        hotelObj.put("name", HotelDataFormatUtil.formatName(name));
        hotelObj.put("city", cityName);
        hotelObj.put("address", HotelDataFormatUtil.formatAddress(addressLine));
        hotelObj.put("phone", HotelDataFormatUtil.formatPhone(phone));
        hotelObj.put("id", hotelId);
        params.add(hotelObj);
        try {
            resultDtos = difyApiService.runBatchWorkflow(params);
            if (CollectionUtils.isEmpty(resultDtos)) {
                return null;
            }
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        List<String> idList = resultDtos.stream().filter(d -> StringUtils.isNotEmpty(d.getTargetId())).map(AIMappingResultDto::getTargetId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(idList)) {
            return resultDtos;
        } else {
            List<JdJdb> jdJdbs = jdJdbService.listByIds(idList);
            Map<String, JdJdb> stringJdJdbMap = jdJdbs.stream().collect(Collectors.toMap(JdJdb::getId, j -> j));
            resultDtos.stream().forEach(r -> {
                r.setInterfacePlat(plat.getValue());
                r.setPlatId(id);
                if (StringUtils.isNotEmpty(r.getTargetId())) {
                    if (stringJdJdbMap.containsKey(r.getTargetId())) {
                        JdJdb jdJdb = stringJdJdbMap.get(r.getTargetId());
                        r.setName(jdJdb.getJdmc());
                        r.setAddress(jdJdb.getJddz());
                        r.setPhone(jdJdb.getJddh());
                        r.setCity(jdJdb.getCityName());
                    }

                }
            });
            return resultDtos;
        }

    }


    public void handlePreMapping() {
        List<PlatEnum> platEnums = new ArrayList<>();
        platEnums.add(PlatEnum.PLAT_KT);
        platEnums.add(PlatEnum.PLAT_MT);
        platEnums.add(PlatEnum.PLAT_HSJL);
        platEnums.add(PlatEnum.PLAT_HSJL_XY);
        platEnums.add(PlatEnum.PLAT_CLGJ);
        platEnums.add(PlatEnum.PLAT_QT);
        RBucket<Map<String, String>> platBucket = redissonClient.getBucket("platHotelGnPreMapping");

        Integer limit = 1000;
        for (int i = 0; i < platEnums.size(); i++) {
            PlatEnum plat = platEnums.get(i);
            long startTime = System.currentTimeMillis();
            // 取值
            Map<String, String> value = platBucket.get();
            if (value == null) {
                value = new HashMap<>();
                value.put("plat", plat.name());
                value.put("offset", "0");
                platBucket.set(value);
            } else {
                if (!plat.name().equals(value.get("plat"))) {
                    continue;
                }
            }
            log.info("处理酒店预映射平台: {} 开始", plat.getName());
            int total = 0;
            for (Integer offset = Integer.valueOf(value.get("offset")); ; offset += limit) {
                List<HotelGnBase> hotelList = hotelGnBaseService.getByLimit(plat, offset, limit, null,  null,null);
                if (CollectionUtils.isEmpty(hotelList)) {
                    value.put("plat", platEnums.get(i + 1).name());
                    value.put("offset", "0");
                    platBucket.set(value);
                    log.info("处理酒店预映射平台: {} 处理完成，共处理 {} 条记录, 耗时{}毫秒", plat, total, (System.currentTimeMillis() - startTime));
                    break;
                }
                total += hotelList.size();
                hotelList = hotelList.stream()
                        .filter(hotel -> !((Integer) 8).equals(hotel.getStatus()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(hotelList)) {
                    value.put("offset", String.valueOf(offset + limit));
                    platBucket.set(value);
                    continue;
                }
                // ai匹配
                batchHotelDifyAi(hotelList, plat);
                value.put("offset", String.valueOf(offset + limit));
                platBucket.set(value);
            }
            log.info("处理酒店预映射平台:{}ai匹配执行结束，共处理 {} 条记录, 耗时{}毫秒", plat, total, (System.currentTimeMillis() - startTime));
        }
    }

    public void handleFirstPreMapping(String param) {
        List<PlatEnum> platEnums = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(param);
        if (!jsonObject.containsKey("platNums")) {
            return;
        }
        boolean repeat = jsonObject.getBooleanValue("repeat");
        int type = jsonObject.containsKey("type") ? jsonObject.getInteger("type") : 0;
        int threadNum = jsonObject.containsKey("threadNum") ? jsonObject.getInteger("threadNum") : 10;
        boolean allStatus = jsonObject.containsKey("allStatus") ? jsonObject.getBooleanValue("allStatus") : Boolean.TRUE;
        int multipleSize = jsonObject.containsKey("multipleSize") ? jsonObject.getInteger("multipleSize") : 7;
        int version = jsonObject.containsKey("version") ? jsonObject.getInteger("version") : 1;
        String minId = jsonObject.containsKey("minId") ? jsonObject.getString("minId") : null;
        String maxId = jsonObject.containsKey("maxId") ? jsonObject.getString("maxId") : null;
        boolean isFilter = jsonObject.containsKey("isFilter") ? jsonObject.getBooleanValue("isFilter") : Boolean.FALSE;
        boolean isStep = jsonObject.containsKey("isStep") ? jsonObject.getBooleanValue("isStep") : Boolean.FALSE;
        for (Object platNum : jsonObject.getJSONArray("platNums")) {
            platEnums.add(PlatEnum.getplatEnum(platNum.toString()));
        }
        Integer limit = 1000;
        for (int i = 0; i < platEnums.size(); i++) {
            PlatEnum plat = platEnums.get(i);
            String platBucketKey = "platHotelGnFirstPreMapping" + plat.getValue();
            RBucket<Integer> platBucket = redissonClient.getBucket(platBucketKey);
            if (repeat) {
                platBucket.delete();
            }
            long startTime = System.currentTimeMillis();
            log.info("处理First酒店预映射平台: {} 开始  threadNum:{} ", plat.getName(), threadNum);
            int total = 0;
            Integer notStatus = allStatus ? null : 8;
            for (Integer offset = platBucket.get() == null ? 0 : platBucket.get(); ; offset += limit) {
                List<HotelGnBase> hotelList = hotelGnBaseService.getByLimit(plat, offset, limit, notStatus,minId, maxId);
                if (CollectionUtils.isEmpty(hotelList)) {
                    log.info("处理First酒店预映射平台: {} 处理完成，共处理 {} 条记录, 耗时{}毫秒", plat, total, (System.currentTimeMillis() - startTime));
                    break;
                }
                total += hotelList.size();
                if (allStatus) {
                    hotelList = hotelList.stream()
                            .filter(hotel -> !((Integer) 8).equals(hotel.getStatus()) && !(StringUtils.isEmpty(hotel.getName()) && StringUtils.isEmpty(hotel.getAddress())
                                    && StringUtils.isEmpty(hotel.getPhone()) && StringUtils.isEmpty(hotel.getCityName())))
                            .collect(Collectors.toList());
                } else {
                    hotelList = hotelList.stream()
                            .filter(hotel -> !(StringUtils.isEmpty(hotel.getName()) && StringUtils.isEmpty(hotel.getAddress())
                                    && StringUtils.isEmpty(hotel.getPhone()) && StringUtils.isEmpty(hotel.getCityName())))
                            .collect(Collectors.toList());
                }
                if (CollectionUtils.isEmpty(hotelList)) {
                    platBucket.set(offset + limit);
                    continue;
                }
                int tmpNum = hotelList.size() / multipleSize + 1;
                threadNum = Math.min(tmpNum, threadNum);
                batchPlatHotel(hotelList, plat, type, threadNum, multipleSize, version, isFilter, isStep);

                platBucket.set(offset + limit);
                log.info("处理First酒店预映射平台:{}，共处理 {} 条记录 threadNum:{}", plat, total, threadNum);
            }
            log.info("处理First酒店预映射平台:{}ai匹配执行结束，共处理 {} 条记录, 耗时{}毫秒", plat, total, (System.currentTimeMillis() - startTime));
        }
    }

    private void batchPlatHotel(List<HotelGnBase> hotelList, PlatEnum plat, int type, int threadNum
            , int multipleSize, int version, boolean isFilter, boolean isStep) {
        int poolSize = threadNum;
        List<List<HotelGnBase>> subList = splitList(hotelList, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        List<HotelGnBase> innerList = subList.get(finalI);
                        List<HotelGnPreMapping> aiHotelList = new ArrayList<>();
                        for (HotelGnBase hotel : innerList) {
                            HotelGnPreMapping hotelGnPreMapping = new HotelGnPreMapping();
                            hotelGnPreMapping.setSourceHotelId(hotel.getId());
                            hotelGnPreMapping.setSourceHotelName(hotel.getName());
                            hotelGnPreMapping.setSourceHotelAddress(hotel.getAddress());
                            hotelGnPreMapping.setSourceHotelPhone(hotel.getPhone());
                            hotelGnPreMapping.setSourceHotelCityName(hotel.getCityName());
                            hotelGnPreMapping.setSourePlatformId(plat.getValue());
                            //根据主键id查询酒店映射是否存在
                            List<ZhJdJdbMapping> hsMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(hotel.getId(), Long.parseLong(plat.getValue()));
                            if (CollectionUtils.isEmpty(hsMappings)) {
                                // id映射
                                HotelGnIdMapping hotelGnIdMapping = hotelGnIdMappingService.queryByPlatformIdAndHotelId(plat.getValue(), hotel.getId());
                                String targetPlatformId = Objects.isNull(hotelGnIdMapping) ? null : hotelGnIdMapping.getMappingPlatformId();
                                String targetHotelId = Objects.isNull(hotelGnIdMapping) ? null : hotelGnIdMapping.getMappingHotelId();
                                hotelGnPreMapping.setTargetHotelId(targetHotelId);
                                hotelGnPreMapping.setTargetPlatformId(targetPlatformId);
                                if (Objects.isNull(hotelGnIdMapping)) {
                                    hotelGnPreMapping.setRemark1("notMapping");
                                    if (type == 0 || type == 2) {
                                        aiHotelList.add(hotelGnPreMapping);
                                    }
                                    continue;
                                }
                                // id映射基础数据
                                JdJdb idJdJdb = PlatEnum.PLAT_BASE.getValue().equals(targetPlatformId) ?
                                        jdJdbService.selectJdJdbById(hotelGnIdMapping.getMappingHotelId()) :
                                        jdJdbService.selectJdJdbById(hotelGnIdMapping.getMappingPlatformId() + hotelGnIdMapping.getMappingHotelId());
//                                    if (Objects.isNull(idJdJdb)) {
//                                        hotelGnPreMapping.setRemark1("notMapping");
//                                        aiHotelList.add(hotelGnPreMapping);
//                                        continue;
//                                    }
                                hotelGnPreMapping.setId(String.valueOf(sequence.nextId()));
                                hotelGnPreMapping.setMappingType("ID");
                                if (Objects.nonNull(idJdJdb)) {
                                    hotelGnPreMapping.setTargetHotelName(idJdJdb.getJdmc());
                                    hotelGnPreMapping.setTargetHotelAddress(idJdJdb.getJddz());
                                    hotelGnPreMapping.setTargetHotelPhone(idJdJdb.getJddh());
                                    hotelGnPreMapping.setTargetHotelCityName(idJdJdb.getCityName());
                                }
                                hotelGnPreMapping.setScore(100);
                                hotelGnPreMapping.setStatus(Byte.valueOf("0"));
                                hotelGnPreMapping.setCreateTime(new Date());
                                hotelGnPreMapping.setCreateBy("system");
                                if (type == 0 || type == 1) {
                                    hotelGnPreMappingService.addHotelGnPreMapping(hotelGnPreMapping);
                                }
                                continue;
                            }
                            JdJdb aiJdJdb = jdJdbService.selectJdJdbById(hsMappings.get(hsMappings.size() - 1).getLocalId());
                            hotelGnPreMapping.setId(String.valueOf(sequence.nextId()));
                            hotelGnPreMapping.setMappingType("ID");
                            hotelGnPreMapping.setTargetHotelId(hsMappings.get(hsMappings.size() - 1).getLocalId());
                            if (Objects.nonNull(aiJdJdb)) {
                                hotelGnPreMapping.setTargetHotelName(aiJdJdb.getJdmc());
                                hotelGnPreMapping.setTargetHotelAddress(aiJdJdb.getJddz());
                                hotelGnPreMapping.setTargetHotelPhone(aiJdJdb.getJddh());
                                hotelGnPreMapping.setTargetHotelCityName(aiJdJdb.getCityName());
                            }
                            hotelGnPreMapping.setTargetPlatformId("-1");
                            hotelGnPreMapping.setScore(100);
                            hotelGnPreMapping.setStatus(Byte.valueOf("0"));
                            hotelGnPreMapping.setCreateTime(new Date());
                            hotelGnPreMapping.setCreateBy("system");
                            if (type == 0 || type == 1) {
                                hotelGnPreMappingService.addHotelGnPreMapping(hotelGnPreMapping);
                            }
                        }

                        // ai匹配
                        if (aiHotelList.size() > 0) {
                            batchPlatHotelDifyAi(aiHotelList, plat, multipleSize, version, isFilter, isStep);
                        }
                    } catch (Exception e) {
                        log.error("处理酒店预映射平台:{}ai匹配执行错误: ", plat.getName(), e);
                    }
                }
            });
            try {
                if (type == 2) {
                    Thread.sleep(7000);
                }
            } catch (InterruptedException e) {
                log.error("处理酒店预映射平台:{}ai匹配线程等待错误: ", plat.getName(), e);
            }
        }

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        try {
            // 等待最多60秒，确保所有任务完成
            if (!executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS)) {
                log.warn("处理酒店预映射平台:{}部分任务未在指定时间内完成", plat.getName());
            }
        } catch (InterruptedException e) {
            log.error("处理酒店预映射平台:{}等待线程池关闭时发生中断: ", plat.getName(), e);
        }
    }

    private void batchPlatHotelDifyAi(List<HotelGnPreMapping> aiHotelList, PlatEnum plat, int multipleSize, int version, boolean isFilter, boolean isStep) {
        if (version == 1) {
            batchPlatHotelDifyAiV3(aiHotelList, plat, multipleSize, isFilter, isStep);
        } else {
            batchPlatHotelDifyAiV2(aiHotelList, plat, multipleSize, isFilter, isStep);
        }
    }

    private void batchPlatHotelDifyAiV1(List<HotelGnPreMapping> aiHotelList, PlatEnum plat, int multipleSize) {
        List<List<HotelGnPreMapping>> subList = splitListWithRemainder(aiHotelList, multipleSize);
        try {
            for (List<HotelGnPreMapping> innerList : subList) {
                List<AIMappingResultDto> resultDtos = new ArrayList<>();
                JSONArray params = new JSONArray();
                for (HotelGnPreMapping hotel : innerList) {
                    JSONObject hotelObj = new JSONObject();
                    hotelObj.put("id", hotel.getSourceHotelId());
                    hotelObj.put("name", HotelDataFormatUtil.formatName(hotel.getSourceHotelName()));
                    hotelObj.put("city", hotel.getSourceHotelCityName());
                    hotelObj.put("address", HotelDataFormatUtil.formatAddress(hotel.getSourceHotelAddress()));
                    hotelObj.put("phone", HotelDataFormatUtil.formatPhone(hotel.getSourceHotelPhone()));
                    params.add(hotelObj);
                }
                try {
                    log.info("处理酒店预映射平台: ai params:{} innerList:{} ", params, innerList);
                    resultDtos = difyApiService.runBatchWorkflow(params);
                    log.info("处理酒店预映射平台: ai resultDtos:{} ", resultDtos);
//                    if (resultDtos == null) {
//                        for(HotelGnPreMapping hotel : innerList) {
//                            hotel.setId(String.valueOf(sequence.nextId()));
//                            if(StringUtils.isEmpty(hotel.getMappingType())) {
//                                hotel.setMappingType("AI");
//                            }
//                            switch(hotel.getRemark1()) {
//                                case "notMapping":
//                                    hotel.setStatus(Byte.valueOf("0"));
//                                    break;
//                                case "idMapping":
//                                    hotel.setScore(hotelDifyDefaultScore);
//                                    hotel.setStatus(Byte.valueOf("3"));
//                                    break;
//                                case "hasMapping":
//                                    hotel.setStatus(Byte.valueOf("3"));
//                            }
//
//                            hotel.setCreateTime(new Date());
//                            hotel.setCreateBy("system");
//                            hotelGnPreMappingService.addHotelGnPreMapping(hotel);
//                        }
//                    }
                } catch (Exception e) {
                    log.error("处理酒店预映射平台: 异常信息：{}", ExceptionUtil.getStackTrace(e));
                }
                for (int j = 0; j < resultDtos.size(); j++) {
                    AIMappingResultDto aiMappingResultDto = resultDtos.get(j);
                    aiMappingResultDto.setInterfacePlat(plat.getValue());
                    aiMappingResultDto.setPlatId(innerList.get(j).getId());
                    HotelGnPreMapping hotel = innerList.get(j);
                    hotel.setId(String.valueOf(sequence.nextId()));
                    if (StringUtils.isEmpty(hotel.getMappingType())) {
                        hotel.setMappingType("AI");
                    }
                    switch (hotel.getRemark1()) {
                        case "notMapping":
                            hotel.setStatus(Byte.valueOf("0"));
                            hotel.setScore(aiMappingResultDto.getScore());
                            hotel.setScoreReason(aiMappingResultDto.getReason());
                            if (StringUtils.isNotBlank(aiMappingResultDto.getId())) {
                                hotel.setTargetHotelId(aiMappingResultDto.getId());
                                hotel.setTargetPlatformId("-1");
                            }
                            hotel.setTargetHotelName(aiMappingResultDto.getName());
                            hotel.setTargetHotelAddress(aiMappingResultDto.getAddress());
                            hotel.setTargetHotelPhone(aiMappingResultDto.getPhone());
                            hotel.setTargetHotelCityName(aiMappingResultDto.getCity());
                            break;
                        case "idMapping":
                            if (aiMappingResultDto.getScore() == 0 || aiMappingResultDto.getId().equals(hotel.getRemark2())) {
                                continue;
                            }
                            hotel.setScore(hotelDifyDefaultScore);
                            hotel.setStatus(Byte.valueOf("3"));
                            hotel.setScoreReason(aiMappingResultDto.getReason() + " 可能重复酒店");
                            break;
                        case "hasMapping":
                            if (aiMappingResultDto.getId().equals(hotel.getTargetHotelId())) {
                                continue;
                            }
                            hotel.setScore(aiMappingResultDto.getScore());
                            hotel.setScoreReason(aiMappingResultDto.getReason());
                            hotel.setStatus(Byte.valueOf("3"));
                    }
                    hotel.setCreateTime(new Date());
                    hotel.setCreateBy("system");
                    hotel.setHandlerReason("现映射id=" + hotel.getRemark2() + ";新匹配映射id=" + aiMappingResultDto.getId());
                    hotelGnPreMappingService.addHotelGnPreMapping(hotel);
                }
            }
        } catch (Exception e) {
            log.error("处理酒店预映射平台:{}ai匹配执行错误: ", plat.getName(), e);
        }
    }

    private void batchPlatHotelDifyAiV3(List<HotelGnPreMapping> aiHotelList, PlatEnum plat, int multipleSize, boolean isFilter, boolean isStep) {
        if (isFilter) {
            hotelGnPreMappingService.filterListByStatus(aiHotelList);
        }
        if (CollectionUtils.isEmpty(aiHotelList)) {
            return;
        }
        List<List<HotelGnPreMapping>> subList = splitListWithRemainder(aiHotelList, multipleSize);
        try {
            for (List<HotelGnPreMapping> innerList : subList) {
                List<AIMappingResultDto> resultDtos = new ArrayList<>();
                List<HotelInfoDTO> hotelList = new ArrayList<>();
                JSONArray params = new JSONArray();
                for (HotelGnPreMapping hotel : innerList) {
                    HotelInfoDTO hotelInfoDTO = new HotelInfoDTO();
                    hotelInfoDTO.setId(hotel.getSourceHotelId());
                    hotelInfoDTO.setName(HotelDataFormatUtil.formatName(hotel.getSourceHotelName()));
                    hotelInfoDTO.setCity(hotel.getSourceHotelCityName());
                    hotelInfoDTO.setAddress(HotelDataFormatUtil.formatAddress(hotel.getSourceHotelAddress()));
                    hotelInfoDTO.setPhone(HotelDataFormatUtil.formatPhone(hotel.getSourceHotelPhone()));
                    hotelList.add(hotelInfoDTO);
                }
                try {
                    log.info("处理酒店预映射平台: ai params:{} innerList:{} ", hotelList, innerList);
                    resultDtos = difyApiService2.runBatchWorkflowV2(hotelList, isStep);
                    log.info("处理酒店预映射平台: ai resultDtos:{} ", resultDtos);
//                    if (resultDtos == null) {
//                        for(HotelGnPreMapping hotel : innerList) {
//                            hotel.setId(String.valueOf(sequence.nextId()));
//                            if(StringUtils.isEmpty(hotel.getMappingType())) {
//                                hotel.setMappingType("AI");
//                            }
//                            switch(hotel.getRemark1()) {
//                                case "notMapping":
//                                    hotel.setStatus(Byte.valueOf("0"));
//                                    break;
//                                case "idMapping":
//                                    hotel.setScore(hotelDifyDefaultScore);
//                                    hotel.setStatus(Byte.valueOf("3"));
//                                    break;
//                                case "hasMapping":
//                                    hotel.setStatus(Byte.valueOf("3"));
//                            }
//
//                            hotel.setCreateTime(new Date());
//                            hotel.setCreateBy("system");
//                            hotelGnPreMappingService.addHotelGnPreMapping(hotel);
//                        }
//                    }
                } catch (Exception e) {
                    log.error("处理酒店预映射平台: 异常信息：{}", ExceptionUtil.getStackTrace(e));
                }
                for (int j = 0; j < resultDtos.size(); j++) {
                    AIMappingResultDto aiMappingResultDto = resultDtos.get(j);
                    aiMappingResultDto.setInterfacePlat(plat.getValue());
                    aiMappingResultDto.setPlatId(innerList.get(j).getId());
                    HotelGnPreMapping hotel = innerList.get(j);
                    hotel.setId(String.valueOf(sequence.nextId()));
                    if (StringUtils.isEmpty(hotel.getMappingType())) {
                        hotel.setMappingType("AI");
                    }
                    switch (hotel.getRemark1()) {
                        case "notMapping":
                            hotel.setStatus(Byte.valueOf("0"));
                            hotel.setScore(aiMappingResultDto.getScore());
                            hotel.setScoreReason(aiMappingResultDto.getReason());
                            if (StringUtils.isNotBlank(aiMappingResultDto.getId())) {
                                hotel.setTargetHotelId(aiMappingResultDto.getId());
                                hotel.setTargetPlatformId("-1");
                            }
                            hotel.setTargetHotelName(aiMappingResultDto.getName());
                            hotel.setTargetHotelAddress(aiMappingResultDto.getAddress());
                            hotel.setTargetHotelPhone(aiMappingResultDto.getPhone());
                            hotel.setTargetHotelCityName(aiMappingResultDto.getCity());
                            break;
                        case "idMapping":
                            if (aiMappingResultDto.getScore() == 0 || aiMappingResultDto.getId().equals(hotel.getRemark2())) {
                                continue;
                            }
                            hotel.setScore(hotelDifyDefaultScore);
                            hotel.setStatus(Byte.valueOf("3"));
                            hotel.setScoreReason(aiMappingResultDto.getReason() + " 可能重复酒店");
                            break;
                        case "hasMapping":
                            if (aiMappingResultDto.getId().equals(hotel.getTargetHotelId())) {
                                continue;
                            }
                            hotel.setScore(aiMappingResultDto.getScore());
                            hotel.setScoreReason(aiMappingResultDto.getReason());
                            hotel.setStatus(Byte.valueOf("3"));
                    }
                    hotel.setCreateTime(new Date());
                    hotel.setCreateBy("system");
                    hotel.setHandlerReason("现映射id=" + hotel.getRemark2() + ";新匹配映射id=" + aiMappingResultDto.getId());
                    hotelGnPreMappingService.addHotelGnPreMapping(hotel);
                }
            }
        } catch (Exception e) {
            log.error("处理酒店预映射平台:{}ai匹配执行错误: ", plat.getName(), e);
        }
    }

    private void batchPlatHotelDifyAiV2(List<HotelGnPreMapping> aiHotelList, PlatEnum plat, int multipleSize, boolean isFilter, boolean isStep) {
        if (isFilter) {
            hotelGnPreMappingService.filterListByStatus(aiHotelList);
        }
        if (CollectionUtils.isEmpty(aiHotelList)) {
            return;
        }
        List<List<HotelGnPreMapping>> subList = splitListWithRemainder(aiHotelList, multipleSize);
        try {
            for (List<HotelGnPreMapping> innerList : subList) {
                List<AIMappingResultDto> resultDtos = new ArrayList<>();
                List<HotelInfoDTO> hotelList = new ArrayList<>();
                JSONArray params = new JSONArray();
                for (HotelGnPreMapping hotel : innerList) {
                    HotelInfoDTO hotelInfoDTO = new HotelInfoDTO();
                    hotelInfoDTO.setId(hotel.getSourceHotelId());
                    hotelInfoDTO.setName(HotelDataFormatUtil.formatName(hotel.getSourceHotelName()));
                    hotelInfoDTO.setCity(hotel.getSourceHotelCityName());
                    hotelInfoDTO.setAddress(HotelDataFormatUtil.formatAddress(hotel.getSourceHotelAddress()));
                    hotelInfoDTO.setPhone(HotelDataFormatUtil.formatPhone(hotel.getSourceHotelPhone()));
                    hotelList.add(hotelInfoDTO);
                }
                try {
                    log.info("处理酒店预映射平台: ai params:{} innerList:{} ", hotelList, innerList);
                    resultDtos = difyApiService.runBatchWorkflowV2(hotelList, isStep);
                    log.info("处理酒店预映射平台: ai resultDtos:{} ", resultDtos);
//                    if (resultDtos == null) {
//                        for(HotelGnPreMapping hotel : innerList) {
//                            hotel.setId(String.valueOf(sequence.nextId()));
//                            if(StringUtils.isEmpty(hotel.getMappingType())) {
//                                hotel.setMappingType("AI");
//                            }
//                            switch(hotel.getRemark1()) {
//                                case "notMapping":
//                                    hotel.setStatus(Byte.valueOf("0"));
//                                    break;
//                                case "idMapping":
//                                    hotel.setScore(hotelDifyDefaultScore);
//                                    hotel.setStatus(Byte.valueOf("3"));
//                                    break;
//                                case "hasMapping":
//                                    hotel.setStatus(Byte.valueOf("3"));
//                            }
//
//                            hotel.setCreateTime(new Date());
//                            hotel.setCreateBy("system");
//                            hotelGnPreMappingService.addHotelGnPreMapping(hotel);
//                        }
//                    }
                } catch (Exception e) {
                    log.error("处理酒店预映射平台: 异常信息：{}", ExceptionUtil.getStackTrace(e));
                }
                for (int j = 0; j < resultDtos.size(); j++) {
                    AIMappingResultDto aiMappingResultDto = resultDtos.get(j);
                    aiMappingResultDto.setInterfacePlat(plat.getValue());
                    aiMappingResultDto.setPlatId(innerList.get(j).getId());
                    HotelGnPreMapping hotel = innerList.get(j);
                    hotel.setId(String.valueOf(sequence.nextId()));
                    if (StringUtils.isEmpty(hotel.getMappingType())) {
                        hotel.setMappingType("AI");
                    }
                    switch (hotel.getRemark1()) {
                        case "notMapping":
                            hotel.setStatus(Byte.valueOf("0"));
                            hotel.setScore(aiMappingResultDto.getScore());
                            hotel.setScoreReason(aiMappingResultDto.getReason());
                            if (StringUtils.isNotBlank(aiMappingResultDto.getId())) {
                                hotel.setTargetHotelId(aiMappingResultDto.getId());
                                hotel.setTargetPlatformId("-1");
                            }
                            hotel.setTargetHotelName(aiMappingResultDto.getName());
                            hotel.setTargetHotelAddress(aiMappingResultDto.getAddress());
                            hotel.setTargetHotelPhone(aiMappingResultDto.getPhone());
                            hotel.setTargetHotelCityName(aiMappingResultDto.getCity());
                            break;
                        case "idMapping":
                            if (aiMappingResultDto.getScore() == 0 || aiMappingResultDto.getId().equals(hotel.getRemark2())) {
                                continue;
                            }
                            hotel.setScore(hotelDifyDefaultScore);
                            hotel.setStatus(Byte.valueOf("3"));
                            hotel.setScoreReason(aiMappingResultDto.getReason() + " 可能重复酒店");
                            break;
                        case "hasMapping":
                            if (aiMappingResultDto.getId().equals(hotel.getTargetHotelId())) {
                                continue;
                            }
                            hotel.setScore(aiMappingResultDto.getScore());
                            hotel.setScoreReason(aiMappingResultDto.getReason());
                            hotel.setStatus(Byte.valueOf("3"));
                    }
                    hotel.setCreateTime(new Date());
                    hotel.setCreateBy("system");
                    hotel.setHandlerReason("现映射id=" + hotel.getRemark2() + ";新匹配映射id=" + aiMappingResultDto.getId());
                    hotelGnPreMappingService.addHotelGnPreMapping(hotel);
                }
            }
        } catch (Exception e) {
            log.error("处理酒店预映射平台:{}ai匹配执行错误: ", plat.getName(), e);
        }
    }


    public void handleSecondPreMapping(String param) {
        List<PlatEnum> platEnums = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(param);
        if (!jsonObject.containsKey("platNums")) {
            return;
        }
        boolean repeat = jsonObject.getBooleanValue("repeat");
        int multipleSize = jsonObject.containsKey("multipleSize") ? jsonObject.getInteger("multipleSize") : 7;
        int version = jsonObject.containsKey("version") ? jsonObject.getInteger("version") : 1;
        for (Object platNum : jsonObject.getJSONArray("platNums")) {
            platEnums.add(PlatEnum.getplatEnum(platNum.toString()));
        }

        Integer limit = 1000;
        for (int i = 0; i < platEnums.size(); i++) {
            PlatEnum plat = platEnums.get(i);
            String platBucketKey = "platHotelGnSecondPreMappingHandle" + plat.getValue();
            RBucket<Integer> platBucket = redissonClient.getBucket(platBucketKey);
            if (repeat) {
                platBucket.delete();
            }
            long startTime = System.currentTimeMillis();

            log.info("处理Second酒店预映射平台: {} 开始", plat.getName());
            int total = 0;
            for (Integer offset = platBucket.get() == null ? 0 : platBucket.get(); ; offset += limit) {
                List<HotelGnBase> hotelList = hotelGnBaseService.getByLimit(plat, offset, limit, null, null,null);
                if (CollectionUtils.isEmpty(hotelList)) {
                    log.info("处理Second酒店预映射平台: {} 处理完成，共处理 {} 条记录, 耗时{}毫秒", plat, total, (System.currentTimeMillis() - startTime));
                    break;
                }
                total += hotelList.size();
                hotelList = hotelList.stream()
                        .filter(hotel -> !(StringUtils.isEmpty(hotel.getName()) && StringUtils.isEmpty(hotel.getAddress())
                                && StringUtils.isEmpty(hotel.getPhone()) && StringUtils.isEmpty(hotel.getCityName())))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(hotelList)) {
                    platBucket.set(offset + limit);
                    continue;
                }

                batchSecondPlatHotel(hotelList, plat, multipleSize, version);

                log.info("处理Second酒店预映射平台: {} 处理完成，共处理 {} 条记录, 耗时{}毫秒", plat, total, (System.currentTimeMillis() - startTime));
                platBucket.set(offset + limit);
            }
            log.info("处理Second酒店预映射平台:{}ai匹配执行结束，共处理 {} 条记录, 耗时{}毫秒", plat, total, (System.currentTimeMillis() - startTime));
        }
    }

    private void batchSecondPlatHotel(List<HotelGnBase> hotelList, PlatEnum plat, int multipleSize, int version) {
        int poolSize = 5;
        List<List<HotelGnBase>> subList = splitList(hotelList, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        List<HotelGnBase> innerList = subList.get(finalI);
                        List<HotelGnPreMapping> aiHotelList = new ArrayList<>();
                        for (HotelGnBase hotel : innerList) {
                            HotelGnPreMapping hotelGnPreMapping = new HotelGnPreMapping();
                            hotelGnPreMapping.setSourceHotelId(hotel.getId());
                            hotelGnPreMapping.setSourceHotelName(hotel.getName());
                            hotelGnPreMapping.setSourceHotelAddress(hotel.getAddress());
                            hotelGnPreMapping.setSourceHotelPhone(hotel.getPhone());
                            hotelGnPreMapping.setSourceHotelCityName(hotel.getCityName());
                            hotelGnPreMapping.setSourePlatformId(plat.getValue());
                            //根据主键id查询酒店映射是否存在
                            List<ZhJdJdbMapping> hsMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(hotel.getId(), Long.parseLong(plat.getValue()));
                                if (CollectionUtils.isEmpty(hsMappings)) {
//                                    // id映射
//                                    HotelGnIdMapping hotelGnIdMapping = hotelGnIdMappingService.queryByPlatformIdAndHotelId(plat.getValue(), hotel.getId());
//                                    String targetPlatformId = Objects.isNull(hotelGnIdMapping) ? null : hotelGnIdMapping.getMappingPlatformId();
//                                    String targetHotelId = Objects.isNull(hotelGnIdMapping) ? null : hotelGnIdMapping.getMappingHotelId();
//                                    hotelGnPreMapping.setTargetHotelId(targetHotelId);
//                                    hotelGnPreMapping.setTargetPlatformId(targetPlatformId);
//                                    if (Objects.isNull(hotelGnIdMapping)) {
//                                        continue;
//                                    }
//                                    // id映射基础数据
//                                    JdJdb idJdJdb = PlatEnum.PLAT_BASE.getValue().equals(targetPlatformId) ?
//                                            jdJdbService.selectJdJdbById(hotelGnIdMapping.getMappingHotelId()) :
//                                            jdJdbService.selectJdJdbById(hotelGnIdMapping.getMappingPlatformId() + hotelGnIdMapping.getMappingHotelId());
//                                    if (Objects.isNull(idJdJdb)) {
//                                        continue;
//                                    }
//                                    hotelGnPreMapping.setMappingType("ID");
//                                    if (Objects.nonNull(idJdJdb)) {
//                                        hotelGnPreMapping.setTargetHotelName(idJdJdb.getJdmc());
//                                        hotelGnPreMapping.setTargetHotelAddress(idJdJdb.getJddz());
//                                        hotelGnPreMapping.setTargetHotelPhone(idJdJdb.getJddh());
//                                        hotelGnPreMapping.setTargetHotelCityName(idJdJdb.getCityName());
//                                        hotelGnPreMapping.setRemark2(idJdJdb.getId());
//                                    }
//                                    hotelGnPreMapping.setRemark1("idMapping");
//                                    aiHotelList.add(hotelGnPreMapping);
                                    continue;
                                }
                            JdJdb aiJdJdb = jdJdbService.selectJdJdbById(hsMappings.get(hsMappings.size() - 1).getLocalId());
                            hotelGnPreMapping.setMappingType("ID");
                            hotelGnPreMapping.setTargetHotelId(hsMappings.get(hsMappings.size() - 1).getLocalId());
                            if (Objects.nonNull(aiJdJdb)) {
                                hotelGnPreMapping.setTargetHotelName(aiJdJdb.getJdmc());
                                hotelGnPreMapping.setTargetHotelAddress(aiJdJdb.getJddz());
                                hotelGnPreMapping.setTargetHotelPhone(aiJdJdb.getJddh());
                                hotelGnPreMapping.setTargetHotelCityName(aiJdJdb.getCityName());
                                hotelGnPreMapping.setRemark2(hotelGnPreMapping.getTargetHotelId());
                            }
                            hotelGnPreMapping.setTargetPlatformId("-1");
                            hotelGnPreMapping.setRemark1("hasMapping");
                            aiHotelList.add(hotelGnPreMapping);
                        }

                        // ai匹配
                        if (aiHotelList.size() > 0) {
                            batchPlatHotelDifyAi(aiHotelList, plat, multipleSize, version, false, false);
                        }
                    } catch (Exception e) {
                        log.error("处理酒店预映射平台:{}ai匹配执行错误: ", plat.getName(), e);
                    }
                }
            });
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.error("处理酒店预映射平台:{}ai匹配线程等待错误: ", plat.getName(), e);
            }
        }

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        try {
            // 等待最多60秒，确保所有任务完成
            if (!executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS)) {
                log.warn("处理酒店预映射平台:{}部分任务未在指定时间内完成", plat.getName());
            }
        } catch (InterruptedException e) {
            log.error("处理酒店预映射平台:{}等待线程池关闭时发生中断: ", plat.getName(), e);
        }
    }


    public void batchHotelDifyAi(List<HotelGnBase> hotelList, PlatEnum plat) {
        int poolSize = 10;
        List<List<HotelGnBase>> subList = splitList(hotelList, poolSize);
        ExecutorService executorService = Executors.newFixedThreadPool(poolSize);
        for (int i = 0; i < poolSize; i++) {
            int finalI = i;
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        int multipleSize = 7;
                        List<HotelGnBase> subHotelList = subList.get(finalI);
                        List<List<HotelGnBase>> sendList = splitListWithRemainder(subHotelList, multipleSize);

                        for (List<HotelGnBase> innerList : sendList) {
                            List<AIMappingResultDto> resultDtos = new ArrayList<>();
                            JSONArray params = new JSONArray();
                            for (HotelGnBase hotel : innerList) {
                                JSONObject hotelObj = new JSONObject();
                                hotelObj.put("name", HotelDataFormatUtil.formatName(hotel.getName()));
                                hotelObj.put("city", hotel.getCityName());
                                hotelObj.put("address", HotelDataFormatUtil.formatAddress(hotel.getAddress()));
                                hotelObj.put("phone", HotelDataFormatUtil.formatPhone(hotel.getPhone()));
                                params.add(hotelObj);
                            }
                            try {
                                log.info("处理酒店预映射平台: ai params:{} ", params);
                                resultDtos = difyApiService.runBatchWorkflow(params);
                                if (resultDtos == null) {
                                    for (HotelGnBase hotel : innerList) {
                                        handleHotelPreMapping(null, plat, hotel.getName(), hotel.getCityName(), hotel.getAddress(), hotel.getPhone(), hotel.getId());
                                    }
                                }
                            } catch (Exception e) {
                                log.error("处理酒店预映射平台: 异常信息：{}", ExceptionUtil.getStackTrace(e));
                            }
                            for (int j = 0; j < resultDtos.size(); j++) {
                                AIMappingResultDto aiMappingResultDto = resultDtos.get(j);
                                aiMappingResultDto.setInterfacePlat(plat.getValue());
                                aiMappingResultDto.setPlatId(innerList.get(j).getId());
                                handleHotelPreMapping(aiMappingResultDto, plat, innerList.get(j).getName(), innerList.get(j).getCityName(), innerList.get(j).getAddress(), innerList.get(j).getPhone(), innerList.get(j).getId());
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理酒店预映射平台:{}ai匹配执行错误: ", plat.getName(), e);
                    }
                }
            });
            try {
                Thread.sleep(7000);
            } catch (InterruptedException e) {
                log.error("处理酒店预映射平台:{}ai匹配线程等待错误: ", plat.getName(), e);
            }
        }

        // 关闭线程池并等待所有任务完成
        executorService.shutdown();
        try {
            // 等待最多60秒，确保所有任务完成
            if (!executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS)) {
                log.warn("处理酒店预映射平台:{}部分任务未在指定时间内完成", plat.getName());
            }
        } catch (InterruptedException e) {
            log.error("处理酒店预映射平台:{}等待线程池关闭时发生中断: ", plat.getName(), e);
        }


    }

    public void handleHotelPreMapping(AIMappingResultDto aiMappingResultDto, PlatEnum plat, String name, String cityName, String addressLine, String phone, String id) {
        //根据主键id查询酒店映射是否存在
        List<ZhJdJdbMapping> hsMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(id, Long.parseLong(plat.getValue()));
        // ai匹配基础数据
        JdJdb aiJdJdb = (Objects.isNull(aiMappingResultDto) || StringUtils.isEmpty(aiMappingResultDto.getId())) ? null : jdJdbService.selectJdJdbById(aiMappingResultDto.getId());
        if (CollectionUtils.isEmpty(hsMappings)) {
            // id映射
            HotelGnIdMapping hotelGnIdMapping = hotelGnIdMappingService.queryByPlatformIdAndHotelId(plat.getValue(), id);
            String targetPlatformId = Objects.isNull(hotelGnIdMapping) ? null : hotelGnIdMapping.getMappingPlatformId();
            String targetHotelId = Objects.isNull(hotelGnIdMapping) ? null : hotelGnIdMapping.getMappingHotelId();
            if (Objects.isNull(hotelGnIdMapping)) {
                if (Objects.nonNull(aiMappingResultDto)) {
                    targetPlatformId = "-1";
                }
                addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "AI", aiMappingResultDto, aiJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                return;
            }
            // id映射基础数据
            log.info("处理酒店预映射平台: hotelGnIdMapping:{} ", hotelGnIdMapping);
            JdJdb idJdJdb = PlatEnum.PLAT_BASE.getValue().equals(targetPlatformId) ?
                    jdJdbService.selectJdJdbById(hotelGnIdMapping.getMappingHotelId()) :
                    jdJdbService.selectJdJdbById(hotelGnIdMapping.getMappingPlatformId() + hotelGnIdMapping.getMappingHotelId());
            if (Objects.isNull(idJdJdb)) {
                addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "AI", aiMappingResultDto, aiJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                return;
            }
            if (Objects.nonNull(aiMappingResultDto) && aiMappingResultDto.getScore() == 0) {
                aiMappingResultDto.setScore(100);
                addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, aiJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                return;
            }
            if (Objects.nonNull(aiMappingResultDto) && aiMappingResultDto.getId().equals(idJdJdb.getId())) {
                aiMappingResultDto.setScore(100);
                addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, aiJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
                return;
            }
            if (Objects.nonNull(aiMappingResultDto)) {
                aiMappingResultDto.setScore(hotelDifyDefaultScore);
                aiMappingResultDto.setReason("可能重复酒店");
            }
            addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, aiJdJdb, Byte.valueOf("0"), null, targetPlatformId, targetHotelId);
            return;
        }
        if (Objects.nonNull(aiMappingResultDto) && aiMappingResultDto.getId().equals(hsMappings.get(hsMappings.size() - 1).getLocalId())) {
            aiMappingResultDto.setScore(100);
            addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, aiJdJdb, Byte.valueOf("0"), hsMappings.get(hsMappings.size() - 1).getLocalId(), "-1", hsMappings.get(hsMappings.size() - 1).getLocalId());
//            hotelGnBaseService.updateStatusById(plat,  id, 8);
            return;
        }
        addHotelGnPreMapping(id, name, addressLine, phone, cityName, plat, "ID", aiMappingResultDto, aiJdJdb, Byte.valueOf("3"), hsMappings.get(hsMappings.size() - 1).getLocalId(), "-1", hsMappings.get(hsMappings.size() - 1).getLocalId());
    }

    public static <T> List<List<T>> splitList(List<T> list, final int size) {
        List<List<T>> newList = new ArrayList<>();
        int num = list.size() / size;
        for (int i = 0; i < size; i++) {
            if (i == size - 1) {
                newList.add(list.subList(i * num, list.size()));
            } else {
                newList.add(list.subList(i * num, (i + 1) * num));
            }
        }
        return newList;
    }

    public static <T> List<List<T>> splitListWithRemainder(List<T> list, final int size) {
        List<List<T>> newList = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            newList.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return newList;
    }
}
