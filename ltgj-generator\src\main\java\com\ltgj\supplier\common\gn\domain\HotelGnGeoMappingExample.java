package com.ltgj.supplier.common.gn.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class HotelGnGeoMappingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public HotelGnGeoMappingExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdIsNull() {
            addCriterion("platform_city_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdIsNotNull() {
            addCriterion("platform_city_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdEqualTo(String value) {
            addCriterion("platform_city_id =", value, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdNotEqualTo(String value) {
            addCriterion("platform_city_id <>", value, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdGreaterThan(String value) {
            addCriterion("platform_city_id >", value, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_city_id >=", value, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdLessThan(String value) {
            addCriterion("platform_city_id <", value, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdLessThanOrEqualTo(String value) {
            addCriterion("platform_city_id <=", value, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdLike(String value) {
            addCriterion("platform_city_id like", value, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdNotLike(String value) {
            addCriterion("platform_city_id not like", value, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdIn(List<String> values) {
            addCriterion("platform_city_id in", values, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdNotIn(List<String> values) {
            addCriterion("platform_city_id not in", values, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdBetween(String value1, String value2) {
            addCriterion("platform_city_id between", value1, value2, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformCityIdNotBetween(String value1, String value2) {
            addCriterion("platform_city_id not between", value1, value2, "platformCityId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdIsNull() {
            addCriterion("platform_geo_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdIsNotNull() {
            addCriterion("platform_geo_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdEqualTo(String value) {
            addCriterion("platform_geo_id =", value, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdNotEqualTo(String value) {
            addCriterion("platform_geo_id <>", value, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdGreaterThan(String value) {
            addCriterion("platform_geo_id >", value, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_geo_id >=", value, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdLessThan(String value) {
            addCriterion("platform_geo_id <", value, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdLessThanOrEqualTo(String value) {
            addCriterion("platform_geo_id <=", value, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdLike(String value) {
            addCriterion("platform_geo_id like", value, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdNotLike(String value) {
            addCriterion("platform_geo_id not like", value, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdIn(List<String> values) {
            addCriterion("platform_geo_id in", values, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdNotIn(List<String> values) {
            addCriterion("platform_geo_id not in", values, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdBetween(String value1, String value2) {
            addCriterion("platform_geo_id between", value1, value2, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoIdNotBetween(String value1, String value2) {
            addCriterion("platform_geo_id not between", value1, value2, "platformGeoId");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameIsNull() {
            addCriterion("platform_geo_name is null");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameIsNotNull() {
            addCriterion("platform_geo_name is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameEqualTo(String value) {
            addCriterion("platform_geo_name =", value, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameNotEqualTo(String value) {
            addCriterion("platform_geo_name <>", value, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameGreaterThan(String value) {
            addCriterion("platform_geo_name >", value, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameGreaterThanOrEqualTo(String value) {
            addCriterion("platform_geo_name >=", value, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameLessThan(String value) {
            addCriterion("platform_geo_name <", value, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameLessThanOrEqualTo(String value) {
            addCriterion("platform_geo_name <=", value, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameLike(String value) {
            addCriterion("platform_geo_name like", value, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameNotLike(String value) {
            addCriterion("platform_geo_name not like", value, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameIn(List<String> values) {
            addCriterion("platform_geo_name in", values, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameNotIn(List<String> values) {
            addCriterion("platform_geo_name not in", values, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameBetween(String value1, String value2) {
            addCriterion("platform_geo_name between", value1, value2, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andPlatformGeoNameNotBetween(String value1, String value2) {
            addCriterion("platform_geo_name not between", value1, value2, "platformGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdIsNull() {
            addCriterion("mapping_platform_id is null");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdIsNotNull() {
            addCriterion("mapping_platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdEqualTo(String value) {
            addCriterion("mapping_platform_id =", value, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdNotEqualTo(String value) {
            addCriterion("mapping_platform_id <>", value, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdGreaterThan(String value) {
            addCriterion("mapping_platform_id >", value, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("mapping_platform_id >=", value, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdLessThan(String value) {
            addCriterion("mapping_platform_id <", value, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("mapping_platform_id <=", value, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdLike(String value) {
            addCriterion("mapping_platform_id like", value, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdNotLike(String value) {
            addCriterion("mapping_platform_id not like", value, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdIn(List<String> values) {
            addCriterion("mapping_platform_id in", values, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdNotIn(List<String> values) {
            addCriterion("mapping_platform_id not in", values, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdBetween(String value1, String value2) {
            addCriterion("mapping_platform_id between", value1, value2, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingPlatformIdNotBetween(String value1, String value2) {
            addCriterion("mapping_platform_id not between", value1, value2, "mappingPlatformId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdIsNull() {
            addCriterion("mapping_city_id is null");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdIsNotNull() {
            addCriterion("mapping_city_id is not null");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdEqualTo(String value) {
            addCriterion("mapping_city_id =", value, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdNotEqualTo(String value) {
            addCriterion("mapping_city_id <>", value, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdGreaterThan(String value) {
            addCriterion("mapping_city_id >", value, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdGreaterThanOrEqualTo(String value) {
            addCriterion("mapping_city_id >=", value, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdLessThan(String value) {
            addCriterion("mapping_city_id <", value, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdLessThanOrEqualTo(String value) {
            addCriterion("mapping_city_id <=", value, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdLike(String value) {
            addCriterion("mapping_city_id like", value, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdNotLike(String value) {
            addCriterion("mapping_city_id not like", value, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdIn(List<String> values) {
            addCriterion("mapping_city_id in", values, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdNotIn(List<String> values) {
            addCriterion("mapping_city_id not in", values, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdBetween(String value1, String value2) {
            addCriterion("mapping_city_id between", value1, value2, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingCityIdNotBetween(String value1, String value2) {
            addCriterion("mapping_city_id not between", value1, value2, "mappingCityId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdIsNull() {
            addCriterion("mapping_geo_id is null");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdIsNotNull() {
            addCriterion("mapping_geo_id is not null");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdEqualTo(String value) {
            addCriterion("mapping_geo_id =", value, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdNotEqualTo(String value) {
            addCriterion("mapping_geo_id <>", value, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdGreaterThan(String value) {
            addCriterion("mapping_geo_id >", value, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdGreaterThanOrEqualTo(String value) {
            addCriterion("mapping_geo_id >=", value, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdLessThan(String value) {
            addCriterion("mapping_geo_id <", value, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdLessThanOrEqualTo(String value) {
            addCriterion("mapping_geo_id <=", value, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdLike(String value) {
            addCriterion("mapping_geo_id like", value, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdNotLike(String value) {
            addCriterion("mapping_geo_id not like", value, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdIn(List<String> values) {
            addCriterion("mapping_geo_id in", values, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdNotIn(List<String> values) {
            addCriterion("mapping_geo_id not in", values, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdBetween(String value1, String value2) {
            addCriterion("mapping_geo_id between", value1, value2, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoIdNotBetween(String value1, String value2) {
            addCriterion("mapping_geo_id not between", value1, value2, "mappingGeoId");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameIsNull() {
            addCriterion("mapping_geo_name is null");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameIsNotNull() {
            addCriterion("mapping_geo_name is not null");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameEqualTo(String value) {
            addCriterion("mapping_geo_name =", value, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameNotEqualTo(String value) {
            addCriterion("mapping_geo_name <>", value, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameGreaterThan(String value) {
            addCriterion("mapping_geo_name >", value, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameGreaterThanOrEqualTo(String value) {
            addCriterion("mapping_geo_name >=", value, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameLessThan(String value) {
            addCriterion("mapping_geo_name <", value, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameLessThanOrEqualTo(String value) {
            addCriterion("mapping_geo_name <=", value, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameLike(String value) {
            addCriterion("mapping_geo_name like", value, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameNotLike(String value) {
            addCriterion("mapping_geo_name not like", value, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameIn(List<String> values) {
            addCriterion("mapping_geo_name in", values, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameNotIn(List<String> values) {
            addCriterion("mapping_geo_name not in", values, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameBetween(String value1, String value2) {
            addCriterion("mapping_geo_name between", value1, value2, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andMappingGeoNameNotBetween(String value1, String value2) {
            addCriterion("mapping_geo_name not between", value1, value2, "mappingGeoName");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(String value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(String value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(String value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(String value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(String value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(String value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLike(String value) {
            addCriterion("update_by like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotLike(String value) {
            addCriterion("update_by not like", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<String> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<String> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(String value1, String value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(String value1, String value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Boolean value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Boolean value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Boolean value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Boolean value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Boolean value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Boolean> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Boolean> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Boolean value1, Boolean value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}