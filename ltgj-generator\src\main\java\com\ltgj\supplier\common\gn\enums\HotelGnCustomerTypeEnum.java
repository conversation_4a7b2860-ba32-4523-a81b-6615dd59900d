package com.ltgj.supplier.common.gn.enums;

/**
 * 国内酒店外宾枚举
 */
public enum HotelGnCustomerTypeEnum {
    /**
     * 1: 接待全球客人，不限制客人身份，须持护照入住
     */
    GLOBAL_GUEST(1, "接待全球客人，不限制客人身份，须持护照入住"),

    /**
     * 2: 仅接待大陆客人，须持中国身份证入住
     */
    MAINLAND_GUEST(2, "仅接待大陆客人，须持中国身份证入住"),

    /**
     * 3: 仅接待大陆和港澳台客人，须持港澳台身份证或大陆身份证入住
     */
    MAINLAND_HKMO_TW_GUEST(3, "仅接待大陆和港澳台客人，须持港澳台身份证或大陆身份证入住"),

    /**
     * 4: 仅接待外宾，须持国外护照入住
     */
    FOREIGN_GUEST(4, "仅接待外宾，须持国外护照入住"),

    /**
     * 5: null，不展示
     */
    NONE(5, "不展示");

    private final int code;          // 数字编码（如1, 2, 3...）
    private final String description; // 描述（如"全球客人"）

    HotelGnCustomerTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // Getter 方法
    public int getCode() {
        return this.code;
    }

    public String getDescription() {
        return this.description;
    }


    // 可选：根据 code 查找枚举（方便外部调用）
    public static HotelGnCustomerTypeEnum fromCode(int code) {
        for (HotelGnCustomerTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return NONE; // 默认返回 NONE（不展示）
    }

    // 可选：根据描述查找枚举
    public static HotelGnCustomerTypeEnum fromDescription(String description) {
        for (HotelGnCustomerTypeEnum type : values()) {
            if (type.getDescription().equals(description)) {
                return type;
            }
        }
        return NONE;
    }
}