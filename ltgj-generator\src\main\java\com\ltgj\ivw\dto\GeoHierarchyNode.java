package com.ltgj.ivw.dto;

import lombok.Data;
import lombok.ToString;
import java.util.List;
import java.util.ArrayList;

/**
 * 地理位置层级节点DTO
 * 用于构建四级联动的树形结构数据
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@ToString
public class GeoHierarchyNode {
    
    /** 节点ID */
    private String id;
    
    /** 节点名称 */
    private String name;
    
    /** 英文名称 */
    private String enName;
    
    /** 全称 */
    private String fullName;
    
    /** 节点类型：country, province, city, district */
    private String type;
    
    /** 节点级别：1-国家，2-省份，3-城市，4-区县 */
    private Integer level;
    
    /** 上级节点ID */
    private String parentId;
    
    /** 上级节点名称 */
    private String parentName;
    
    /** 地区类型（仅对district有效：1商圈 2行政区 3标志物） */
    private Long geoType;
    
    /** 经度 */
    private String longitude;
    
    /** 纬度 */
    private String latitude;
    
    /** 子节点列表 */
    private List<GeoHierarchyNode> children;
    
    /** 扩展属性 */
    private Object extra;
    
    public GeoHierarchyNode() {
        this.children = new ArrayList<>();
    }
    
    public GeoHierarchyNode(String id, String name, String type, Integer level) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.level = level;
        this.children = new ArrayList<>();
    }
    
    /**
     * 添加子节点
     */
    public void addChild(GeoHierarchyNode child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
    }
    
    /**
     * 判断是否有子节点
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }
    
} 