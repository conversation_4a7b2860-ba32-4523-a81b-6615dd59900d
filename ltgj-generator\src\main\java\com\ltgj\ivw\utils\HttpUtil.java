package com.ltgj.ivw.utils;

import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.utils.ExceptionUtil;
import com.ltgj.common.utils.StringUtils;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jsoup.Connection;
import org.jsoup.HttpStatusException;
import org.jsoup.Jsoup;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2024年11月08日 18:12
 */
@Slf4j
public class HttpUtil {

    public static void main(String[] args) throws IOException {

        boolean b = checkImgUrl("https://image-cdn.didatravel.com/Image/US/99962/HD/Featured Image/69c010e7_z.jpg");
        System.out.println(b);


//        System.out.println(checkImgUrl("https://image.qiantaohotel.com/Hotels/M2334983/hotel_EBFBEFF99E672C08BCAC6017B88521D4CG.jpg"));


//        String url = "http://test-dify.aitojoy.com/v1/datasets/4e9ca58a-216e-4fa7-adfd-1f3e18f9b5ca/document/create_by_text";
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("name", "我乐意");
//        jsonObject.put("text", "{1}\n{2}");
//
//        jsonObject.put("indexing_technique", "high_quality");
//
//        JSONObject processRule = new JSONObject();
//        processRule.put("mode", "custom");
//
//        JSONObject rules = new JSONObject();
//
//        rules.put("pre_processing_rules", new JSONArray());
//        JSONObject segmentation = new JSONObject();
//        segmentation.put("separator", "\n");
//        segmentation.put("max_tokens", 1000);
//        rules.put("segmentation", segmentation);
//
//        processRule.put("rules", rules);
//
//        jsonObject.put("process_rule", processRule);
//
//
//        HttpUtil.postJson(url, "Bearer dataset-1e02uVYmcwKpPwQl5SoRxaru", jsonObject);


//        String url = "http://test-dify.aitojoy.com/v1/workflows/run";
//        JSONObject params = new JSONObject();
//
//        JSONObject inputs = new JSONObject();
//        inputs.put("name", "凯玄酒店(西安大明宫西地铁站店）");
//        inputs.put("city", "西安");
//        inputs.put("address", "未央路与玄武路交汇处凯旋大厦17层1716室");
//        inputs.put("phone", "+86-17791386928");
//        params.put("inputs", inputs);
//
//        params.put("response_mode", "blocking");
//        params.put("user", "test");
//
//        JSONObject jsonObject = HttpUtil.postJson(url, "Bearer app-JmU1WyVddlOuvY0Z6VfFRv6R", params);
//        log.info("ai响应结果: {}", jsonObject);
//        JSONObject data = jsonObject.getJSONObject("data");
//        JSONObject outputs = data.getJSONObject("outputs");
//        String text = outputs.getString("text");
//        JSONObject textObj = JSONObject.parseObject(text);
//        String id = textObj.getString("id");
//        Integer score = textObj.getInteger("score");
//        log.info("酒店匹配结果: id={}, score={}", id, score);
    }

    //    public static boolean checkImgUrl(String url) throws IOException {
//        Connection.Response execute;
//        try {
//            log.debug("探测url: {}", url);
//            execute = Jsoup.connect(url)
//                .maxBodySize(0)
//                .timeout(3000)
//                .method(Connection.Method.GET)
//                .followRedirects(false)
//                .ignoreContentType(true)
//                .execute();
//        } catch (IOException e) {
//            log.error("img url探测失败: " + url, e);
//            return false;
//        }
//        int i = execute.statusCode();
//        log.debug("探测url结果: {}", i);
//        if (i == 200) {
//            return true;
//        }
//        return false;
//    }
    public static boolean checkImgUrl(String urlString) throws IOException {
        log.debug("探测url: {}", urlString);
        if (StringUtils.isBlank(urlString)) {
            return false;
        }
        StringBuilder sb = new StringBuilder();
        if (urlString.startsWith("https://") || urlString.startsWith("http://")) {
            if (urlString.startsWith("https://")) {
                sb.append("https://");
                String substring = urlString.substring("https://".length());
                String[] split = substring.split("/");
                if (split.length > 0) {
                    for (String path : split) {
                        sb.append(URLEncoder.encode(path, "UTF-8")).append("/");
                    }
                    urlString = sb.substring(0, sb.length() - 1);
                }
            } else if (urlString.startsWith("http://")) {
                sb.append("http://");
                String substring = urlString.substring("http://".length());
                String[] split = substring.split("/");
                if (split.length > 0) {
                    for (String path : split) {
                        sb.append(URLEncoder.encode(path, "UTF-8")).append("/");
                    }
                    urlString = sb.substring(0, sb.length() - 1);
                }
            }
        } else if (urlString.contains("/")) {
            String[] split = urlString.split("/");
            if (split.length > 0) {
                for (String path : split) {
                    sb.append(URLEncoder.encode(path, "UTF-8")).append("/");
                }
                urlString = sb.substring(0, sb.length() - 1);
            }
        }
        HttpURLConnection connection = null;
        try {
            connection = (HttpURLConnection) new URL(urlString).openConnection();
            connection.setRequestMethod("HEAD"); // 使用HEAD方法进行请求，不获取内容
            connection.setConnectTimeout(3000); // 设置连接超时时间
            connection.setReadTimeout(3000); // 设置读取超时时间
            int responseCode = connection.getResponseCode();
            int contentLength = connection.getContentLength();
            log.info("探测url结果: url={}, resCode={}, resLen={}", urlString, responseCode, contentLength);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                return true;
            }
        } catch (Exception e) {
            log.error("img url探测失败: " + urlString, e);
        } finally {
            if (connection != null) {
                connection.disconnect(); // 断开连接
            }
        }
        return false;
    }

    public static JSONObject postJson(String url, String token, JSONObject param) {
        log.info("HTTP请求: url={}, token={}, param={}", url, token, param.toJSONString());
        Connection.Response execute;
        try {
            execute = Jsoup.connect(url)
                    .maxBodySize(10 * 1024 * 102)
                    .timeout(60000)
                    .method(Connection.Method.POST)
                    .header("Authorization", token)
                    .header("Content-Type", "application/json")
                    .requestBody(param.toJSONString())
                    .followRedirects(false)
                    .ignoreContentType(true)
                    .execute();

            int i = execute.statusCode();
            log.info("请求结果结果: code={}", i);
            if (i == 200) {
                try {
                    JSONObject jsonObject = JSONObject.parseObject(execute.body());
                    log.info("HTTP请求响应结果: {}", jsonObject.toJSONString());
                    return jsonObject;
                } catch (Exception e) {
                    log.info("HTTP请求响应结果不是json: {}", execute.body());
                    return null;
                }
            }
        } catch (HttpStatusException e) {
            log.error("请求失败: url=" + url + ",  token=" + token + "code={},error={}", e.getStatusCode(), ExceptionUtils.getStackTrace(e));
            return null;
        } catch (IOException e) {
            log.error("请求失败: url=" + url + ",  token=" + token + "error={}", ExceptionUtils.getStackTrace(e));
            return null;
        }

        return null;
    }

    public static JSONObject get(String url, String token, Map<String, String> queryParams) {
        // 拼接查询参数到URL
        if (queryParams != null && !queryParams.isEmpty()) {
            StringBuilder sb = new StringBuilder(url);
            if (!url.contains("?")) {
                sb.append("?");
            } else if (!url.endsWith("&")) {
                sb.append("&");
            }

            for (Map.Entry<String, String> entry : queryParams.entrySet()) {
                try {
                    sb.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                            .append("=")
                            .append(URLEncoder.encode(entry.getValue(), "UTF-8"))
                            .append("&");
                } catch (UnsupportedEncodingException e) {
                    log.error("编码查询参数失败: key={}, value={}", entry.getKey(), entry.getValue(), e);
                }
            }

            // 删除最后一个多余的 &
            if (sb.charAt(sb.length() - 1) == '&') {
                sb.deleteCharAt(sb.length() - 1);
            }

            url = sb.toString();
        }

        log.info("HTTP GET请求: url={}, token={}", url, token);
        Connection.Response execute;
        try {
            execute = Jsoup.connect(url)
                    .maxBodySize(0)
                    .timeout(60000)
                    .method(Connection.Method.GET)
                    .header("Authorization", token)
                    .followRedirects(false)
                    .ignoreContentType(true)
                    .execute();
        } catch (IOException e) {
            log.error("GET请求失败: url=" + url + ", token=" + token, e);
            return null;
        }

        int statusCode = execute.statusCode();
        log.debug("GET请求结果状态码: {}", statusCode);
        if (statusCode == 200) {
            JSONObject responseBody = JSONObject.parseObject(execute.body());
            log.info("HTTP GET响应结果: {}", responseBody.toJSONString());
            return responseBody;
        }
        return null;
    }


    public static JSONObject delete(String url, String token) {
        log.info("HTTP DELETE请求: url={}, token={}", url, token);
        Connection.Response execute;
        try {
            execute = Jsoup.connect(url)
                    .maxBodySize(0)
                    .timeout(60000)
                    .method(Connection.Method.DELETE)
                    .header("Authorization", token)
                    .followRedirects(false)
                    .ignoreContentType(true)
                    .execute();
        } catch (IOException e) {
            log.error("DELETE请求失败: url=" + url + ", token=" + token, e);
            return null;
        }
        int statusCode = execute.statusCode();
        log.debug("DELETE请求结果状态码: {}", statusCode);
        if (statusCode == 200) {
            JSONObject responseBody = JSONObject.parseObject(execute.body());
            log.info("HTTP DELETE响应结果: {}", responseBody.toJSONString());
            return responseBody;
        }
        return null;
    }


}
