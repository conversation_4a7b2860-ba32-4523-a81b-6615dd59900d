package com.ltgj.supplier.cozyTime.mapping;

import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 城市映射策略工厂类
 * 根据映射类型返回对应的策略实现
 * 
 * <AUTHOR>
 * @date 2024-12-21
 */
@Component
public class CityMappingStrategyFactory {
    
    @Autowired
    private List<CityMappingStrategy> strategies;
    
    private final Map<String, CityMappingStrategy> strategyMap = Maps.newHashMapWithExpectedSize(2);
    
    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void init() {
        for (CityMappingStrategy strategy : strategies) {
            strategyMap.put(strategy.getMappingType(), strategy);
        }
    }
    
    /**
     * 获取映射策略
     * 
     * @param mappingType 映射类型（1-ID映射；2-名称映射）
     * @return 映射策略实现
     */
    public CityMappingStrategy getStrategy(String mappingType) {
        // 如果映射类型为空或者不存在对应的策略，则默认使用ID映射策略
        if (mappingType == null || !strategyMap.containsKey(mappingType)) {
            return strategyMap.get("1");
        }
        return strategyMap.get(mappingType);
    }
} 