package com.ltgj.sdk.cozyTime.model.price;

import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据开始和结束时间查询有更新的起价酒店ID列表，建议每10分钟查询一次
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelApi20HotelLowestPriceIncrementRequest implements CozyTimeRequestInterface<CozyTimeHotelApi20HotelLowestPriceIncrementResponse> {
    @Override
    public String getRequestMethod() {
        return "POST";
    }

    @Override
    public String getEndpoint() {
        return "/hotel_api/2.0/hotelLowestPriceIncrement";
    }

    @Override
    public Class<CozyTimeHotelApi20HotelLowestPriceIncrementResponse> getResponseClass() {
        return CozyTimeHotelApi20HotelLowestPriceIncrementResponse.class;
    }

    /**
     * 开始时间
     * 时间格式：
     * yyyy-MM-ddTHH:mm:ss
     * 必填：true
     */
    private String startDate;

    /**
     * 结束时间
     * 时间格式：
     * yyyy-MM-ddTHH:mm:ss
     * 必填：true
     */
    private String endDate;
}
