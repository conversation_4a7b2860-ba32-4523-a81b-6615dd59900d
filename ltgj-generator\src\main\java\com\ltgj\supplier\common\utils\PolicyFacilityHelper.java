package com.ltgj.supplier.common.utils;

import com.ltgj.supplier.common.enums.YesNoStatus;

/**
 * 酒店政策和设施状态业务帮助类
 * 提供不同业务场景下的语义化操作方法
 * 
 * <AUTHOR>
 * @Date 2025/6/5
 */
public class PolicyFacilityHelper {
    
    // ==================== 设施有无相关 ====================
    
    /**
     * 是否有该设施
     * @param status 状态
     * @return true表示有该设施
     */
    public static boolean hasFacility(YesNoStatus status) {
        return status != null && status.isTrue();
    }
    
    /**
     * 是否没有该设施
     * @param status 状态
     * @return true表示没有该设施
     */
    public static boolean noFacility(YesNoStatus status) {
        return status != null && status.isFalse();
    }
    
    /**
     * 获取设施有无的中文描述
     * @param status 状态
     * @return "有"或"无"
     */
    public static String getFacilityDescription(YesNoStatus status) {
        if (status == null) return "未知";
        return status == YesNoStatus.YES ? "有" : "无";
    }
    
    // ==================== 收费相关 ====================
    
    /**
     * 是否收费
     * @param status 状态
     * @return true表示收费
     */
    public static boolean isCharged(YesNoStatus status) {
        return status != null && status.isTrue();
    }
    
    /**
     * 是否免费
     * @param status 状态
     * @return true表示免费
     */
    public static boolean isFree(YesNoStatus status) {
        return status != null && status.isFalse();
    }
    
    /**
     * 获取收费状态的中文描述
     * @param status 状态
     * @return "收费"或"免费"
     */
    public static String getChargeDescription(YesNoStatus status) {
        if (status == null) return "未知";
        return status == YesNoStatus.YES ? "收费" : "免费";
    }
    
    // ==================== 政策允许相关 ====================
    
    /**
     * 是否允许
     * @param status 状态
     * @return true表示允许
     */
    public static boolean isAllowed(YesNoStatus status) {
        return status != null && status.isTrue();
    }
    
    /**
     * 是否禁止
     * @param status 状态
     * @return true表示禁止
     */
    public static boolean isForbidden(YesNoStatus status) {
        return status != null && status.isFalse();
    }
    
    /**
     * 获取政策允许状态的中文描述
     * @param status 状态
     * @return "允许"或"禁止"
     */
    public static String getPolicyDescription(YesNoStatus status) {
        if (status == null) return "未知";
        return status == YesNoStatus.YES ? "允许" : "禁止";
    }
    
    // ==================== 通用工具方法 ====================
    
    /**
     * 批量检查是否都为肯定状态
     * @param statuses 状态数组
     * @return 所有状态都为YES时返回true
     */
    public static boolean allTrue(YesNoStatus... statuses) {
        if (statuses == null || statuses.length == 0) {
            return false;
        }
        for (YesNoStatus status : statuses) {
            if (status == null || status.isFalse()) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 批量检查是否都为否定状态
     * @param statuses 状态数组
     * @return 所有状态都为NO时返回true
     */
    public static boolean allFalse(YesNoStatus... statuses) {
        if (statuses == null || statuses.length == 0) {
            return false;
        }
        for (YesNoStatus status : statuses) {
            if (status == null || status.isTrue()) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 检查是否至少有一个为肯定状态
     * @param statuses 状态数组
     * @return 至少有一个状态为YES时返回true
     */
    public static boolean anyTrue(YesNoStatus... statuses) {
        if (statuses == null || statuses.length == 0) {
            return false;
        }
        for (YesNoStatus status : statuses) {
            if (status != null && status.isTrue()) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取肯定状态的数量
     * @param statuses 状态数组
     * @return 状态为YES的数量
     */
    public static int countTrue(YesNoStatus... statuses) {
        if (statuses == null || statuses.length == 0) {
            return 0;
        }
        int count = 0;
        for (YesNoStatus status : statuses) {
            if (status != null && status.isTrue()) {
                count++;
            }
        }
        return count;
    }
} 