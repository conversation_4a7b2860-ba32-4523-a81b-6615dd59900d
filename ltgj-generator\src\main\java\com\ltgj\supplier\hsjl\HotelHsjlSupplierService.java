package com.ltgj.supplier.hsjl;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.GeoCityGeo;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IGeoCityGeoService;
import com.ltgj.ivw.service.IHotelCityService;
import com.ltgj.ivw.service.IHotelGnGeoMappingService;
import com.ltgj.ivw.utils.hotelApi.HsjlApi;
import com.ltgj.sdk.hsjl.model.staticdata.HsjlCityListResponse;
import com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping;
import com.ltgj.supplier.cozyTime.TransformUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Set;
import java.util.HashSet;

/**
 * 红色加力酒店供应商服务类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class HotelHsjlSupplierService {

    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private IGeoCityGeoService geoCityGeoService;

    @Autowired
    private IHotelGnGeoMappingService hotelGnGeoMappingService;

    /**
     * 同步红色加力城市数据
     *
     * @param idempotent 是否幂等
     * @return 处理结果
     */
    public String syncCityData(String idempotent) {
        try {
            // 1. 调用API获取红色加力城市数据
            String cityListJson = HsjlApi.queryCityList();
            if (StringUtils.isEmpty(cityListJson)) {
                return "获取红色加力城市数据失败";
            }

            // 2. 解析JSON响应
            JSONObject jsonResponse = JSONObject.parseObject(cityListJson);
            HsjlCityListResponse citiesResponse = jsonResponse.toJavaObject(HsjlCityListResponse.class);
            
            if (citiesResponse == null) {
                return "解析红色加力城市数据失败";
            }

            // 3. 处理城市数据
            int cityCount = this.processCityData(citiesResponse, idempotent);

            return String.format("红色加力城市数据同步完成，共处理城市数据: %d条", cityCount);

        } catch (Exception e) {
            log.error("同步红色加力城市数据异常", e);
            return "同步失败: " + e.getMessage();
        }
    }

    /**
     * 处理红色加力城市数据
     *
     * @param citiesResponse 城市响应数据
     * @param idempotent     是否幂等
     * @return 处理的城市数量
     */
    public int processCityData(HsjlCityListResponse citiesResponse, String idempotent) {
        int count = 0;
        if (citiesResponse == null || citiesResponse.getBussinessResponse() == null || 
            CollectionUtils.isEmpty(citiesResponse.getBussinessResponse().getProvinces())) {
            return count;
        }

        List<HsjlCityListResponse.Province> provinces = citiesResponse.getBussinessResponse().getProvinces();

        // 用于城市去重的集合
        Set<String> processedCityIds = new HashSet<>();
        
        for (HsjlCityListResponse.Province province : provinces) {
            if (CollectionUtils.isEmpty(province.getCitys())) {
                continue;
            }
            
            // 处理每个城市，现在返回多条记录（城市+区县）
            for (HsjlCityListResponse.City city : province.getCitys()) {
                try {
                    List<HotelCity> hotelCities = this.createHotelCity(province, city);
                    
                    for (HotelCity hotelCity : hotelCities) {
                        // 检查是否已存在相同的数据（根据平台、城市ID和区县ID）
                        HotelCity existingQuery = new HotelCity();
                        existingQuery.setReserve1(PlatEnum.PLAT_HSJL.getValue());
                        existingQuery.setCityId(hotelCity.getCityId());
                        existingQuery.setLocationId(hotelCity.getLocationId());
                        List<HotelCity> existingCities = this.hotelCityService.selectHotelCityList(existingQuery);
                        
                        // 如果是城市记录（locationId为null），需要进行全局去重检查
                        boolean isCityRecord = hotelCity.getLocationId() == null;
                        if (isCityRecord) {
                            if (processedCityIds.contains(hotelCity.getCityId())) {
                                log.debug("城市记录已在本次处理中创建，跳过: {} [城市ID: {}]", 
                                    hotelCity.getCityName(), hotelCity.getCityId());
                                continue;
                            }
                        }
                        
                        if (StringUtils.equals(idempotent, "1") && !existingCities.isEmpty()) {
                            // 幂等模式下，如果已存在则跳过
                            String recordType = isCityRecord ? "城市" : "区县";
                            log.debug("红色加力{}数据已存在，跳过: {} [城市ID: {}, 区县ID: {}]", 
                                recordType, hotelCity.getCityName(), hotelCity.getCityId(), hotelCity.getLocationId());
                            continue;
                        }
                        
                        if (existingCities.isEmpty()) {
                            this.hotelCityService.insertHotelCity(hotelCity);
                            count++;
                            
                            // 如果是城市记录，添加到已处理集合中
                            if (isCityRecord) {
                                processedCityIds.add(hotelCity.getCityId());
                            }
                            
                            String recordType = isCityRecord ? "城市" : "区县";
                            log.debug("插入红色加力{}数据: {} [城市ID: {}, 区县ID: {}]", 
                                recordType, hotelCity.getCityName(), hotelCity.getCityId(), hotelCity.getLocationId());
                        } else {
                            String recordType = isCityRecord ? "城市" : "区县";
                            log.debug("红色加力{}数据已存在: {} [城市ID: {}, 区县ID: {}]", 
                                recordType, hotelCity.getCityName(), hotelCity.getCityId(), hotelCity.getLocationId());
                        }
                    }
                } catch (Exception e) {
                    log.error("处理红色加力城市数据异常: {}", city.getCityName(), e);
                }
            }
            // 先注释
            // this.dealWithHsjlGeoMapping(province, hotelGnGeoMappings);
        }

        return count;
    }

    @Deprecated
    private void dealWithHsjlGeoMapping(HsjlCityListResponse.Province province, List<List<HotelGnGeoMapping>> hotelGnGeoMappings) {
        int hotelGnGeoMappingCount;
        TransformUtils.transformAndCollectTwoParam(
                province.getCitys(),
                PlatEnum.PLAT_HSJL.getValue(),
                this::createGnGeoMapping,
                hotelGnGeoMappings,
                e -> log.error("处理红色加力城市映射数据异常: {}",
                        province.getCitys().stream()
                                .findFirst()
                                .map(HsjlCityListResponse.City::getCityName)
                                .orElse("Unknown"), e) // Error handler
        );

        List<HotelGnGeoMapping> hotelGnGeoMappingList = hotelGnGeoMappings.stream().flatMap(Collection::stream).collect(Collectors.toList());
        hotelGnGeoMappingCount = hotelGnGeoMappingService.batchInsertOrUpdate(hotelGnGeoMappingList);

        log.info("处理红色加力城市映射数据完成，共处理城市数据: {}条", hotelGnGeoMappingCount);
    }

    public List<HotelGnGeoMapping> createGnGeoMapping(HsjlCityListResponse.City city, String context) {
        List<HotelGnGeoMapping> hotelGnGeoMappingList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(city.getParentCityCode())) {

            GeoCityGeo geoCityGeoTheCityCode = GeoCityGeo.builder().cityId(city.getParentCityCode()).geoName(city.getCityCode()).build();
            GeoCityGeo geoCityGeoTheCityName = GeoCityGeo.builder().cityId(city.getParentCityCode()).geoName(city.getCityName()).build();
            // 从base库查询对应商圈数据
            List<GeoCityGeo> geoCityGeos = geoCityGeoService.selectGeoCityGeoListBybase(geoCityGeoTheCityCode);
            geoCityGeos = CollectionUtils.isEmpty(geoCityGeos) ? geoCityGeoService.selectGeoCityGeoListBybase(geoCityGeoTheCityName) : geoCityGeos;

            if (CollectionUtils.isNotEmpty(geoCityGeos)) {
                hotelGnGeoMappingList.add(HotelGnGeoMapping.builder()
                        .id(city.getCityCode()+"_"+city.getCityName())
                        .platformId(context)
                        .platformCityId(city.getParentCityCode())
                        .platformGeoId(city.getCityCode())
                        .platformGeoName(city.getCityName())
                        .mappingPlatformId(PlatEnum.PLAT_BASE.getValue())
                        .mappingCityId(geoCityGeos.get(0).getCityId())
                        .mappingGeoId(geoCityGeos.get(0).getGeoId())
                        .mappingGeoName(geoCityGeos.get(0).getGeoName())
                        .remark("红色加力-打底商圈映射数据")
                        .createBy("system")
                        .createTime(DateUtils.getNowDate())
                        .updateBy("system")
                        .build());
            }
        }
        return hotelGnGeoMappingList;
    }

    /**
     * 创建城市对象列表
     *
     * @param province 省份信息
     * @param city 红色加力城市数据
     * @return 酒店城市对象列表
     */
    private List<HotelCity> createHotelCity(HsjlCityListResponse.Province province, HsjlCityListResponse.City city) {
        List<HotelCity> hotelCities = Lists.newArrayList();
        
        if (StringUtils.isNotEmpty(city.getParentCityCode())) {
            // 有parentCityCode说明当前是区县，需要创建城市记录和区县记录
            
            // 1. 创建城市记录
            HotelCity cityRecord = createBasicHotelCity(province);
            cityRecord.setCityId(city.getParentCityCode());
            cityRecord.setCityName(city.getParentCityName());
            cityRecord.setCityNameEn(city.getParentCityEngName());
            // 城市记录的区县信息为空
            cityRecord.setLocationId(null);
            cityRecord.setLocationName(null);
            cityRecord.setLocationNameEn(null);
            hotelCities.add(cityRecord);
            
            log.debug("创建城市记录: 城市[{}:{}]", 
                city.getParentCityCode(), city.getParentCityName());
            
            // 2. 创建区县记录
            HotelCity districtRecord = createBasicHotelCity(province);
            districtRecord.setCityId(city.getParentCityCode());
            districtRecord.setCityName(city.getParentCityName());
            districtRecord.setCityNameEn(city.getParentCityEngName());
            // 设置区县信息
            districtRecord.setLocationId(city.getCityCode());
            districtRecord.setLocationName(city.getCityName());
            districtRecord.setLocationNameEn(city.getAreaEngName());
            hotelCities.add(districtRecord);
            
            log.debug("创建区县记录: 城市[{}:{}], 区县[{}:{}]", 
                city.getParentCityCode(), city.getParentCityName(),
                city.getCityCode(), city.getCityName());
        } else {
            // 没有parentCityCode说明当前是纯城市，只创建城市记录
            HotelCity cityRecord = createBasicHotelCity(province);
            cityRecord.setCityId(city.getCityCode());
            cityRecord.setCityName(city.getCityName());
            cityRecord.setCityNameEn(city.getAreaEngName());
            // 城市记录的区县信息为空
            cityRecord.setLocationId(null);
            cityRecord.setLocationName(null);
            cityRecord.setLocationNameEn(null);
            hotelCities.add(cityRecord);
            
            log.debug("创建纯城市记录: 城市[{}:{}]", 
                city.getCityCode(), city.getCityName());
        }

        return hotelCities;
    }
    
    /**
     * 创建基础的酒店城市对象
     *
     * @param province 省份信息
     * @return 基础的酒店城市对象
     */
    private HotelCity createBasicHotelCity(HsjlCityListResponse.Province province) {
        HotelCity hotelCity = new HotelCity();
        
        // 设置平台标识
        hotelCity.setReserve1(PlatEnum.PLAT_HSJL.getValue());

        // 设置国家信息
        hotelCity.setCountryId(StringUtils.defaultString(province.getCountryCode(),"CN"));
        hotelCity.setCountryName(StringUtils.defaultString(province.getCountryName(),"中国"));
        hotelCity.setCountryNameEn(StringUtils.defaultString(province.getCountryEngName(),"CHINA"));

        // 设置省份信息
        hotelCity.setProvinceId(province.getProvinceCode());
        hotelCity.setProvinceName(province.getProvinceName());
        hotelCity.setProvinceNameEn(province.getProvinceEngName());

        return hotelCity;
    }
} 