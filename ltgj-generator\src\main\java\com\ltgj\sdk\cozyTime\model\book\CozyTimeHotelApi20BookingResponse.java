package com.ltgj.sdk.cozyTime.model.book;

import com.ltgj.sdk.cozyTime.base.CozyTimeBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CozyTimeHotelApi20BookingResponse extends CozyTimeBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;


    private List<CozyTimeHotelApi20BookingResponseBooking> bookings;

    @Data
    class CozyTimeHotelApi20BookingResponseBooking {
        // 科坦订单号
        private String orderNo;

        // 合作方订单号
        private String clientReference;

        // 订单状态
        // 1：预定成功
        // 2：预定失败
        private Integer status;

        // 是否可取消
        // true: 可取消
        // false: 不可取消
        private Boolean cancellable;

        // 订单总金额
        private String totalNet;

        // 币种
        private String currency;

        // 入住日期（格式:yyyy-MM-dd）
        private String checkIn;

        // 离店日期（格式:yyyy-MM-dd）
        private String checkOut;
    }

}
