package com.ltgj.ivw.service;

import com.alibaba.fastjson.JSON;
import com.ltgj.ivw.utils.dto.HotelInfoDTO;
import com.ltgj.web.job.handler.UploadAIKnowledgeHandlerV2;
import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class AiMappingServiceTest {

    @Autowired
    private UploadAIKnowledgeHandlerV2 uploadAIKnowledgeHandlerV2;

    @Autowired
    @Qualifier("difyApiServiceImpl")
    private DifyApiService difyApiService;

    @Autowired
    @Qualifier("difyApiService2Impl")
    private DifyApiService difyApiService2;

    @org.junit.Test
    public void uploadKnowledgeByCity() {
        String param = "{\"knowledgeId\":\"\",\"date\":\"\", \"cityIds\":[]}";
        try {
            uploadAIKnowledgeHandlerV2.execute(param);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @org.junit.Test
    public void aiByStep() {
        for (int i = 0; i < 10; i++) {
            String list = "[{\"id\":\"1076638\",\"name\":\"维景水镇别墅\",\"address\":\"古北水镇售票处旁龙湖长城源著一期\",\"phone\":\"+86-18811783949\",\"cityName\":\"北京\"},{\"id\":\"1129498\",\"name\":\"富华大酒店（渤海路店）\",\"address\":\"寿光市渤海路336号\",\"phone\":\"0536-5218111\",\"cityName\":\"潍坊\"},{\"id\":\"1129565\",\"name\":\"如家酒店(西安西京医院兴庆路店)\",\"address\":\"碑林区兴庆路与仁厚庄北路交叉口东北角（西京医院第四军医大学附近）\",\"phone\":\"029-89330081\",\"cityName\":\"西安\"},{\"id\":\"1010311\",\"name\":\"铂乐思酒店(南宁民主长堽二店)\",\"address\":\"民主长堽路金牛桥星级市场3-7楼\",\"phone\":\"0771-3388885\",\"cityName\":\"南宁\"},{\"id\":\"1012006\",\"name\":\"资兴刘家农庄\",\"address\":\"东江镇梧洞村大江组\",\"phone\":\"+86-18175539369\",\"cityName\":\"资兴\"},{\"id\":\"1020467\",\"name\":\"张家界兰乡小舍\",\"address\":\"张家界武陵源区X020\",\"phone\":\"+86-21-26137886-5095308\",\"cityName\":\"张家界\"},{\"id\":\"1054203\",\"name\":\"南阳一米阳光主题公寓\",\"address\":\"师院西区温馨巷内\",\"phone\":\"0377-66031688\",\"cityName\":\"南阳\"},{\"id\":\"1099570\",\"name\":\"南宁阳光家庭公寓\",\"address\":\"凤岭名园小区9栋\",\"phone\":\"+86-18677104933;+86-13299262106\",\"cityName\":\"南宁\"},{\"id\":\"1129500\",\"name\":\"她他会酒店公寓（广州塔店）\",\"address\":\"海珠区滨江东玉菡路12号（广州大桥南端西侧）\",\"phone\":\"020-89851111\",\"cityName\":\"广州\"},{\"id\":\"1129554\",\"name\":\"望海度假村\",\"address\":\"山海关区龙海大道1号\",\"phone\":\"13930320051\",\"cityName\":\"秦皇岛\"}]";
            List<HotelInfoDTO> hotelList = JSON.parseArray(list, HotelInfoDTO.class);
            try {
                difyApiService.runBatchWorkflowV2(hotelList, true);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

            list = "[{\n" +
                    "\t\"id\": \"M10442870\",\n" +
                    "\t\"name\": \"逸品酒店\",\n" +
                    "\t\"address\": \"文山州富宁县新华镇新兴社区迎宾路12号(近富宁汽车客运站）\",\n" +
                    "\t\"phone\": \"0086-0876-3071288\",\n" +
                    "\t\"cityName\": \"文山\"\n" +
                    "},{\n" +
                    "\t\"id\": \"M32330755\",\n" +
                    "\t\"name\": \"恩平奢享温泉度假别墅\",\n" +
                    "\t\"address\": \"恒大泉都月泉街\",\n" +
                    "\t\"phone\": \"19866079438\",\n" +
                    "\t\"cityName\": \"江门\"\n" +
                    "}]";
            hotelList = JSON.parseArray(list, HotelInfoDTO.class);
            try {
                difyApiService2.runBatchWorkflowV2(hotelList, true);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }
}
