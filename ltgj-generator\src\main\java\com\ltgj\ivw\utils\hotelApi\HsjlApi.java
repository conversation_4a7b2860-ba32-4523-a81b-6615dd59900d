package com.ltgj.ivw.utils.hotelApi;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.sign.Md5Utils;
import com.ltgj.ivw.domain.MinPriceReq;
import com.ltgj.ivw.utils.MyTools;

import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
@Slf4j
public class HsjlApi {
    private static OkHttpClient okHttpClient = new OkHttpClient();

    public static String urlKey = "hsjl.url";
    public static String clientIdKey = "hsjl.clientid";
//    public static String partnerCodeKey = "hsjl.partnercode";
//    public static String secureKeyKey = "hsjl.securekey";
    public static String licenseKeyKey = "hsjl.licensekey";

    private final static String VERSION = "1.0.0";

    private static String partnerCode = "";
    private static String secureKey = "";

    public static void main(String[] args) throws InterruptedException {
       System.out.println(queryCityList());

//        System.out.println(queryDistrictList());


//        List<Long> ids = new ArrayList<>();
//        ids.add(112241l);
//        ids.add(112244l);
//        ids.add(112246l);
//        ids.add(112248l);
//        ids.add(112249l);
//        ids.add(112250l);
//        ids.add(112251l);
//        ids.add(112267l);
//        ids.add(112269l);
//        ids.add(112270l);

//        while (true) {
//            for (int i = 0; i < 110; i++) {
//                new Thread(() -> System.out.println(queryHotelIdList(1))).start();
//            }
//            Thread.sleep(5000);
//        }

//        System.out.println(getMinPrice("112270"));

//        System.out.println(queryHotelInfo(ids));

//        System.out.println(queryHotelIncrement("2024-03-03 00:00:00", "2024-03-04 00:00:00", 1));

//        System.out.println(queryHotelImage());

//        System.out.println(queryHotelLowestPrice());

//        System.out.println(queryProductDetail());

//        System.out.println(queryCityList());
//        String s = queryHotelInfo(Lists.newArrayList(888750L), "*********", "DVGjdoQvr5zpzBxwPrFMKc25");
//        System.out.println(s);
        String s = queryCityList();
        System.out.println(s);
    }

    public static Request createRequest(String requestType, JSONObject req){
        return createRequest(requestType, req, PropertiesUtil.getProp(clientIdKey), PropertiesUtil.getProp(licenseKeyKey));
    }
    public static Request createRequest(String requestType, JSONObject req, String partnerCode, String secureKey){
        long timestamp = new Date().getTime();
        String signature = Md5Utils.hash(timestamp +  partnerCode + (Md5Utils.hash(secureKey)).toUpperCase() + requestType).toUpperCase();
        JSONObject header = new JSONObject();
        header.put("partnerCode", partnerCode);
        header.put("requestType", requestType);
        header.put("signature", signature);
        header.put("timestamp", timestamp+"");
        header.put("version", VERSION);
        req.put("header", header);
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody requestBody = RequestBody.create(mediaType,req.toString());
        String url = "http://www.fangcang.com/tmc-hub/"/*PropertiesUtil.getProp(urlKey)*/ + requestType;
        log.info("调用红色加力接口: 请求地址: {}, 请求头: {}, 请求参数: {}", url, header.toJSONString(), req.toJSONString());
        Request request = new Request.Builder().post(requestBody).url(url).build();//构建
        return request;
    }

    //城市信息查询接口
    public static String queryCityList(){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("countryCode", "CN");
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryCityList", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            if (Objects.isNull(response.body())) {
                log.info("调用红色加力接口: 响应数据: null");
                return null;
            }
            String responseStr = response.body().string();
            log.info("调用红色加力接口: 响应数据: {}", responseStr);
            return responseStr;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //行政区信息查询接口
    public static String queryDistrictList(){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("countryCode", "CN");
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryDistrictList", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            if (Objects.isNull(response.body())) {
                log.info("调用红色加力接口: 响应数据: null");
                return null;
            }
            String responseStr = response.body().string();
            log.info("调用红色加力接口: 响应数据: {}", responseStr);
            return responseStr;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //酒店ID列表查询接口
    public static String queryHotelIdList(String cityCode, int pageNo, String pc, String sk){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("pageNo", pageNo);
        businessRequest.put("checkInType", 1);
        businessRequest.put("cityCode", cityCode);
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelIdList", req,  pc, sk));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            if (Objects.isNull(response.body())) {
                log.info("调用红色加力接口: 响应数据: null");
                return null;
            }
            String responseStr = response.body().string();
            log.info("调用红色加力接口: 响应数据: {}", responseStr);
            return responseStr;
        }catch (Exception e){
            log.error("调用红色加力接口异常: ", e);
            return null;
        }
    }

    //酒店基本信息查询接口
    public static String queryHotelInfo(List<Long> ids, String pc, String sk){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("hotelIds", ids);
        JSONArray jsonArray = new JSONArray();
        jsonArray.add("comment");
        jsonArray.add("hotelFacilityNew");
        jsonArray.add("hotelStructuredPolicies.childPolicy");
        jsonArray.add("hotelStructuredPolicies.extraBedPolicy");
        businessRequest.put("settings", jsonArray);
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelInfo", req, pc, sk));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            if (Objects.isNull(response.body())) {
                log.info("调用红色加力接口: 响应数据: null");
                return null;
            }
            String responseStr = response.body().string();
            log.info("调用红色加力接口响应数据:{},酒店获取红色加力酒店id={}", responseStr,JSONObject.toJSONString(ids));
            return responseStr;
        }catch (Exception e){
            log.error("### queryHotelInfo,e={}",e);
            return null;
        }
    }

    //酒店增量查询接口
    public static String queryHotelIncrement(String startDate, String endDate, int pageNo){
        JSONObject req = new JSONObject();
        req.put("data", "");
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("endTime", endDate);
        businessRequest.put("pageNo", pageNo);
        businessRequest.put("startTime", startDate);
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().connectTimeout(60, TimeUnit.SECONDS).readTimeout(60, TimeUnit.SECONDS).build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelIncrement", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            if (Objects.isNull(response.body())) {
                log.info("调用红色加力接口: 响应数据: null");
                return null;
            }
            String responseStr = response.body().string();
            log.info("调用红色加力接口: 响应数据: {}", responseStr);
            return responseStr;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //酒店图片查询接口
    public static String queryHotelImage(){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        JSONArray hotelIds = new JSONArray();
        hotelIds.add(163011);
        businessRequest.put("hotelIds", hotelIds);
        businessRequest.put("type", 0);
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelImage", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            if (Objects.isNull(response.body())) {
                log.info("调用红色加力接口: 响应数据: null");
                return null;
            }
            String responseStr = response.body().string();
            log.info("调用红色加力接口: 响应数据: {}", responseStr);
            return responseStr;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //酒店每日起价查询接口
    public static String queryHotelLowestPrice(){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        JSONArray hotelIds = new JSONArray();     //与城市编码二选一
        hotelIds.add(163011);
        businessRequest.put("hotelIds", hotelIds);
//        businessRequest.put("cityCode", "SHA");     //与酒店ID二选一，按城市编码提示，系统异常
        businessRequest.put("checkInDate", "2024-01-05");
        businessRequest.put("checkOutDate", "2024-01-06");
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelLowestPrice", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            if (Objects.isNull(response.body())) {
                log.info("调用红色加力接口: 响应数据: null");
                return null;
            }
            String responseStr = response.body().string();
            log.info("调用红色加力接口: 响应数据: {}", responseStr);
            return responseStr;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }


    //酒店每日起价查询接口
    public static String queryHotelLowestPrice(MinPriceReq minPriceReq){
        partnerCode = PropertiesUtil.getProp(clientIdKey);
        secureKey = PropertiesUtil.getProp(licenseKeyKey);
        if(StringUtils.isNotEmpty(minPriceReq.getPartnerCode()) && StringUtils.isNotEmpty(minPriceReq.getSecureKey())) {
            partnerCode = minPriceReq.getPartnerCode();
            secureKey = minPriceReq.getSecureKey();
        }
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        JSONArray hotelIds = new JSONArray();     //与城市编码二选一
        hotelIds.addAll(minPriceReq.getHotelIdList());
        businessRequest.put("hotelIds", hotelIds);
//        businessRequest.put("cityCode", "SHA");     //与酒店ID二选一，按城市编码提示，系统异常
        businessRequest.put("checkInDate", minPriceReq.getCheckInDate());
        businessRequest.put("checkOutDate", minPriceReq.getCheckOutDate());
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryHotelLowestPrice", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            return response.body().string();
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //酒店实时产品查询接口
    public static BigDecimal getMinPrice(String id){
        JSONObject req = new JSONObject();
        JSONObject businessRequest = new JSONObject();
        businessRequest.put("hotelId", Long.valueOf(id));     //与酒店ID二选一，按城市编码提示，系统异常
        businessRequest.put("checkInDate", MyTools.getDateByCurr(2));
        businessRequest.put("checkOutDate", MyTools.getDateByCurr(3));
        req.put("businessRequest", businessRequest);
        try {
            OkHttpClient client = okHttpClient.newBuilder().build();
            //通过mOkHttpClient调用请求得到Call
            final Call call = client.newCall(createRequest("queryProductDetail", req));
            //执行同步请求，获取Response对象
            Response response = call.execute();
            String res = response.body().string();
            System.out.println(res);
            JSONArray roomItems = JSONObject.parseObject(res).getJSONObject("bussinessResponse").getJSONArray("roomItems");
            BigDecimal minPrice = null;
            for (int i = 0; i < roomItems.size(); i++) {
                JSONObject roomItem = roomItems.getJSONObject(i);
                JSONArray products = roomItem.getJSONArray("products");
                for (int j = 0; j < products.size(); j++) {
                    JSONObject product = products.getJSONObject(j);
                    JSONArray priceItems = product.getJSONArray("priceItems");
                    for (int k = 0; k < priceItems.size(); k++) {
                        JSONObject priceItem = priceItems.getJSONObject(k);
                        BigDecimal price = priceItem.getBigDecimal("salePrice");
                        if (minPrice == null || price.compareTo(minPrice) < 0) {
                            minPrice = price;
                        }
                    }
                }
            }
            return minPrice;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}
