package com.ltgj.supplier.common.gn.service;

import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import com.ltgj.supplier.common.gn.mapper.HotelGnIdMappingMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class HotelGnIdMappingService {

    @Autowired
    private HotelGnIdMappingMapper idMappingMapper;

    public long addOrUpdateBatch(List<HotelGnIdMapping> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0L;
        }
        list.forEach(mapping -> {
            mapping.setId(mapping.getPlatformId() + "_" + mapping.getPlatformHotelId());
            mapping.setCreateBy("system");
            mapping.setUpdateBy("system");
            mapping.setCreateTime(LocalDateTime.now());
            mapping.setUpdateTime(LocalDateTime.now());
            mapping.setIsDelete(0);
        });
        return idMappingMapper.addOrUpdateBatch(list);
    }

    public HotelGnIdMapping queryByPlatformIdAndHotelId(String platformId, String platformHotelId) {
        return idMappingMapper.queryByPlatformIdAndHotelId(platformId, platformHotelId);
    }
}
