package com.ltgj.ivw.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ltgj.common.annotation.Excel;
import com.ltgj.common.core.domain.BaseEntity;
import com.ltgj.common.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店基础信息对象 jd_jdb
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@Data
@ApiModel("酒店基础信息对象")
public class JdJdb extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 本地酒店编号 规则 前缀+平台酒店id
     */
    private String id;
    /**
     * 默认平台标识，对应InterfacePlatEnum
     */
    @ApiModelProperty(value = "默认平台标识，对应InterfacePlatEnum")
    @Excel(name = "默认平台标识，对应InterfacePlatEnum")
    private String interfacePlat;

    /**
     * 酒店名称
     */
    @ApiModelProperty(value = "酒店名称")
    @Excel(name = "酒店名称")
    @NotNull(message = "酒店名称不能为空")
    @Size(min = 3, max = 100, message = "酒店名称长度必须在3-100个字符之间")
    private String jdmc;

    /**
     * 酒店英文名称
     */
    @ApiModelProperty(value = "酒店英文名称")
    @Excel(name = "酒店英文名称")
    private String jdmcEn;

    /**
     * 酒店类型id 如公寓等 多个以,隔开
     */
    @ApiModelProperty(value = "酒店类型id 如公寓等 多个以,隔开")
    @Excel(name = "酒店类型id 如公寓等 多个以,隔开")
    @NotNull(message = "酒店类型不能为空")
    private String jdlx;

    /**
     * 酒店地址
     */
    @ApiModelProperty(value = "酒店地址")
    @Excel(name = "酒店地址")
    private String jddz;

    /**
     * 酒店英文地址
     */
    @ApiModelProperty(value = "酒店英文地址")
    @Excel(name = "酒店英文地址")
    private String jddzEn;

    /**
     * 酒店联系电话
     */
    @ApiModelProperty(value = "酒店联系电话")
    @Excel(name = "酒店联系电话")
    @NotNull(message = "酒店联系电话不能为空")
    private String jddh;

    /**
     * 酒店首图地址
     */
    @ApiModelProperty(value = "酒店首图地址")
    @Excel(name = "酒店首图地址")
    private String imgUrl;

    /**
     * 酒店可用状态 0--可用，1--不可用
     */
    @ApiModelProperty(value = "酒店可用状态 0--可用，1--不可用")
    @Excel(name = "酒店可用状态 0--可用，1--不可用")
    private Integer status;

    /**
     * 谷歌经度
     */
    @ApiModelProperty(value = "谷歌经度")
    @Excel(name = "谷歌经度")
    @DecimalMin(value = "-180.0", message = "经度必须大于等于-180")
    @DecimalMax(value = "180.0", message = "经度必须小于等于180")
    @Digits(integer = 3, fraction = 11, message = "经度格式不正确")
    private BigDecimal lonGoogle;

    /**
     * 谷歌纬度
     */
    @ApiModelProperty(value = "谷歌纬度")
    @Excel(name = "谷歌纬度")
    @DecimalMin(value = "-90.0", message = "纬度必须大于等于-90")
    @DecimalMax(value = "90.0", message = "纬度必须小于等于90")
    @Digits(integer = 2, fraction = 11, message = "纬度格式不正确")
    private BigDecimal latGoogle;

    /**
     * 百度经度
     */
    @ApiModelProperty(value = "百度经度")
    @Excel(name = "百度经度")
    @DecimalMin(value = "-180.0", message = "经度必须大于等于-180")
    @DecimalMax(value = "180.0", message = "经度必须小于等于180")
    @Digits(integer = 3, fraction = 11, message = "经度格式不正确")
    private BigDecimal lonBaidu;

    /**
     * 百度纬度
     */
    @ApiModelProperty(value = "百度纬度")
    @Excel(name = "百度纬度")
    @DecimalMin(value = "-90.0", message = "纬度必须大于等于-90")
    @DecimalMax(value = "90.0", message = "纬度必须小于等于90")
    @Digits(integer = 2, fraction = 11, message = "纬度格式不正确")
    private BigDecimal latBaidu;

    /**
     * 高德经度
     */
    @ApiModelProperty(value = "高德经度")
    @Excel(name = "高德经度")
    @DecimalMin(value = "-180.0", message = "经度必须大于等于-180")
    @DecimalMax(value = "180.0", message = "经度必须小于等于180")
    @Digits(integer = 3, fraction = 11, message = "经度格式不正确")
    private BigDecimal lonGaode;

    /**
     * 高德纬度
     */
    @ApiModelProperty(value = "高德纬度")
    @Excel(name = "高德纬度")
    @DecimalMin(value = "-90.0", message = "纬度必须大于等于-90")
    @DecimalMax(value = "90.0", message = "纬度必须小于等于90")
    @Digits(integer = 2, fraction = 11, message = "纬度格式不正确")
    private BigDecimal latGaode;

    /**
     * 所在城市ID，对应B_CITY中的ID
     */
    @ApiModelProperty(value = "所在城市ID，对应B_CITY中的ID")
    @Excel(name = "所在城市ID，对应B_CITY中的ID")
    private String cityId;

    /**
     * 所在城市名称
     */
    @ApiModelProperty(value = "所在城市名称")
    @Excel(name = "所在城市名称")
    private String cityName;

    /**
     * 酒店品牌id
     */
    @ApiModelProperty(value = "酒店品牌id")
    @Excel(name = "酒店品牌id")
    private String brandId;

    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    @Excel(name = "品牌名称")
    private String brandName;

    /**
     * 行政区编码
     */
    @ApiModelProperty(value = "行政区编码")
    @Excel(name = "行政区编码")
    private String district;

    /**
     * 行政区名称
     */
    @ApiModelProperty(value = "行政区名称")
    @Excel(name = "行政区名称")
    private String districtName;

    /**
     * 商圈编码
     */
    @ApiModelProperty(value = "行政区编码")
    @Excel(name = "商圈编码")
    private String businessZone;

    /**
     * 商圈名称
     */
    @ApiModelProperty(value = "商圈名称")
    @Excel(name = "商圈名称")
    private String businessZoneName;

    /**
     * 酒店星级 0 无星 1-5星
     */
    @ApiModelProperty(value = "酒店星级 0 无星 1-5星")
    @Excel(name = "酒店星级 0 无星 1-5星")
    @NotNull(message = "酒店星级不能为空")
    private String jdxj;

    /**
     * 酒店评分
     */
    @ApiModelProperty(value = "酒店评分")
    @Excel(name = "酒店评分")
    @NotNull(message = "酒店评分不能为空")
    private BigDecimal score;

    /**
     * 集团id
     */
    @ApiModelProperty(value = "集团id")
    @Excel(name = "集团id")
    private String jtid;

    /**
     * 集团名称
     */
    @ApiModelProperty(value = "集团名称")
    @Excel(name = "集团名称")
    private String jtmc;

    /**
     * 开业时间
     */
    @ApiModelProperty(value = "开业时间")
    @JsonFormat(pattern = DateUtils.YYYY)
    @Excel(name = "开业时间")
    @NotNull(message = "开业时间不能为空")
    private String kysj;

    /**
     * 最后装修时间
     */
    @ApiModelProperty(value = "最后装修时间")
    @JsonFormat(pattern = DateUtils.YYYY)
    @Excel(name = "最后装修时间")
    private String zhzxsj;

    /**
     * 酒店推荐度 数值越大 推荐度越高
     */
    @ApiModelProperty(value = "酒店推荐度 数值越大 推荐度越高")
    @Excel(name = "酒店推荐度 数值越大 推荐度越高")
    @NotNull(message = "酒店推荐度不能为空")
    private Long rank;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "创建时间", width = 30, dateFormat = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date createdate;

    /**
     * 更新人名称
     */
    @ApiModelProperty(value = "更新人名称")
    @Excel(name = "更新人名称")
    private String mender;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "更新时间", width = 30, dateFormat = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date savedate;

    /**
     * 最低价
     */
    @ApiModelProperty(value = "最低价")
    @Excel(name = "最低价")
    private BigDecimal minPrice;

    /**
     * 告知信息
     */
    @ApiModelProperty(value = "告知信息")
    @Excel(name = "告知信息")
    private String noticeInfo;

    /**
     * 政策信息
     */
    @ApiModelProperty(value = "政策信息")
    @Excel(name = "政策信息")
    private String policyInfo;

    /**
     * 设施信息
     */
    @ApiModelProperty(value = "设施信息")
    @Excel(name = "设施信息")
    private String facilitiesInfo;

    /**
     * ai知识库id
     */
    @ApiModelProperty(value = "ai知识库id")
    @Excel(name = "ai知识库id")
    private String aiDocumentId;

    /**
     * 推荐等级1=主推，2=次推
     */
    @ApiModelProperty(value = "推荐等级1=主推，2=次推")
    private Integer recommendLevel;

    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    @Excel(name = "简介", readConverterExp = "$column.readConverterExp()")
    private String reserve1;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve2;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve3;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve4;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve5;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve6;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve7;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve8;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve9;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String reserve0;

    /**
     * 国家id
     */
    @ApiModelProperty(value = "国家id")
    @Excel(name = "国家id")
    @NotNull(message = "国家id不能为空")
    private String countryId;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    @Excel(name = "国家名称")
    @NotNull(message = "国家名称不能为空")
    private String countryName;
    /**
     * 省份id
     */
    @ApiModelProperty(value = "省份id")
    @Excel(name = "省份id")
    @NotNull(message = "省份id不能为空")
    private String provinceId;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    @Excel(name = "省份名称")
    @NotNull(message = "省份名称不能为空")
    private String provinceName;

    /**
     * 酒店亮点
     */
    @ApiModelProperty(value = "酒店亮点")
    @Excel(name = "酒店亮点")
    private String sparkle;

    /**
     * 原始id
     */
    @ApiModelProperty(value = "原始id")
    @Excel(name = "原始id")
    private String originId;

    /**
     * 周边信息
     */
    @ApiModelProperty(value = "周边信息")
    @Excel(name = "周边信息")
    private String roundInfo;




    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("interfacePlat", getInterfacePlat())
                .append("jdmc", getJdmc())
                .append("jdmcEn", getJdmcEn())
                .append("jdlx", getJdlx())
                .append("jddz", getJddz())
                .append("jddzEn", getJddzEn())
                .append("jddh", getJddh())
                .append("imgUrl", getImgUrl())
                .append("status", getStatus())
                .append("lonGoogle", getLonGoogle())
                .append("latGoogle", getLatGoogle())
                .append("lonBaidu", getLonBaidu())
                .append("latBaidu", getLatBaidu())
                .append("lonGaode", getLonGaode())
                .append("latGaode", getLatGaode())
                .append("cityId", getCityId())
                .append("cityName", getCityName())
                .append("brandId", getBrandId())
                .append("brandName", getBrandName())
                .append("district", getDistrict())
                .append("districtName", getDistrictName())
                .append("businessZone", getBusinessZone())
                .append("businessZoneName", getBusinessZoneName())
                .append("jdxj", getJdxj())
                .append("score", getScore())
                .append("jtid", getJtid())
                .append("jtmc", getJtmc())
                .append("kysj", getKysj())
                .append("zhzxsj", getZhzxsj())
                .append("rank", getRank())
                .append("createdate", getCreatedate())
                .append("mender", getMender())
                .append("savedate", getSavedate())
                .append("minPrice", getMinPrice())
                .append("reserve1", getReserve1())
                .append("reserve2", getReserve2())
                .append("reserve3", getReserve3())
                .append("reserve4", getReserve4())
                .append("reserve5", getReserve5())
                .append("reserve6", getReserve6())
                .append("reserve7", getReserve7())
                .append("reserve8", getReserve8())
                .append("reserve9", getReserve9())
                .append("reserve0", getReserve0())
                .append("recommendLevel", getRecommendLevel())
                .append("countryId", getCountryId())
                .append("countryName", getCountryName())
                .append("provinceId", getProvinceId())
                .append("provinceName", getProvinceName())
                .append("sparkle", getSparkle())
                .append("aiDocumentId", getAiDocumentId())
                .append("originId", getOriginId())
                .append("roundInfo", getRoundInfo())
                .toString();
    }
}
