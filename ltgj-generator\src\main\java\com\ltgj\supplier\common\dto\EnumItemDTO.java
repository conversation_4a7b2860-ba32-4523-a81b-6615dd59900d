package com.ltgj.supplier.common.dto;

/**
 * 通用枚举项 DTO
 * 用于封装枚举的基本信息，提供给前端使用
 */
public class EnumItemDTO {
    /**
     * 枚举编码（对应 HotelGnCustomerTypeEnum 的 code 或 HotelGnFacilityDetail 的 id）
     */
    private Integer code;
    
    /**
     * 枚举名称（对应 description 或 name）
     */
    private String name;
    
    /**
     * 枚举常量名称（如 GLOBAL_GUEST, PARKING 等）
     */
    private String key;

    public EnumItemDTO() {
    }

    public EnumItemDTO(Integer code, String name, String key) {
        this.code = code;
        this.name = name;
        this.key = key;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public String toString() {
        return "EnumItemDTO{" +
                "code=" + code +
                ", name='" + name + '\'' +
                ", key='" + key + '\'' +
                '}';
    }
} 