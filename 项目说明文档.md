# 旅途管家酒店数据系统项目说明文档

## 项目概述

**项目名称**: 旅途管家酒店数据系统 (cn-ltgj-travel-hotel)  
**项目版本**: v3.8.5 (当前开发版本: v1.21.0)  
**项目描述**: 基于Spring Cloud Alibaba的微服务架构酒店数据管理系统，用于整合、管理和维护来自多个渠道的酒店基础数据。

## 核心业务功能

### 1. 酒店数据源整合管理
系统整合了多个主流酒店数据渠道：
- **差旅平台**: 达到旅游(daolv)、科坦(corp)等
- **主流OTA**: 美团(meituan)、携程(corp)、艺龙(elong)等
- **酒店集团**: 华住(huazhu)、锦江(jinjiang)、东呈(dongcheng)等
- **国际平台**: 国际酒店数据(hotel_gj系列)
- **其他渠道**: 千淘(qiantao)、富迅(fuxun)、银旅(yinglv)等

### 2. 酒店基础数据管理
- **国内酒店数据**: 从原分散的 `hotel_info_xxx` 表迁移到统一的 `hotel_gn_xxx` 表结构
- **国际酒店数据**: 新增 `hotel_gj_xxx` 系列表统一管理国际酒店数据
- **重复数据处理**: 基于酒店名称(jdmc)、地址(jddz)和城市ID(cityId)的三重匹配去重机制

### 3. 地理位置数据服务
- **四级地理层次**: 国家→省份→城市→区县的完整地理结构
- **地理数据缓存**: 基于Redis的地理层级数据缓存系统，提供高性能查询服务
- **经纬度校验**: 酒店位置信息的精确性验证
- **城市映射**: 不同平台城市代码的标准化映射

### 4. 数据质量保障
- **自动去重**: 定时任务识别和处理重复酒店数据
- **数据校验**: 酒店名称长度限制、必填字段验证
- **数据格式化**: 统一的数据格式标准化处理
- **异常监控**: 完善的日志记录和异常处理机制

## 技术架构

### 后端技术栈
- **框架基础**: Spring Boot 2.x + Spring Cloud Alibaba
- **微服务**: Spring Cloud Gateway、Nacos服务发现与配置
- **数据访问**: MyBatis Plus + MySQL
- **缓存系统**: Redis (地理数据缓存)
- **任务调度**: Spring Quartz (数据同步和清理任务)
- **API文档**: Swagger 3.0
- **开发工具**: Spring Boot DevTools

### 前端技术栈
- **框架**: Vue.js 2.x + Element UI
- **路由**: Vue Router
- **状态管理**: Vuex
- **HTTP客户端**: Axios
- **图表**: ECharts
- **构建工具**: Vue CLI

### 数据存储
- **主数据库**: MySQL (酒店基础数据、地理数据)
- **缓存层**: Redis (地理层级缓存、会话管理)
- **文件存储**: 本地文件系统 + 云存储支持

## 项目结构说明

### 后端模块结构
```
cn-ltgj-travel-hotel/
├── ltgj-admin/          # Web服务入口模块
├── ltgj-common/         # 公共组件模块
│   ├── ltgj-common-core/    # 核心工具类
│   └── ltgj-commom-es/      # Elasticsearch支持
├── ltgj-framework/      # 框架配置模块
├── ltgj-generator/      # 代码生成器模块
├── ltgj-quartz/         # 定时任务模块
├── ltgj-system/         # 系统管理模块
└── ruoyi-ui/           # 前端Vue项目
```

### 前端功能模块
```
ruoyi-ui/src/views/
├── ivw/                 # 酒店数据管理模块
│   ├── bcity/              # 基础城市数据
│   ├── brand/              # 酒店品牌管理
│   ├── cityHotel/          # 城市酒店管理
│   ├── meituan/            # 美团数据管理
│   ├── huazhu/             # 华住数据管理
│   └── ...                 # 其他数据源模块
├── system/              # 系统管理
├── monitor/             # 系统监控
└── tool/               # 开发工具
```

## 核心功能特性

### 1. 数据整合与同步
- **多源数据接入**: 支持20+个主流酒店数据源的接入和同步
- **增量更新**: 智能识别数据变化，支持增量同步
- **数据映射**: 不同数据源字段的标准化映射
- **错误处理**: 完善的数据同步异常处理和重试机制

### 2. 重复数据管理
- **智能去重**: 基于酒店名称、地址、城市ID的多维度重复检测
- **合并策略**: 重复数据的智能合并和保留策略
- **人工审核**: 支持人工介入复杂重复数据的处理

### 3. 地理位置服务
- **高性能缓存**: Redis缓存的地理层级数据，6小时自动刷新
- **四级联动**: 国家-省份-城市-区县的完整地理结构查询
- **坐标验证**: GPS坐标的有效性检验
- **地理搜索**: 基于地理位置的酒店搜索功能

### 4. 系统监控与管理
- **实时监控**: 数据同步状态、系统性能的实时监控
- **日志管理**: 详细的操作日志和错误日志记录
- **权限控制**: 基于角色的访问权限控制
- **配置管理**: 动态配置参数的在线调整

## 最新版本特性 (v1.21.0)

### 数据结构优化
- ✅ 国内酒店数据表从 `hotel_info_xxx` 迁移到 `hotel_gn_xxx`
- ✅ 新增国际酒店数据管理 `hotel_gj_xxx` 系列表
- ✅ 完善基础数据模型结构

### 性能优化
- ✅ 地理位置查询性能优化，新增Redis缓存机制
- ✅ 线程池并发处理优化
- ✅ 接口限流机制实现

### 数据质量增强
- ✅ 重复酒店数据判断逻辑优化（添加cityId字段）
- ✅ 酒店名称长度限制和格式验证
- ✅ 必填字段NotNull注解完善

### 功能增强
- ✅ DifyApiService重构
- ✅ 异常订单查询功能
- ✅ 动态配置刷新机制

## 部署说明

### 环境要求
- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 5.7+
- **Redis**: 5.0+
- **Node.js**: 14+

### 部署步骤

#### 后端部署
1. **数据库初始化**
   ```bash
   # 创建数据库并执行SQL脚本
   mysql -u root -p < database/init.sql
   ```

2. **Redis配置**
   ```bash
   # 启动Redis服务
   redis-server redis.conf
   ```

3. **Maven构建**
   ```bash
   mvn clean package -DskipTests
   ```

4. **启动服务**
   ```bash
   # Windows
   bin/run.bat
   
   # Linux
   java -jar ltgj-admin/target/ltgj-admin.jar
   ```

#### 前端部署
1. **安装依赖**
   ```bash
   cd ruoyi-ui
   npm install
   ```

2. **开发环境**
   ```bash
   npm run dev
   ```

3. **生产环境**
   ```bash
   npm run build:prod
   ```

### 访问地址
- **管理后台**: http://localhost:80
- **API文档**: http://localhost:8080/swagger-ui/
- **监控面板**: http://localhost:8080/druid/

## 开发指南

### 新增数据源
1. 在 `ruoyi-ui/src/views/ivw/` 下创建对应模块
2. 在 `ruoyi-ui/src/api/ivw/` 下添加API接口
3. 后端添加对应的Controller、Service和Mapper
4. 配置定时同步任务

### 数据映射配置
1. 在 `ltgj-generator` 模块中配置字段映射关系
2. 实现数据转换逻辑
3. 添加数据验证规则

## 注意事项

1. **数据备份**: 定期备份MySQL数据库，特别是生产环境
2. **缓存管理**: Redis缓存默认6小时过期，可根据需要调整
3. **日志监控**: 关注应用日志，及时处理数据同步异常
4. **性能监控**: 定期检查数据库性能，优化慢查询
5. **版本升级**: 新版本部署前务必进行充分测试

## 联系信息

- **开发团队**: 旅途管家技术团队
- **项目负责人**: [待填写]
- **技术支持**: [待填写]

---

*文档最后更新时间: 2024年12月*
*当前版本: v1.21.0* 