package com.ltgj.supplier.common.gn.domain;

import com.ltgj.common.annotation.LogDynamicInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@LogDynamicInfo(title = "国内酒店-商圈映射")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotelGnGeoMapping {

    /**
     * ID
     */
    private String id;

    /**
     * 平台编号，20000XX
     */
    private String platformId;

    /**
     * 平台-城市id
     */
    private String platformCityId;

    /**
     * 平台-商圈id
     */
    private String platformGeoId;

    /**
     * 平台-商圈名称
     */
    private String platformGeoName;

    /**
     * 映射-平台编号：-1，基础平台
     */
    private String mappingPlatformId;

    /**
     * 映射-城市id
     */
    private String mappingCityId;

    /**
     * 映射-商圈id
     */
    private String mappingGeoId;

    /**
     * 映射-商圈名称
     */
    private String mappingGeoName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 删除标识，0：未删除；1：删除
     */
    private Boolean isDelete;
}
