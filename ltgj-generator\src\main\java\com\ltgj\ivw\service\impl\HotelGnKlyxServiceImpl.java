package com.ltgj.ivw.service.impl;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.domain.HotelInfo;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnBaseCommonMapper;
import com.ltgj.ivw.service.HotelGnKlyxService;
import com.ltgj.ivw.service.HotelGnPreMappingService;
import com.ltgj.ivw.service.IHotelGnBaseService;
import com.ltgj.ivw.service.IHotelInfoService;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnKlyx;
import com.ltgj.supplier.common.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 康旅严选酒店信息Service业务层处理
 *
 * <AUTHOR>
 * @Date 2025/3/17
 * @description: 康旅严选特有的业务逻辑实现
 */
@Service("hotelGnKlyxService")
@Slf4j
public class HotelGnKlyxServiceImpl extends BaseHotelServiceImpl<HotelGnBase, HotelGnKlyx>
        implements HotelGnKlyxService, IHotelInfoService, IHotelGnBaseService {

    @Autowired
    private HotelGnBaseCommonMapper<HotelGnKlyx> mapper;

    @Override
    protected Class<HotelGnKlyx> getEntityClass() {
        return HotelGnKlyx.class;
    }

    @Override
    protected List<HotelGnBase> doSelectListWithParams(HotelGnBase entity, Map<String, Object> queryParams) {
        return mapper.selectHotelGnBaseCommonList(entity,getEntityClass());
    }

    @Override
    protected HotelGnBase doSelectById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    protected int doInsert(HotelGnBase entity) {
        return 0;
    }

    @Override
    protected int doUpdate(HotelGnBase entity) {
        return mapper.updateHotelGnBaseCommon(entity,getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        return mapper.deleteHotelGnBaseCommonByIds(ids,getEntityClass());
    }

    // ==================== 实现抽象的数据访问方法 ====================

    @Override
    protected PlatEnum getPlatEnum() {
        return PlatEnum.PLAT_KLYX;
    }

    // ==================== 实现旧接口的方法（兼容性） ====================


    // ==================== 业务逻辑定制 ====================

    @Override
    public Map<String, Object> buildDefaultQueryParams(HotelGnBase entity, HotelGnKlyx searchParams) {
        Map<String, Object> params = super.buildDefaultQueryParams(entity, searchParams);
        
        // 添加康旅严选特有的查询参数处理逻辑
        // 例如：如果将来 HotelGnKlyx 有特有属性，可以在这里处理
        log.debug("康旅严选特有参数处理：{}", params);
        
        return params;
    }

    @Override
    public String validateEntity(HotelGnBase entity, boolean isInsert) {
        // 先调用父类的基础验证
        String baseValidation = super.validateEntity(entity, isInsert);
        if (StringUtils.isNotEmpty(baseValidation)) {
            return baseValidation;
        }
        
        // 康旅严选特有的验证逻辑
        if (entity.getName() == null || entity.getName().trim().isEmpty()) {
            return "康旅严选酒店名称不能为空";
        }
        
        // 可以添加更多康旅严选特有的验证规则
        
        return null;
    }

    @Override
    public HotelGnBase preprocessEntity(HotelGnBase entity, HotelGnKlyx searchParams, boolean isInsert) {
        // 康旅严选特有的预处理逻辑
        if (isInsert) {
            log.info("康旅严选新增酒店预处理：{}", entity.getName());
            // 可以设置一些康旅严选特有的默认值
        } else {
            log.info("康旅严选更新酒店预处理：{}", entity.getName());
        }
        
        return super.preprocessEntity(entity, searchParams, isInsert);
    }

    @Override
    public String validateBeforeDelete(String[] ids, HotelGnKlyx searchParams) {
        if (ids == null || ids.length == 0) {
            return "请选择要删除的康旅严选酒店数据";
        }
        
        // 康旅严选特有的删除前验证逻辑
        for (String id : ids) {
            if (StringUtils.isEmpty(id)) {
                return "酒店ID不能为空";
            }
        }
        
        return null; // 验证通过
    }

    @Override
    public void afterInsert(HotelGnBase entity, int result) {
        super.afterInsert(entity, result);
        if (result > 0) {
            log.info("康旅严选酒店新增成功，触发后续处理：{}", entity.getName());
            // 可以添加康旅严选特有的后处理逻辑，如发送通知等
        }
    }

    @Override
    public void afterUpdate(HotelGnBase entity, int result) {
        super.afterUpdate(entity, result);
        if (result > 0) {
            log.info("康旅严选酒店更新成功，触发后续处理：{}", entity.getName());
            // 可以添加康旅严选特有的后处理逻辑
        }
    }

    @Override
    public void afterDelete(String[] ids, int result) {
        if (result > 0) {
            log.info("康旅严选酒店删除成功，数量：{}", result);
            // 可以添加康旅严选特有的删除后处理逻辑
        }
    }

    // ==================== 导出相关方法实现 ====================

    @Override
    public List<?> exportData(HotelGnBase entity, HotelGnKlyx searchParams) {
        log.info("开始获取康旅严选酒店导出数据，实体：{}，查询参数：{}", entity, searchParams);
        
        // 使用现有的查询方法获取数据，这里不进行分页
        List<HotelGnBase> result = this.selectListWithParams(entity, null);
        
        log.info("获取康旅严选酒店导出数据完成，数据量：{}", result.size());
        return result;
    }

    @Override
    public Class<?> getExportEntityClass() {
        return HotelGnKlyx.class;
    }

    @Override
    public String getExportFileName(HotelGnKlyx searchParams) {
        return "康旅严选酒店信息数据";
    }

    /**
     * FIXME:应该是要删除掉不用了
     * @param id
     * @return
     */
    @Override
    public HotelInfo selectHotelInfoById(String id) {
        return null;
    }

    /**
     * FIXME:应该是要删除掉不用了
     * @param hotelInfo
     * @return
     */
    @Override
    public int updateHotelInfo(HotelInfo hotelInfo) {
        return 0;
    }

    @Override
    public HotelGnBase selectHotelGnBaseById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    public void updateHotelGnBase(HotelGnBase hotelGnBase) {
        this.update(hotelGnBase);
    }
}
