package com.ltgj.supplier.cozyTime.mapping;

import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.domain.SupplierElongCityMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * ID映射策略实现类
 * 通过城市ID进行映射
 * 
 * <AUTHOR>
 * @date 2024-12-21
 */
@Component
public class IdMappingStrategy implements CityMappingStrategy {
    
    private static final Logger log = LoggerFactory.getLogger(IdMappingStrategy.class);
    
    /**
     * 映射类型：ID映射
     */
    private static final String MAPPING_TYPE = "1";
    
    @Override
    public HotelCityMapping createMapping(SupplierElongCityMapping mapping, 
                                        Map<String, HotelCity> ketanCityMap,
                                        List<HotelCityMapping> elongCityMappings) {
        String ketanCityCode = mapping.getSupplierCityCode();
        String elongCityCode = mapping.getElongCityCode();
        
        if (StringUtils.isEmpty(ketanCityCode) || StringUtils.isEmpty(elongCityCode)) {
            log.warn("科坦城市编码或艺龙城市编码为空，无法创建映射");
            return null;
        }
        
        // 获取科坦城市对象
        HotelCity ketanCity = ketanCityMap.get(ketanCityCode);
        if (ketanCity == null) {
            log.warn("未找到科坦城市编码{}对应的城市数据，无法创建映射", ketanCityCode);
            return null;
        }
        
        if (elongCityMappings == null || elongCityMappings.isEmpty()) {
            log.warn("未找到艺龙城市编码{}对应的映射数据，无法创建映射", elongCityCode);
            return null;
        }
        
        // 创建科坦到本地的映射
        HotelCityMapping ketanMapping = new HotelCityMapping();
        ketanMapping.setInterfacePlat(Long.valueOf(ketanCity.getReserve1()));
        ketanMapping.setLocalId(elongCityMappings.get(0).getLocalId());
        ketanMapping.setPlatNum(ketanCity.getCityId());
        ketanMapping.setGjgn(1);
        ketanMapping.setCityName(ketanCity.getCityName());
        
        return ketanMapping;
    }
    
    @Override
    public String getMappingType() {
        return MAPPING_TYPE;
    }
} 