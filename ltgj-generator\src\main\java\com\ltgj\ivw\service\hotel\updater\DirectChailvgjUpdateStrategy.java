package com.ltgj.ivw.service.hotel.updater;

import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.ivw.domain.HotelUpdateRecode;
import com.ltgj.ivw.domain.dto.CityDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelUpdateRecodeService;
import com.ltgj.ivw.service.impl.HotelInfoChailvgjServiceImpl;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 差旅管家酒店数据直连更新策略(策略模式实现)
 *
 * <AUTHOR>
 */
@Slf4j
public class DirectChailvgjUpdateStrategy implements HotelDataUpdateStrategy {
    private final HotelInfoChailvgjServiceImpl service;
    private final IHotelUpdateRecodeService hotelUpdateRecodeService;
    
    /**
     * 构造方法
     * 
     * @param service 酒店服务实现
     * @param hotelUpdateRecodeService 酒店更新记录服务
     */
    public DirectChailvgjUpdateStrategy(HotelInfoChailvgjServiceImpl service, IHotelUpdateRecodeService hotelUpdateRecodeService) {
        this.service = service;
        this.hotelUpdateRecodeService = hotelUpdateRecodeService;
    }
    
    @Override
    public void execute(String userName) {
        // 创建更新记录
        HotelUpdateRecode updateRecode = new HotelUpdateRecode();
        updateRecode.setYl2(PlatEnum.PLAT_CLGJ.getValue());
        updateRecode.setId("CLGJ_DIRECT_" + DateUtils.dateTimeNow());
        updateRecode.setUpdateBy(userName);
        updateRecode.setTimeStart(new Date());
        
        // 创建进度观察者
        ProgressObserver observer = new DefaultProgressObserver(updateRecode, hotelUpdateRecodeService);
        
        // 设置更新状态
        HotelUpdateStatus.expStatusCLGJ = 1;
        
        try {
            // 获取所有城市
            List<CityDTO> cities = service.getAllCities();
            if (cities.isEmpty()) {
                observer.onError(new RuntimeException("获取城市数据失败"));
                return;
            }

            // 设置更新状态为处理中
            HotelUpdateStatus.expStatusCLGJ = 2;

            // 创建数据处理器工厂
            HotelDataProcessorFactory processorFactory = new HotelDataProcessorFactory();
            
            // 统计数据
            AtomicInteger processedCount = new AtomicInteger(0);
            AtomicInteger addCount = new AtomicInteger(0);
            AtomicInteger updateCount = new AtomicInteger(0);
            AtomicInteger totalCount = new AtomicInteger(0);
            
            // 任务提交与执行
            for (CityDTO city : cities) {
                try {
                    // 创建城市酒店数据处理器
                    HotelDataProcessor processor = processorFactory.createProcessor(
                        HotelDataProcessorType.CITY_HOTEL, 
                        service, 
                        city
                    );
                    
                    // 执行数据处理
                    ProcessResult result = processor.process(userName);
                    
                    // 更新统计数据
                    processedCount.addAndGet(result.getProcessedCount().get());
                    addCount.addAndGet(result.getAddCount().get());
                    updateCount.addAndGet(result.getUpdateCount().get());
                    totalCount.addAndGet(result.getTotalCount().get());

                    observer.onProgressUpdated(processedCount.get(), totalCount.get(), addCount.get(), updateCount.get());
                } catch (Exception e) {
                    log.error("处理城市[{}]数据异常", city.getCityId(), e);
                }
            }
            
            // 通知观察者完成
            observer.onCompleted(processedCount.get(), addCount.get(), updateCount.get());
        } catch (Exception e) {
            // 通知观察者错误
            observer.onError(e);
        }
    }
} 