package com.ltgj.web.job.handler;

import com.ltgj.ivw.service.HotelGnPreMappingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "hotelGnPreMappingHighScoreHandler")
public class HotelGnPreMappingHighScoreHandler extends IJobHandler {

    @Autowired
    private HotelGnPreMappingService hotelGnPreMappingService;


    /**
     * 未处理的并且评分为100的预映射数据处理
     * @param s
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        long startTime = System.currentTimeMillis();
        log.info("----处理未处理的并且评分为100的预映射数据开始----");
        hotelGnPreMappingService.processHighScoreMappingAsync(s);
        log.info("----处理未处理的并且评分为100的预映射数据结束----");
        XxlJobLogger.log( "处理未处理的并且评分为100的预映射数据 执行用时：" + (System.currentTimeMillis() - startTime) / 1000 + "秒");
        return ReturnT.SUCCESS;
    }
}
