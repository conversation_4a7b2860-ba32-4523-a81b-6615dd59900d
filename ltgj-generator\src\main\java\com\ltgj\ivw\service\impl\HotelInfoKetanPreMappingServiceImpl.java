package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.google.common.collect.Lists;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.ketan.HotelMapping;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelInfoCozytimeMapper;
import com.ltgj.ivw.mapper.HotelInfoKetanPreMappingMapper;
import com.ltgj.ivw.mapper.JdJdbMapper;
import com.ltgj.ivw.mapper.ZhJdJdbMapper;
import com.ltgj.ivw.service.IHotelCityService;
import com.ltgj.ivw.service.IHotelInfoKetanPreMappingService;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.KeTanApi;
import com.ltgj.sdk.cozyTime.CozyTimeSdkApi;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelIdsResponse;
import com.ltgj.sdk.cozyTime.model.hotel.CozyTimeHotelContentApi20HotelInfoResponse;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20HotelAndRoomMappingResponse;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.common.gn.service.HotelGnIdMappingService;
import com.ltgj.supplier.cozyTime.HotelCozyTimeSupplierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service("HotelInfoKetanPreMappingServiceImpl")
public class HotelInfoKetanPreMappingServiceImpl implements IHotelInfoKetanPreMappingService {

    @Autowired
    private HotelInfoKetanPreMappingMapper hotelInfoKetanPreMappingMapper;

    @Autowired
    private HotelInfoCozytimeMapper hotelInfoCozytimeMapper;

    @Autowired
    private KeTanApi keTanApi;

    @Autowired
    private JdJdbMapper jdJdbMapper;

    @Autowired
    private ZhJdJdbMapper zhJdJdbMapper;

    @Autowired
    private CozyTimeSdkApi cozyTimeSdkApi;

    @Autowired
    private HotelGnBaseService hotelGnBaseService;

    @Autowired
    private HotelGnIdMappingService hotelGnIdMappingService;

    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private HotelCozyTimeSupplierService hotelCozyTimeSupplierService;

    @Resource
    private ThreadPoolTaskExecutor allKetanHotelGnElongExecutor;

    @Override
    public void addMapping() {
        int size = 100;
        int total = this.hotelInfoCozytimeMapper.selectCount();

        if (total < 0) {
            return;
        }

        total = (int) Math.ceil((double) total / size);
        if (total == 0) {
            return;
        }

        // 创建固定线程池（根据你的 CPU 核心数调整）
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        List<Future<?>> futures = new ArrayList<>();

        for (int i = 1; i <= total; i++) {
            final int pageNum = i;
            final int start = (pageNum - 1) * size;

            // 提交任务到线程池
            futures.add(executorService.submit(() -> {
                List<String> ids = this.hotelInfoCozytimeMapper.selectIdsByPage(start, size);

                if (ids == null || ids.isEmpty()) {
                    return;
                }

                // 下面是原 for 循环体的逻辑，复制过来即可
                List<Long> idsLong = ids.stream()
                        .map(Long::parseLong)
                        .collect(Collectors.toList());

                List<HotelInfoKetanPreMapping> hotelInfoKetanPreMappingList = this.hotelInfoKetanPreMappingMapper.selectBySourceIds(idsLong);
                Set<String> collectIds = hotelInfoKetanPreMappingList.stream()
                        .map(HotelInfoKetanPreMapping::getSourceId)
                        .collect(Collectors.toSet());

                List<String> notExistIds = ids.stream()
                        .filter(item -> !collectIds.contains(item))
                        .collect(Collectors.toList());

                if (notExistIds.isEmpty()) {
                    return;
                }
                log.info("需要查询的id, notExistIds = {}", JSONObject.toJSONString(notExistIds));
                List<HotelMapping> hotelAndRoomMapping;
                try {
                    CozyTimeHotelContentApi20HotelAndRoomMappingResponse response = retryOperation(
                            () -> {
                                try {
                                    return this.cozyTimeSdkApi.getHotelAndRoomMapping(notExistIds.stream().map(Long::parseLong).collect(Collectors.toList()));
                                } catch (Exception e) {
                                    throw new RuntimeException(e);
                                }
                            },
                            3,
                            100
                    );

                    if (ObjectUtils.isEmpty(response)) {
                        return;
                    }
                    hotelAndRoomMapping = response.getHotelMappingList();
                    log.info("调用科坦接口返回值， response = {}", JSONObject.toJSONString(response));
                } catch (Exception e) {
                    log.error("调用科坦接口失败，ids: {}", notExistIds, e);
                    return;
                }

                List<HotelInfoKetanPreMapping> hotelInfoKetanPreMappings = new ArrayList<>();
                for (HotelMapping mapping : hotelAndRoomMapping) {
                    HotelInfoKetanPreMapping preMapping = new HotelInfoKetanPreMapping();
                    preMapping.setSourceId(mapping.getHotelId().toString());
                    preMapping.setTargetId(mapping.getElongHotelId());
                    preMapping.setTargetPlat(PlatEnum.PLAT_EL.getValue());
                    preMapping.setMappingType(1);
                    preMapping.setMappingState(0);
                    preMapping.setMappingScore("100");
                    preMapping.setCreateTime(new Date());
                    preMapping.setIsDelete(0);
                    hotelInfoKetanPreMappings.add(preMapping);
                }

                if (!hotelInfoKetanPreMappings.isEmpty()) {
                    int count = this.hotelInfoKetanPreMappingMapper.insertBatch(hotelInfoKetanPreMappings);
                    log.info("成功插入 {} 条预映射记录", count);
                }
            }));
        }

        // 等待所有线程完成
        for (Future<?> future : futures) {
            try {
                future.get(); // 阻塞直到任务完成
            } catch (Exception e) {
                log.error("并发任务异常", e);
            }
        }
        executorService.shutdown(); // 关闭线程池
    }

    public static <T> T retryOperation(Supplier<T> operation, int maxRetries, long delayMillis) throws Exception {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return operation.get();
            } catch (Exception e) {
                if (attempt < maxRetries) {
                    Thread.sleep(delayMillis * attempt);
                } else {
                    log.info("调用科坦接口失败");
                }
            }
        }
        return null;
    }

    @Override
    public void processMapping() {
        int size = 100;
        int total = this.hotelInfoKetanPreMappingMapper.selectCount();
        log.info("数据总条数total: {}", total);

        if (total < 0) {
            return;
        }

        total = (int) Math.ceil((double) total / size);
        if (total == 0) {
            return;
        }

        ExecutorService executorService = Executors.newFixedThreadPool(5); // 根据CPU核心数调整
        List<Future<?>> futures = new ArrayList<>();

        Date now = new Date(); // 所有线程共享当前时间

        for (int i = 1; i <= total; i++) {
            final int pageNum = i;
            final int start = (pageNum - 1) * size;

            futures.add(executorService.submit(() -> {
                try {
                    List<HotelInfoKetanPreMapping> ketanPreMappingList = this.hotelInfoKetanPreMappingMapper.selectNeedProcessByPage(start, size);

                    if (ketanPreMappingList == null || ketanPreMappingList.isEmpty()) {
                        return;
                    }

                    // 筛选状态为初始化的数据
                    List<HotelInfoKetanPreMapping> toProcessList = ketanPreMappingList.stream()
                            .filter(m -> m.getMappingState() == 0)
                            .collect(Collectors.toList());

                    if (toProcessList.isEmpty()) {
                        return;
                    }

                    // 收集 sourceIds 用于批量查询酒店信息
                    List<String> sourceIds = toProcessList.stream()
                            .map(HotelInfoKetanPreMapping::getSourceId)
                            .collect(Collectors.toList());

                    Map<String, HotelInfoCozytime> hotelMap = this.hotelInfoCozytimeMapper.selectBatchByIds(sourceIds).stream()
                            .collect(Collectors.toMap(HotelInfoCozytime::getId, Function.identity()));

                    List<String> localIdList = toProcessList.stream()
                            .map(m -> m.getTargetPlat() + m.getTargetId())
                            .distinct()
                            .collect(Collectors.toList());

                    // 批量查询打底数据和映射关系
                    Map<String, JdJdb> jdJdbMap = this.jdJdbMapper.selectBatchByIds(localIdList).stream()
                            .collect(Collectors.toMap(JdJdb::getId, Function.identity()));

                    Map<String, ZhJdJdbMapping> zhJdJdbMappingMap = this.zhJdJdbMapper
                            .selectByLocalIdsAndInterfacePlat(localIdList, Long.parseLong(PlatEnum.PLAT_KT.getValue()))
                            .stream()
                            .collect(Collectors.toMap(ZhJdJdbMapping::getLocalId, Function.identity()));

                    // 准备更新/插入数据
                    List<ZhJdJdbMapping> toInsertList = new ArrayList<>();
                    List<HotelInfoKetanPreMapping> toUpdateList = new ArrayList<>();
                    List<HotelInfoCozytime> toKetanUpdateList = new ArrayList<>();

                    for (HotelInfoKetanPreMapping mapping : toProcessList) {
                        String targetPlatStr = mapping.getTargetPlat();
                        String targetId = mapping.getTargetId();
                        String localId = targetPlatStr + targetId;

                        // 判断是否打底数据存在
                        if (!jdJdbMap.containsKey(localId)) {
                            mapping.setMappingState(2);
                            mapping.setMappingReason("未找到目标酒店数据");
                            mapping.setUpdateTime(now);
                            toUpdateList.add(mapping);
                            continue;
                        }

                        // 判断是否已存在映射
                        if (zhJdJdbMappingMap.containsKey(localId)) {
                            mapping.setMappingState(2);
                            mapping.setMappingReason("已经添加过该映射");
                            mapping.setUpdateTime(now);
                            toUpdateList.add(mapping);
                            continue;
                        }

                        // 查询科坦酒店信息
                        HotelInfoCozytime ketanHotel = hotelMap.get(mapping.getSourceId());
                        if (ketanHotel == null) {
                            mapping.setMappingState(2);
                            mapping.setMappingReason("未找到对应的科坦酒店信息");
                            mapping.setUpdateTime(now);
                            toUpdateList.add(mapping);
                            continue;
                        }

                        // 插入映射表
                        ZhJdJdbMapping insert = ZhJdJdbMapping.builder()
                                .id(null)
                                .interfacePlat(Long.parseLong(PlatEnum.PLAT_KT.getValue()))
                                .localId(localId)
                                .platId(mapping.getSourceId())
                                .isGnGj(1)
                                .status(0)
                                .saveDate(now)
                                .jdName(jdJdbMap.get(localId).getJdmc())
                                .platJdName(ketanHotel.getName())
                                .build();
                        toInsertList.add(insert);

                        // 更新预映射状态
                        mapping.setMappingState(1);
                        mapping.setUpdateTime(now);
                        toUpdateList.add(mapping);

                        // 更新科坦酒店状态
                        ketanHotel.setStatus(8);
                        ketanHotel.setUpdateTime(now);
                        toKetanUpdateList.add(ketanHotel);
                    }

                    // 数据库操作
                    if (!toInsertList.isEmpty()) {
                        this.zhJdJdbMapper.batchInsert(toInsertList);
                        log.info("批量插入映射表, count={}", toInsertList.size());
                    }

                    if (!toUpdateList.isEmpty()) {
                        this.hotelInfoKetanPreMappingMapper.batchUpdate(toUpdateList);
                        log.info("批量更新预映射表, count={}", toUpdateList.size());
                    }

                    if (!toKetanUpdateList.isEmpty()) {
                        this.hotelInfoCozytimeMapper.batchUpdate(toKetanUpdateList);
                        log.info("批量更新科坦表, count={}", toKetanUpdateList.size());
                    }

                } catch (Exception e) {
                    log.error("处理分页数据时发生异常", e);
                }
            }));
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("并发任务异常", e);
            }
        }

        executorService.shutdown(); // 关闭线程池
    }

    @Deprecated
    public void processHotelGnELongMapping_bak() {
        int size = 100;
        int total = this.hotelGnBaseService.selectCount(PlatEnum.PLAT_KT);
        if (total < 0) {
            return;
        }

        total = (int) Math.ceil((double) total / size);
        if (total == 0) {
            return;
        }

        // 创建固定线程池（根据你的 CPU 核心数调整）
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        List<Future<?>> futures = new ArrayList<>();

        for (int i = 1; i <= total; i++) {
            final int pageNum = i;
            final int start = (pageNum - 1) * size;

            // 提交任务到线程池
            futures.add(executorService.submit(() -> {
                List<String> ids = this.hotelGnBaseService.selectIdsByPage(PlatEnum.PLAT_KT, start, size);

                if (ids == null || ids.isEmpty()) {
                    return;
                }

                // 下面是原 for 循环体的逻辑，复制过来即可
                List<Long> idsLong = ids.stream().map(Long::parseLong).collect(Collectors.toList());


                try {
                    CozyTimeHotelContentApi20HotelAndRoomMappingResponse response = retryOperation(() -> {
                        try {
                            return this.cozyTimeSdkApi.getHotelAndRoomMapping(idsLong);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }, 3, 100);


                    List<HotelMapping> hotelAndRoomMapping = response.getHotelMappingList();
                    if (CollectionUtils.isNotEmpty(hotelAndRoomMapping)) {
                        List<HotelGnIdMapping> mappingList = hotelAndRoomMapping.stream().map(ketanHotelMapping -> {
                            HotelGnIdMapping hotelGnIdMapping = new HotelGnIdMapping();
                            hotelGnIdMapping.setPlatformId(PlatEnum.PLAT_KT.getValue());
                            hotelGnIdMapping.setPlatformHotelId(ketanHotelMapping.getHotelId().toString());
                            hotelGnIdMapping.setMappingPlatformId(PlatEnum.PLAT_EL.getValue());
                            hotelGnIdMapping.setMappingHotelId(ketanHotelMapping.getElongHotelId());
                            return hotelGnIdMapping;
                        }).collect(Collectors.toList());
                        log.info("处理科坦国内酒店艺龙id映射,插入数据, ids:{}, mappingList:{}", JSON.toJSONString(idsLong), JSON.toJSONString(mappingList));
                        this.hotelGnIdMappingService.addOrUpdateBatch(mappingList);
                    }
                } catch (Exception e) {
                    log.error("处理科坦国内酒店艺龙id映射失败，ids: {}", idsLong, e);
                    return;
                }


            }));
        }

        // 等待所有线程完成
        for (Future<?> future : futures) {
            try {
                future.get(); // 阻塞直到任务完成
            } catch (Exception e) {
                log.error("并发任务异常", e);
            }
        }
        executorService.shutdown(); // 关闭线程池

    }

    @Override
    public void processHotelGnELongMapping() {
        HotelCity cityParam = new HotelCity();
        cityParam.setReserve1(PlatEnum.PLAT_KT.getValue());
        List<HotelCity> hotelCities = this.hotelCityService.selectHotelCityList(cityParam);
        this.log.info("更新科坦国内酒店艺龙id映射总共 {} 城市", hotelCities.size());
        int count = 0;
        for (HotelCity city : hotelCities) {

            try {

                CozyTimeHotelContentApi20HotelIdsResponse availableHotelIds = this.cozyTimeSdkApi.getHotelIds(city.getCityId(), "0");
                CozyTimeHotelContentApi20HotelIdsResponse soldOutHotelIds = this.cozyTimeSdkApi.getHotelIds(city.getCityId(), "1");
                List<Long> ids = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(availableHotelIds.getHotelIds())) {
                    ids.addAll(availableHotelIds.getHotelIds());
                }
                if (CollectionUtils.isNotEmpty(soldOutHotelIds.getHotelIds())) {
                    ids.addAll(soldOutHotelIds.getHotelIds());
                }

                if (CollectionUtils.isEmpty(ids)) {
                    this.log.warn("更新科坦国内酒店艺龙id映射总共 {} 城市 , 第 {} 个城市 {} 没有酒店数据", hotelCities.size(), count, city.getCityName());
                    continue;
                }

                List<List<Long>> idsList = Lists.partition(ids, 100);
                List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
                for (List<Long> groupIds : idsList) {

                    CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                        try {
                            CozyTimeHotelContentApi20HotelAndRoomMappingResponse response = (CozyTimeHotelContentApi20HotelAndRoomMappingResponse) hotelCozyTimeSupplierService.fetchDataFunction(PlatEnum.PLAT_KT.getValue() + "_elong_hotelAndRoomMapping", 1, 5, (result) -> {
                                try {
                                    return cozyTimeSdkApi.getHotelAndRoomMapping(groupIds);
                                } catch (Exception e) {
                                    this.log.error("处理科坦国内酒店艺龙id映射,分流获取信息,api调用异常", e);
                                }
                                return null;
                            });

                            if(response != null && response.getResultCode() != null &&  429 == response.getResultCode()){
                                this.log.error("处理科坦国内酒店艺龙id映射,分流获取信息被限流");
                            }

                            if (response != null && CollectionUtils.isNotEmpty(response.getHotelMappingList())) {
                                List<HotelGnIdMapping> mappingList = response.getHotelMappingList().stream().map(ketanHotelMapping -> {
                                    HotelGnIdMapping hotelGnIdMapping = new HotelGnIdMapping();
                                    hotelGnIdMapping.setPlatformId(PlatEnum.PLAT_KT.getValue());
                                    hotelGnIdMapping.setPlatformHotelId(ketanHotelMapping.getHotelId().toString());
                                    hotelGnIdMapping.setMappingPlatformId(PlatEnum.PLAT_EL.getValue());
                                    hotelGnIdMapping.setMappingHotelId(ketanHotelMapping.getElongHotelId());
                                    return hotelGnIdMapping;
                                }).collect(Collectors.toList());
                                log.info("处理科坦国内酒店艺龙id映射,插入数据, ids:{}, mappingList:{}", JSON.toJSONString(groupIds), JSON.toJSONString(mappingList));
                                this.hotelGnIdMappingService.addOrUpdateBatch(mappingList);
                            }
                        } catch (Exception e) {
                            this.log.error("处理科坦国内酒店艺龙id映射,分组处理异常, id:{}", JSON.toJSONString(groupIds), e);
                        }

                    }, this.allKetanHotelGnElongExecutor);
                    completableFutures.add(completableFuture);

                }

                CompletableFuture<?> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));
                try {
                    allOf.get();
                } catch (Exception e) {
                    log.error("处理科坦国内酒店艺龙id映射,处理城市数据异常 : {}", city.getCityName(), e);
                }

            } catch (Exception e) {
                log.error(ExceptionUtil.getStackTrace(e));
                log.error("科坦国内酒店艺龙id映射,科坦城市id数据报错-城市{}", city.getCityName());
            } finally {
                count++;
                this.log.info("更新科坦国内酒店艺龙id映射总共 {} 城市 , 第 {} 个城市已经完成", hotelCities.size(), count);
            }
        }
    }


}
