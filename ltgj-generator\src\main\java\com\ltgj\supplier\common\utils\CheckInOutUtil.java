package com.ltgj.supplier.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CheckInOutUtil {

    public static String[] getCheckInAndOutTime(String text) {
        // 正则表达式匹配时间格式（HH:mm）
        String regex = "(\\d{2}:\\d{2})";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        String[] times = new String[2];
        // 提取匹配的时间
        int index = 1;
        while (matcher.find()) {
            if (index == 1) {
                times[0] = matcher.group(0);
            } else if (index == 2) {
                times[1] = matcher.group(0);
            } else {
                break;
            }
            index++;
        }
        return times;
    }
}
