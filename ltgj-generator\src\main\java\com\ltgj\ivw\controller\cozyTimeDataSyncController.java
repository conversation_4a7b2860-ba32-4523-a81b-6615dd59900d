package com.ltgj.ivw.controller;

import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.domain.CityMapping;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.service.CozyTimeDataSyncService;
import com.ltgj.ivw.service.impl.CozyTimeSyncResult;
import com.ltgj.ivw.service.idempotent.IdempotentResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 科坦数据同步控制器
 * 提供幂等性数据同步API
 */
@Slf4j
@RestController
@RequestMapping("/ketan/sync")
public class cozyTimeDataSyncController extends BaseController {
    
    @Autowired
    private CozyTimeDataSyncService cozyTimeDataSyncService;
    
    /**
     * 同步HotelCity数据
     */
    @PostMapping("/hotelCity")
    public AjaxResult syncHotelCityData(@RequestBody List<HotelCity> hotelCityList) {
        try {
            log.info("接收到HotelCity数据同步请求，数据量: {}", hotelCityList.size());
            
            IdempotentResult<HotelCity> result = cozyTimeDataSyncService.syncHotelCityData(hotelCityList);
            
            log.info("HotelCity数据同步完成: {}", result.getSummary());
            
            return AjaxResult.success("同步完成", result);
        } catch (Exception e) {
            log.error("同步HotelCity数据时发生异常", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 同步CityMapping数据
     */
    @PostMapping("/cityMapping")
    public AjaxResult syncCityMappingData(@RequestBody List<CityMapping> cityMappingList) {
        try {
            log.info("接收到CityMapping数据同步请求，数据量: {}", cityMappingList.size());
            
            IdempotentResult<CityMapping> result = cozyTimeDataSyncService.syncCityMappingData(cityMappingList);
            
            log.info("CityMapping数据同步完成: {}", result.getSummary());
            
            return AjaxResult.success("同步完成", result);
        } catch (Exception e) {
            log.error("同步CityMapping数据时发生异常", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 同步HotelCityMapping数据
     */
    @PostMapping("/hotelCityMapping")
    public AjaxResult syncHotelCityMappingData(@RequestBody List<HotelCityMapping> hotelCityMappingList) {
        try {
            log.info("接收到HotelCityMapping数据同步请求，数据量: {}", hotelCityMappingList.size());
            
            IdempotentResult<HotelCityMapping> result = cozyTimeDataSyncService.syncHotelCityMappingData(hotelCityMappingList);
            
            log.info("HotelCityMapping数据同步完成: {}", result.getSummary());
            
            return AjaxResult.success("同步完成", result);
        } catch (Exception e) {
            log.error("同步HotelCityMapping数据时发生异常", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量同步所有相关数据
     */
    @PostMapping("/all")
    public AjaxResult syncAllData(@RequestBody KetanSyncRequest request) {
        try {
            log.info("接收到科坦数据批量同步请求");
            
            CozyTimeSyncResult result = cozyTimeDataSyncService.syncAllData(
                request.getHotelCityList(),
                request.getCityMappingList(),
                request.getHotelCityMappingList()
            );
            
            log.info("科坦数据批量同步完成: {}", result.getOverallSummary());
            
            return AjaxResult.success("批量同步完成", result);
        } catch (Exception e) {
            log.error("批量同步科坦数据时发生异常", e);
            return AjaxResult.error("批量同步失败: " + e.getMessage());
        }
    }
    
    /**
     * 科坦同步请求对象
     */
    public static class KetanSyncRequest {
        private List<HotelCity> hotelCityList;
        private List<CityMapping> cityMappingList;
        private List<HotelCityMapping> hotelCityMappingList;
        
        // Getters and Setters
        public List<HotelCity> getHotelCityList() {
            return hotelCityList;
        }
        
        public void setHotelCityList(List<HotelCity> hotelCityList) {
            this.hotelCityList = hotelCityList;
        }
        
        public List<CityMapping> getCityMappingList() {
            return cityMappingList;
        }
        
        public void setCityMappingList(List<CityMapping> cityMappingList) {
            this.cityMappingList = cityMappingList;
        }
        
        public List<HotelCityMapping> getHotelCityMappingList() {
            return hotelCityMappingList;
        }
        
        public void setHotelCityMappingList(List<HotelCityMapping> hotelCityMappingList) {
            this.hotelCityMappingList = hotelCityMappingList;
        }
    }
} 