package com.ltgj.web.job.handler;

import com.ltgj.ivw.controller.HotelInfoQiantaoController;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 千淘拉取全量酒店信息任务处理器
 *
 * <AUTHOR>
 * @date 2025-06-09
 */
@Slf4j
@Component
@JobHandler(value = "qiantaoAllHotelGnHandler")
public class QiantaoAllHotelGnHandler extends IJobHandler {

    @Autowired
    private HotelInfoQiantaoController hotelInfoQiantaoController;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("开始执行千淘拉取全量酒店信息时任务，参数：{}", param);

        try {
            this.hotelInfoQiantaoController.updateQiantaoAllNew();
            log.info("千淘拉取全量酒店信息时任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("千淘拉取全量酒店信息时任务执行异常: {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行异常：" + e.getMessage());
        }
    }


} 