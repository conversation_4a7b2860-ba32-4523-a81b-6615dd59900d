package com.ltgj.ivw.domain;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 
 **/ 
@Data
@Accessors(chain = true)
@ApiModel(value = "hotel_facility_cozy_time")
public class HotelFacilityCozyTimeEntity  {

    /** 
     * 
     **/
    @ApiModelProperty(value = "")
    private String id;

    /** 
     * 
     **/
    @ApiModelProperty(value = "")
    private String facilityCode;

    /** 
     * 
     **/
    @ApiModelProperty(value = "")
    private String facilityName;

    /** 
     * 
     **/
    @ApiModelProperty(value = "")
    private String facilityType;

    /** 
     * 
     **/
    @ApiModelProperty(value = "")
    private String categoryName;

    /** 
     * 
     **/
    @ApiModelProperty(value = "")
    private String region;

}
