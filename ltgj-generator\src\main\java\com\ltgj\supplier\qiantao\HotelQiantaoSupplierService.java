package com.ltgj.supplier.qiantao;

import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelCityService;
import com.ltgj.sdk.qiantao.model.staticdata.QiantaoQueryCityResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 千淘供应商服务
 */
@Slf4j
@Service
public class HotelQiantaoSupplierService {

    @Autowired
    private IHotelCityService hotelCityService;

    /**
     * 处理千淘城市数据
     *
     * @param citiesResponse 千淘城市响应数据
     * @param idempotent     是否幂等处理
     * @return 处理的城市数量
     */
    public int processCityData(QiantaoQueryCityResponse citiesResponse, String idempotent) {
        int count = 0;
        if (citiesResponse == null || CollectionUtils.isEmpty(citiesResponse.getReturnJson())) {
            return count;
        }

        List<QiantaoQueryCityResponse.City> cities = citiesResponse.getReturnJson();

        for (QiantaoQueryCityResponse.City city : cities) {
            try {
                HotelCity hotelCity = this.createHotelCity(city);
                // 检查是否已存在相同的城市数据（根据平台和城市ID）
                HotelCity existingQuery = new HotelCity();
                existingQuery.setReserve1(PlatEnum.PLAT_QT.getValue());
                existingQuery.setCityId(hotelCity.getCityId());
                List<HotelCity> existingCities = this.hotelCityService.selectHotelCityList(existingQuery);

                if (StringUtils.equals(idempotent, "1") && !existingCities.isEmpty()) {
                    // 幂等模式下，如果已存在则跳过
                    log.debug("千淘城市数据已存在，跳过: {}", city.getCityName());
                    continue;
                }

                if (existingCities.isEmpty()) {
                    this.hotelCityService.insertHotelCity(hotelCity);
                    count++;
                    log.debug("插入千淘城市数据: {}", city.getCityName());
                } else {
                    log.debug("千淘城市数据已存在: {}", city.getCityName());
                }
            } catch (Exception e) {
                log.error("处理千淘城市数据异常: {}", city.getCityName(), e);
            }
        }

        return count;
    }

    /**
     * 创建城市对象
     *
     * @param city 千淘城市数据
     * @return 酒店城市对象
     */
    private HotelCity createHotelCity(QiantaoQueryCityResponse.City city) {
        HotelCity hotelCity = new HotelCity();
        // 设置平台标识 - 千淘
        hotelCity.setReserve1(PlatEnum.PLAT_QT.getValue());

        //国家
        hotelCity.setCountryId(Optional.ofNullable(city.getCountryCode()).orElse("CN"));
        hotelCity.setCountryName(Optional.ofNullable(city.getCountryName()).orElse("中国"));
        hotelCity.setCountryNameEn(Optional.ofNullable(city.getCountryNameEn()).orElse("CHINA"));

        //省份
        hotelCity.setProvinceId(Optional.ofNullable(city.getProvinceCode()).orElse(city.getProvince()));
        hotelCity.setProvinceName(Optional.ofNullable(city.getProvince()).orElse(""));
        hotelCity.setProvinceNameEn(Optional.ofNullable(city.getProvince()).orElse(""));

        // 设置城市信息
        hotelCity.setCityId(city.getCityCode());
        hotelCity.setCityName(city.getCityName());
        
        return hotelCity;
    }
} 