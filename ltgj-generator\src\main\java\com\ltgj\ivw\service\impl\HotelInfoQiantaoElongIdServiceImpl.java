package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelInfoQiantaoElongId;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelInfoQiantaoElongIdMapper;
import com.ltgj.ivw.service.HotelInfoQiantaoElongIdService;

import com.ltgj.ivw.utils.ConstantList;
import com.ltgj.ivw.utils.ListUtil;
import com.ltgj.ivw.utils.hotelApi.Qi<PERSON>aoApi;
import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import com.ltgj.supplier.common.gn.service.HotelGnIdMappingService;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * @description:
 * @author: GuangFu Wei
 * @date: 2024年10月31日 16:16
 */
@Slf4j
@Service
public class HotelInfoQiantaoElongIdServiceImpl implements HotelInfoQiantaoElongIdService {

    @Autowired
    private HotelInfoQiantaoElongIdMapper hotelInfoQiantaoElongIdMapper;
    @Autowired
    private HotelGnIdMappingService hotelGnIdMappingService;


    @Override
    public void updateInitStatus() {
        hotelInfoQiantaoElongIdMapper.updateInitStatus();
    }

    @Override
    public void updateStatus(List<String> containIdList) {
        hotelInfoQiantaoElongIdMapper.updateStatus(containIdList);
    }

    @Override
    public void updateDelStatus() {
        hotelInfoQiantaoElongIdMapper.updateDelStatus();
    }

    @Override
    public void delInitAllStatus() {
        hotelInfoQiantaoElongIdMapper.delInitAllStatus();
    }

    @Override
    public void insertBatch(List<HotelInfoQiantaoElongId> insertList) {
        hotelInfoQiantaoElongIdMapper.insertBatch(insertList);
    }

    @Override
    public List<String> selectIdIn(List<String> idList) {
        return hotelInfoQiantaoElongIdMapper.selectIdIn(idList);
    }

    @Override
    public List<HotelInfoQiantaoElongId> selectIdInStatus(List<String> idList) {
        return hotelInfoQiantaoElongIdMapper.selectIdInStatus(idList);
    }

    @Override
    public void batchUpdateHotelGnELongMapping() {
        String elongHotelsfileName = "QiantaoElongHotels";
        QiantaoApi.getElongHotels(elongHotelsfileName);
        List<String> fileNameList = QiantaoApi.unzipElongHotels(elongHotelsfileName);
        // 读取千淘映射text数据
        if (CollectionUtils.isEmpty(fileNameList)) {
           log.error("千淘艺龙映射,解压文件失败");
           return;
        }
        for (String fileName : fileNameList) {
            try {
                List<String> qiantaoElongIdList = QiantaoApi.readElongHotels(fileName);
                // 循环批处理映射关系
                if (!CollectionUtils.isEmpty(qiantaoElongIdList)) {
                    List<List<String>> subList = ListUtil.subList(qiantaoElongIdList, 2000);
                    for (List<String> list : subList) {
                        List<HotelGnIdMapping> hotelInfoQiantaoElongIdList = new ArrayList<>();
                        for (String line : list) {
                            if (StringUtils.isBlank(line)) {
                                continue;
                            }
                            String[] s = line.split(" ");
                            if (s.length < 2) {
                                continue;
                            }
                            String elongHotelId = s[0].trim();
                            String qiantaoHotelId = s[1].trim();
                            HotelGnIdMapping hotelGnIdMapping = new HotelGnIdMapping();
                            hotelGnIdMapping.setPlatformId(PlatEnum.PLAT_QT.getValue());
                            hotelGnIdMapping.setPlatformHotelId(qiantaoHotelId);
                            hotelGnIdMapping.setMappingPlatformId(PlatEnum.PLAT_EL.getValue());
                            hotelGnIdMapping.setMappingHotelId(elongHotelId);
                            hotelInfoQiantaoElongIdList.add(hotelGnIdMapping);
                        }
                        if(CollectionUtils.isEmpty(hotelInfoQiantaoElongIdList)){
                            continue;
                        }
                        this.hotelGnIdMappingService.addOrUpdateBatch(Lists.newArrayList(hotelInfoQiantaoElongIdList));

                    }
                }
            }catch (Exception e){
                log.error("千淘艺龙映射,读取文件失败 fileName:{}", fileName, e);
            }

        }
    }
}
