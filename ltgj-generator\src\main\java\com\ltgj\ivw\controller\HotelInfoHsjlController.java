package com.ltgj.ivw.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.poi.ExcelUtil;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.dto.HsjlElongExcelData;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.*;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.HsjlApi;
import com.ltgj.ivw.utils.hotelApi.HsjlxyApi;
import com.ltgj.sdk.hsjl.model.staticdata.HsjlCityListResponse;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnHsjl;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.hsjl.HotelHsjlSupplierService;
import com.ltgj.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 红色加力酒店数据Controller
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@RestController
@RequestMapping("/ivw/hsjl")
@Slf4j
public class HotelInfoHsjlController extends AbstractIvwController<HotelGnBase, HotelGnHsjl, IHotelInfoHsjlService>
{
    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private IHotelCityMappingService hotelCityMappingService;
    @Autowired
    private IJdJdbMappingService jdJdbMappingService;
    @Autowired
    private IHotelUpdateRecodeService hotelUpdateRecodeService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private IJdJdbHsjlCorpService jdJdbHsjlCorpService;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;

    @Autowired
    private IHotelCityService hotelCityService;
    @Autowired
    private HotelInfoHsjlElongIdService hotelInfoHsjlElongIdService;

    @Autowired
    private HotelHsjlSupplierService hotelHsjlSupplierService;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private HotelGnBaseService hotelGnBaseService;

    //红色加力酒店数据  请求100次/分钟
    private static int maxCountDetailHsjl = 10;

    //获取酒店更新状态信息
    @PreAuthorize("@ss.hasPermi('ivw:hsjl:query')")
    @GetMapping("/getInfoStatus")
    public AjaxResult getInfoStatus() {
        Map<String, Object> map = hotelService.getStatus(PlatEnum.PLAT_HSJL);
        log.info("状态查询:{}", JSON.toJSONString(map));
        return AjaxResult.success(map);
    }

    @PreAuthorize("@ss.hasPermi('ivw:hsjl:query')")
    @GetMapping("/updateData")
    public void updateData(String partnerCode, String secureKey, String corpId) throws Exception {
        hotelService.updateHsjlAll(corpId,  getUsername());
    }

    @PreAuthorize("@ss.hasPermi('ivw:hsjl:query')")
    @GetMapping("/resetStatus")
    public void resetStatus() {
        hotelService.resetStatus();
    }


    public HotelCityMapping getMapping(String platId) {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
        mapping.setPlatNum(platId);
        List<HotelCityMapping> list = hotelCityMappingService.selectHotelCityMappingList(mapping);
        if(list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 平台酒店导入本地
     */
    @PreAuthorize("@ss.hasPermi('ivw:hsjl:remove')")
    @Log(title = "红色加力酒店数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult platToLocal(@PathVariable String[] ids) throws Exception {
        hotelService.platToLocal(ids);
        return AjaxResult.success();
    }


    public void updateHsjlAll(String partnerCode, String secureKey, String corpId) throws Exception {
        if(HotelUpdateStatus.status == 1){
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        if((StringUtils.isNotEmpty(partnerCode) && StringUtils.isEmpty(secureKey)) ||
                (StringUtils.isEmpty(partnerCode) && StringUtils.isNotEmpty(secureKey))) {
            throw new Exception("CODE和密钥参数错误！");
        }
        HotelUpdateStatus.status = 1;
        HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
        hotelUpdateRecode.setId("HSJL" + MyTools.getTimesNo());
        hotelUpdateRecode.setYl2(PlatEnum.PLAT_HSJL.getValue());
        hotelUpdateRecode.setTimeStart(new Date());
        hotelUpdateRecode.setUpdateBy(getUsername());
        while (true) {
            if (HotelUpdateStatus.expStatusHSJL == 0) {
                new Thread(() -> {
                    try {
                        //通过调用红色加力酒店id接口，与本地红色加力酒店信息对比，进行本地数据的新增和删除
                        insertHotelHSJL(hotelUpdateRecode, 1, 2, partnerCode, secureKey, corpId);
                    } catch (Exception e) {
                        log.info("插入酒店列表失败: ", e);
                    }
                }).start();
            }
            if (HotelUpdateStatus.expStatusHSJL == 2) {
                new Thread(() -> {
                    try {
                        updateHotelHSJL(hotelUpdateRecode, 3, 4, partnerCode, secureKey);
                    } catch (Exception e) {
                        log.info("更新酒店列表失败: ", e);
                    }
                }).start();
                break;
            }
            Thread.sleep(10000);
            System.err.println("等待10秒执行下一轮操作！当前状态"+HotelUpdateStatus.expStatusHSJL);
        }
    }

    //插入红色加力酒店数据
    @RequestMapping("/insertHotelHSJL")
    public void insertHotelHSJL(HotelUpdateRecode recode, int start, int end, String partnerCode, String secureKey, String corpId) {
        boolean insertCorpFlag = false;
        if(StringUtils.isNotEmpty(partnerCode) && StringUtils.isNotEmpty(secureKey)) {
            insertCorpFlag = true;
        }
        //流程标记赋值
        HotelUpdateStatus.expStatusHSJL = start;
        //定义需要删除的酒店id集合
        List<String> idsAll = new ArrayList<>();

        //查询红色加力酒店数据
        List<HotelGnBase> list =  hotelGnBaseService.getAll(PlatEnum.PLAT_HSJL);
        if (CollectionUtils.isEmpty(list)){
            //流程标记赋值
            HotelUpdateStatus.expStatusHSJL = 0;
            log.info("红色加力酒店查询为null,任务结束");
            return;
        }

        //按酒店列表信息主键分组
        Map<String, HotelGnBase> map = list.stream().collect(Collectors.toMap(HotelGnBase::getId, mapping -> mapping));

        //查询红色加力城市信息
        HotelCity hc = new HotelCity();
        hc.setReserve1(PlatEnum.PLAT_HSJL.getValue());
        List<HotelCity> hcs = hotelCityService.selectHotelCityList(hc);
        if (CollectionUtils.isEmpty(hcs)){
            //流程标记赋值
            HotelUpdateStatus.expStatusHSJL = 0;
            log.info("红色加力酒店城市数据查询为null,任务结束");
            return;
        }

        //遍历处理
        for(HotelCity hotelCity : hcs) {
            //红色根据城市查询酒店列表
            String cityId = StringUtils.isEmpty(hotelCity.getLocationId()) ? hotelCity.getCityId() : hotelCity.getLocationId();
            String infoText = HsjlApi.queryHotelIdList(cityId, 1, partnerCode, secureKey);
            log.info("红色根据城市查询酒店列表,ID：{} 返回报文infoText:{}", cityId,infoText);

            //查询失败二次查询
            JSONObject obj = JSONObject.parseObject(infoText);
            if (obj.getString("returnCode").equals("009")) {
                log.error("调用红色加力供应商失败");
                try {
                    Thread.sleep(5000);
                    log.info("继续执行");
                    infoText = HsjlxyApi.queryHotelIdList(cityId, 1, partnerCode, secureKey);
                    log.info("根据城市查询酒店列表第二次,ID：{} 返回报文infoText:{}", cityId,infoText);
                    obj = JSONObject.parseObject(infoText);
                } catch (InterruptedException e) {
                    log.error("重复执行失败，直接抛出异常 cityId:{} errorMessage:{}", cityId,e.getMessage(),e);
                    throw new RuntimeException(e);
                }
            }

            //红色加力接口结果处理（没有数据直接处理下个城市）
            JSONObject info = obj.getJSONObject("bussinessResponse");
            int pageCount = 0;
            JSONArray ids = null;
            try{
                if (info != null) {
                    pageCount = info.getInteger("totalPage");
                    ids = info.getJSONArray("hotelIds");
                    if(pageCount <= 0){
                        log.error("页数为0，cityId:{}", cityId);
                        Thread.sleep(1000);
                        continue;
                    }
                } else {
                    log.error("info 为空，cityId: {}", cityId);
                    Thread.sleep(1000);
                    continue; // 或者抛出异常
                }
            }catch (Exception e){
                log.error("休眠异常 errorMessage:{}",e.getMessage(),e);
            }

            HotelGnHsjl hotelInfoHsjl = new HotelGnHsjl();

            //循环红色加力接口返回的酒店id
            for (int j = 0; j < ids.size(); j++) {
                String id = ids.getString(j);

                //本地数据库没有此id就插入
                if (map.get(id) == null) {
                    hotelInfoHsjl.setId(id);
                    hotelInfoHsjl.setReserve9("1");
                    if (insertCorpFlag) {
                        //插入jd_jdb_hsjl_corp表
                        insertCorp(id, corpId);
                        hotelInfoHsjl.setReserve8("1");
                    }
                    try {
                        //根据id查询全表数据
                        HotelGnBase temp = hotelGnBaseService.getById(PlatEnum.PLAT_HSJL, id);
                        //不存在：直接插入
                        if (ObjectUtils.isEmpty(temp)){
                            hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Collections.singletonList(hotelInfoHsjl));
                        }else{
                            //存在：修改删除状态为未删除，状态置为初始化方便第二步拉取补全信息
                            temp.setIsDelete(0);
                            temp.setStatus(0);
                            hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Collections.singletonList(temp));
                        }
                    } catch (Exception e) {
                        HotelGnBase hotelInfoHsjl1 = hotelGnBaseService.getById(PlatEnum.PLAT_HSJL, id);
                        if (Objects.isNull(hotelInfoHsjl1)) {
                            log.error("插入红色加力酒店数据失败: ", e);
                        } else {
                            map.put(id, hotelInfoHsjl1);
                        }
                    }
                    HotelUpdateStatus.hsjlIdsCountAdd++;
                } else {
                    //本地数据库存在此id就放入idsAll等待待处理
                    idsAll.add(id);
                }
            }

            //处理第二页及后面数据
            for (int k = 2; k <= pageCount; k++) {
                try {
                    infoText = HsjlApi.queryHotelIdList(cityId, k, partnerCode, secureKey);
                    info = JSONObject.parseObject(infoText).getJSONObject("bussinessResponse");
                    if (info == null) {
                        continue;
                    }
                    ids = info.getJSONArray("hotelIds");
                    for (int m = 0; m < ids.size(); m++) {
                        String id = ids.getString(m);
                        if (map.get(id) == null) {
                            hotelInfoHsjl.setId(id);
                            hotelInfoHsjl.setReserve9("1");
                            if (insertCorpFlag) {
                                insertCorp(id, corpId);
                                hotelInfoHsjl.setReserve8("1");
                            }
                            //根据id查询全表数据
                            HotelGnBase temp = hotelGnBaseService.getById(PlatEnum.PLAT_HSJL, id);
                            //不存在：直接插入
                            if (ObjectUtils.isEmpty(temp)){
                                hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Collections.singletonList(hotelInfoHsjl));
                            }else{
                                //存在：修改删除状态为未删除，状态置为初始化方便第二步拉取补全信息
                                temp.setIsDelete(0);
                                temp.setStatus(0);
                                hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Collections.singletonList(temp));
                            }
                            HotelUpdateStatus.hsjlIdsCountAdd++;
                        } else {
                            idsAll.add(id);
                        }
                    }
                    Thread.sleep(5000);
                } catch (Exception e) {
                    log.error("插入HSJL进异常了 errorMessage:{}",e.getMessage(),e);
                    continue;
                }
            }
//            }
        }

        //删除已失效酒店及映射，如果是企业更新，则不删除
        /*if (!insertCorpFlag) {
            for (HotelGnBase hotel : list) {
                if (!idsAll.contains(hotel.getId())) {
                    if (hotel.getReserve8() == null) {   //非企业酒店，删除
                        hotelService.deleteHotelInfoHsjlById(hotel.getId());
                        HotelUpdateStatus.hsjlDeleteJS++;
                        JdJdbMapping queryMapping = new JdJdbMapping();
                        queryMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
                        queryMapping.setPlatId(hotel.getId());
                        for (JdJdbMapping jdJdbMapping : jdJdbMappingService.selectJdJdbMappingList(queryMapping)) {
                            jdJdbMappingService.deleteJdJdbMappingByIdV1(jdJdbMapping.getId());
                        }
                    }
                }
            }
        }*/

        HotelUpdateStatus.expStatusHSJL = end;
        recode.setCountDel(HotelUpdateStatus.hsjlDeleteJS);
        recode.setCountAdd(HotelUpdateStatus.hsjlIdsCountAdd);
        hotelUpdateRecodeService.insertHotelUpdateRecode(recode);
    }

    //更新红色加力酒店详情
    @RequestMapping("/updateHotelHSJL")
    public void updateHotelHSJL(HotelUpdateRecode recode, int start, int end, String partnerCode, String secureKey) {
        //赋值流程标记
        HotelUpdateStatus.expStatusHSJL = start;

        //查询状态为初始化的数据 status = 0
        HotelInfoHsjl hotelInfoHsjl = new HotelInfoHsjl();
        hotelInfoHsjl.setStatus(0);
        List<HotelInfoHsjl> hotelInfoHsjlList = hotelService.selectHotelInfoHsjlList2(hotelInfoHsjl);
        HotelUpdateStatus.hsjlDetailCount = hotelInfoHsjlList.size();

        //酒店id分组，每组10条
        List<List<Long>> idsList = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        HotelInfoHsjl hotelInfoHsjl1 = new HotelInfoHsjl();
        for (int i = 0;i<hotelInfoHsjlList.size();i++){
            HotelInfoHsjl hsjl = hotelInfoHsjlList.get(i);
            if(ids.size() == maxCountDetailHsjl){
                idsList.add(ids);
                ids = new ArrayList<>();
            }
            ids.add(Long.valueOf(hsjl.getId()));
            if(i == hotelInfoHsjlList.size()-1){
                idsList.add(ids);
            }
        }

        //遍历id数据集合
        for(int j = 0; j<idsList.size(); j++){
            List<Long> ids0 = idsList.get(j);
            try{
                log.info("抓取红色加力酒店数据: ids={}", ids0);
                String infoText = HsjlApi.queryHotelInfo(ids0, partnerCode, secureKey);
                log.info("抓取红色加力酒店数据: result={}", infoText);
                JSONObject info = JSONObject.parseObject(infoText);
                if(info.getString("returnCode").equals("000")){
                    JSONArray hotelInfos = info.getJSONObject("bussinessResponse").getJSONArray("hotelInfos");
                    if(hotelInfos != null) {
                        for(int k = 0; k < hotelInfos.size(); k++){
                            try {
                                HotelGnHsjl hotelGnHsjl = hotelService.buildHotelGnHsjl(PlatEnum.PLAT_HSJL, hotelInfos.getJSONObject(k));
                                hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Collections.singletonList(hotelGnHsjl));
                                HotelUpdateStatus.hsjlDetailCountUpdate++;
                            }catch (Exception e) {
                                log.error("获取红色加力详情进异常: {}", e);
                                TXTUtil.writeTXT(new Date() + " 获取红色加力详情进异常了： " + infoText,
                                        ConstantList.LOG_PATH, "log.txt");
                            }
                        }
                    }
                }else if(info.getString("returnCode").equals("009")){
                    j--;
                    System.out.println(infoText);
                    log.info("红色加力协议返回code {},infoText:{}",info.getString("returnCode"),infoText);
                }else {
                    TXTUtil.writeTXT(new Date() +" 获取红色加力详情进else了： " + infoText,
                            ConstantList.LOG_PATH, "log.txt");
                    log.info("获取红色加力协议详情进else了 {}",infoText);
                }
            }catch (Exception e) {
                j--;
//                e.printStackTrace();
//                TXTUtil.writeTXT(new Date() + " 获取付红色加力详情进异常了： " +e,
//                        ConstantList.LOG_PATH, "log.txt");
                log.error("获取付红色加力协议详情进异常了 errorMessage:{}",e.getMessage(),e);
                continue;
            }finally {
                try {
                    Thread.sleep(6000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        HotelUpdateStatus.expStatusHSJL = end;
        HotelUpdateStatus.status = 0;
        recode.setCountFail(HotelUpdateStatus.hsjlDetailCountUpdate);
        recode.setCountAdd(HotelUpdateStatus.hsjlIdsCountAdd);
        recode.setTimeEnd(new Date());
        hotelUpdateRecodeService.updateHotelUpdateRecode(recode);
    }


    public List<HotelCityMapping> getCitys2(String interfacePlat) {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setInterfacePlat(Long.valueOf(interfacePlat));
        return hotelCityMappingService.selectHotelCityMappingList(mapping);
    }

    public List<String> getCityStr() {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setLocalData(1);
        List<HotelCityMapping> mappingList = hotelCityMappingService.selectHotelCityMappingList(mapping);
        List<String> cityStr = new ArrayList<>();
        for (HotelCityMapping hotelCityMapping : mappingList) {
            cityStr.add(hotelCityMapping.getCityName());
        }
        return cityStr;
    }

    /**
     * 红色加力酒店数据映射
     * @return
     * @throws Exception
     */
    @RequestMapping("/mapping")
    public AjaxResult mapping() throws Exception {
        if(HotelUpdateStatus.status == 1){
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        Map<String, JdJdbMapping> jdjdbmappingMap = new ConcurrentHashMap<>();
        HotelUpdateStatus.status = 1;
        HotelUpdateStatus.expStatusHSJL = 11;
        List<HotelCityMapping> citys = getCitys2(PlatEnum.PLAT_HSJL.getValue());
        Map<String, HotelCityMapping> mapCity = new HashedMap();
        for (HotelCityMapping city : citys) {
            mapCity.put(city.getPlatNum(), city);
        }

        //查询未映射的酒店数据
        HotelInfoHsjl search = new HotelInfoHsjl();
        search.setStatus(1);
        List<HotelInfoHsjl> list = hotelService.selectHotelInfoHsjlList2(search);
        HotelUpdateStatus.hsjlMappingCount = list.size();
        //处理红色加力与艺龙的映射数据
//        parseExcel4Elong();

        List<HotelInfoHsjl> newList = new ArrayList<>();
        List<List<HotelInfoHsjl>> lists = ListUtil.subList(list, 1000);
        for (List<HotelInfoHsjl> l : lists) {
            List<String> idList = l.stream().map(HotelInfoHsjl::getId).collect(Collectors.toList());
            //根据红色加力ID批量查询艺龙映射关系数据
            List<HotelInfoHsjlElongId> containIdList = hotelInfoHsjlElongIdService.selectIdInStatus(idList);
            List<String> jdJdbInList = jdJdbService.selectInIds(containIdList.stream().map(e->PlatEnum.PLAT_EL.getValue()+e.getElongId()).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(containIdList)) {
                Map<String, HotelInfoHsjlElongId> relationMap
                    = containIdList.stream().collect(Collectors.toMap(HotelInfoHsjlElongId::getId, Function.identity()));
                List<String> updateSuccessList = new ArrayList<>();
                for (HotelInfoHsjl hotelInfoHsjl : l) {
                    HotelInfoHsjlElongId hotelInfoHsjlElongId = relationMap.get(hotelInfoHsjl.getId());
                    if (Objects.nonNull(hotelInfoHsjlElongId) && jdJdbInList.contains(PlatEnum.PLAT_EL.getValue() + hotelInfoHsjlElongId.getElongId())) {
                        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                        zhJdJdbMapping.setLocalId(PlatEnum.PLAT_EL.getValue() + hotelInfoHsjlElongId.getElongId());
                        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
                        zhJdJdbMapping.setPlatId(hotelInfoHsjl.getId());
                        zhJdJdbMapping.setJdName(hotelInfoHsjl.getName());
                        zhJdJdbMapping.setStatus(0);
                        zhJdJdbMapping.setSaveDate(new Date());
                        zhJdJdbMapping.setIsGnGj(1);
                        logger.info("红色加力酒店zh_jd_jdb_mapping插入前数据 {}", JSONObject.toJSONString(zhJdJdbMapping));
                        //插入前校验重复
                        log.info("红色加力酒店zh_jd_jdb_mapping localId {},interfacePlat {}", zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat());
                        if(null == zhJdJdbMappingService.findByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(),zhJdJdbMapping.getInterfacePlat())) {
                            zhJdJdbMappingService.insert(zhJdJdbMapping);
                            updateSuccessList.add(hotelInfoHsjl.getId());
                        }
                        HotelUpdateStatus.hsjlMappingCounted.incrementAndGet();
                    } else {
                        newList.add(hotelInfoHsjl);
                    }
                }
                if (!CollectionUtils.isEmpty(updateSuccessList)) {
                    hotelService.updateSuccess(updateSuccessList);
                }
            }
        }
        list = newList;

        if(HotelUpdateStatus.jdMap.get("10119") == null) {
            HotelCityMapping city = new HotelCityMapping();
            city.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_EL.getValue()));
            List<HotelCityMapping> elongCityList = hotelCityMappingService.selectHotelCityMappingList(city);
            JdJdb jdJdb = new JdJdb();
            for (HotelCityMapping cityMapping : elongCityList) {
                jdJdb.setCityId(cityMapping.getLocalId());
                HotelUpdateStatus.jdMap.put(cityMapping.getLocalId(), jdJdbService.selectJdJdbList(jdJdb));
            }
        }
        List<String> cityStr = getCityStr();

        int tc = Integer.valueOf(sysDictDataService.selectDictLabel("hotel_params", "mapping_thread_count"));
        AtomicInteger threadSize = new AtomicInteger(tc);

        int count = list.size();
        int tcount = count / tc;

        for(int t = 0; t < tc; t++) {
            List<HotelInfoHsjl> listSub0;
            if(t == tc - 1) {
                listSub0 = list.subList(t * tcount, count);
            }else {
                listSub0 = list.subList(t * tcount, (t + 1) * tcount);
            }
            List<HotelInfoHsjl> listSub = listSub0;
            new Thread(() -> {
                try {
                    HotelCityMapping hotelCityMapping = new HotelCityMapping();
                    hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
                    for (int j = 0; j < listSub.size(); j++) {
                        HotelInfoHsjl noys = listSub.get(j);
                        hotelCityMapping.setPlatNum(noys.getCityId());
                        List<HotelCityMapping> mappings = hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
                        if (mappings.size() == 0) {
                            HotelUpdateStatus.hsjlMappingCountFail.incrementAndGet();
                            noys.setStatus(9);
                            hotelService.updateHotelInfoHsjl(noys);
                            continue;
                        }
                        List<JdJdb> jdList = HotelUpdateStatus.jdMap.get(mappings.get(0).getLocalId());
                        if (jdList == null || jdList.size() == 0) {
                            HotelUpdateStatus.hsjlMappingCountFail.incrementAndGet();
                            noys.setStatus(9);
                            hotelService.updateHotelInfoHsjl(noys);
                            continue;
                        }
                        String namePlat = MatchRate.getOk(noys.getName());
                        String addressPlat = MatchRate.getOk(noys.getAddressLine());
                        String telPlat = MatchRate.getOk(noys.getPhone());
                        BigDecimal lonGDPlat = noys.getLonGd();
                        BigDecimal latGDPlat = noys.getLatGd();
                        BigDecimal lonBDPlat = noys.getLonBd();
                        BigDecimal latBDPlat = noys.getLatBd();
                        JdJdbMapping jdJdbMapping = new JdJdbMapping();
                        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
                        for (int i = 0; i < jdList.size(); i++) {
                            JdJdb baseys = jdList.get(i);
                            String name = MatchRate.getOk(baseys.getJdmc());
                            String address = MatchRate.getOk(baseys.getJddz());
                            String tel = MatchRate.getOk(baseys.getJddh());
                            BigDecimal lonGD = baseys.getLonGaode();
                            BigDecimal latGD = baseys.getLatGaode();
                            BigDecimal lonBD = baseys.getLonBaidu();
                            BigDecimal latBD = baseys.getLatBaidu();

                            int matchRateAddr = MatchRate.getMatchRate(null, address, addressPlat) * 2;
                            int matchRateName = MatchRate.getMatchRate2(cityStr, name, namePlat) * 5;
                            String name0 = name.replace("酒店", "").replace("店", "");
                            String namePlat0 = namePlat.replace("酒店", "").replace("店", "");
                            if(name0.equals(namePlat0)){
                                matchRateName = 700;
                            }
                            int matchRate = matchRateAddr + matchRateName;

                            if (StringUtils.isNotEmpty(tel) && StringUtils.isNotEmpty(telPlat)) {
                                if (tel.contains(telPlat) || telPlat.contains(tel)) {
                                    matchRate += 200;
                                }
                            } else {
                                matchRate += 100;
                            }

                            int dist = 9999;
                            if (latGDPlat != null && lonGDPlat != null && lonGD != null && latGD != null) {
                                int dist2 = GetDistance.calculateDistance(lonGD.doubleValue(), latGD.doubleValue(),
                                        lonGDPlat.doubleValue(), latGDPlat.doubleValue());
                                if (dist2 < dist) {
                                    dist = dist2;
                                }
                            }
                            if (latBDPlat != null && lonBDPlat != null && lonBD != null && latBD != null) {
                                int dist3 = GetDistance.calculateDistance(lonBD.doubleValue(), latBD.doubleValue(),
                                        lonBDPlat.doubleValue(), latBDPlat.doubleValue());
                                if (dist3 < dist) {
                                    dist = dist3;
                                }
                            }
                            if (dist < 50) {
                                matchRate += 100;
                            }

                            if (matchRate > 799) {
                                JdJdbMapping jdJdbMapping2 = jdjdbmappingMap.get(baseys.getId());
                                if (jdJdbMapping2 != null) {
                                    if (Integer.valueOf(jdJdbMapping2.getReserve0()) < matchRate) {
                                        HotelInfoHsjl noys2 = new HotelInfoHsjl();
                                        noys2.setId(jdJdbMapping2.getPlatId());
                                        noys2.setStatus(1);
                                        hotelService.updateHotelInfoHsjl(noys2);
                                        jdJdbMapping2.setReserve0(matchRate + "");
                                        jdJdbMapping2.setPlatId(noys.getId());
                                        jdjdbmappingMap.put(baseys.getId(), jdJdbMapping2);
                                        HotelUpdateStatus.hsjlMappingCountFail.incrementAndGet();
                                        break;
                                    }else {
                                        if(i == jdList.size() - 1) {
                                            HotelUpdateStatus.hsjlMappingCountFail.incrementAndGet();
                                            noys.setStatus(9);
                                            hotelService.updateHotelInfoHsjl(noys);
                                            break;
                                        }
                                        continue;
                                    }
                                }

                                jdJdbMapping.setPlatId(noys.getId());
                                jdJdbMapping.setLocalId(baseys.getId());
                                jdJdbMapping.setReserve0(matchRate + "");
                                jdJdbMapping.setJdName(baseys.getJdmc());
                                jdJdbMapping.setReserve3(noys.getName());
                                jdjdbmappingMap.put(baseys.getId(), jdJdbMapping);
                                HotelUpdateStatus.hsjlMappingCounted.incrementAndGet();
                                break;
                            }

                            if (i == jdList.size() - 1) {
                                HotelUpdateStatus.hsjlMappingCountFail.incrementAndGet();
                                noys.setStatus(9);
                                hotelService.updateHotelInfoHsjl(noys);
                            }
                        }
                    }
                }catch (Exception e) {
                    e.printStackTrace();
                }
                threadSize.decrementAndGet();
            }).start();
        }
        while (true) {
            if(threadSize.get() == 0) {
                Set<Map.Entry<String, JdJdbMapping>> entries = jdjdbmappingMap.entrySet();
                HotelInfoHsjl hotel = new HotelInfoHsjl();
                hotel.setStatus(8);
                for (Map.Entry<String, JdJdbMapping> entry : entries) {
                    JdJdbMapping value = entry.getValue();
                    log.info("jdjdbmapping localId {},interfacePlat {}", value.getLocalId(), value.getInterfacePlat());
                    if(null == jdJdbMappingService.selectJdJdbMappingByLocalIdAndInterfacePlat(value.getLocalId(), value.getInterfacePlat())) {
                        jdJdbMappingService.insertJdJdbMapping(value);
                    }
                    hotel.setId(value.getPlatId());
                    hotelService.updateHotelInfoHsjl(hotel);

                    ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                    zhJdJdbMapping.setLocalId(value.getLocalId());
                    zhJdJdbMapping.setInterfacePlat(value.getInterfacePlat());
                    zhJdJdbMapping.setPlatId(value.getPlatId());
                    zhJdJdbMapping.setJdName(value.getJdName());
                    zhJdJdbMapping.setStatus(0);
                    zhJdJdbMapping.setSaveDate(new Date());
                    zhJdJdbMapping.setIsGnGj(1);
                    logger.info("红色加力zh_jd_jdb_mapping插入前数据 {}", JSONObject.toJSONString(zhJdJdbMapping));
                    //插入前校验重复
                    log.info("zhjdjdbmapping localId {},interfacePlat {}", zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat());
                    if(null == zhJdJdbMappingService.findByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(),zhJdJdbMapping.getInterfacePlat())) {
                        zhJdJdbMappingService.insert(zhJdJdbMapping);;
                    }
                }
                HotelUpdateStatus.expStatusHSJL = 12;
                HotelUpdateStatus.status = 0;
                break;
            }
            Thread.sleep(10000);
            System.out.println("红色加力 10秒巡逻一次，检测完成");
        }
        return AjaxResult.success();
    }

    private void parseExcel4Elong() {
        try {
            ExcelReaderBuilder read = EasyExcel.read(PropertiesUtil.getProp("hsjl.elongidxlsxpath"));
            ExcelReader build = read.build();
            List<ReadSheet> readSheets = build.excelExecutor().sheetList();
            Date now = new Date();
            for (ReadSheet readSheet : readSheets) {
                log.info("红色加力与艺龙映射关系处理, sheetNo={}", readSheet.getSheetName());
    //            CustomExcelListener listener = new CustomExcelListener();
                List<HotelInfoHsjlElongId> hotelInfoHsjlElongIdList = new ArrayList<>();
                ReadSheet re = EasyExcel.readSheet(readSheet.getSheetNo())
                    .headRowNumber(1).head(HsjlElongExcelData.class).registerReadListener(new AnalysisEventListener<HsjlElongExcelData>() {
                        @Override
                        public void invoke(HsjlElongExcelData hsjlElongExcelData, AnalysisContext analysisContext) {
                            log.info("红色加力与艺龙绑定关系: data={}", JSONObject.toJSONString(hsjlElongExcelData));
                            HotelInfoHsjlElongId hotelInfoHsjlElongId = new HotelInfoHsjlElongId();
                            hotelInfoHsjlElongId.setId(hsjlElongExcelData.getHsjlId());
                            hotelInfoHsjlElongId.setElongId(hsjlElongExcelData.getElongId().replaceAll("'", ""));
                            hotelInfoHsjlElongId.setStatus(1);
                            hotelInfoHsjlElongId.setCreateTime(now);
                            hotelInfoHsjlElongId.setUpdateTime(now);
                            hotelInfoHsjlElongIdList.add(hotelInfoHsjlElongId);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                        }
                    }).build();

                build.read(re);

                List<List<HotelInfoHsjlElongId>> lists = ListUtil.subList(hotelInfoHsjlElongIdList, 1000);
                for (List<HotelInfoHsjlElongId> list : lists) {
                    List<String> idList = list.stream().map(HotelInfoHsjlElongId::getId).collect(Collectors.toList());
                    List<String> containIdList = hotelInfoHsjlElongIdService.selectIdIn(idList);
                    if (CollectionUtils.isEmpty(containIdList)) {
                        hotelInfoHsjlElongIdService.insertBatch(list);
                    } else {
                        List<HotelInfoHsjlElongId> collect = list.stream().filter(e -> !containIdList.contains(e.getId())).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(collect)) {
                            hotelInfoHsjlElongIdService.insertBatch(collect);
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.error("红色加力读取与艺龙映射关系失败: ", e);
        }

    }

    public void insertCorp(String jdidHsjl, String corpId) {
        JdJdbHsjlCorp jdJdbHsjlCorp = new JdJdbHsjlCorp();
        jdJdbHsjlCorp.setJdidHsjl(jdidHsjl);
        jdJdbHsjlCorp.setCorpId(corpId);
        List<JdJdbHsjlCorp> list = jdJdbHsjlCorpService.selectJdJdbHsjlCorpList(jdJdbHsjlCorp);
        if(list.size() == 0) {
            jdJdbHsjlCorpService.insertJdJdbHsjlCorp(jdJdbHsjlCorp);
        }
    }

    /**
     * 单独处理红色加力城市数据
     */
    @PostMapping("/processCityData")
    public AjaxResult processCityData(@RequestBody HsjlCityListResponse citiesResponse,
                                      @RequestParam(defaultValue = "1") String idempotent) {
        try {
            int count = this.hotelHsjlSupplierService.processCityData(citiesResponse, idempotent);
            return AjaxResult.success("处理成功，共处理城市数据: " + count + "条");
        } catch (Exception e) {
            log.error("处理红色加力城市数据异常", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 单独获取红色加力城市数据
     */
    @GetMapping("/getCityData")
    public AjaxResult getCityData() {
        try {
            String cityListJson = HsjlApi.queryCityList();
            if (StringUtils.isNotEmpty(cityListJson)) {
                return AjaxResult.success("获取红色加力城市数据成功", JSONObject.parseObject(cityListJson));
            } else {
                return AjaxResult.error("获取红色加力城市数据失败，返回为空");
            }
        } catch (Exception e) {
            log.error("获取红色加力城市数据异常", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 同步红色加力城市数据（异步执行）
     */
    @PostMapping("/syncCityData")
    public AjaxResult syncCityData(@RequestParam(defaultValue = "1") String idempotent) {
        log.info("开始异步同步红色加力城市数据");

        // 使用线程池异步执行（需要注入ThreadPoolTaskExecutor）
        threadPoolTaskExecutor.execute(() -> {
            try {
                log.info("红色加力城市数据同步任务开始执行");
                long startTime = System.currentTimeMillis();

                String result = this.hotelHsjlSupplierService.syncCityData(idempotent);
                log.info("红色加力城市数据同步结果: {}", result);

                long endTime = System.currentTimeMillis();
                long totalTime = (endTime - startTime) / 1000;
                log.info("红色加力城市数据同步完成！耗时：{}秒", totalTime);

            } catch (Exception e) {
                log.error("红色加力城市数据同步异常", e);
            }
        });

        return AjaxResult.success("红色加力城市数据同步任务已提交，正在后台执行");
    }

    /**
     * 同步红色加力城市数据（同步执行，用于测试）
     */
    @PostMapping("/syncCityDataSync")
    public AjaxResult syncCityDataSync(@RequestParam(defaultValue = "1") String idempotent) {
        try {
            String result = this.hotelHsjlSupplierService.syncCityData(idempotent);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("同步红色加力城市数据异常", e);
            return AjaxResult.error("同步失败: " + e.getMessage());
        }
    }

}
