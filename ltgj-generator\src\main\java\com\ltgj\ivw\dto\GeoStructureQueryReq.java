package com.ltgj.ivw.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地理位置层次结构查询请求DTO
 * 用于根据任意层级的ID获取其完整的四级联动层次结构
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GeoStructureQueryReq {
    
    /**
     * 要查询的ID（可以是任意层级：country, province, city, district）
     * 必填参数
     */
    private String id;
    
    /**
     * ID对应的层级类型：country, province, city, district
     * 可选参数，如果不提供将自动识别
     */
    private String idType;
    
    /**
     * 是否包含完整的子级数据
     * true: 返回当前节点及其所有下级数据的完整层次结构
     * false: 只返回当前节点及其直接下级
     */
    private Boolean includeFullChildren = true;
    
    /**
     * 是否包含上级数据
     * true: 返回当前节点的所有上级节点信息
     * false: 只返回当前节点
     */
    private Boolean includeParents = true;
    
    /**
     * 地区类型（针对区县级别查询）
     * 1-商圈 2-行政区 3-标志物
     * 仅在查询区县下级数据时有效
     */
    private String geoType;
    
    @Override
    public String toString() {
        return "GeoStructureQueryReq{" +
                "id='" + id + '\'' +
                ", idType='" + idType + '\'' +
                ", includeFullChildren=" + includeFullChildren +
                ", includeParents=" + includeParents +
                ", geoType='" + geoType + '\'' +
                '}';
    }
} 