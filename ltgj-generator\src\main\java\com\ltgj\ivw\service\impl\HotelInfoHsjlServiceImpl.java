package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnBaseCommonMapper;
import com.ltgj.ivw.mapper.HotelInfoHsjlMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.ConstantList;
import com.ltgj.ivw.utils.ListUtil;
import com.ltgj.ivw.utils.MyTools;
import com.ltgj.ivw.utils.TXTUtil;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.HsjlApi;
import com.ltgj.ivw.utils.hotelApi.HsjlxyApi;
import com.ltgj.limiter.LtgjRateLimiter;
import com.ltgj.supplier.common.domain.FacilitiesInfo;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnHsjl;
import com.ltgj.supplier.common.gn.enums.HotelGnCustomerTypeEnum;
import com.ltgj.supplier.common.gn.enums.HotelGnFacilityDetail;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.common.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RRateLimiter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;

/**
 * 红色加力酒店数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Slf4j
@Service("hotelInfoHsjlService")
public class HotelInfoHsjlServiceImpl extends BaseHotelServiceImpl<HotelGnBase, HotelGnHsjl> implements IHotelInfoHsjlService, IHotelInfoService,IHotelGnBaseService {
    @Autowired
    private HotelInfoHsjlMapper hotelInfoHsjlMapper;
    @Autowired
    private IHotelCityService hotelCityService;
    @Autowired
    private HotelGnBaseService hotelGnBaseService;
    @Autowired
    private IJdJdbHsjlCorpService jdJdbHsjlCorpService;
    @Autowired
    private IHotelUpdateRecodeService hotelUpdateRecodeService;
    @Autowired
    private HotelGnBaseCommonMapper<HotelGnHsjl> mapper;

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private LtgjRateLimiter limiter;

    @Value("${hsjl.clientid}")
    private String partnerCode;
    @Value("${hsjl.licensekey}")
    private String secureKey;

    public static String initData ="initData_%s";
    private ExecutorService hsjlThreadPool = newExecutorService(3, 5);

    private static int maxCountDetailHsjl = 10;

    @Override
    public HotelInfo selectHotelInfoById(String id) {
        return hotelInfoHsjlMapper.selectHotelInfoHsjlById(id);
    }

    @Override
    public int updateHotelInfo(HotelInfo hotelInfo) {
        return hotelInfoHsjlMapper.updateHotelInfoHsjl((HotelInfoHsjl) hotelInfo);
    }

    /**
     * 查询红色加力酒店数据
     *
     * @param id 红色加力酒店数据主键
     * @return 红色加力酒店数据
     */
    @Override
    public HotelInfoHsjl selectHotelInfoHsjlById(String id) {
        return hotelInfoHsjlMapper.selectHotelInfoHsjlById(id);
    }

    /**
     * 查询红色加力酒店数据列表
     *
     * @param hotelInfoHsjl 红色加力酒店数据
     * @return 红色加力酒店数据
     */
    @Override
    public List<HotelInfoHsjl> selectHotelInfoHsjlList(HotelInfoHsjl hotelInfoHsjl) {
        return hotelInfoHsjlMapper.selectHotelInfoHsjlList(hotelInfoHsjl);
    }

    @Override
    public List<HotelInfoHsjl> selectHotelInfoHsjlList2(HotelInfoHsjl hotelInfoHsjl) {
        return hotelInfoHsjlMapper.selectHotelInfoHsjlList2(hotelInfoHsjl);
    }

    /**
     * 新增红色加力酒店数据
     *
     * @param hotelInfoHsjl 红色加力酒店数据
     * @return 结果
     */
    @Override
    public int insertHotelInfoHsjl(HotelInfoHsjl hotelInfoHsjl) {
        hotelInfoHsjl.setCreateTime(DateUtils.getNowDate());
        return hotelInfoHsjlMapper.insertHotelInfoHsjl(hotelInfoHsjl);
    }

    /**
     * 修改红色加力酒店数据
     *
     * @param hotelInfoHsjl 红色加力酒店数据
     * @return 结果
     */
    @Override
    public int updateHotelInfoHsjl(HotelInfoHsjl hotelInfoHsjl) {
        hotelInfoHsjl.setUpdateTime(DateUtils.getNowDate());
        return hotelInfoHsjlMapper.updateHotelInfoHsjl(hotelInfoHsjl);
    }

    /**
     * 批量删除红色加力酒店数据
     *
     * @param ids 需要删除的红色加力酒店数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoHsjlByIds(String[] ids) {
        return hotelInfoHsjlMapper.deleteHotelInfoHsjlByIds(ids);
    }

    /**
     * 删除红色加力酒店数据信息
     *
     * @param id 红色加力酒店数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoHsjlById(String id) {
        return hotelInfoHsjlMapper.deleteHotelInfoHsjlById(id);
    }

    @Override
    public void updateSuccess(List<String> idList) {
        hotelInfoHsjlMapper.updateSuccess(idList);
    }

    @Override
    public List<HotelInfoHsjl> selectHotelInfoNotMapping(List<String> idList, int flag) {
        return hotelInfoHsjlMapper.selectHotelInfoNotMapping(idList, flag);
    }

    @Override
    public List<String> selectNotMappingIdList() {
        return hotelInfoHsjlMapper.selectNotMappingIdList();
    }

    @Override
    public List<HotelInfoHsjl> selectListByIdList(List<String> idList) {
        return hotelInfoHsjlMapper.selectListByIdList(idList);
    }

    @Override
    public int insertOrUpdateHotelInfoHsjl(HotelInfoHsjl hotelInfoHsjl) {
        hotelInfoHsjl.setCreateTime(DateUtils.getNowDate());
        return hotelInfoHsjlMapper.insertOrUpdateHotelInfoHsjl(hotelInfoHsjl);
    }

    public HotelGnHsjl buildHotelGnHsjl(PlatEnum platEnum, JSONObject jsonObject) {
        HotelGnHsjl hotelInfo = new HotelGnHsjl();
        hotelInfo.setId(jsonObject.getString("hotelId"));
        hotelInfo.setName(jsonObject.getString("hotelName"));
        hotelInfo.setNameEn(jsonObject.getString("hotelEngName"));
        hotelInfo.setTypeId(jsonObject.getInteger("hotelSubCategory"));
        hotelInfo.setOpenDate(jsonObject.getString("praciceDate"));
        hotelInfo.setDecorationDate(jsonObject.getString("fitmentDate"));
        hotelInfo.setPhone(jsonObject.getString("telephone"));
        hotelInfo.setStatus(1);
        HotelCity city = hotelCityService.getByCityIdAndPlatIdForCache(PlatEnum.PLAT_HSJL, jsonObject.getString("city"));
        boolean isArea = false;
        if (city == null) {
            log.info("hotel_city中城市数据为空：{}", jsonObject.getString("city"));
            city = hotelCityService.getByAreaIdAndPlatIdForCache(PlatEnum.PLAT_HSJL, jsonObject.getString("city"));
            log.info("获取区县数据：{}, info:{}", jsonObject.getString("city"), JSONObject.toJSONString(city));
            if (city != null) {
                isArea = true;
            }
        }
        if (city != null) {
            hotelInfo.setCountryId(city.getCountryId());
            hotelInfo.setCountryName(city.getCountryName());
            hotelInfo.setProvinceId(city.getProvinceId());
            hotelInfo.setProvinceName(city.getProvinceName());
            hotelInfo.setCityId(city.getCityId());
            hotelInfo.setCityName(city.getCityName());
            if (isArea) {
                hotelInfo.setAreaId(city.getLocationId());
                hotelInfo.setAreaName(city.getLocationName());
            }
        } else {
            log.info("hotel_city中城市数据为空：{}", jsonObject.getString("city"));
            hotelInfo.setCityId(jsonObject.getString("city"));
            hotelInfo.setCityName(jsonObject.getString("cityName"));
        }
        if (!isArea) {
            hotelInfo.setAreaId(jsonObject.getString("distinct"));
            hotelInfo.setAreaName(jsonObject.getString("distinctName"));
        }
        hotelInfo.setBusinessDistrictId(jsonObject.getString("business"));
        hotelInfo.setBusinessDistrictName(jsonObject.getString("businessName"));
        hotelInfo.setAddress(jsonObject.getString("address"));
        hotelInfo.setLonBaidu(jsonObject.getBigDecimal("longitude"));
        hotelInfo.setLatBaidu(jsonObject.getBigDecimal("latitude"));

        hotelInfo.setGroupId(jsonObject.getString("parentHotelGroup"));
        hotelInfo.setGroupName(jsonObject.getString("parentHotelGroupName"));
        hotelInfo.setBrandId(jsonObject.getString("plateID"));
        hotelInfo.setBrandName(jsonObject.getString("plateName"));
        String hotelStar = jsonObject.getString("hotelStar");
        if (StringUtils.isNotBlank(hotelStar)) {
            if (hotelStar.equals("19") || hotelStar.equals("29")) {
                hotelInfo.setStar("5");
            } else if (hotelStar.equals("39") || hotelStar.equals("49")) {
                hotelInfo.setStar("4");
            } else if (hotelStar.equals("59") || hotelStar.equals("64")) {
                hotelInfo.setStar("3");
            } else if (hotelStar.equals("69") || hotelStar.equals("66")) {
                hotelInfo.setStar("2");
            } else if (hotelStar.equals("79")) {
                hotelInfo.setStar("1");
            } else {
                hotelInfo.setStar("0");
            }
        }
        hotelInfo.setImage(jsonObject.getString("appearancePicUrl"));
        JSONArray scoreArray = jsonObject.getJSONArray("comment");
        if (scoreArray != null && !scoreArray.isEmpty()) {
            hotelInfo.setScore(scoreArray.getJSONObject(0).getString("average_score"));
        }
        hotelInfo.setSynopsis(jsonObject.getString("hotelIntroduce"));


        PolicyInfo policyInfo = new PolicyInfo();

        PolicyInfo.CheckInOutPolicy checkInOutPolicy = new PolicyInfo.CheckInOutPolicy();
        checkInOutPolicy.setCheckIn(jsonObject.getString("checkInTime"));
        checkInOutPolicy.setCheckOut(jsonObject.getString("checkOutTime"));
        policyInfo.setCheckInOutPolicy(checkInOutPolicy);

        // 宾客政策
        String applicableGuest = jsonObject.getString("applicableGuest");
        try {
            PolicyInfo.CustomerPolicy customerPolicy = new PolicyInfo.CustomerPolicy();
            if ("3".equals(applicableGuest)) {
                customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_GUEST.getCode()));
                customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.MAINLAND_GUEST.getDescription());
            } else if ("4".equals(applicableGuest)) {
                customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getCode()));
                customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getDescription());
            } else if ("5".equals(applicableGuest)) {
                customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.GLOBAL_GUEST.getCode()));
                customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.GLOBAL_GUEST.getDescription());
            }
            policyInfo.setCustomerPolicy(customerPolicy);
        } catch (Exception e) {
            log.error("{}-处理 guestType 异常:{}, error:{}", platEnum.getName(), applicableGuest, e);
        }
        // 宠物政策
        try {
            PolicyInfo.PetPolicy petPolicy = new PolicyInfo.PetPolicy();
            petPolicy.setIsAllows(jsonObject.getBoolean("allowPet"));
            policyInfo.setPetPolicy(petPolicy);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("{}-处理 petPolicy 异常:{}, error:{}", platEnum.getName(), jsonObject.getString("allowPet"), e);
        }

        // 儿童政策
        JSONObject hotelStructuredPolicies = jsonObject.getJSONObject("hotelStructuredPolicies");
        if (hotelStructuredPolicies != null) {
            JSONObject childPolicyJSON = hotelStructuredPolicies.getJSONObject("childPolicy");
            if (childPolicyJSON != null) {
                String allowChildrenToStay = childPolicyJSON.getString("allowChildrenToStay");
                if ("1".equals(allowChildrenToStay)) {
                    PolicyInfo.ChildPolicy childPolicy = new PolicyInfo.ChildPolicy();
                    childPolicy.setContent(childPolicyJSON.getString("limitOfChildAge"));
                    policyInfo.setChildPolicy(childPolicy);
                }
            }
        }
        hotelInfo.setPolicyInfo(JSON.toJSONString(policyInfo));

        // 设施
        JSONArray facilities = jsonObject.getJSONArray("hotelFacilityNew");
        if (facilities != null && !facilities.isEmpty()) {
            FacilitiesInfo facilitiesInfo = new FacilitiesInfo();
            for (int i = 0; i < facilities.size(); i++) {
                JSONObject facility = facilities.getJSONObject(i);
                if ("1".equals(facility.getString("categoryType"))) {// 酒店设施
                    for (HotelGnFacilityDetail f : HotelGnFacilityDetail.values()) {
                        // 去掉，房、室、场，厅，精准匹配
                        String name = f.getName().replaceAll("房", "").replaceAll("室", "").replaceAll("场", "").replaceAll("厅", "");
                        if (name.contains(facility.getString("name"))) {
                            if (f == HotelGnFacilityDetail.PARKING) {
                                FacilitiesInfo.Parking parking = new FacilitiesInfo.Parking();
                                parking.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setParking(parking);
                                break;
                            }
                            if (f == HotelGnFacilityDetail.CHARGING) {
                                FacilitiesInfo.Charging charging = new FacilitiesInfo.Charging();
                                charging.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setCharging(charging);
                                break;
                            }
                            if (f == HotelGnFacilityDetail.LIFT) {
                                FacilitiesInfo.Lift lift = new FacilitiesInfo.Lift();
                                lift.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setLift(lift);
                                break;
                            }
                            if (f == HotelGnFacilityDetail.BAGGAGE) {
                                FacilitiesInfo.Baggage baggage = new FacilitiesInfo.Baggage();
                                baggage.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setBaggage(baggage);
                                break;
                            }
                            if (f == HotelGnFacilityDetail.RESTAURANT) {
                                FacilitiesInfo.Restaurant restaurant = new FacilitiesInfo.Restaurant();
                                restaurant.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setRestaurant(restaurant);
                                break;
                            }
                            if (f == HotelGnFacilityDetail.MEETING_ROOM) {
                                FacilitiesInfo.MeetingRoom meetingRoom = new FacilitiesInfo.MeetingRoom();
                                meetingRoom.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setMeetingRoom(meetingRoom);
                                break;
                            }
                            if (f == HotelGnFacilityDetail.PUBLIC_WIFI) {
                                FacilitiesInfo.PublicWifi publicWifi = new FacilitiesInfo.PublicWifi();
                                publicWifi.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setPublicWifi(publicWifi);
                                break;
                            }
                            if (f == HotelGnFacilityDetail.GYM) {
                                FacilitiesInfo.Gym gym = new FacilitiesInfo.Gym();
                                gym.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setGym(gym);
                                break;
                            }
                            if (f == HotelGnFacilityDetail.LAUNDRY) {
                                FacilitiesInfo.Laundry laundry = new FacilitiesInfo.Laundry();
                                laundry.setIsHave(Boolean.TRUE);
                                facilitiesInfo.setLaundry(laundry);
                                break;
                            }

                        }
                    }
                }
            }
            hotelInfo.setFacilitiesInfo(JSON.toJSONString(facilitiesInfo));
        } else {
            hotelInfo.setFacilitiesInfo(JSON.toJSONString(new FacilitiesInfo()));
        }
        hotelInfo.setReserve0(jsonObject.toJSONString());
        return hotelInfo;
    }

    public void resetStatus() {
        resetStatus(PlatEnum.PLAT_HSJL);
    }
    public void resetStatus(PlatEnum platEnum) {
        redisTemplate.delete(String.format(initData, platEnum.getValue()));
    }
    public Map<String, Object> getStatus(PlatEnum platEnum) {
        Map queryMap = redisTemplate.opsForHash().entries(String.format(initData, platEnum.getValue()));
        int status = getMapIntKey(queryMap,  "expStatus");
        Map<String, Object> map = new HashMap<>();
        String stepName = "";
        switch (status) {
            case 0:
                stepName = "初始状态";
                break;
            case 1:
                stepName = "插入酒店IDs中";
                break;
            case 2:
                stepName = "插入酒店IDs完成";
                break;
            case 3:
                stepName = "更新酒店信息中";
                break;
            case 4:
                stepName = "更新酒店信息完成";
                break;
            case 11:
                stepName = "酒店映射中";
                break;
            case 12:
                stepName = "酒店映射完成";
                break;
        }
        map.put("stepName", stepName);
        map.put("add", getMapIntKey(queryMap,  "idsCountAdd"));
        map.put("updateJS", getMapIntKey(queryMap,  "detailCountUpdate"));
        map.put("deleteJS", 0);
        map.put("mapping", 0);
        map.put("mappingOK", 0);
        map.put("mappingNO", 0);
        return map;
    }

    public void updateHsjlAll(String corpId, String userName) throws Exception {
        this.updateHsjlAll(corpId, userName, PlatEnum.PLAT_HSJL, partnerCode, secureKey, hsjlThreadPool);
    }
    public void updateHsjlAll(String corpId, String userName, PlatEnum platEnum, String partnerCode, String secureKey, ExecutorService threadPool) throws Exception {
        if ((StringUtils.isNotEmpty(partnerCode) && StringUtils.isEmpty(secureKey)) ||
                (StringUtils.isEmpty(partnerCode) && StringUtils.isNotEmpty(secureKey))) {
            throw new Exception("CODE和密钥参数错误！");
        }
        String cacheKey = String.format(initData, platEnum.getValue());
        if (!"0".equals(getHashKey(cacheKey, "status"))) {
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        setHashKey(cacheKey, "status", 1);
        HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
        hotelUpdateRecode.setId(platEnum.getValue() + MyTools.getTimesNo());
        hotelUpdateRecode.setYl2(platEnum.getValue());
        hotelUpdateRecode.setTimeStart(new Date());
        hotelUpdateRecode.setUpdateBy(userName);
        String expStatus = getHashKey(cacheKey, "expStatus");
        long start = System.currentTimeMillis();
        log.info("{}当前状态：expStatus={}", platEnum.getName(), expStatus);
        if ("0".equals(expStatus)) {
            try {
                //通过调用红色加力酒店id接口，与本地红色加力酒店信息对比，进行本地数据的新增和删除
                insertHotelHSJL(hotelUpdateRecode, cacheKey, 1, 2, partnerCode, secureKey, userName, platEnum, threadPool);
            } catch (Exception e) {
                log.info("插入酒店列表失败: ", e);
            }
        }
        expStatus = getHashKey(cacheKey, "expStatus");
        if ("2".equals(expStatus)) {
            try {
                updateHotelHSJL(hotelUpdateRecode, cacheKey, 3, 4, partnerCode, secureKey, userName, platEnum, threadPool);
            } catch (Exception e) {
                log.info("更新酒店列表失败: ", e);
            }
        }
        long end = System.currentTimeMillis();
        log.info("{}初始化数据完成，耗时:{}s", platEnum.getName(), (end - start) / 1000L);
    }


    public void insertHotelHSJL(HotelUpdateRecode recode, String cacheKey, int start, int end, String partnerCodeParam, String secureKeyParam
            , String userName, PlatEnum platEnum, ExecutorService threadPool) {
        try {
            redisTemplate.opsForHash().put(cacheKey, "expStatus", start);
            //查询红色加力城市信息
            List<HotelCity> hcs = new ArrayList<>();
            String cityInfoText = HsjlxyApi.queryCityList(Boolean.FALSE, partnerCodeParam, secureKeyParam);
            log.info("城市列表返回报文信息：{}", cityInfoText);
            if(StringUtils.isNotEmpty(cityInfoText)) {
                JSONObject bussinessResponse = JSONObject.parseObject(cityInfoText).getJSONObject("bussinessResponse");
                JSONArray provinces = bussinessResponse.getJSONArray("provinces");
                for (int i = 0; i < provinces.size(); i++) {
                    JSONObject province = provinces.getJSONObject(i);
                    log.info("省份：{}", province);
                    JSONArray cities = province.getJSONArray("citys");
                    for (int j = 0; j < cities.size(); j++){
                        JSONObject city = cities.getJSONObject(j);
                        String parentCityCode = city.getString("parentCityCode");
                        log.info("parentCityCode：{}", parentCityCode);
//                    if(StringUtils.isNotEmpty(parentCityCode)){
//                        continue;
//                    }
//                    String cityId = city.getString("cityId");
                        String cityCode = city.getString("cityCode");
//                    log.info("cityId：{},cityCode:{}", cityId,cityCode);
                        HotelCity hotelCity = new HotelCity();
                        hotelCity.setCityId(cityCode);
                        hotelCity.setCityName(city.getString("cityName"));
                        hcs.add(hotelCity);

                    }
                }
            }

            if (CollectionUtils.isEmpty(hcs)) {
                redisTemplate.opsForHash().put(cacheKey, "expStatus", 0);
                log.info("{} 酒店城市数据查询为null,任务结束", platEnum.getName());
                return;
            }
            log.info("{}获取城市信息数量 cityList:{}", platEnum.getName() ,hcs.size());
            //List<CompletableFuture<String>> allTasks = new ArrayList<>();
            for (HotelCity hotelCity : hcs) {
                //CompletableFuture<String> task = CompletableFuture.supplyAsync(() ->{
                    processCity(hotelCity, partnerCodeParam, secureKeyParam, platEnum, cacheKey, userName);
                    //return null;
                //}, threadPool);
                //allTasks.add(task);
            }
            try {
                //CompletableFuture.allOf(allTasks.toArray(new CompletableFuture[0])).join();// 等待所有城市处理完成
            } catch (Exception e) {
                log.error("{}等待线程完成时发生中断异常:{}", platEnum.getName(), e);
            }
            redisTemplate.opsForHash().put(cacheKey, "expStatus", end);
            recode.setCountDel(0L);
            recode.setCountAdd(Long.valueOf(getHashKey(cacheKey, "idsCountAdd")));
            hotelUpdateRecodeService.insertHotelUpdateRecode(recode);
        } catch (Exception e) {
            log.error("更新{}酒店详情异常 errorMessage:{}", platEnum.getName(), e);
        }
    }

    /**
     * 处理单个城市的酒店数据拉取与保存
     */
    private void processCity(HotelCity hotelCity, String partnerCodeParam, String secureKeyParam, PlatEnum platEnum, String cacheKey, String userName) {
        try {
            String cityId = StringUtils.isEmpty(hotelCity.getLocationId()) ? hotelCity.getCityId() : hotelCity.getLocationId();
            String infoText = (String) fetchDataFunction(platEnum.getValue() + "_cityHotelIdList", 1, 1, (result) -> HsjlApi.queryHotelIdList(cityId, 1, partnerCodeParam, secureKeyParam));
            log.info("{}根据城市查询酒店列表,ID：{} 返回报文infoText:{}", platEnum.getName(), cityId, infoText);

            JSONObject obj = JSONObject.parseObject(infoText);
            if (obj.getString("returnCode").equals("009")) {
                log.error("调用{}供应商失败,cityId:{}", platEnum.getName(), cityId);
                try {
                    Thread.sleep(5000);
                    log.info("继续执行");
                    infoText = (String) fetchDataFunction(platEnum.getValue() + "_cityHotelIdList", 1, 1, (result) -> HsjlApi.queryHotelIdList(cityId, 1, partnerCodeParam, secureKeyParam));
                    log.info("根据城市查询酒店列表第二次,ID：{} 返回报文infoText:{}", cityId, infoText);
                    obj = JSONObject.parseObject(infoText);
                } catch (InterruptedException e) {
                    log.error("重复执行失败，直接抛出异常 cityId:{} errorMessage:{}", cityId, e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            }

            JSONObject info = obj.getJSONObject("bussinessResponse");
            int pageCount = 0;
            JSONArray ids = null;
            try {
                if (info != null) {
                    pageCount = info.getInteger("totalPage");
                    ids = info.getJSONArray("hotelIds");
                    if (pageCount <= 0) {
                        log.error("页数为0，cityId:{}", cityId);
                        Thread.sleep(1000);
                        return;
                    }
                    log.info("{} 供应商城市：{} 总页数：{}", platEnum.getName(), cityId, pageCount);
                } else {
                    log.error("info 为空，cityId: {}", cityId);
                    Thread.sleep(1000);
                    return;
                }
            } catch (Exception e) {
                log.error("休眠异常 errorMessage:{}", e.getMessage(), e);
            }

            List<List<HotelGnBase>> saveList = hotelGnBaseService.addOrUpdateBatch(ids.toList(String.class), userName, platEnum);
            if (saveList != null && saveList.size() > 0) {
                getAndSetField(cacheKey, "idsCountAdd", saveList.get(0).size());
            }

            // 处理第二页及后面数据
            for (int k = 2; k <= pageCount; k++) {
                try {
                    int finalK = k;
                    infoText = (String) fetchDataFunction(platEnum.getValue() + "_cityHotelIdList", 1, 1, (result) -> HsjlApi.queryHotelIdList(cityId, finalK, partnerCodeParam, secureKeyParam));
                    info = JSONObject.parseObject(infoText).getJSONObject("bussinessResponse");
                    if (info == null) {
                        continue;
                    }
                    ids = info.getJSONArray("hotelIds");
                    saveList = hotelGnBaseService.addOrUpdateBatch(ids.toList(String.class), userName, platEnum);
                    if (saveList != null && saveList.size() > 0) {
                        getAndSetField(cacheKey, "idsCountAdd", saveList.get(0).size());
                    }
                    Thread.sleep(5000);
                } catch (Exception e) {
                    log.error("插入{},idList进异常了{}, errorMessage:{}", platEnum.getName(), infoText, e);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("{}获取城市酒店id异常, city:{}, error:{}", platEnum.getName(), hotelCity.getCityId(), e);
        }
    }
    private synchronized void getAndSetField(String cacheKey, String hashKey, int newCount ) {
        String count = getHashKey(cacheKey, hashKey);
        int add = (StringUtils.isBlank(count) ? 0 : Integer.parseInt(count));
        setHashKey(cacheKey, hashKey, add +  newCount);
    }
    private String getHashKey(String cacheKey, String hashKey) {
        Object obj = redisTemplate.opsForHash().get(cacheKey, hashKey);
        return obj == null ? "0" : String.valueOf(obj);
    }
    private String getMapKey(Map map, String key) {
        if (map == null || map.isEmpty()) {
            return "0";
        }
        return map.get(key) == null ? "0" : String.valueOf(map.get(key));
    }
    private int getMapIntKey(Map map, String key) {
        return Integer.parseInt(getMapKey(map, key));
    }

    private void setHashKey(String cacheKey, String hashKey, Object value) {
        redisTemplate.opsForHash().put(cacheKey, hashKey, value);
    }

    public Object fetchDataFunction(String key, long second, long num, Function<Integer, Object> function) {
        int tryNum = 1;
        RRateLimiter rRateLimiter = this.limiter.getLimiter(key, second, num);
        boolean tryAcquire = rRateLimiter.tryAcquire(1);
        while (!tryAcquire) {
            log.info("当前供应商渠道【{}】限流.......睡眠500ms", key);
            try {
                Thread.sleep(500);
            } catch (InterruptedException ex) {
                log.info("睡眠异常。。。。。。。。。。");
            }
            tryNum++;
            tryAcquire = rRateLimiter.tryAcquire(1);
        }
        return function.apply(tryNum);
    }

    @Deprecated
    //插入红色加力酒店数据
    private void insertHotelHSJL_old(HotelUpdateRecode recode, int start, int end, String partnerCode, String secureKey, String corpId, String userName) {
        //流程标记赋值
        HotelUpdateStatus.expStatusHSJL = start;
        //查询红色加力城市信息
        HotelCity hc = new HotelCity();
        hc.setReserve1(PlatEnum.PLAT_HSJL.getValue());
        List<HotelCity> hcs = hotelCityService.selectHotelCityList(hc);
        if (CollectionUtils.isEmpty(hcs)) {
            //流程标记赋值
            HotelUpdateStatus.expStatusHSJL = 0;
            log.info("红色加力酒店城市数据查询为null,任务结束");
            return;
        }

        //遍历处理
        for (HotelCity hotelCity : hcs) {
            //红色根据城市查询酒店列表
            String cityId = StringUtils.isEmpty(hotelCity.getLocationId()) ? hotelCity.getCityId() : hotelCity.getLocationId();
            String infoText = HsjlApi.queryHotelIdList(cityId, 1, partnerCode, secureKey);
            log.info("红色根据城市查询酒店列表,ID：{} 返回报文infoText:{}", cityId, infoText);

            //查询失败二次查询
            JSONObject obj = JSONObject.parseObject(infoText);
            if (obj.getString("returnCode").equals("009")) {
                log.error("调用红色加力供应商失败");
                try {
                    Thread.sleep(5000);
                    log.info("继续执行");
                    infoText = HsjlxyApi.queryHotelIdList(cityId, 1, partnerCode, secureKey);
                    log.info("根据城市查询酒店列表第二次,ID：{} 返回报文infoText:{}", cityId, infoText);
                    obj = JSONObject.parseObject(infoText);
                } catch (InterruptedException e) {
                    log.error("重复执行失败，直接抛出异常 cityId:{} errorMessage:{}", cityId, e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            }

            //红色加力接口结果处理（没有数据直接处理下个城市）
            JSONObject info = obj.getJSONObject("bussinessResponse");
            int pageCount = 0;
            JSONArray ids = null;
            try {
                if (info != null) {
                    pageCount = info.getInteger("totalPage");
                    ids = info.getJSONArray("hotelIds");
                    if (pageCount <= 0) {
                        log.error("页数为0，cityId:{}", cityId);
                        Thread.sleep(1000);
                        continue;
                    }
                } else {
                    log.error("info 为空，cityId: {}", cityId);
                    Thread.sleep(1000);
                    continue; // 或者抛出异常
                }
            } catch (Exception e) {
                log.error("休眠异常 errorMessage:{}", e.getMessage(), e);
            }

            List<List<HotelGnBase>> saveList = hotelGnBaseService.addOrUpdateBatch(ids.toList(String.class), userName, PlatEnum.PLAT_HSJL);
            if (saveList != null && saveList.size() > 0) {
                HotelUpdateStatus.hsjlIdsCountAdd = HotelUpdateStatus.hsjlIdsCountAdd + saveList.get(0).size();
            }
            //处理第二页及后面数据
            for (int k = 2; k <= pageCount; k++) {
                try {
                    infoText = HsjlApi.queryHotelIdList(cityId, k, partnerCode, secureKey);
                    info = JSONObject.parseObject(infoText).getJSONObject("bussinessResponse");
                    if (info == null) {
                        continue;
                    }
                    ids = info.getJSONArray("hotelIds");
                    saveList = hotelGnBaseService.addOrUpdateBatch(ids.toList(String.class), userName, PlatEnum.PLAT_HSJL);
                    if (saveList != null && saveList.size() > 0) {
                        HotelUpdateStatus.hsjlIdsCountAdd = HotelUpdateStatus.hsjlIdsCountAdd + saveList.get(0).size();
                    }
                    Thread.sleep(5000);
                } catch (Exception e) {
                    log.error("插入HSJL进异常了 errorMessage:{}", e.getMessage(), e);
                    continue;
                }
            }
        }

        HotelUpdateStatus.expStatusHSJL = end;
        recode.setCountDel(HotelUpdateStatus.hsjlDeleteJS);
        recode.setCountAdd(HotelUpdateStatus.hsjlIdsCountAdd);
        hotelUpdateRecodeService.insertHotelUpdateRecode(recode);
    }

    public void updateHotelHSJL(HotelUpdateRecode recode, String cacheKey, int start, int end, String partnerCodeParam, String secureKeyParam
            , String userName, PlatEnum platEnum, ExecutorService threadPool) {
        try {
            //赋值流程标记
            setHashKey(cacheKey, "expStatus", start);
            //查询状态为初始化的数据 status = 0
            List<HotelGnBase> hotelInfoHsjlList = hotelGnBaseService.getAllIdList(platEnum);
            if (CollectionUtils.isEmpty(hotelInfoHsjlList)) {
                //流程标记赋值
                setHashKey(cacheKey, "expStatus", 0);
                log.info("红色加力酒店查询为null,任务结束");
                return;
            }
            setHashKey(cacheKey, "detailCount", hotelInfoHsjlList.size());
            List<Long> ids = new ArrayList<>();
            for (int i = 0;i<hotelInfoHsjlList.size();i++){
                HotelGnBase hsjl = hotelInfoHsjlList.get(i);
                ids.add(Long.valueOf(hsjl.getId()));
            }
            List<List<Long>> threadIdList = ListUtil.subList(ids, 200);
            //遍历id数据集合
            for(int j = 0; j< threadIdList.size(); j++){
                try {
                    List<List<Long>> queryIdList = ListUtil.subList(threadIdList.get(j), 10);
                    List<HotelGnBase> hotelGnHsjlList = new ArrayList<>();
                    List<CompletableFuture<List<HotelGnBase>>> allTasks = new ArrayList<>();
                    for (int m = 0; m < queryIdList.size() ; m++) {
                        int finalM = m;
                        CompletableFuture<List<HotelGnBase>> task = CompletableFuture.supplyAsync(() -> {
                            try{
                                return queryHotelInfo(queryIdList.get(finalM), platEnum, partnerCodeParam, secureKeyParam, userName, 0);
                            }catch (Exception e) {
                                log.error("获取{}详情进异常了 errorMessage:{}", platEnum.getName(), e);
                            }
                             return null;
                        }, threadPool);
                        allTasks.add(task);
                    }
                    for (int n = 0; n < allTasks.size(); n++) {
                        CompletableFuture<List<HotelGnBase>> future = allTasks.get(n);
                        List<HotelGnBase> hotelGnHsjlListTemp = future.get();
                        if (hotelGnHsjlListTemp != null) {
                            hotelGnHsjlList.addAll(hotelGnHsjlListTemp);
                        }
                    }
                    getAndSetField(cacheKey, "detailCountUpdate", hotelGnHsjlList.size());
                    hotelGnBaseService.addOrUpdate(platEnum, hotelGnHsjlList);
                } catch (Exception e) {
                    log.error("更新{}数据异常 errorMessage:{}", platEnum.getName(), e);
                }
            }

            setHashKey(cacheKey, "expStatus", end);
            setHashKey(cacheKey, "status", 0);

            recode.setCountFail(Long.valueOf(getHashKey(cacheKey, "detailCountUpdate")));
            recode.setCountAdd(Long.valueOf(getHashKey(cacheKey, "idsCountAdd")));
            recode.setTimeEnd(new Date());
            hotelUpdateRecodeService.updateHotelUpdateRecode(recode);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private List<HotelGnBase> queryHotelInfo(List<Long> queryIds, PlatEnum platEnum, String partnerCodeParam, String secureKeyParam, String userName, int num) {
        List<HotelGnBase> hotelList = new ArrayList<>();
        String infoText = null;
        try {
            if (num > 3) {
                return hotelList;
            }
            log.info("抓取{}酒店数据: ids={}", platEnum.getName(), queryIds);
            infoText = (String) fetchDataFunction(platEnum.getValue() + "_hotelDetail", 1, 3, (result) -> HsjlApi.queryHotelInfo(queryIds, partnerCodeParam, secureKeyParam));
            log.info("抓取{}酒店数据: result={}", platEnum.getName(), infoText);
            JSONObject info = JSONObject.parseObject(infoText);
            if(info.getString("returnCode").equals("000")) {
                JSONArray hotelInfos = info.getJSONObject("bussinessResponse").getJSONArray("hotelInfos");
                if (hotelInfos != null) {
                    for (int k = 0; k < hotelInfos.size(); k++) {
                        try {
                            HotelGnHsjl hotelGnHsjl = this.buildHotelGnHsjl(platEnum, hotelInfos.getJSONObject(k));
                            hotelGnHsjl.setUpdateBy(userName);
                            hotelGnHsjl.setUpdateTime(new Date());
                            hotelList.add(hotelGnHsjl);
                        } catch (Exception e) {
                            log.error("获取{}详情:{},异常: {}", platEnum.getName(), hotelInfos.getJSONObject(k), e);
                        }
                    }
                }
            } else if(info.getString("returnCode").equals("009")){
                Thread.sleep(1000);
                log.info("{}返回code {},infoText:{}", platEnum.getName(), info.getString("returnCode"),infoText);
                return queryHotelInfo(queryIds, platEnum, partnerCodeParam, secureKeyParam, userName, num++);
            } else {
                log.info("获取{}详情进else了 {}", platEnum.getName() ,infoText);
            }
        } catch (Exception e) {
            log.error("获取{}详情{},异常: {}", platEnum.getName(), infoText, e);
        }
        return hotelList;
    }

    @Deprecated
    //更新红色加力酒店详情
    private void updateHotelHSJL_old(HotelUpdateRecode recode, int start, int end, String partnerCode, String secureKey, String userName) {
        try {
            //赋值流程标记
            HotelUpdateStatus.expStatusHSJL = start;

            //查询状态为初始化的数据 status = 0
            List<HotelGnBase> hotelInfoHsjlList = hotelGnBaseService.getAllIdList(PlatEnum.PLAT_HSJL);
            if (CollectionUtils.isEmpty(hotelInfoHsjlList)) {
                //流程标记赋值
                HotelUpdateStatus.expStatusHSJL = 0;
                log.info("红色加力酒店查询为null,任务结束");
                return;
            }
            HotelUpdateStatus.hsjlDetailCount = hotelInfoHsjlList.size();

            //酒店id分组，每组10条
            List<List<Long>> idsList = new ArrayList<>();
            List<Long> ids = new ArrayList<>();
            HotelInfoHsjl hotelInfoHsjl1 = new HotelInfoHsjl();
            for (int i = 0;i<hotelInfoHsjlList.size();i++){
                HotelGnBase hsjl = hotelInfoHsjlList.get(i);
                if(ids.size() == maxCountDetailHsjl){
                    idsList.add(ids);
                    ids = new ArrayList<>();
                }
                ids.add(Long.valueOf(hsjl.getId()));
                if(i == hotelInfoHsjlList.size()-1){
                    idsList.add(ids);
                }
            }

            //遍历id数据集合
            for(int j = 0; j<idsList.size(); j++){
                List<Long> ids0 = idsList.get(j);
                try{
                    log.info("抓取红色加力酒店数据: ids={}", ids0);
                    String infoText = HsjlApi.queryHotelInfo(ids0, partnerCode, secureKey);
                    log.info("抓取红色加力酒店数据: result={}", infoText);
                    JSONObject info = JSONObject.parseObject(infoText);
                    if(info.getString("returnCode").equals("000")){
                        JSONArray hotelInfos = info.getJSONObject("bussinessResponse").getJSONArray("hotelInfos");
                        if(hotelInfos != null) {
                            List<HotelGnBase> hotelGnHsjlList = new ArrayList<>();
                            for(int k = 0; k < hotelInfos.size(); k++){
                                try {
                                    HotelGnHsjl hotelGnHsjl = this.buildHotelGnHsjl(PlatEnum.PLAT_HSJL, hotelInfos.getJSONObject(k));
                                    hotelGnHsjl.setUpdateBy(userName);
                                    hotelGnHsjl.setUpdateTime(new Date());
                                    hotelGnHsjlList.add(hotelGnHsjl);
                                    HotelUpdateStatus.hsjlDetailCountUpdate++;
                                }catch (Exception e) {
                                    log.error("获取红色加力详情进异常: {}", e);
                                    TXTUtil.writeTXT(new Date() + " 获取红色加力详情进异常了： " + infoText,
                                            ConstantList.LOG_PATH, "log.txt");
                                }
                            }
                            hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, hotelGnHsjlList);
                        }
                    }else if(info.getString("returnCode").equals("009")){
                        j--;
                        System.out.println(infoText);
                        log.info("红色加力协议返回code {},infoText:{}",info.getString("returnCode"),infoText);
                    }else {
                        TXTUtil.writeTXT(new Date() +" 获取红色加力详情进else了： " + infoText,
                                ConstantList.LOG_PATH, "log.txt");
                        log.info("获取红色加力协议详情进else了 {}",infoText);
                    }
                }catch (Exception e) {
                    j--;
    //                e.printStackTrace();
    //                TXTUtil.writeTXT(new Date() + " 获取付红色加力详情进异常了： " +e,
    //                        ConstantList.LOG_PATH, "log.txt");
                    log.error("获取付红色加力协议详情进异常了 errorMessage:{}",e.getMessage(),e);
                    continue;
                }finally {
                    try {
                        Thread.sleep(6000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
            HotelUpdateStatus.expStatusHSJL = end;
            HotelUpdateStatus.status = 0;
            recode.setCountFail(HotelUpdateStatus.hsjlDetailCountUpdate);
            recode.setCountAdd(HotelUpdateStatus.hsjlIdsCountAdd);
            recode.setTimeEnd(new Date());
            hotelUpdateRecodeService.updateHotelUpdateRecode(recode);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private void insertCorp(String jdidHsjl, String corpId) {
        JdJdbHsjlCorp jdJdbHsjlCorp = new JdJdbHsjlCorp();
        jdJdbHsjlCorp.setJdidHsjl(jdidHsjl);
        jdJdbHsjlCorp.setCorpId(corpId);
        List<JdJdbHsjlCorp> list = jdJdbHsjlCorpService.selectJdJdbHsjlCorpList(jdJdbHsjlCorp);
        if(list.size() == 0) {
            jdJdbHsjlCorpService.insertJdJdbHsjlCorp(jdJdbHsjlCorp);
        }
    }

    @Override
    protected Class<HotelGnHsjl> getEntityClass() {
        return HotelGnHsjl.class;
    }

    @Override
    protected List<HotelGnBase> doSelectListWithParams(HotelGnBase entity, Map<String, Object> queryParams) {
        return mapper.selectHotelGnBaseCommonList2(entity, getEntityClass());
    }

    @Override
    protected HotelGnBase doSelectById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    protected int doInsert(HotelGnBase entity) {
        try {
            List<Long> ids = new ArrayList<>();
            ids.add(Long.valueOf(entity.getId()));
            String infoText = HsjlApi.queryHotelInfo(ids, null, null);
            JSONObject resJson = JSONObject.parseObject(infoText);
            insertChild(entity, resJson.getJSONObject("bussinessResponse").getJSONArray("hotelInfos").getJSONObject(0));
        } catch (RuntimeException e) {
            log.error("获取红色加力详情进异常: {}", e);
            return 0;
        }
        return 1;
    }

    private void insertChild(HotelGnBase entity, JSONObject jsonObject) {
        HotelGnHsjl hotelGnHsjl = this.buildHotelGnHsjl(PlatEnum.PLAT_HSJL, jsonObject);
        hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_HSJL, Lists.newArrayList(hotelGnHsjl));
    }

    @Override
    protected int doUpdate(HotelGnBase entity) {
        return mapper.updateHotelGnBaseCommon(entity,getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        return mapper.deleteHotelGnBaseCommonByIds(ids,getEntityClass());
    }

    @Override
    protected PlatEnum getPlatEnum() {
        return PlatEnum.PLAT_HSJL;
    }

    /**
     * 导出酒店数据
     *
     * @param entity 查询实体
     * @param searchParams 查询参数
     * @return 导出数据列表
     */
    @Override
    public List<?> exportData(HotelGnBase entity, HotelGnHsjl searchParams) {
        return doSelectListWithParams(entity, null);
    }

    /**
     * 获取导出实体类型
     *
     * @return 导出实体类
     */
    @Override
    public Class<?> getExportEntityClass() {
        return HotelGnBase.class;
    }

    /**
     * 获取导出文件名
     *
     * @param searchParams 查询参数
     * @return 导出文件名
     */
    @Override
    public String getExportFileName(HotelGnHsjl searchParams) {
        return "hotel_hsjl_data";
    }

    @Override
    public HotelGnBase selectHotelGnBaseById(String id) {
        return mapper.selectHotelGnBaseCommonById(id, getEntityClass());
    }

    @Override
    public void updateHotelGnBase(HotelGnBase hotelGnBase) {
        this.update(hotelGnBase);
    }

}