package com.ltgj.ivw.enums;

public enum PlatEnum {
    PLAT_BASE("-1", "基础数据", "request", "request", "", ""),
    PLAT_EL("2000004", "艺龙", "request", "request", "hotelInfoElongService", "hotel_info_elong"),
    PLAT_YL("2000028", "萤旅", "give", "give", "hotelInfoYinglvService", "hotel_info_yinglv"),

    PLAT_QT("2000010", "千淘", "down", "request", "hotelInfoQiantaoService", "hotel_gn_qiantao", "qiantao"),
    PLAT_FX("2000067", "付迅", "request", "request", "hotelInfoFuxunService", "hotel_info_fuxun"),
    PLAT_HBE("2000022", "航信Hbe", "request", "give", "hotelInfoHbeService", ""),

    PLAT_HSJL("2000054", "红色加力", "request", "request", "hotelInfoHsjlService", "hotel_gn_hsjl", "hsjl"),

    PLAT_HSJL_XY("2000077", "红色加力协议", "request", "request", "hotelInfoHsjlxyService", "hotel_gn_hsjlxy", "hsjlxy"),

    PLAT_MT("2000023", "美团", "request", "down", "hotelInfoMeituanService", "hotel_gn_meituan", "meituan"),
    PLAT_HZ("2000037", "华住", "request", "request", "hotelInfoHuazhuService", "hotel_info_huazhu"),
    PLAT_JJ("2000044", "锦江", "request", "request", "hotelInfoJinjiangService", "hotel_info_jinjiang"),
    PLAT_DC("2000050", "东呈", "request", "request", "hotelInfoDongchengService", "hotel_info_dongcheng"),
    PLAT_DL("2000072", "道旅", "down", "request", "hotelInfoGjDaolvService", "hotel_info_gj_daolv"),
    PLAT_LT("2000074", "龙腾", "", "request", "hotelInfoLongtengService", "hotel_info_longteng"),

    PLAT_CLGJ("2000079", "差旅管家", "", "request", "hotelInfoChailvService", "hotel_gn_clgj", "clgj"),

    PLAT_KLYX("2000080", "康旅严选", "", "request", "hotelGnKlyxService", "hotel_gn_klyx", "klyx"),
    PLAT_KT("2000082", "科坦", "request", "request", "hotelGnKetanService", "hotel_gn_ketan", "ketan"),

    ;

    //平台编号
    private String value;

    //平台名称
    private String name;

    //酒店数据获取类型
    private String hotelType;

    //城市数据获取类型
    private String cityType;

    //service名称
    private String service;

    private String tableName;

    private String tableNameSuffix;

    public String getValue() {
        return this.value;
    }

    public String getService() {
        return this.service;
    }

    public String getName() {
        return this.name;
    }

    public String getTableName() {
        return this.tableName;
    }

    public String getTableNameSuffix() {
        return this.tableNameSuffix;
    }


    PlatEnum(String value, String name, String hotelType, String cityType, String service, String tableName) {
        this.value = value;
        this.name = name;
        this.hotelType = hotelType;
        this.cityType = cityType;
        this.service = service;
        this.tableName = tableName;
    }

    PlatEnum(String value, String name, String hotelType, String cityType, String service, String tableName, String tableNameSuffix) {
        this.value = value;
        this.name = name;
        this.hotelType = hotelType;
        this.cityType = cityType;
        this.service = service;
        this.tableName = tableName;
        this.tableNameSuffix = tableNameSuffix;
    }

    public static PlatEnum getplatEnum(String platNum) {
        PlatEnum[] platEnums = PlatEnum.values();
        for (PlatEnum platEnum : platEnums) {
            if (platEnum.value.equals(platNum)) {
                return platEnum;
            }
        }
        return null;
    }

}
