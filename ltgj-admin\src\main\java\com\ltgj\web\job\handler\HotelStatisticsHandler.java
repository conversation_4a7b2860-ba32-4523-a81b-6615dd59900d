package com.ltgj.web.job.handler;

import com.ltgj.ivw.service.HotelGnStatisticsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@JobHandler(value = "hotelStatisticsHandler")
public class HotelStatisticsHandler extends IJobHandler {

    @Resource
    private HotelGnStatisticsService hotelGnStatisticsService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            log.info("定时酒店映射统计数据开始执行");
            hotelGnStatisticsService.insertHotelStatistics();
            log.info("定时酒店映射统计数据执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("定时酒店映射统计数据任务执行异常: {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行异常：" + e.getMessage());
        }
    }
}
