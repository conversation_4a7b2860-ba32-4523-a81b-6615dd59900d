package com.ltgj.ivw.service;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelInfoElong;
import com.ltgj.ivw.dto.CityLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.ParentLookupReq;

import java.util.List;

/**
 * 艺龙酒店数据Service接口
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
public interface IHotelInfoElongService {
    /**
     * 查询艺龙酒店数据
     *
     * @param id 艺龙酒店数据主键
     * @return 艺龙酒店数据
     */
    public HotelInfoElong  selectHotelInfoElongById(String id);

    /**
     * 查询艺龙酒店数据列表
     *
     * @param hotelInfoElong 艺龙酒店数据
     * @return 艺龙酒店数据集合
     */
    public List<HotelInfoElong> selectHotelInfoElongList(HotelInfoElong hotelInfoElong);

    public List<HotelInfoElong> selectHotelInfoElongList2(HotelInfoElong hotelInfoElong);

    /**
     * 新增艺龙酒店数据
     *
     * @param hotelInfoElong 艺龙酒店数据
     * @return 结果
     */
    public int insertHotelInfoElong(HotelInfoElong hotelInfoElong);

    public int insertHotelInfoElongs(List<HotelInfoElong> hotelInfoElongs);

    /**
     * 修改艺龙酒店数据
     *
     * @param hotelInfoElong 艺龙酒店数据
     * @return 结果
     */
    public int updateHotelInfoElong(HotelInfoElong hotelInfoElong);

    public void initElongData();

    /**
     * 批量删除艺龙酒店数据
     *
     * @param ids 需要删除的艺龙酒店数据主键集合
     * @return 结果
     */
    public int deleteHotelInfoElongByIds(String[] ids);

    /**
     * 删除艺龙酒店数据信息
     *
     * @param id 艺龙酒店数据主键
     * @return 结果
     */
    public int deleteHotelInfoElongById(String id);

    List<String> selectIdIn(List<String> idList);

    AjaxResult updateCityInfo();

    /**
     * 获取四级联动城市数据（国家->省份->城市->区县）
     * 
     * @param platformId 平台ID（可选，用于筛选特定平台的数据）
     * @return 四级联动数据结构
     */
    AjaxResult getCityHierarchy(String platformId);

    /**
     * 分级查询城市数据
     * 根据传入的参数层级返回下一级数据
     * - 无参数：返回所有国家
     * - 传入国家：返回该国家下的省份
     * - 传入国家+省份：返回该省份下的城市
     * - 传入国家+省份+城市：返回该城市下的区县
     * 
     * @param request 查询请求参数
     * @return 下一级数据
     */
    AjaxResult getCityDataByLevel(CityLevelQueryReq request);

    /**
     * 获取上级行政区划信息
     * 支持两种查询模式：
     * 1. 通过城市ID获取上级省份信息
     * 2. 通过区县ID获取上级城市信息
     * 
     * @param request 上级查询请求参数
     * @return 上级行政区划信息
     */
    AjaxResult getParentInfo(ParentLookupReq request);

    /**
     * 清除缓存
     */
    void clearCache();

    /**
     * 清除向上查询缓存
     */
    void clearCacheForParent();


    /**
     * 通过条件获取所有区域结构
     * @param cityLevelQueryReq
     * @return
     */
    AjaxResult<GeoHierarchyNode> getAllRegionByCondition(CityLevelQueryReq cityLevelQueryReq);

    /**
     * 通过平台编码和城市id获取艺龙的城市基础数据
     *
     * @param cityLevelQueryReq
     * @return
     */
    AjaxResult<List<HotelCity>> getHotelCityByCondition(CityLevelQueryReq cityLevelQueryReq);
}
