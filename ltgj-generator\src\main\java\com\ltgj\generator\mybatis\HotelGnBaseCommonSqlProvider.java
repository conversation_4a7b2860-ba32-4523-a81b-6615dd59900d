package com.ltgj.generator.mybatis;

import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.ivw.utils.ReflectForServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.builder.annotation.ProviderMethodResolver;
import org.apache.ibatis.jdbc.SQL;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义解析器
 *
 * <AUTHOR>
 * @Date 2024/12/27
 * @description:
 */
@Slf4j
public class HotelGnBaseCommonSqlProvider<T> implements ProviderMethodResolver {

    public String selectHotelGnBaseCommonById(@Param("id") String id, @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        return new SQL() {
            {
                SELECT("  id,\n" +
                        "        name,\n" +
                        "        name_en,\n" +
                        "        type_id,\n" +
                        "        open_date,\n" +
                        "        decoration_date,\n" +
                        "        phone,\n" +
                        "        country_id,\n" +
                        "        country_name,\n" +
                        "        province_id,\n" +
                        "        province_name,\n" +
                        "        city_id,\n" +
                        "        city_name,\n" +
                        "        area_id,\n" +
                        "        area_name,\n" +
                        "        business_district_id,\n" +
                        "        business_district_name,\n" +
                        "        address,\n" +
                        "        address_en,\n" +
                        "        lon_google,\n" +
                        "        lat_google,\n" +
                        "        lon_baidu,\n" +
                        "        lat_baidu,\n" +
                        "        lon_gaode,\n" +
                        "        lat_gaode,\n" +
                        "        group_id,\n" +
                        "        group_name,\n" +
                        "        brand_id,\n" +
                        "        brand_name,\n" +
                        "        star,\n" +
                        "        image,\n" +
                        "        score,\n" +
                        "        synopsis,\n" +
                        "        sparkle,\n" +
                        "        policy_info,\n" +
                        "        facilities_info,\n" +
                        "        round_info,\n" +
                        "        status,\n" +
                        "        remark,\n" +
                        "        increment_status,\n" +
                        "        increment_time,\n" +
                        "        increment_type,\n" +
                        "        create_time,\n" +
                        "        update_time,\n" +
                        "        create_by,\n" +
                        "        update_by,\n" +
                        "        reserve1,\n" +
                        "        reserve2,\n" +
                        "        reserve3,\n" +
                        "        reserve4,\n" +
                        "        reserve5,\n" +
                        "        reserve6,\n" +
                        "        reserve7,\n" +
                        "        reserve8,\n" +
                        "        reserve9 ");
                FROM(tableName);
                WHERE("id = #{id}");
            }
        }.toString();
    }

    public String selectHotelGnBaseCommonList(@Param("hotelGnBase") HotelGnBase entity,
                                            @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        return new SQL() {
            {
                SELECT(" id,\n" +
                        "        name,\n" +
                        "        name_en,\n" +
                        "        type_id,\n" +
                        "        open_date,\n" +
                        "        decoration_date,\n" +
                        "        phone,\n" +
                        "        country_id,\n" +
                        "        country_name,\n" +
                        "        province_id,\n" +
                        "        province_name,\n" +
                        "        city_id,\n" +
                        "        city_name,\n" +
                        "        area_id,\n" +
                        "        area_name,\n" +
                        "        business_district_id,\n" +
                        "        business_district_name,\n" +
                        "        address,\n" +
                        "        address_en,\n" +
                        "        lon_google,\n" +
                        "        lat_google,\n" +
                        "        lon_baidu,\n" +
                        "        lat_baidu,\n" +
                        "        lon_gaode,\n" +
                        "        lat_gaode,\n" +
                        "        group_id,\n" +
                        "        group_name,\n" +
                        "        brand_id,\n" +
                        "        brand_name,\n" +
                        "        star,\n" +
                        "        image,\n" +
                        "        score,\n" +
                        "        synopsis,\n" +
                        "        sparkle,\n" +
                        "        policy_info,\n" +
                        "        facilities_info,\n" +
                        "        round_info,\n" +
                        "        status,\n" +
                        "        remark,\n" +
                        "        increment_status,\n" +
                        "        increment_time,\n" +
                        "        increment_type,\n" +
                        "        create_time,\n" +
                        "        update_time,\n" +
                        "        create_by,\n" +
                        "        update_by,\n" +
                        "        reserve1,\n" +
                        "        reserve2,\n" +
                        "        reserve3,\n" +
                        "        reserve4,\n" +
                        "        reserve5,\n" +
                        "        reserve6,\n" +
                        "        reserve7,\n" +
                        "        reserve8,\n" +
                        "        reserve9");
                FROM(tableName);
                // 根据 entity 对象添加 WHERE 条件
                StringBuilder sb = new StringBuilder();
                if (entity != null) {
                    appendWhereCondition(sb, "id", entity.getId(), "hotelGnBase.id", false);
                    appendWhereCondition(sb, "name", entity.getName(), "hotelGnBase.name", true);
                    appendWhereCondition(sb, "name_en", entity.getNameEn(), "hotelGnBase.nameEn", false);
                    appendWhereCondition(sb, "phone", entity.getPhone(), "hotelGnBase.phone", false);
                    appendWhereCondition(sb, "country_id", entity.getCountryId(), "hotelGnBase.countryId", false);
                    appendWhereCondition(sb, "country_name", entity.getCountryName(), "hotelGnBase.countryName", false);
                    appendWhereCondition(sb, "province_id", entity.getProvinceId(), "hotelGnBase.provinceId", false);
                    appendWhereCondition(sb, "province_name", entity.getProvinceName(), "hotelGnBase.provinceName", false);
                    appendWhereCondition(sb, "city_id", entity.getCityId(), "hotelGnBase.cityId", false);
                    appendWhereCondition(sb, "city_name", entity.getCityName(), "hotelGnBase.cityName", true);
                    appendWhereCondition(sb, "area_id", entity.getAreaId(), "hotelGnBase.areaId", false);
                    appendWhereCondition(sb, "area_name", entity.getAreaName(), "hotelGnBase.areaName", false);
                    appendWhereCondition(sb, "business_district_id", entity.getBusinessDistrictId(), "hotelGnBase.businessDistrictId", false);
                    appendWhereCondition(sb, "business_district_name", entity.getBusinessDistrictName(), "hotelGnBase.businessDistrictName", false);
                    appendWhereCondition(sb, "address", entity.getAddress(), "hotelGnBase.address", false);
                    appendWhereCondition(sb, "address_en", entity.getAddressEn(), "hotelGnBase.addressEn", false);
                    appendWhereCondition(sb, "open_date", entity.getOpenDate(), "hotelGnBase.openDate", false);
                    appendWhereCondition(sb, "decoration_date", entity.getDecorationDate(), "hotelGnBase.decorationDate", false);
                    appendWhereCondition(sb, "synopsis", entity.getSynopsis(), "hotelGnBase.synopsis", false);
                    appendWhereCondition(sb, "sparkle", entity.getSparkle(), "hotelGnBase.sparkle", false);
                    appendWhereCondition(sb, "brand_id", entity.getBrandId(), "hotelGnBase.brandId", false);
                    appendWhereCondition(sb, "brand_name", entity.getBrandName(), "hotelGnBase.brandName", false);
                    appendWhereCondition(sb, "group_id", entity.getGroupId(), "hotelGnBase.groupId", false);
                    appendWhereCondition(sb, "group_name", entity.getGroupName(), "hotelGnBase.groupName", false);
                    if (entity.getTypeId() != null) {
                        sb.append(" and type_id = #{hotelGnBase.typeId}");
                    }
                    appendWhereCondition(sb, "star", entity.getStar(), "hotelGnBase.star", false);
                    appendWhereCondition(sb, "image", entity.getImage(), "hotelGnBase.image", false);
                    appendWhereCondition(sb, "score", entity.getScore(), "hotelGnBase.score", false);
                    appendWhereCondition(sb, "policy_info", entity.getPolicyInfo(), "hotelGnBase.policyInfo", false);
                    appendWhereCondition(sb, "facilities_info", entity.getFacilitiesInfo(), "hotelGnBase.facilitiesInfo", false);
                    appendWhereCondition(sb, "round_info", entity.getRoundInfo(), "hotelGnBase.roundInfo", false);
                    if (entity.getLonGoogle() != null) {
                        sb.append(" and lon_google = #{hotelGnBase.lonGoogle}");
                    }
                    if (entity.getLatGoogle() != null) {
                        sb.append(" and lat_google = #{hotelGnBase.latGoogle}");
                    }
                    if (entity.getLonBaidu() != null) {
                        sb.append(" and lon_baidu = #{hotelGnBase.lonBaidu}");
                    }
                    if (entity.getLatBaidu() != null) {
                        sb.append(" and lat_baidu = #{hotelGnBase.latBaidu}");
                    }
                    if (entity.getLonGaode() != null) {
                        sb.append(" and lon_gaode = #{hotelGnBase.lonGaode}");
                    }
                    if (entity.getLatGaode() != null) {
                        sb.append(" and lat_gaode = #{hotelGnBase.latGaode}");
                    }
//                    appendWhereCondition(sb, "reserve0", entity.getReserve0(), "hotelGnBase.reserve0", false);
                    appendWhereCondition(sb, "reserve1", entity.getReserve1(), "hotelGnBase.reserve1", false);
                    appendWhereCondition(sb, "reserve2", entity.getReserve2(), "hotelGnBase.reserve2", false);
                    appendWhereCondition(sb, "reserve3", entity.getReserve3(), "hotelGnBase.reserve3", false);
                    appendWhereCondition(sb, "reserve4", entity.getReserve4(), "hotelGnBase.reserve4", false);
                    appendWhereCondition(sb, "reserve5", entity.getReserve5(), "hotelGnBase.reserve5", false);
                    appendWhereCondition(sb, "reserve6", entity.getReserve6(), "hotelGnBase.reserve6", false);
                    appendWhereCondition(sb, "reserve7", entity.getReserve7(), "hotelGnBase.reserve7", false);
                    appendWhereCondition(sb, "reserve8", entity.getReserve8(), "hotelGnBase.reserve8", false);
                    appendWhereCondition(sb, "reserve9", entity.getReserve9(), "hotelGnBase.reserve9", false);
                    appendWhereCondition(sb, "remark", entity.getRemark(), "hotelGnBase.remark", false);
                    if (entity.getStatus() != null) {
                        sb.append(" and status = #{hotelGnBase.status}");
                    }
                    if (entity.getIsDelete() != null) {
                        sb.append(" and is_delete = #{hotelGnBase.isDelete}");
                    }
                    if (entity.getIncrementStatus() != null) {
                        sb.append(" and increment_status = #{hotelGnBase.incrementStatus}");
                    }
                    if (entity.getIncrementType() != null) {
                        sb.append(" and increment_type = #{hotelGnBase.incrementType}");
                    }
                    if (entity.getUpdateTime() != null) {
                        sb.append(" and update_time = #{hotelGnBase.updateTime}");
                    }
                }
                WHERE("1=1" + sb);
            }
        }.toString();
    }

    public String selectHotelGnBaseCommonList2(@Param("hotelGnBase") HotelGnBase hotelGnBase,
                                             @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        return new SQL() {
            {
                SELECT(" id,\n" +
                        "        name,\n" +
                        "        name_en,\n" +
                        "        type_id,\n" +
                        "        open_date,\n" +
                        "        decoration_date,\n" +
                        "        phone,\n" +
                        "        country_id,\n" +
                        "        country_name,\n" +
                        "        province_id,\n" +
                        "        province_name,\n" +
                        "        city_id,\n" +
                        "        city_name,\n" +
                        "        area_id,\n" +
                        "        area_name,\n" +
                        "        business_district_id,\n" +
                        "        business_district_name,\n" +
                        "        address,\n" +
                        "        address_en,\n" +
                        "        lon_google,\n" +
                        "        lat_google,\n" +
                        "        lon_baidu,\n" +
                        "        lat_baidu,\n" +
                        "        lon_gaode,\n" +
                        "        lat_gaode,\n" +
                        "        group_id,\n" +
                        "        group_name,\n" +
                        "        brand_id,\n" +
                        "        brand_name,\n" +
                        "        star,\n" +
                        "        image,\n" +
                        "        score,\n" +
                        "        synopsis,\n" +
                        "        sparkle,\n" +
                        "        policy_info,\n" +
                        "        facilities_info,\n" +
                        "        round_info,\n" +
                        "        status,\n" +
                        "        remark,\n" +
                        "        increment_status,\n" +
                        "        increment_time,\n" +
                        "        increment_type,\n" +
                        "        create_time,\n" +
                        "        update_time,\n" +
                        "        create_by,\n" +
                        "        update_by,\n" +
                        "        reserve1,\n" +
                        "        reserve2,\n" +
                        "        reserve3,\n" +
                        "        reserve4,\n" +
                        "        reserve5,\n" +
                        "        reserve6,\n" +
                        "        reserve7,\n" +
                        "        reserve8,\n" +
                        "        reserve9");
                FROM(tableName);
                // 根据 entity 对象添加 WHERE 条件
                StringBuilder sb = new StringBuilder();
                if (Objects.nonNull(hotelGnBase)) {
                    sb.append(" and is_delete = 0");
                    if (hotelGnBase.getStatus() != null) {
                        sb.append(" and status = #{hotelGnBase.status}");
                    }
                    appendWhereCondition(sb, "city_id", hotelGnBase.getCityId(), "hotelGnBase.cityId", false);
                    appendWhereCondition(sb, "city_name", hotelGnBase.getCityName(), "hotelGnBase.cityName", true);
                    appendWhereCondition(sb, "id", hotelGnBase.getId(), "hotelGnBase.id", false);
                    appendWhereCondition(sb, "name", hotelGnBase.getName(), "hotelGnBase.name", true);
                    appendWhereCondition(sb, "reserve9", hotelGnBase.getReserve9(), "hotelGnBase.reserve9", false);
                    if (isStringNotEmpty(hotelGnBase.getBrandId())) {
                        sb.append(" and brand_id is not null and brand_id != ''");
                    }
                    if (hotelGnBase.getIncrementStatus() != null) {
                        sb.append(" and increment_status = #{hotelGnBase.incrementStatus}");
                    }
                    if (hotelGnBase.getUpdateTime() != null) {
                        sb.append(" and update_time = #{hotelGnBase.updateTime}");
                    }
                }
                WHERE("1=1" + sb);
            }
        }.toString();
    }

    public String updateHotelGnBaseCommon(@Param("hotelGnBase") HotelGnBase hotelGnBase,
                                        @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        String objectName = "hotelGnBase";
        return new SQL() {
            {
                UPDATE(tableName);
                // 根据 entity 对象添加 SET 条件
                StringBuilder sb = new StringBuilder();
                appendForUpdateIfNotNull(sb, "increment_status", "incrementStatus", hotelGnBase.getIncrementStatus(), objectName);
                appendForUpdateIfNotNull(sb, "increment_time", "incrementTime", hotelGnBase.getIncrementTime(), objectName);
                appendForUpdateIfNotNull(sb, "increment_type", "incrementType", hotelGnBase.getIncrementType(), objectName);
                appendForUpdateIfNotNull(sb, "is_delete", "isDelete", hotelGnBase.getIsDelete(), objectName);
                appendForUpdateIfNotNull(sb, "status", "status", hotelGnBase.getStatus(), objectName);
                appendForUpdateIfNotNull(sb, "name", "name", hotelGnBase.getName(), objectName);
                appendForUpdateIfNotNull(sb, "name_en", "nameEn", hotelGnBase.getNameEn(), objectName);
                appendForUpdateIfNotNull(sb, "phone", "phone", hotelGnBase.getPhone(), objectName);
                appendForUpdateIfNotNull(sb, "country_id", "countryId", hotelGnBase.getCountryId(), objectName);
                appendForUpdateIfNotNull(sb, "country_name", "countryName", hotelGnBase.getCountryName(), objectName);
                appendForUpdateIfNotNull(sb, "province_id", "provinceId", hotelGnBase.getProvinceId(), objectName);
                appendForUpdateIfNotNull(sb, "province_name", "provinceName", hotelGnBase.getProvinceName(), objectName);
                appendForUpdateIfNotNull(sb, "city_id", "cityId", hotelGnBase.getCityId(), objectName);
                appendForUpdateIfNotNull(sb, "city_name", "cityName", hotelGnBase.getCityName(), objectName);
                appendForUpdateIfNotNull(sb, "area_id", "areaId", hotelGnBase.getAreaId(), objectName);
                appendForUpdateIfNotNull(sb, "area_name", "areaName", hotelGnBase.getAreaName(), objectName);
                appendForUpdateIfNotNull(sb, "business_district_id", "businessDistrictId", hotelGnBase.getBusinessDistrictId(), objectName);
                appendForUpdateIfNotNull(sb, "business_district_name", "businessDistrictName", hotelGnBase.getBusinessDistrictName(), objectName);
                appendForUpdateIfNotNull(sb, "address", "address", hotelGnBase.getAddress(), objectName);
                appendForUpdateIfNotNull(sb, "address_en", "addressEn", hotelGnBase.getAddressEn(), objectName);
                appendForUpdateIfNotNull(sb, "open_date", "openDate", hotelGnBase.getOpenDate(), objectName);
                appendForUpdateIfNotNull(sb, "decoration_date", "decorationDate", hotelGnBase.getDecorationDate(), objectName);
                appendForUpdateIfNotNull(sb, "synopsis", "synopsis", hotelGnBase.getSynopsis(), objectName);
                appendForUpdateIfNotNull(sb, "sparkle", "sparkle", hotelGnBase.getSparkle(), objectName);
                appendForUpdateIfNotNull(sb, "brand_id", "brandId", hotelGnBase.getBrandId(), objectName);
                appendForUpdateIfNotNull(sb, "brand_name", "brandName", hotelGnBase.getBrandName(), objectName);
                appendForUpdateIfNotNull(sb, "group_id", "groupId", hotelGnBase.getGroupId(), objectName);
                appendForUpdateIfNotNull(sb, "group_name", "groupName", hotelGnBase.getGroupName(), objectName);
                appendForUpdateIfNotNull(sb, "type_id", "typeId", hotelGnBase.getTypeId(), objectName);
                appendForUpdateIfNotNull(sb, "star", "star", hotelGnBase.getStar(), objectName);
                appendForUpdateIfNotNull(sb, "image", "image", hotelGnBase.getImage(), objectName);
                appendForUpdateIfNotNull(sb, "score", "score", hotelGnBase.getScore(), objectName);
                appendForUpdateIfNotNull(sb, "policy_info", "policyInfo", hotelGnBase.getPolicyInfo(), objectName);
                appendForUpdateIfNotNull(sb, "facilities_info", "facilitiesInfo", hotelGnBase.getFacilitiesInfo(), objectName);
                appendForUpdateIfNotNull(sb, "round_info", "roundInfo", hotelGnBase.getRoundInfo(), objectName);
                appendForUpdateIfNotNull(sb, "lon_google", "lonGoogle", hotelGnBase.getLonGoogle(), objectName);
                appendForUpdateIfNotNull(sb, "lat_google", "latGoogle", hotelGnBase.getLatGoogle(), objectName);
                appendForUpdateIfNotNull(sb, "lon_baidu", "lonBaidu", hotelGnBase.getLonBaidu(), objectName);
                appendForUpdateIfNotNull(sb, "lat_baidu", "latBaidu", hotelGnBase.getLatBaidu(), objectName);
                appendForUpdateIfNotNull(sb, "lon_gaode", "lonGaode", hotelGnBase.getLonGaode(), objectName);
                appendForUpdateIfNotNull(sb, "lat_gaode", "latGaode", hotelGnBase.getLatGaode(), objectName);
                appendForUpdateIfNotNull(sb, "reserve0", "reserve0", hotelGnBase.getReserve0(), objectName);
                appendForUpdateIfNotNull(sb, "reserve1", "reserve1", hotelGnBase.getReserve1(), objectName);
                appendForUpdateIfNotNull(sb, "reserve2", "reserve2", hotelGnBase.getReserve2(), objectName);
                appendForUpdateIfNotNull(sb, "reserve3", "reserve3", hotelGnBase.getReserve3(), objectName);
                appendForUpdateIfNotNull(sb, "reserve4", "reserve4", hotelGnBase.getReserve4(), objectName);
                appendForUpdateIfNotNull(sb, "reserve5", "reserve5", hotelGnBase.getReserve5(), objectName);
                appendForUpdateIfNotNull(sb, "reserve6", "reserve6", hotelGnBase.getReserve6(), objectName);
                appendForUpdateIfNotNull(sb, "reserve7", "reserve7", hotelGnBase.getReserve7(), objectName);
                appendForUpdateIfNotNull(sb, "reserve8", "reserve8", hotelGnBase.getReserve8(), objectName);
                appendForUpdateIfNotNull(sb, "reserve9", "reserve9", hotelGnBase.getReserve9(), objectName);
                appendForUpdateIfNotNull(sb, "remark", "remark", hotelGnBase.getRemark(), objectName);
                appendForUpdateIfNotNull(sb, "update_time", "updateTime", hotelGnBase.getUpdateTime(), objectName);
                appendForUpdateIfNotNull(sb, "update_by", "updateBy", hotelGnBase.getUpdateBy(), objectName);

                if (sb.length() > 0 && sb.charAt(0) == ',') {
                    sb.deleteCharAt(0);
                }
                SET(sb.toString());
                WHERE("id = #{hotelGnBase.id}");
            }
        }.toString();
    }

    public String insertHotelGnBaseCommon(@Param("hotelGnBase") Object hotelGnBase,
                                        @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        String objectName = "hotelGnBase";

        return new SQL() {
            {
                INSERT_INTO(tableName);

                // 动态添加INSERT字段和值
                StringBuilder columns = new StringBuilder();
                StringBuilder values = new StringBuilder();

                // 使用反射获取HotelGnBase对象的所有字段值
                if (hotelGnBase instanceof HotelGnBase) {
                    HotelGnBase hotel = (HotelGnBase) hotelGnBase;

                    appendForInsertIfNotNull(columns, values, "id", "id", hotel.getId(), objectName);
                    appendForInsertIfNotNull(columns, values, "increment_status", "incrementStatus", hotel.getIncrementStatus(), objectName);
                    appendForInsertIfNotNull(columns, values, "increment_time", "incrementTime", hotel.getIncrementTime(), objectName);
                    appendForInsertIfNotNull(columns, values, "increment_type", "incrementType", hotel.getIncrementType(), objectName);
                    appendForInsertIfNotNull(columns, values, "is_delete", "isDelete", hotel.getIsDelete(), objectName);
                    appendForInsertIfNotNull(columns, values, "status", "status", hotel.getStatus(), objectName);
                    appendForInsertIfNotNull(columns, values, "name", "name", hotel.getName(), objectName);
                    appendForInsertIfNotNull(columns, values, "name_en", "nameEn", hotel.getNameEn(), objectName);
                    appendForInsertIfNotNull(columns, values, "phone", "phone", hotel.getPhone(), objectName);
                    appendForInsertIfNotNull(columns, values, "country_id", "countryId", hotel.getCountryId(), objectName);
                    appendForInsertIfNotNull(columns, values, "country_name", "countryName", hotel.getCountryName(), objectName);
                    appendForInsertIfNotNull(columns, values, "province_id", "provinceId", hotel.getProvinceId(), objectName);
                    appendForInsertIfNotNull(columns, values, "province_name", "provinceName", hotel.getProvinceName(), objectName);
                    appendForInsertIfNotNull(columns, values, "city_id", "cityId", hotel.getCityId(), objectName);
                    appendForInsertIfNotNull(columns, values, "city_name", "cityName", hotel.getCityName(), objectName);
                    appendForInsertIfNotNull(columns, values, "area_id", "areaId", hotel.getAreaId(), objectName);
                    appendForInsertIfNotNull(columns, values, "area_name", "areaName", hotel.getAreaName(), objectName);
                    appendForInsertIfNotNull(columns, values, "business_district_id", "businessDistrictId", hotel.getBusinessDistrictId(), objectName);
                    appendForInsertIfNotNull(columns, values, "business_district_name", "businessDistrictName", hotel.getBusinessDistrictName(), objectName);
                    appendForInsertIfNotNull(columns, values, "address", "address", hotel.getAddress(), objectName);
                    appendForInsertIfNotNull(columns, values, "address_en", "addressEn", hotel.getAddressEn(), objectName);
                    appendForInsertIfNotNull(columns, values, "open_date", "openDate", hotel.getOpenDate(), objectName);
                    appendForInsertIfNotNull(columns, values, "decoration_date", "decorationDate", hotel.getDecorationDate(), objectName);
                    appendForInsertIfNotNull(columns, values, "synopsis", "synopsis", hotel.getSynopsis(), objectName);
                    appendForInsertIfNotNull(columns, values, "sparkle", "sparkle", hotel.getSparkle(), objectName);
                    appendForInsertIfNotNull(columns, values, "brand_id", "brandId", hotel.getBrandId(), objectName);
                    appendForInsertIfNotNull(columns, values, "brand_name", "brandName", hotel.getBrandName(), objectName);
                    appendForInsertIfNotNull(columns, values, "group_id", "groupId", hotel.getGroupId(), objectName);
                    appendForInsertIfNotNull(columns, values, "group_name", "groupName", hotel.getGroupName(), objectName);
                    appendForInsertIfNotNull(columns, values, "type_id", "typeId", hotel.getTypeId(), objectName);
                    appendForInsertIfNotNull(columns, values, "star", "star", hotel.getStar(), objectName);
                    appendForInsertIfNotNull(columns, values, "image", "image", hotel.getImage(), objectName);
                    appendForInsertIfNotNull(columns, values, "score", "score", hotel.getScore(), objectName);
                    appendForInsertIfNotNull(columns, values, "policy_info", "policyInfo", hotel.getPolicyInfo(), objectName);
                    appendForInsertIfNotNull(columns, values, "facilities_info", "facilitiesInfo", hotel.getFacilitiesInfo(), objectName);
                    appendForInsertIfNotNull(columns, values, "round_info", "roundInfo", hotel.getRoundInfo(), objectName);
                    appendForInsertIfNotNull(columns, values, "lon_google", "lonGoogle", hotel.getLonGoogle(), objectName);
                    appendForInsertIfNotNull(columns, values, "lat_google", "latGoogle", hotel.getLatGoogle(), objectName);
                    appendForInsertIfNotNull(columns, values, "lon_baidu", "lonBaidu", hotel.getLonBaidu(), objectName);
                    appendForInsertIfNotNull(columns, values, "lat_baidu", "latBaidu", hotel.getLatBaidu(), objectName);
                    appendForInsertIfNotNull(columns, values, "lon_gaode", "lonGaode", hotel.getLonGaode(), objectName);
                    appendForInsertIfNotNull(columns, values, "lat_gaode", "latGaode", hotel.getLatGaode(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve0", "reserve0", hotel.getReserve0(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve1", "reserve1", hotel.getReserve1(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve2", "reserve2", hotel.getReserve2(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve3", "reserve3", hotel.getReserve3(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve4", "reserve4", hotel.getReserve4(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve5", "reserve5", hotel.getReserve5(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve6", "reserve6", hotel.getReserve6(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve7", "reserve7", hotel.getReserve7(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve8", "reserve8", hotel.getReserve8(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve9", "reserve9", hotel.getReserve9(), objectName);
                    appendForInsertIfNotNull(columns, values, "remark", "remark", hotel.getRemark(), objectName);
                    appendForInsertIfNotNull(columns, values, "create_time", "createTime", hotel.getCreateTime(), objectName);
                    appendForInsertIfNotNull(columns, values, "create_by", "createBy", hotel.getCreateBy(), objectName);
                    appendForInsertIfNotNull(columns, values, "update_time", "updateTime", hotel.getUpdateTime(), objectName);
                    appendForInsertIfNotNull(columns, values, "update_by", "updateBy", hotel.getUpdateBy(), objectName);
                }

                // 移除开头的逗号
                if (columns.length() > 0 && columns.charAt(0) == ',') {
                    columns.deleteCharAt(0);
                }
                if (values.length() > 0 && values.charAt(0) == ',') {
                    values.deleteCharAt(0);
                }

                INTO_COLUMNS(columns.toString());
                INTO_VALUES(values.toString());
            }
        }.toString();
    }

    public String deleteHotelGnBaseCommonByIds(@Param("ids") String[] ids, @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        return new SQL() {{
            DELETE_FROM(tableName);
            // 修复SQL注入风险：使用参数化查询
            WHERE("id IN (" +
                    Arrays.stream(ids)
                            .map(id -> "'" + id.replace("'", "''") + "'")  // 简单的SQL转义
                            .collect(Collectors.joining(",")) +
                    ")");
        }}.toString();
    }

    /**
     * 检查字符串是否为空
     */
    private boolean isStringEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查字符串是否不为空
     */
    private boolean isStringNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }

    /**
     * 添加WHERE条件的通用方法
     */
    private void appendWhereCondition(StringBuilder sb, String columnName, String value, String paramName, boolean useLike) {
        if (isStringNotEmpty(value)) {
            if (useLike) {
                sb.append(" and ").append(columnName).append(" like concat('%', #{").append(paramName).append("}, '%')");
            } else {
                sb.append(" and ").append(columnName).append(" = #{").append(paramName).append("}");
            }
        }
    }

    private void appendForUpdateIfNotNull(StringBuilder sb, String columnName, String fieldName, Object value, String objectName) {
        if (value != null) {
            sb.append(",").append(columnName).append(" = #{").append(objectName).append(".").append(fieldName).append("}");
        }
    }

    /**
     * 为INSERT语句添加字段和值（如果值不为null）
     */
    private void appendForInsertIfNotNull(StringBuilder columns, StringBuilder values, String columnName, String fieldName, Object value, String objectName) {
        if (value != null) {
            columns.append(",").append(columnName);
            values.append(",#{").append(objectName).append(".").append(fieldName).append("}");
        }
    }
}