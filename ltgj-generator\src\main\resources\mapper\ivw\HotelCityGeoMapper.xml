<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.HotelCityGeoMapper">
  <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.HotelCityGeo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="interface_plat_id" jdbcType="VARCHAR" property="interfacePlatId" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="poi_id" jdbcType="VARCHAR" property="poiId" />
    <result column="poi_name" jdbcType="VARCHAR" property="poiName" />
    <result column="poi_name_cn" jdbcType="VARCHAR" property="poiNameCn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, interface_plat_id, city_id, poi_id, poi_name, poi_name_cn, create_time, update_time,
    remark, created_by
  </sql>
  <select id="selectByExample" parameterType="com.ltgj.ivw.domain.HotelCityGeoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hotel_city_geo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from hotel_city_geo
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from hotel_city_geo
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.ltgj.ivw.domain.HotelCityGeoExample">
    delete from hotel_city_geo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.ltgj.ivw.domain.HotelCityGeo" useGeneratedKeys="true">
    insert into hotel_city_geo (interface_plat_id, city_id, poi_id,
                                poi_name, poi_name_cn, create_time,
                                update_time, remark, created_by
    )
    values (#{interfacePlatId,jdbcType=VARCHAR}, #{cityId,jdbcType=VARCHAR}, #{poiId,jdbcType=VARCHAR},
            #{poiName,jdbcType=VARCHAR}, #{poiNameCn,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}
           )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ltgj.ivw.domain.HotelCityGeo" useGeneratedKeys="true">
    insert into hotel_city_geo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="interfacePlatId != null">
        interface_plat_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="poiId != null">
        poi_id,
      </if>
      <if test="poiName != null">
        poi_name,
      </if>
      <if test="poiNameCn != null">
        poi_name_cn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="interfacePlatId != null">
        #{interfacePlatId,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="poiId != null">
        #{poiId,jdbcType=VARCHAR},
      </if>
      <if test="poiName != null">
        #{poiName,jdbcType=VARCHAR},
      </if>
      <if test="poiNameCn != null">
        #{poiNameCn,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ltgj.ivw.domain.HotelCityGeoExample" resultType="java.lang.Long">
    select count(*) from hotel_city_geo
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hotel_city_geo
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.interfacePlatId != null">
        interface_plat_id = #{record.interfacePlatId,jdbcType=VARCHAR},
      </if>
      <if test="record.cityId != null">
        city_id = #{record.cityId,jdbcType=VARCHAR},
      </if>
      <if test="record.poiId != null">
        poi_id = #{record.poiId,jdbcType=VARCHAR},
      </if>
      <if test="record.poiName != null">
        poi_name = #{record.poiName,jdbcType=VARCHAR},
      </if>
      <if test="record.poiNameCn != null">
        poi_name_cn = #{record.poiNameCn,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hotel_city_geo
    set id = #{record.id,jdbcType=VARCHAR},
    interface_plat_id = #{record.interfacePlatId,jdbcType=VARCHAR},
    city_id = #{record.cityId,jdbcType=VARCHAR},
    poi_id = #{record.poiId,jdbcType=VARCHAR},
    poi_name = #{record.poiName,jdbcType=VARCHAR},
    poi_name_cn = #{record.poiNameCn,jdbcType=VARCHAR},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    remark = #{record.remark,jdbcType=VARCHAR},
    created_by = #{record.createdBy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ltgj.ivw.domain.HotelCityGeo">
    update hotel_city_geo
    <set>
      <if test="interfacePlatId != null">
        interface_plat_id = #{interfacePlatId,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="poiId != null">
        poi_id = #{poiId,jdbcType=VARCHAR},
      </if>
      <if test="poiName != null">
        poi_name = #{poiName,jdbcType=VARCHAR},
      </if>
      <if test="poiNameCn != null">
        poi_name_cn = #{poiNameCn,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ltgj.ivw.domain.HotelCityGeo">
    update hotel_city_geo
    set interface_plat_id = #{interfacePlatId,jdbcType=VARCHAR},
        city_id = #{cityId,jdbcType=VARCHAR},
        poi_id = #{poiId,jdbcType=VARCHAR},
        poi_name = #{poiName,jdbcType=VARCHAR},
        poi_name_cn = #{poiNameCn,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        created_by = #{createdBy,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <!-- 批量插入酒店城市商圈数据 -->
  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="records.id" keyColumn="id">
    insert into hotel_city_geo (interface_plat_id, city_id, poi_id, poi_name, poi_name_cn, create_time, update_time, remark, created_by)
    values
    <foreach collection="records" item="item" separator=",">
      (#{item.interfacePlatId,jdbcType=VARCHAR}, #{item.cityId,jdbcType=VARCHAR}, #{item.poiId,jdbcType=VARCHAR},
       #{item.poiName,jdbcType=VARCHAR}, #{item.poiNameCn,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
       #{item.updateTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!-- 批量插入酒店城市商圈数据 -->
  <insert id="batchInsertForContidion" parameterType="java.util.List">
    insert into hotel_city_geo (id, interface_plat_id, city_id, poi_id, poi_name, poi_name_cn, create_time, update_time, remark, created_by)
    values
    <foreach collection="records" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR},#{item.interfacePlatId,jdbcType=VARCHAR}, #{item.cityId,jdbcType=VARCHAR}, #{item.poiId,jdbcType=VARCHAR},
       #{item.poiName,jdbcType=VARCHAR}, #{item.poiNameCn,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
       #{item.updateTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!-- 批量插入或更新酒店城市商圈数据 -->
  <insert id="batchInsertOrUpdate" parameterType="java.util.List">
    insert into hotel_city_geo (id, interface_plat_id, city_id, poi_id, poi_name, poi_name_cn, create_time, update_time, remark, created_by)
    values
    <foreach collection="records" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.interfacePlatId,jdbcType=VARCHAR}, #{item.cityId,jdbcType=VARCHAR}, #{item.poiId,jdbcType=VARCHAR},
       #{item.poiName,jdbcType=VARCHAR}, #{item.poiNameCn,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
       #{item.updateTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR})
    </foreach>
    on duplicate key update
    interface_plat_id = values(interface_plat_id),
    city_id = values(city_id),
    poi_id = values(poi_id),
    poi_name = values(poi_name),
    poi_name_cn = values(poi_name_cn),
    update_time = values(update_time),
    remark = values(remark),
    created_by = values(created_by)
  </insert>

  <!-- 批量插入或更新酒店城市商圈数据 -->
  <insert id="batchInsertOrUpdateSmart" parameterType="java.util.List">
    insert into hotel_city_geo (id, interface_plat_id, city_id, poi_id, poi_name, poi_name_cn, create_time, update_time, remark, created_by)
    values
    <foreach collection="records" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.interfacePlatId,jdbcType=VARCHAR}, #{item.cityId,jdbcType=VARCHAR}, #{item.poiId,jdbcType=VARCHAR},
       #{item.poiName,jdbcType=VARCHAR}, #{item.poiNameCn,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
       #{item.updateTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR})
    </foreach>
    on duplicate key update
    interface_plat_id = if(values(interface_plat_id) is not null and values(interface_plat_id) != '', values(interface_plat_id), interface_plat_id),
    city_id = if(values(city_id) is not null and values(city_id) != '', values(city_id), city_id),
    poi_id = if(values(poi_id) is not null and values(poi_id) != '', values(poi_id), poi_id),
    poi_name = if(values(poi_name) is not null and values(poi_name) != '', values(poi_name), poi_name),
    poi_name_cn = if(values(poi_name_cn) is not null and values(poi_name_cn) != '', values(poi_name_cn), poi_name_cn),
    update_time = values(update_time),
    remark = if(values(remark) is not null, values(remark), remark),
    created_by = if(values(created_by) is not null and values(created_by) != '', values(created_by), created_by)
  </insert>

</mapper>