package com.ltgj.ivw.service;

import com.alibaba.fastjson2.JSON;
import com.ltgj.common.es.domain.Result;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.supplier.common.gn.domain.HotelGnGB;
import com.ltgj.supplier.common.gn.enums.HotelGnGBTypeEnum;
import com.ltgj.supplier.common.gn.manager.HotelGnGBManager;
import com.ltgj.supplier.common.gn.service.HotelGnGBMappingService;
import com.ltgj.supplier.common.gn.service.HotelGnGBService;
import com.ltgj.supplier.common.gn.vo.req.AddAndSaveMappingGBReq;
import com.ltgj.supplier.common.gn.vo.req.GetBrandByGroupIdReq;
import com.ltgj.supplier.common.gn.vo.req.QueryBrandListReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HotelGnGBServiceTest {
    @Autowired
    private HotelGnGBService hotelGnGBService;
    @Autowired
    private HotelGnGBManager hotelGnGBManager;

    @org.junit.Test
    public void  getAllGBInfo() {
        Result result = hotelGnGBManager.getAllGBInfo();
        System.out.println(JSON.toJSONString(result));
    }

    @org.junit.Test
    public void  getAllGroup() {
        QueryBrandListReq req = new QueryBrandListReq();
        req.setGbName("");
        Result result = hotelGnGBManager.getAllGroup(req);
        System.out.println(JSON.toJSONString(result));
    }

    @org.junit.Test
    public void  getAllBrandByGroupId() {
        GetBrandByGroupIdReq req = new GetBrandByGroupIdReq();
        req.setGroupId("*********");
        Result result = hotelGnGBManager.getAllBrandByGroupId(req);
        System.out.println(JSON.toJSONString(result));
    }


    @org.junit.Test
    public void  saveBaseAndKetanGBInfo() {
        try (Workbook workbook = new XSSFWorkbook(new FileInputStream("d://集团品牌信息.xlsx"))) {
            Sheet sheet = workbook.getSheetAt(0);
            Map<String, HotelGnGB> map = new HashMap<>();
            List<HotelGnGB> list = new ArrayList<>();
            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue; // 跳过表头
                }
                String groupId = getCellValueAsString(row.getCell(0));
                String groupName = getCellValueAsString(row.getCell(1));
                String groupNameEn = getCellValueAsString(row.getCell(2));

                String brandId = getCellValueAsString(row.getCell(3));
                String brandName = getCellValueAsString(row.getCell(4));
                String brandNameEn = getCellValueAsString(row.getCell(5));
                hotelGnGBManager.saveBaseGBInfo(new AddAndSaveMappingGBReq(PlatEnum.PLAT_KT, groupId, groupName, groupNameEn, brandId, brandName, brandNameEn));

            }

        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @org.junit.Test
    public void  saveBrandInfo() {
        try (Workbook workbook = new XSSFWorkbook(new FileInputStream("d://集团品牌信息.xlsx"))) {
            Sheet sheet = workbook.getSheetAt(0);
            Map<String, HotelGnGB> map = new HashMap<>();
            List<HotelGnGB> list = new ArrayList<>();
            for (Row row : sheet) {
                if (row.getRowNum() == 0) {
                    continue; // 跳过表头
                }
                String groupId = getCellValueAsString(row.getCell(0));
                String groupName = getCellValueAsString(row.getCell(1));
                String groupNameEn = getCellValueAsString(row.getCell(2));

                String brandId = getCellValueAsString(row.getCell(3));
                String brandName = getCellValueAsString(row.getCell(4));
                String brandNameEn = getCellValueAsString(row.getCell(5));
                if (map.containsKey(brandId)) {
                  continue;
                }

                HotelGnGB hotelGnGB = new HotelGnGB();
                hotelGnGB.setGbId(brandId);
                hotelGnGB.setGbName(brandName);
                hotelGnGB.setGbNameEn(brandNameEn);
                hotelGnGB.setType(HotelGnGBTypeEnum.BRAND.getType());
                hotelGnGB.setParentGbId(groupId);
                hotelGnGB.setPlatformId(PlatEnum.PLAT_KT.getValue());

                map.put(brandId, hotelGnGB);
                list.add(hotelGnGB);
            }
            hotelGnGBService.addBatch(list);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue();
    }
}
