<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.JdJdbMapper">

    <resultMap type="com.ltgj.ivw.domain.JdJdb" id="JdJdbResult">
        <result property="id"    column="id"    />
        <result property="interfacePlat"    column="interfacePlat"    />
        <result property="jdmc"    column="jdmc"    />
        <result property="jdmcEn"    column="jdmc_en"    />
        <result property="jdlx"    column="jdlx"    />
        <result property="jddz"    column="jddz"    />
        <result property="jddzEn"    column="jddz_en"    />
        <result property="jddh"    column="jddh"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="status"    column="status"    />
        <result property="lonGoogle"    column="lon_google"    />
        <result property="latGoogle"    column="lat_google"    />
        <result property="lonBaidu"    column="lon_baidu"    />
        <result property="latBaidu"    column="lat_baidu"    />
        <result property="lonGaode"    column="lon_gaode"    />
        <result property="latGaode"    column="lat_gaode"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
        <result property="brandId"    column="brand_id"    />
        <result property="brandName"    column="brand_name"    />
        <result property="district"    column="district"    />
        <result property="districtName"    column="district_name"    />
        <result property="businessZone"    column="business_zone"    />
        <result property="businessZoneName"    column="business_zone_name"    />
        <result property="jdxj"    column="jdxj"    />
        <result property="score"    column="score"    />
        <result property="jtid"    column="jtid"    />
        <result property="jtmc"    column="jtmc"    />
        <result property="kysj"    column="kysj"    />
        <result property="zhzxsj"    column="zhzxsj"    />
        <result property="rank"    column="rank"    />
        <result property="createdate"    column="createdate"    />
        <result property="mender"    column="mender"    />
        <result property="savedate"    column="savedate"    />
        <result property="minPrice"    column="min_price"    />
        <result property="noticeInfo"    column="notice_info"    />
        <result property="policyInfo"    column="policy_info"    />
        <result property="facilitiesInfo"    column="facilities_info"    />
        <result property="reserve1"    column="reserve1"    />
        <result property="reserve2"    column="reserve2"    />
        <result property="reserve3"    column="reserve3"    />
        <result property="reserve4"    column="reserve4"    />
        <result property="reserve5"    column="reserve5"    />
        <result property="reserve6"    column="reserve6"    />
        <result property="reserve7"    column="reserve7"    />
        <result property="reserve8"    column="reserve8"    />
        <result property="reserve9"    column="reserve9"    />
        <result property="reserve0"    column="reserve0"    />
        <result property="recommendLevel"    column="recommend_level"    />
        <result property="countryId"    column="country_id"    />
        <result property="countryName"    column="country_name"    />
        <result property="provinceId"    column="province_id"    />
        <result property="provinceName"    column="province_name"    />
        <result property="sparkle"    column="sparkle"    />
        <result property="aiDocumentId"    column="ai_document_id"    />
        <result property="originId"    column="origin_id"    />
        <result property="roundInfo"    column="round_info"    />
    </resultMap>

    <resultMap type="com.ltgj.ivw.dto.JdJdbRepeatDTO" id="JdJdbRepeatDTOResult">
        <result property="jdmc"    column="jdmc"    />
        <result property="jddz"    column="jddz"    />
        <result property="cityId"    column="city_id"    />
        <result property="duplicateCount"    column="duplicate_count"    />
    </resultMap>

    <sql id="selectJdJdbxjscore">
        select a.id,a.img_url,a.brand_id, a.brand_name, a.jdxj, a.score, a.jtid, a.jtmc, a.kysj, a.zhzxsj, a.createdate, a.mender, b.plat_id,a.`rank`,
    </sql>

    <sql id="selectJdJdb">
        select DISTINCT a.id, a.jdmc, a.jdmc_en, a.jdlx, a.jddz, a.jddh, a.img_url, a.status, a.lon_google, a.lat_google, a.lon_baidu, a.lat_baidu, a.city_id, a.city_name, a.brand_id, a.brand_name, a.district, a.district_name, a.business_zone, a.business_zone_name, a.jdxj, a.score, a.jtid, a.jtmc, a.kysj, a.zhzxsj, a.createdate, a.mender, a.`rank`,a.jddz_en,a.notice_info,a.policy_info,a.facilities_info,a.recommend_level,
    </sql>
    <sql id="selectJdJdbVo">
        select id, interfacePlat, jdmc, jdmc_en, jdlx, jddz, jddz_en, jddh, img_url, status, lon_google, lat_google, lon_baidu, lat_baidu, lon_gaode, lat_gaode, city_id, city_name, brand_id, brand_name, district, district_name, business_zone, business_zone_name, jdxj, score, jtid, jtmc, kysj, zhzxsj, createdate, mender, `rank`, savedate, min_price, reserve1, reserve2, reserve3, reserve4, reserve5, reserve6, reserve7, reserve8, reserve9, reserve0, recommend_level, country_id, country_name, province_id, province_name, sparkle, ai_document_id, origin_id, round_info, notice_info, policy_info, facilities_info from jd_jdb
    </sql>

    <sql id="selectJdJdbListVo">
        select id, interfacePlat, jdmc, jdlx, jddz, jddh, img_url, status, lon_baidu, lat_baidu, lon_gaode, lat_gaode, city_id, city_name, brand_id, brand_name, district, district_name, jtmc, min_price, `rank`, reserve2, reserve0, savedate from jd_jdb
    </sql>

    <select id="getJdJdbByPage"  resultMap="JdJdbResult">
        <include refid="selectJdJdb" />
        GROUP_CONCAT(m.interface_plat SEPARATOR ',') AS interfacePlat
        FROM jd_jdb a INNER JOIN zh_jd_jdb_mapping m ON a.id = m.local_id
        GROUP BY a.id ORDER BY	a.id LIMIT #{number},#{pageSize}
    </select>


    <select id="getInterfacePlatById" resultMap="JdJdbResult">
        <include refid="selectJdJdb" /> GROUP_CONCAT(m.interface_plat SEPARATOR ',') AS interfacePlat
        FROM jd_jdb a INNER JOIN zh_jd_jdb_mapping m ON a.id = m.local_id
        where a.id = #{id}
        GROUP BY a.id ORDER BY	a.id
    </select>

    <select id="getJdJdbByIds" parameterType="String" resultMap="JdJdbResult">
        SELECT DISTINCT t.*,
        GROUP_CONCAT(t.interface_plat SEPARATOR ',') AS interfacePlat
        FROM (
            SELECT a.*, m.interface_plat
            FROM jd_jdb a
            LEFT JOIN zh_jd_jdb_mapping m ON a.id = m.local_id
            WHERE a.id IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        ) t
        GROUP BY t.id
    </select>
    <select id="getJdJdbByIdsNew" parameterType="String" resultMap="JdJdbResult">
        SELECT * from jd_jdb
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByBeanCount" resultType="integer" >
        SELECT COUNT(DISTINCT b.local_id)
        FROM jd_jdb a
                 INNER JOIN zh_jd_jdb_mapping b ON a.id = b.local_id and b.status != 1;
    </select>

    <select id="findJdJdbCount" resultType="integer" >
        SELECT COUNT(DISTINCT b.local_id)
            FROM jd_jdb a
        INNER JOIN zh_jd_jdb_mapping b ON a.id = b.local_id and b.status != 1;
    </select>

    <select id="selectJdJdbList" parameterType="JdJdb" resultMap="JdJdbResult">
        <include refid="selectJdJdbListVo"/>
        <where>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="interfacePlat != null  and interfacePlat != ''"> and interfacePlat = #{interfacePlat}</if>
            <if test="jdmc != null  and jdmc != ''"> and jdmc like concat('%', #{jdmc}, '%')</if>
            <if test="jdmcEn != null  and jdmcEn != ''"> and jdmc_en = #{jdmcEn}</if>
            <if test="jdlx != null  and jdlx != ''"> and jdlx like concat('%', #{jdlx}, '%')</if>
            <if test="jddz != null  and jddz != ''"> and jddz = #{jddz}</if>
            <if test="jddzEn != null  and jddzEn != ''"> and jddz_en = #{jddzEn}</if>
            <if test="jddh != null  and jddh != ''"> and jddh = #{jddh}</if>
            <if test="imgUrl != null  and imgUrl != ''"> and img_url = #{imgUrl}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="lonGoogle != null "> and lon_google = #{lonGoogle}</if>
            <if test="latGoogle != null "> and lat_google = #{latGoogle}</if>
            <if test="lonBaidu != null "> and lon_baidu = #{lonBaidu}</if>
            <if test="latBaidu != null "> and lat_baidu = #{latBaidu}</if>
            <if test="lonGaode != null "> and lon_gaode = #{lonGaode}</if>
            <if test="latGaode != null "> and lat_gaode = #{latGaode}</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="brandId != null  and brandId != ''"> and brand_id = #{brandId}</if>
            <if test="brandName != null  and brandName != ''"> and brand_name is not null</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="districtName != null  and districtName != ''"> and district_name like concat('%', #{districtName}, '%')</if>
            <if test="businessZone != null  and businessZone != ''"> and business_zone = #{businessZone}</if>
            <if test="businessZoneName != null  and businessZoneName != ''"> and business_zone_name like concat('%', #{businessZoneName}, '%')</if>
            <if test="jdxj != null "> and jdxj = #{jdxj}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="jtid != null  and jtid != ''"> and jtid = #{jtid}</if>
            <if test="jtmc != null  and jtmc != ''"> and jtmc = #{jtmc}</if>
            <if test="kysj != null  and kysj != ''"> and kysj = #{kysj}</if>
            <if test="zhzxsj != null  and zhzxsj != ''"> and zhzxsj = #{zhzxsj}</if>
            <if test="createdate != null "> and createdate = #{createdate}</if>
            <if test="mender != null  and mender != ''"> and mender = #{mender}</if>
            <if test="savedate != null "> and savedate = #{savedate}</if>
            <if test="minPrice != null "> and min_price = #{minPrice}</if>
            <if test="reserve1 != null  and reserve1 != ''"> and reserve1 = #{reserve1}</if>
            <if test="reserve2 != null  and reserve2 != ''"> and reserve2 = #{reserve2}</if>
            <if test="reserve3 != null  and reserve3 != ''"> and reserve3 = #{reserve3}</if>
            <if test="reserve4 != null  and reserve4 != ''"> and reserve4 = #{reserve4}</if>
            <if test="reserve5 != null  and reserve5 != ''"> and reserve5 = #{reserve5}</if>
            <if test="reserve6 != null  and reserve6 != ''"> and reserve6 = #{reserve6}</if>
            <if test="reserve7 != null  and reserve7 != ''"> and reserve7 = #{reserve7}</if>
            <if test="reserve8 != null  and reserve8 != ''"> and reserve8 = #{reserve8}</if>
            <if test="reserve9 != null  and reserve9 != ''"> and reserve9 = #{reserve9}</if>
            <if test="reserve0 != null  and reserve0 != ''"> and reserve0 = #{reserve0}</if>
        </where>
    </select>

    <select id="selectJdJdbListNew" parameterType="JdJdb" resultMap="JdJdbResult">
        <include refid="selectJdJdbListVo"/>
        <where>
            <if test="jdmc != null  and jdmc != ''"> and jdmc = #{jdmc}</if>
            <if test="jddz != null  and jddz != ''"> and jddz = #{jddz}</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
        </where>
    </select>

    <select id="selectJdJdbById" parameterType="String" resultMap="JdJdbResult">
        <include refid="selectJdJdbVo"/>
        where id = #{id}
    </select>
    <select id="selectJdJdbByInterfacePlat" parameterType="String" resultMap="JdJdbResult">
        select id from jd_jdb
        where interfacePlat = #{interfacePlat}
    </select>

    <insert id="insertJdJdb" parameterType="JdJdb">
        insert into jd_jdb
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="interfacePlat != null">interfacePlat,</if>
            <if test="jdmc != null and jdmc != ''">jdmc,</if>
            <if test="jdmcEn != null">jdmc_en,</if>
            <if test="jdlx != null">jdlx,</if>
            <if test="jddz != null">jddz,</if>
            <if test="jddzEn != null">jddz_en,</if>
            <if test="jddh != null">jddh,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="status != null">status,</if>
            <if test="lonGoogle != null">lon_google,</if>
            <if test="latGoogle != null">lat_google,</if>
            <if test="lonBaidu != null">lon_baidu,</if>
            <if test="latBaidu != null">lat_baidu,</if>
            <if test="lonGaode != null">lon_gaode,</if>
            <if test="latGaode != null">lat_gaode,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="district != null">district,</if>
            <if test="districtName != null">district_name,</if>
            <if test="businessZone != null">business_zone,</if>
            <if test="businessZoneName != null">business_zone_name,</if>
            <if test="jdxj != null">jdxj,</if>
            <if test="score != null">score,</if>
            <if test="jtid != null">jtid,</if>
            <if test="jtmc != null">jtmc,</if>
            <if test="kysj != null">kysj,</if>
            <if test="zhzxsj != null">zhzxsj,</if>
            <if test="rank != null">rank,</if>
            <if test="createdate != null">createdate,</if>
            <if test="mender != null">mender,</if>
            <if test="savedate != null">savedate,</if>
            <if test="minPrice != null">min_price,</if>
            <if test="noticeInfo != null">notice_info,</if>
            <if test="policyInfo != null">policy_info,</if>
            <if test="facilitiesInfo != null">facilities_info,</if>
            <if test="reserve1 != null">reserve1,</if>
            <if test="reserve2 != null">reserve2,</if>
            <if test="reserve3 != null">reserve3,</if>
            <if test="reserve4 != null">reserve4,</if>
            <if test="reserve5 != null">reserve5,</if>
            <if test="reserve6 != null">reserve6,</if>
            <if test="reserve7 != null">reserve7,</if>
            <if test="reserve8 != null">reserve8,</if>
            <if test="reserve9 != null">reserve9,</if>
            <if test="reserve0 != null">reserve0,</if>
            <if test="recommendLevel != null">recommend_level,</if>
            <if test="countryId != null">country_id,</if>
            <if test="countryName != null">country_name,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="provinceName != null">province_name,</if>
            <if test="sparkle != null">sparkle,</if>
            <if test="aiDocumentId != null">ai_document_id,</if>
            <if test="originId != null">origin_id,</if>
            <if test="roundInfo != null">round_info,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="interfacePlat != null">#{interfacePlat},</if>
            <if test="jdmc != null and jdmc != ''">#{jdmc},</if>
            <if test="jdmcEn != null">#{jdmcEn},</if>
            <if test="jdlx != null">#{jdlx},</if>
            <if test="jddz != null">#{jddz},</if>
            <if test="jddzEn != null">#{jddzEn},</if>
            <if test="jddh != null">#{jddh},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="lonGoogle != null">#{lonGoogle},</if>
            <if test="latGoogle != null">#{latGoogle},</if>
            <if test="lonBaidu != null">#{lonBaidu},</if>
            <if test="latBaidu != null">#{latBaidu},</if>
            <if test="lonGaode != null">#{lonGaode},</if>
            <if test="latGaode != null">#{latGaode},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="district != null">#{district},</if>
            <if test="districtName != null">#{districtName},</if>
            <if test="businessZone != null">#{businessZone},</if>
            <if test="businessZoneName != null">#{businessZoneName},</if>
            <if test="jdxj != null">#{jdxj},</if>
            <if test="score != null">#{score},</if>
            <if test="jtid != null">#{jtid},</if>
            <if test="jtmc != null">#{jtmc},</if>
            <if test="kysj != null">#{kysj},</if>
            <if test="zhzxsj != null">#{zhzxsj},</if>
            <if test="rank != null">#{rank},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="mender != null">#{mender},</if>
            <if test="savedate != null">#{savedate},</if>
            <if test="minPrice != null">#{minPrice},</if>
            <if test="noticeInfo != null">#{noticeInfo},</if>
            <if test="policyInfo != null">#{policyInfo},</if>
            <if test="facilitiesInfo != null">#{facilitiesInfo},</if>
            <if test="reserve1 != null">#{reserve1},</if>
            <if test="reserve2 != null">#{reserve2},</if>
            <if test="reserve3 != null">#{reserve3},</if>
            <if test="reserve4 != null">#{reserve4},</if>
            <if test="reserve5 != null">#{reserve5},</if>
            <if test="reserve6 != null">#{reserve6},</if>
            <if test="reserve7 != null">#{reserve7},</if>
            <if test="reserve8 != null">#{reserve8},</if>
            <if test="reserve9 != null">#{reserve9},</if>
            <if test="reserve0 != null">#{reserve0},</if>
            <if test="recommendLevel != null">#{recommendLevel},</if>
            <if test="countryId != null">#{countryId},</if>
            <if test="countryName != null">#{countryName},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="provinceName != null">#{provinceName},</if>
            <if test="sparkle != null">#{sparkle},</if>
            <if test="aiDocumentId != null">#{aiDocumentId},</if>
            <if test="originId != null">#{originId},</if>
            <if test="roundInfo != null">#{roundInfo},</if>
        </trim>
    </insert>

    <insert id="insertJdJdbZh" parameterType="JdJdb">
        insert into zh_jd_jdb_${params.cityIdYs}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="interfacePlat != null">interfacePlat,</if>
            <if test="jdmc != null and jdmc != ''">jdmc,</if>
            <if test="jdmcEn != null">jdmc_en,</if>
            <if test="jdlx != null">jdlx,</if>
            <if test="jddz != null">jddz,</if>
            <if test="jddzEn != null">jddz_en,</if>
            <if test="jddh != null">jddh,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="status != null">status,</if>
            <if test="lonGoogle != null">lon_google,</if>
            <if test="latGoogle != null">lat_google,</if>
            <if test="lonBaidu != null">lon_baidu,</if>
            <if test="latBaidu != null">lat_baidu,</if>
            <if test="lonGaode != null">lon_gaode,</if>
            <if test="latGaode != null">lat_gaode,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="brandName != null">brand_name,</if>
            <if test="district != null">district,</if>
            <if test="districtName != null">district_name,</if>
            <if test="businessZone != null">business_zone,</if>
            <if test="businessZoneName != null">business_zone_name,</if>
            <if test="jdxj != null">jdxj,</if>
            <if test="score != null">score,</if>
            <if test="jtid != null">jtid,</if>
            <if test="jtmc != null">jtmc,</if>
            <if test="kysj != null">kysj,</if>
            <if test="zhzxsj != null">zhzxsj,</if>
            <if test="rank != null">rank,</if>
            <if test="createdate != null">createdate,</if>
            <if test="mender != null">mender,</if>
            <if test="savedate != null">savedate,</if>
            <if test="minPrice != null">min_price,</if>
            <if test="noticeInfo != null">notice_info,</if>
            <if test="policyInfo != null">policy_info,</if>
            <if test="facilitiesInfo != null">facilities_info,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="interfacePlat != null">#{interfacePlat},</if>
            <if test="jdmc != null and jdmc != ''">#{jdmc},</if>
            <if test="jdmcEn != null">#{jdmcEn},</if>
            <if test="jdlx != null">#{jdlx},</if>
            <if test="jddz != null">#{jddz},</if>
            <if test="jddzEn != null">#{jddzEn},</if>
            <if test="jddh != null">#{jddh},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="lonGoogle != null">#{lonGoogle},</if>
            <if test="latGoogle != null">#{latGoogle},</if>
            <if test="lonBaidu != null">#{lonBaidu},</if>
            <if test="latBaidu != null">#{latBaidu},</if>
            <if test="lonGaode != null">#{lonGaode},</if>
            <if test="latGaode != null">#{latGaode},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="brandName != null">#{brandName},</if>
            <if test="district != null">#{district},</if>
            <if test="districtName != null">#{districtName},</if>
            <if test="businessZone != null">#{businessZone},</if>
            <if test="businessZoneName != null">#{businessZoneName},</if>
            <if test="jdxj != null">#{jdxj},</if>
            <if test="score != null">#{score},</if>
            <if test="jtid != null">#{jtid},</if>
            <if test="jtmc != null">#{jtmc},</if>
            <if test="kysj != null">#{kysj},</if>
            <if test="zhzxsj != null">#{zhzxsj},</if>
            <if test="rank != null">#{rank},</if>
            <if test="createdate != null">#{createdate},</if>
            <if test="mender != null">#{mender},</if>
            <if test="savedate != null">#{savedate},</if>
            <if test="minPrice != null">#{minPrice},</if>
            <if test="noticeInfo != null">#{noticeInfo},</if>
            <if test="policyInfo != null">#{policyInfo},</if>
            <if test="facilitiesInfo != null">#{facilitiesInfo},</if>
        </trim>
    </insert>

    <update id="updateJdJdb" parameterType="JdJdb">
        update jd_jdb
        <trim prefix="SET" suffixOverrides=",">
            <if test="interfacePlat != null">interfacePlat = #{interfacePlat},</if>
            <if test="jdmc != null and jdmc != ''">jdmc = #{jdmc},</if>
            <if test="jdmcEn != null">jdmc_en = #{jdmcEn},</if>
            <if test="jdlx != null">jdlx = #{jdlx},</if>
            <if test="jddz != null">jddz = #{jddz},</if>
            <if test="jddzEn != null">jddz_en = #{jddzEn},</if>
            <if test="jddh != null">jddh = #{jddh},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lonGoogle != null">lon_google = #{lonGoogle},</if>
            <if test="latGoogle != null">lat_google = #{latGoogle},</if>
            <if test="lonBaidu != null">lon_baidu = #{lonBaidu},</if>
            <if test="latBaidu != null">lat_baidu = #{latBaidu},</if>
            <if test="lonGaode != null">lon_gaode = #{lonGaode},</if>
            <if test="latGaode != null">lat_gaode = #{latGaode},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="brandName != null">brand_name = #{brandName},</if>
            <if test="district != null">district = #{district},</if>
            <if test="district == null">district = null,</if>
            <if test="districtName != null">district_name = #{districtName},</if>
            <if test="districtName == null">district_name = null,</if>
            <if test="businessZone != null">business_zone = #{businessZone},</if>
            <if test="businessZone == null">business_zone = null,</if>
            <if test="businessZoneName != null">business_zone_name = #{businessZoneName},</if>
            <if test="businessZoneName == null">business_zone_name = null,</if>
            <if test="jdxj != null">jdxj = #{jdxj},</if>
            <if test="score != null">score = #{score},</if>
            <if test="jtid != null">jtid = #{jtid},</if>
            <if test="jtmc != null">jtmc = #{jtmc},</if>
            <if test="kysj != null">kysj = #{kysj},</if>
            <if test="zhzxsj != null">zhzxsj = #{zhzxsj},</if>
            <if test="rank != null">`rank` = #{rank},</if>
            <if test="createdate != null">createdate = #{createdate},</if>
            <if test="mender != null">mender = #{mender},</if>
            <if test="savedate != null">savedate = #{savedate},</if>
            <if test="minPrice != null">min_price = #{minPrice},</if>
            <if test="reserve1 != null">reserve1 = #{reserve1},</if>
            <if test="reserve2 != null">reserve2 = #{reserve2},</if>
            <if test="reserve3 != null">reserve3 = #{reserve3},</if>
            <if test="reserve4 != null">reserve4 = #{reserve4},</if>
            <if test="reserve5 != null">reserve5 = #{reserve5},</if>
            <if test="reserve6 != null">reserve6 = #{reserve6},</if>
            <if test="reserve7 != null">reserve7 = #{reserve7},</if>
            <if test="reserve8 != null">reserve8 = #{reserve8},</if>
            <if test="reserve9 != null">reserve9 = #{reserve9},</if>
            <if test="reserve0 != null">reserve0 = #{reserve0},</if>
            <if test="recommendLevel != null">recommend_level = #{recommendLevel},</if>
            <if test="countryId != null">country_id = #{countryId},</if>
            <if test="countryName != null">country_name = #{countryName},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="sparkle != null">sparkle = #{sparkle},</if>
            <if test="aiDocumentId != null">ai_document_id = #{aiDocumentId},</if>
            <if test="originId != null">origin_id = #{originId},</if>
            <if test="roundInfo != null">round_info = #{roundInfo},</if>
            <if test="noticeInfo != null">notice_info = #{noticeInfo},</if>
            <if test="policyInfo != null">policy_info = #{policyInfo},</if>
            <if test="facilitiesInfo != null">facilities_info = #{facilitiesInfo},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateImageData">
        update zh_jd_jdb
        <set>
            <if test="dto.imgUrl != null and dto.imgUrl != ''">
                img_url = #{dto.imgUrl},
            </if>
            <if test="dto.brandName != null and dto.brandName != ''">
                brand_name = #{dto.brandName},
            </if>
        </set>
        where id = #{dto.id}
    </update>

    <delete id="deleteJdJdbById" parameterType="String">
        delete from jd_jdb where id = #{id}
    </delete>

    <delete id="deleteJdJdbByIdZh" parameterType="String">
        delete from zh_jd_jdb_${cityIdYs} where id = #{id}
    </delete>

    <delete id="deleteJdJdbByIds" parameterType="String">
        delete from jd_jdb where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAllByCityIdYs" parameterType="String">
        delete from zh_jd_jdb_${cityIdYs}
    </delete>

    <select id="getThirdHotelId" resultType="java.lang.String">
        SELECT
        b.plat_id AS id
        FROM
        jd_jdb a
        INNER JOIN zh_jd_jdb_mapping b ON a.id = b.local_id
        WHERE b.interface_plat in (<foreach collection="interfacePlats" separator="," item="item">#{item}</foreach>)
        ORDER BY a.id
        LIMIT #{number},#{pageSize}
    </select>
    <select id="getHotelIdList" resultType="java.lang.String">
        SELECT id
        FROM jd_jdb
        ORDER BY id
        LIMIT #{number},#{pageSize}
    </select>
    <select id="selectInIds" resultType="java.lang.String">
        select id from jd_jdb
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAllJson" resultType="java.lang.String">
        select CONCAT('{"id": "',id,'", "name": "',jdmc,'", "phone": "',jddh,'", "address": "',jddz,'", "city": "',city_name,'","platform":"',interfacePlat,'"}')
        from jd_jdb
        order by id
        limit #{start}, #{pageSize}
    </select>
    <select id="selectIdList" resultType="java.lang.String">
        select id
        from jd_jdb
        where id > #{minId}
        order by id asc
        limit #{pageNumber}
    </select>

    <select id="selectJdJdbByPlaId" resultType="JdJdb">
        select a.* from jd_jdb a join jd_jdb_mapping m on m.local_id = a.id WHERE m.interface_plat = #{plat,jdbcType=VARCHAR} and m.plat_id = #{platId,jdbcType=VARCHAR}
    </select>

    <update id="updateBatch">
        update jd_jdb
        <set>
            jddz = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.jddz}
            </foreach>
            END,
            jdmc = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.jdmc}
            </foreach>
            END,
            jddh = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.jddh}
            </foreach>
            END,
            province_id  = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.provinceId}
            </foreach>
            END,
            province_name  = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.provinceName}
            </foreach>
            END,
            country_id  = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.countryId}
            </foreach>
            END,
            country_name  = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.countryName}
            </foreach>
            END,
            savedate=NOW()
        </set>
        where id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateBatchJdbDocumentId">
        update jd_jdb
        <set>
            ai_document_id = CASE id
            <foreach collection="list" item="item">
                WHEN #{item.id} THEN #{item.aiDocumentId}
            </foreach>
            END
        </set>
        where id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updateById" parameterType="com.ltgj.ivw.domain.JdJdb">
    UPDATE jd_jdb
    SET
        interfacePlat = #{interfacePlat},
        jdmc = #{jdmc},
        jdmc_en = #{jdmcEn},
        jdlx = #{jdlx},
        jddz = #{jddz},
        jddz_en = #{jddzEn},
        jddh = #{jddh},
        img_url = #{imgUrl},
        status = #{status},
        lon_google = #{lonGoogle},
        lat_google = #{latGoogle},
        lon_baidu = #{lonBaidu},
        lat_baidu = #{latBaidu},
        lon_gaode = #{lonGaode},
        lat_gaode = #{latGaode},
        city_id = #{cityId},
        city_name = #{cityName},
        brand_id = #{brandId},
        brand_name = #{brandName},
        district = #{district},
        district_name = #{districtName},
        business_zone = #{businessZone},
        business_zone_name = #{businessZoneName},
        jdxj = #{jdxj},
        score = #{score},
        jtid = #{jtid},
        jtmc = #{jtmc},
        kysj = #{kysj},
        zhzxsj = #{zhzxsj},
        rank = #{rank},
        createdate = #{createdate},
        mender = #{mender},
        savedate = #{savedate},
        min_price = #{minPrice},
        reserve1 = #{reserve1},
        reserve2 = #{reserve2},
        reserve3 = #{reserve3},
        reserve4 = #{reserve4},
        reserve5 = #{reserve5},
        reserve6 = #{reserve6},
        reserve7 = #{reserve7},
        reserve8 = #{reserve8},
        reserve9 = #{reserve9},
        reserve0 = #{reserve0},
        recommend_level = #{recommendLevel},
        country_id = #{countryId},
        country_name = #{countryName},
        province_id = #{provinceId},
        province_name = #{provinceName},
        sparkle = #{sparkle},
        ai_document_id = #{aiDocumentId},
        origin_id = #{originId},
        round_info = #{roundInfo},
        notice_info = #{noticeInfo},
        policy_info = #{policyInfo},
        facilities_info = #{facilitiesInfo}
    WHERE id = #{id}
    </update>
    <update id="updateKnowledgeIdByCityIdAndDate">
        UPDATE jd_jdb
        SET ai_dataset_id = #{knowledgeId}
        WHERE city_id = #{cityId}
          and savedate >= #{date}
    </update>


    <select id="selectJdJdbRepeatByJdmcAndJddz"  resultMap="JdJdbRepeatDTOResult">
        SELECT jdmc, jddz, city_id, COUNT(*) AS duplicate_count
        FROM jd_jdb
        <where>
            <if test="jdmc != null and jdmc!=''">
                and jdmc=#{jdmc}
            </if>
            <if test="cityId != null and cityId!=''">
                and city_id=#{cityId}
            </if>
        </where>
        GROUP BY jdmc, jddz, city_id
        HAVING duplicate_count > 1
    </select>

    <select id="selectAllJsonForScore" resultType="java.lang.String">
        select CONCAT('{"id": "',id,'", "name": "',name,'", "phone": "',phone,'", "address": "',address_line,'", "city": "',city_name,'"}')
        from tmp_ai_scoring
        where vendor_type=1
        limit #{start}, #{pageSize}
    </select>

    <select id="selectBatchByIds" resultMap="JdJdbResult">
        <include refid="selectJdJdbVo"/>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectAll" resultType="com.ltgj.ivw.domain.JdJdb">
        select * from jd_jdb where province_id is null
        limit #{start}, #{pageSize}
    </select>
    <select id="selectCityId" resultType="java.lang.String">
        select city_id from jd_jdb group by city_id
    </select>
    <select id="selectByCityId" resultType="com.ltgj.ivw.domain.JdJdb">
        select
        id,jdmc,jddh,jddz,city_name, interfacePlat
        from jd_jdb
        <where>
            <!-- 如果 cityId 不是 null，则使用 city_id = #{cityId} -->
            <!-- 如果 cityId 是 null，则使用 city_id IS NULL -->
            <choose>
                <when test="cityId != null and cityId != ''">
                    city_id = #{cityId}
                </when>
                <otherwise>
                    city_id IS NULL
                </otherwise>
            </choose>
            <if test="date!=null">
                and createdate >= #{date}
            </if>
        </where>
    </select>
    <select id="count" resultType="java.lang.Integer">
        select count(*)
        from jd_jdb
        where province_id is null
    </select>
    <select id="listByIds" resultType="com.ltgj.ivw.domain.JdJdb">
        select * from jd_jdb where id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
