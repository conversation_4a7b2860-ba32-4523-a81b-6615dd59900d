# 地理位置四级联动SQL修复说明

## 问题诊断

根据错误信息：
```
java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '1=1 
        ORDER BY geo_name' at line 8
```

问题出现在MyBatis动态SQL的WHERE条件拼接中，当多个条件同时存在时，会出现孤立的"1=1"导致语法错误。

## 表结构分析

根据用户提供的表结构，四级联动关系如下：

### 1. geo_province (省份表)
- `nation` - 国家ID
- `nation_name` - 国家名称  
- `id` - 省份ID
- `province_name` - 省份名称

### 2. b_city (城市表)
- `nation` - 国家ID
- `province` - 省份ID
- `id` - 城市ID
- `city_name` - 城市名称

### 3. geo_city_geo (地区表)
- `city_id` - 城市ID
- `geo_id` - 地区ID
- `geo_name` - 地区名称
- `geo_type` - 地区类型(1商圈 2行政区 3标志物)

## 修复内容

### 1. GeoCityGeoMapper.xml
修复前：
```xml
<where>
    <if test="cityId != null and cityId != ''">
        city_id = #{cityId}
    </if>
    <if test="geoType != null">
        <if test="cityId != null and cityId != ''">AND</if>
        geo_type = #{geoType}
    </if>
</where>
```

修复后：
```xml
<where>
    <if test="cityId != null and cityId != ''">
        AND city_id = #{cityId}
    </if>
    <if test="geoType != null">
        AND geo_type = #{geoType}
    </if>
</where>
```

### 2. BCityMapper.xml
修复前：
```xml
<where>
    <if test="provinceId != null and provinceId != ''">
        province = #{provinceId}
    </if>
    <if test="validOnly == true">
        <if test="provinceId != null and provinceId != ''">AND</if>
        (del_flag is null OR del_flag = 0)
    </if>
</where>
```

修复后：
```xml
<where>
    <if test="provinceId != null and provinceId != ''">
        AND province = #{provinceId}
    </if>
    <if test="validOnly == true">
        AND (del_flag is null OR del_flag = 0)
    </if>
</where>
```

### 3. GeoProvinceMapper.xml
修复前：
```xml
<where>
    <if test="countryId != null and countryId != ''">
        nation = #{countryId}
    </if>
    <if test="validOnly == true">
        <if test="countryId != null and countryId != ''">AND</if>
        (del_flag is null OR del_flag = '' OR del_flag = '0')
    </if>
</where>
```

修复后：
```xml
<where>
    <if test="countryId != null and countryId != ''">
        AND nation = #{countryId}
    </if>
    <if test="validOnly == true">
        AND (del_flag is null OR del_flag = '' OR del_flag = '0')
    </if>
</where>
```

## 修复原理

MyBatis的`<where>`标签会自动处理第一个AND关键字，所以所有条件都可以统一使用`AND`开头，避免复杂的条件判断。

## 新增功能

### 1. 地区类型查询
在GeoCityGeoMapper.xml中新增了`selectDistrictsByType`方法，支持按地区类型查询商圈、行政区或标志物。

### 2. 层级关系映射
根据实际表结构，正确映射了四级联动关系：
- 国家 (geo_province.nation)
- 省份 (geo_province.id)  
- 城市 (b_city.id)
- 地区 (geo_city_geo.geo_id)

## 测试建议

建议测试以下场景：
1. 仅传入cityId参数
2. 仅传入geoType参数  
3. 同时传入cityId和geoType参数
4. 参数为空的情况

确保所有场景下SQL语法都正确。 