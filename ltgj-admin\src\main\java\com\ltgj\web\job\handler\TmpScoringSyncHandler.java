package com.ltgj.web.job.handler;

import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.AiMappingService;
import com.ltgj.quartz.domain.TmpAiScoring;
import com.ltgj.quartz.task.AIScoringTask;
import com.ltgj.quartz.task.HotelIncrementTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@JobHandler(value = "tmpScoringSyncHandler")
public class TmpScoringSyncHandler extends IJobHandler {

    @Resource
    private AIScoringTask scoringTask;
    @Resource
    private AiMappingService aiMappingService;
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try{
            aiMappingService.aiKeTanMapping();
        }catch (Exception e) {
            log.error("ai打分同步任务异常：{}", e);
        }
        return ReturnT.SUCCESS;
    }
    public TmpScoringSyncHandler() {
    }
}
