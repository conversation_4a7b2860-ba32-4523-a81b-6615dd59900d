package com.ltgj.supplier.common.gn.mapper;

import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * @author: hp.long
 * @create: 2024-12-23 11:45
 */
public interface HotelGnBaseMapper {

    int addOrUpdateBatch(@Param("tableSuffix") String tableSuffix, @Param("list") List<HotelGnBase> list);

    int addOrUpdateBatchNoFunction(@Param("tableSuffix") String tableSuffix, @Param("list") List<HotelGnBase> list);

    HotelGnBase selectById(@Param("tableSuffix") String tableSuffix, @Param("id") String id);

    List<HotelGnBase> selectByIdList(@Param("tableSuffix") String tableSuffix, @Param("idList") List<String> idList);

    // 返回字段不会包含reverse0, 原始报文太大
    List<HotelGnBase> selectHotelGnBaseList(@Param("tableSuffix") String tableSuffix, @Param("hotelGnBase") HotelGnBase hotelGnBase);

    @Delete("update hotel_gn_${tableSuffix} set is_delete = 1, status = 7 where id = #{id} ")
    int deleteHotelGnById(@Param("tableSuffix") String tableSuffix, @Param("id") String id);

    int insertSelective(@Param("tableSuffix") String tableSuffix, @Param("hotelGnBase") HotelGnBase hotelGnBase);

    int updateByPrimaryKeySelective(@Param("tableSuffix") String tableSuffix, @Param("hotelGnBase") HotelGnBase hotelGnBase);

    List<HotelGnBase> selectAll(@Param("tableSuffix") String tableSuffix);
    List<HotelGnBase> selectAllId(@Param("tableSuffix") String tableSuffix);

    List<HotelGnBase> selectByIncrementStatus(@Param("tableSuffix") String tableSuffix, @Param("incrementStatus") int incrementStatus);

    List<HotelGnBase> selectByStatus(@Param("tableSuffix") String tableSuffix, @Param("status") int status);

    @Select("select count(1) from hotel_gn_${tableSuffix} where is_delete = 0")
    int selectCount(@Param("tableSuffix") String tableSuffix);

    @Select("select id from hotel_gn_${tableSuffix} where is_delete = 0 order by id limit #{start}, #{size}")
    List<String> selectIdsByPage(@Param("tableSuffix") String tableSuffix, @Param("start") int start, @Param("size") int size);
    int updateStatusById(@Param("tableSuffix") String tableSuffix, @Param("id") String id, @Param("status") Integer status, @Param("updateBy") String updateBy, @Param("updateTime") Date updateTime);

    List<HotelGnBase> selectByLimit(@Param("tableSuffix") String tableSuffix, @Param("offset") Integer offset, @Param("limit") Integer limit
            , @Param("notStatus") Integer notStatus, @Param("minId") String minId, @Param("maxId") String maxId);

    List<HotelGnBase> selectByCityIds(@Param("tableSuffix") String tableSuffix, @Param("cityIds") List<String> cityIds);

}
