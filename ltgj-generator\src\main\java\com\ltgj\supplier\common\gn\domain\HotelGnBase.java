package com.ltgj.supplier.common.gn.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ltgj.common.annotation.Excel;
import com.ltgj.common.utils.DateUtils;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 酒店-国内-基础
 * 对应hotel_gn_xxxx
 * hotel_gn_ketan 、hotel_gn_hsjl 等
 */
@Data
public class HotelGnBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Excel(name = "id")
    private String id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    private String nameEn;

    /**
     * 酒店类型
     */
    @Excel(name = "酒店类型")
    private Integer typeId;

    /**
     * 开业时间
     */
    @Excel(name = "开业时间")
    private String openDate;

    /**
     * 最后装修时间
     */
    @Excel(name = "最后装修时间")
    private String decorationDate;

    /**
     * 电话
     */
    @Excel(name = "电话")
    private String phone;

    /**
     * 国家id
     */
    @Excel(name = "国家id")
    private String countryId;

    /**
     * 国家名称
     */
    @Excel(name = "国家名称")
    private String countryName;

    /**
     * 省份id
     */
    @Excel(name = "省份id")
    private String provinceId;

    /**
     * 省份名称
     */
    @Excel(name = "省份名称")
    private String provinceName;

    /**
     * 城市id
     */
    @Excel(name = "城市id")
    private String cityId;

    /**
     * 城市名称
     */
    @Excel(name = "城市名称")
    private String cityName;

    /**
     * 区县id
     */
    @Excel(name = "区县id")
    private String areaId;

    /**
     * 区县名称
     */
    @Excel(name = "区县名称")
    private String areaName;

    /**
     * 商圈id
     */
    @Excel(name = "商圈id")
    private String businessDistrictId;

    /**
     * 商圈名称
     */
    @Excel(name = "商圈名称")
    private String businessDistrictName;

    /**
     * 地址
     */
    @Excel(name = "地址")
    private String address;

    /**
     * 地址(英文)
     */
    @Excel(name = "地址(英文)")
    private String addressEn;

    /**
     * 经度(谷歌)
     */
    @Excel(name = "经度(谷歌)")
    private BigDecimal lonGoogle;

    /**
     * 纬度(谷歌)
     */
    @Excel(name = "纬度(谷歌)")
    private BigDecimal latGoogle;

    /**
     * 经度(百度)
     */
    @Excel(name = "经度(百度)")
    private BigDecimal lonBaidu;

    /**
     * 纬度(百度)
     */
    @Excel(name = "纬度(百度)")
    private BigDecimal latBaidu;

    /**
     * 经度(高德)
     */
    @Excel(name = "经度(高德)")
    private BigDecimal lonGaode;

    /**
     * 纬度(高德)
     */
    @Excel(name = "纬度(高德)")
    private BigDecimal latGaode;

    /**
     * 所属集团id
     */
    @Excel(name = "所属集团id")
    private String groupId;

    /**
     * 所属集团名称
     */
    @Excel(name = "所属集团名称")
    private String groupName;

    /**
     * 品牌信息
     */
    @Excel(name = "品牌信息")
    private String brandId;

    /**
     * 品牌信息
     */
    @Excel(name = "品牌信息")
    private String brandName;

    /**
     * 星级
     */
    @Excel(name = "星级")
    private String star;

    /**
     * 酒店首图
     */
    @Excel(name = "酒店首图")
    private String image;

    /**
     * 星级(重复字段，可能与score字段重复)
     */
    @Excel(name = "星级(重复字段，可能与score字段重复)")
    private String score;

    /**
     * 简介
     */
    @Excel(name = "简介")
    private String synopsis;

    /**
     * 亮点
     */
    @Excel(name = "亮点")
    private String sparkle;

    /**
     * 政策信息，包括：入住须知、入离政策、外宾政策、儿童政策、宠物政策等
     */
    @Excel(name = "政策信息，包括：入住须知、入离政策、外宾政策、儿童政策、宠物政策等")
    private String policyInfo;

    /**
     * 设施信息，包括：停车场、充电桩、电梯、行李寄存、餐厅、会议室、公共区域wifi、健身房、洗衣房等
     */
    @Excel(name = "设施信息，包括：停车场、充电桩、电梯、行李寄存、餐厅、会议室、公共区域wifi、健身房、洗衣房等")
    private String facilitiesInfo;

    /**
     * 周边信息，包括：车站机场、景点、美食、购物
     */
    @Excel(name = "周边信息，包括：车站机场、景点、美食、购物")
    private String roundInfo;

    /**
     * 状态，0：无详情；1：未映射；7：验证无效；8：映射成功；9：映射失败；
     */
    @Excel(name = "状态，0：无详情；1：未映射；7：验证无效；8：映射成功；9：映射失败；")
    private Integer status;

    /**
     * 删除标识，0：未删除；1：删除
     */
    @Excel(name = "删除标识，0：未删除；1：删除")
    private Integer isDelete;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 增量状态 0初始化 1 新增未处理 2 新增已处理
     */
    @Excel(name = "增量状态 0初始化 1 新增未处理 2 新增已处理")
    private Integer incrementStatus;

    /**
     * 增量时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "增量时间")
    private Date incrementTime;

    /**
     * 增量类型 0初始化 1上架 2更新 3下架
     */
    @Excel(name = "增量类型 0初始化 1上架 2更新 3下架")
    private Integer incrementType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "创建时间")
    private Date createTime;

    /**
     * 最后更新时间
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "最后更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @Excel(name = "更新人")
    private String updateBy;

    /**
     * 预留字段0
     */
    @Excel(name = "预留字段0")
    private String reserve0;

    /**
     * 预留字段1
     */
    @Excel(name = "预留字段1")
    private String reserve1;

    /**
     * 预留字段2
     */
    @Excel(name = "预留字段2")
    private String reserve2;

    /**
     * 预留字段3
     */
    @Excel(name = "预留字段3")
    private String reserve3;

    /**
     * 预留字段4
     */
    @Excel(name = "预留字段4")
    private String reserve4;

    /**
     * 预留字段5
     */
    @Excel(name = "预留字段5")
    private String reserve5;

    /**
     * 预留字段6
     */
    @Excel(name = "预留字段6")
    private String reserve6;

    /**
     * 预留字段7
     */
    @Excel(name = "预留字段7")
    private String reserve7;

    /**
     * 预留字段8
     */
    @Excel(name = "预留字段8")
    private String reserve8;

    /**
     * 预留字段9
     */
    @Excel(name = "预留字段9")
    private String reserve9;


}