package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.exception.ServiceException;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.FileSplitter;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.chalvgj.*;
import com.ltgj.ivw.domain.response.HSJSResponse;
import com.ltgj.ivw.domain.response.HotelLowestPrices;
import com.ltgj.ivw.domain.response.PriceItems;
import com.ltgj.ivw.domain.response.QtMinPriceResponse;
import com.ltgj.ivw.dto.HotelCheckImageDto;
import com.ltgj.ivw.dto.JdJdbRepeatDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.event.InterfacePlatEvent;
import com.ltgj.ivw.mapper.HotelInfoCozytimeMapper;
import com.ltgj.ivw.mapper.JdJdbMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.AsciiUtils;
import com.ltgj.ivw.utils.CoordinateValidator;
import com.ltgj.ivw.utils.DownloadAndUnzip;
import com.ltgj.ivw.utils.ListUtil;
import com.ltgj.ivw.utils.MyTools;
import com.ltgj.ivw.utils.hotelApi.*;
import com.ltgj.limiter.LtgjRateLimiter;
import com.ltgj.sdk.cozyTime.CozyTimeSdkApi;
import com.ltgj.sdk.cozyTime.model.price.*;
import com.ltgj.supplier.common.domain.FacilitiesInfo;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.redisson.api.RLock;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ltgj.common.core.domain.AjaxResult.success;
import static com.ltgj.common.utils.SecurityUtils.getUsername;
import static com.ltgj.ivw.utils.hotelApi.QiantaoApi.clientIdKey;

/**
 * 酒店基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Service
@Slf4j
public class JdJdbServiceImpl implements IJdJdbService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private JdJdbMapper jdJdbMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource(name = "threadPoolTaskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource(name = "threadPoolTaskExecutor2")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor2;

    @Autowired
    private IHotelInfoChailvgjService hotelInfoChailvgjService;

    @Autowired
    private IHotelInfoHsjlxyService hotelInfoHsjlxyService;

    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;

    @Autowired
    private IZhJdJdbMinPriceService zhJdJdbMinPriceService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private IHotelCityMappingService hotelCityMappingService;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;

    @Autowired
    private HotelGnBaseService hotelGnBaseService;

    @Autowired
    private LtgjRateLimiter limiter;
    @Value("${ones.batch.count:100}")
    private Integer batchCount;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 30天数据
     */
    final static int noMappingDays = 30;


    public static String PROJECT_NAME = "ivw_hotel";

    public static int MAX_TIME = 5;

    @Resource
    private ThreadPoolTaskExecutor cozytimePriceExecutor;

    @Resource
    private HotelInfoCozytimeMapper hotelInfoCozytimeMapper;

    @Autowired
    private CozyTimeSdkApi cozyTimeSdkApi;

    /**
     * 查询酒店基础信息
     *
     * @param id 酒店基础信息主键
     * @return 酒店基础信息
     */
    @Override
    public JdJdb selectJdJdbById(String id) {
        JdJdb jdJdb = this.jdJdbMapper.selectJdJdbById(id);
        if (Objects.isNull(jdJdb)) {
            return null;
        }
        if (StringUtils.isBlank(jdJdb.getFacilitiesInfo())) {
            jdJdb.setFacilitiesInfo(JSONObject.toJSONString(FacilitiesInfo.builder().build()));
        }
        if (StringUtils.isBlank(jdJdb.getPolicyInfo())) {
            jdJdb.setPolicyInfo(JSONObject.toJSONString(PolicyInfo.builder().build()));
        }
        return jdJdb;
    }

    /**
     * 查询酒店基础信息
     *
     * @param interfacePlat 酒店平台标识
     * @return 酒店基础信息
     */
    @Override
    public List<JdJdb> selectJdJdbByInterfacePlat(String interfacePlat) {
        return this.jdJdbMapper.selectJdJdbByInterfacePlat(interfacePlat);
    }

    @Override
    public void updateAllForeElements(List<JdJdb> list) {
        jdJdbMapper.updateBatch(list);
    }

    /**
     * 查询酒店基础信息列表
     *
     * @param jdJdb 酒店基础信息
     * @return 酒店基础信息
     */
    @Override
    public List<JdJdb> selectJdJdbList(JdJdb jdJdb) {
        return this.jdJdbMapper.selectJdJdbList(jdJdb);
    }

    @Override
    public List<JdJdb> selectJdJdbListNew(JdJdb jdJdb) {
        return this.jdJdbMapper.selectJdJdbListNew(jdJdb);
    }

    /**
     * 新增酒店基础信息
     *
     * @param jdJdb 酒店基础信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertJdJdb(JdJdb jdJdb) {
        // 校验经纬度数据
        validateCoordinates(jdJdb);
        
        //id处理
        if (ObjectUtils.isEmpty(jdJdb.getId())) {
            String id = this.generateId(jdJdb.getInterfacePlat());
            jdJdb.setId(id);
        }
        jdJdb.setId(AsciiUtils.convertStringToAscii(jdJdb.getId()));
        //最低价处理
        if (ObjectUtils.isEmpty(jdJdb.getMinPrice())) {
            jdJdb.setMinPrice(BigDecimal.ZERO);
        }
        this.handlerJdmc(jdJdb);
        // todo 调用四要素进行格式化处理再存储
        return this.jdJdbMapper.insertJdJdb(jdJdb);
    }

    /**
     * 生成id  平台编码+10位随机数
     *
     * @param interfacePlat
     * @return
     */
    private String generateId(String interfacePlat) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(10);

        for (int i = 0; i < 10; i++) {
            sb.append(random.nextInt(10)); // 生成0到9之间的随机数
        }

        String randomNumber = sb.toString();
        return interfacePlat + randomNumber;
    }

    /**
     * 修改酒店基础信息
     *
     * @param jdJdb 酒店基础信息
     * @return 结果
     */
    @Override
    public int updateJdJdb(JdJdb jdJdb) {
        // 校验经纬度数据
        validateCoordinates(jdJdb);
        
        this.handlerJdmc(jdJdb);
        jdJdb.setSavedate(new Date());
        // todo 调用四要素进行格式化处理再存储
        return this.jdJdbMapper.updateJdJdb(jdJdb);
    }

    /**
     * 校验经纬度数据
     *
     * @param jdJdb 酒店基础信息
     */
    private void validateCoordinates(JdJdb jdJdb) {
        // 校验谷歌经纬度
        if (jdJdb.getLonGoogle() != null && !CoordinateValidator.isValidLongitude(jdJdb.getLonGoogle())) {
            throw new ServiceException("谷歌经度格式不正确，经度范围应为 -180 到 180");
        }
        if (jdJdb.getLatGoogle() != null && !CoordinateValidator.isValidLatitude(jdJdb.getLatGoogle())) {
            throw new ServiceException("谷歌纬度格式不正确，纬度范围应为 -90 到 90");
        }
        
        // 校验百度经纬度
        if (jdJdb.getLonBaidu() != null && !CoordinateValidator.isValidLongitude(jdJdb.getLonBaidu())) {
            throw new ServiceException("百度经度格式不正确，经度范围应为 -180 到 180");
        }
        if (jdJdb.getLatBaidu() != null && !CoordinateValidator.isValidLatitude(jdJdb.getLatBaidu())) {
            throw new ServiceException("百度纬度格式不正确，纬度范围应为 -90 到 90");
        }
        
        // 校验高德经纬度
        if (jdJdb.getLonGaode() != null && !CoordinateValidator.isValidLongitude(jdJdb.getLonGaode())) {
            throw new ServiceException("高德经度格式不正确，经度范围应为 -180 到 180");
        }
        if (jdJdb.getLatGaode() != null && !CoordinateValidator.isValidLatitude(jdJdb.getLatGaode())) {
            throw new ServiceException("高德纬度格式不正确，纬度范围应为 -90 到 90");
        }
    }

    /**
     * 批量删除酒店基础信息
     *
     * @param ids 需要删除的酒店基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJdJdbByIds(String[] ids) {
        this.logger.info("### deleteJdJdbByIds_开始删除酒店基础信息");
        return this.jdJdbMapper.deleteJdJdbByIds(ids);
    }

    /**
     * 删除酒店基础信息信息
     *
     * @param id 酒店基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJdJdbById(String id) {
        return this.jdJdbMapper.deleteJdJdbById(id);
    }

    @Override
    public int deleteAllByCityIdYs(String cityIdYs) {
        return this.jdJdbMapper.deleteAllByCityIdYs(cityIdYs);
    }

    @Override
    public void deleteJdJdbByIdZh(String id) {
        this.jdJdbMapper.deleteJdJdbByIdZh(id);
    }

    public AjaxResult listMinPriceHSJLOld(MinPriceReq minPriceReq) {

        String dateKey = MyTools.getDateByCurr(noMappingDays);
        if (null != minPriceReq.getMappingDays()) {
            dateKey = MyTools.getDateByCurr(minPriceReq.getMappingDays());
        }

        String key = PROJECT_NAME + ":" + "jd_min_price_hsjl" + ":" + PlatEnum.PLAT_HSJL.getValue() + ":" + dateKey;
        String runningKey = PROJECT_NAME + ":" + "jd_min_price" + ":" + PlatEnum.PLAT_HSJL.getValue() + ":" + "running";
        String runningValue = this.stringRedisTemplate.opsForValue().get(runningKey);
        if (StringUtils.isNotEmpty(runningValue)) {
            this.logger.error("红色加力最低价正在更新,请勿重复操作");
//            return AjaxResult.error("红色加力最低价正在更新,请勿重复操作");
        }
        if (StringUtils.isEmpty(minPriceReq.getUserName())) {
            minPriceReq.setUserName(getUsername());
        }
        this.stringRedisTemplate.opsForValue().set(runningKey, minPriceReq.getUserName());

        if (CollectionUtils.isEmpty(minPriceReq.getHotelIdList())) {
            HotelInfoHsjl search = new HotelInfoHsjl();
            //search.setStatus(8);//未映射数据
            List<HotelInfoHsjl> list = this.hotelInfoHsjlService.selectHotelInfoHsjlList2(search);
            this.logger.info("红色加力酒店数据数量：{}", list.size());
            List<Long> hotelIdList = list.stream().map(dto -> Long.parseLong(dto.getId())).collect(Collectors.toList());
            minPriceReq.setHotelIdList(hotelIdList);
        }

        if (org.apache.commons.lang3.StringUtils.isEmpty(minPriceReq.getCheckInDate())) {
            //明天
            String tomorrow = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String tenDays = LocalDateTime.now().plusDays(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            minPriceReq.setCheckInDate(tomorrow);
            minPriceReq.setCheckOutDate(tenDays);
        }
        long startTime = System.currentTimeMillis();
        this.logger.info("红色加力最低价开始执行 当前时间：{}", startTime);
        //hotelIdList按照每100个值进行分组
        List<List<Long>> hotelIdList = MyTools.splitList(minPriceReq.getHotelIdList(), this.batchCount);
        LongAdder count = new LongAdder();
        this.logger.info("红色加力count:{},酒店分组数量 {}", count, hotelIdList.size());
        this.asyncForListMinPriceHSJL(minPriceReq, key, runningKey, hotelIdList, count);
        try {
            this.logger.info("红色加力休眠完成 count:{}", count);
            if (count.intValue() == hotelIdList.size()) {
                this.stringRedisTemplate.opsForValue().set(key, "1");
                this.deleteRunningKey(runningKey);
                HotelUpdateStatus.statusUpdateMinPrice = false;
                //发送event
                this.applicationContext.publishEvent(new InterfacePlatEvent(this, PlatEnum.PLAT_HSJL.getValue()));
                this.logger.info("红色加力发布通知事件成功 总耗时为:{}", (System.currentTimeMillis() - startTime) / 1000);

            }
        } catch (Exception e) {
            this.stringRedisTemplate.opsForValue().set(key, "0");
            this.deleteRunningKey(runningKey);
            HotelUpdateStatus.statusUpdateMinPrice = false;
            this.logger.error("红色加力协议最低价暂停异常 errorMessage:{}", e.getMessage(), e);
        }
        return success();
    }

    @Override
    public AjaxResult listMinPriceHSJL(MinPriceReq minPriceReq) {
        String dateKey = MyTools.getDateByCurr(Objects.nonNull(minPriceReq.getMappingDays()) ? minPriceReq.getMappingDays() : noMappingDays);
        String key = PROJECT_NAME + ":" + "jd_min_price_hsjl" + ":" + PlatEnum.PLAT_HSJL.getValue() + ":" + dateKey;
        String runningKey = PROJECT_NAME + "_" + "jd_min_price" + "_" + PlatEnum.PLAT_HSJL.getValue() + "_" + "running1";
        RLock rLock = this.redissonClient.getLock(runningKey);
        Boolean lock = false;
        try {
            lock = rLock.tryLock(2L, 20, TimeUnit.SECONDS);
            if (lock) {
                if (CollectionUtils.isEmpty(minPriceReq.getHotelIdList())) {
                    List<HotelGnBase> list = this.hotelGnBaseService.getAllIdList(PlatEnum.PLAT_HSJL);
                    this.logger.info("红色加力酒店数据总数：{}", list.size());
                    List<Long> hotelIdList = list.stream().map(dto -> Long.parseLong(dto.getId())).collect(Collectors.toList());
                    minPriceReq.setHotelIdList(hotelIdList);
                }
                if (StringUtils.isEmpty(minPriceReq.getCheckInDate())) {
                    String tomorrow = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    String tenDays = LocalDateTime.now().plusDays(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    minPriceReq.setCheckInDate(tomorrow);
                    minPriceReq.setCheckOutDate(tenDays);
                }
                long startTime = System.currentTimeMillis();
                this.logger.info("红色加力最低价开始执行 当前时间：{}", startTime);
                //hotelIdList按照每100个值进行分组
                List<List<Long>> hotelIdList = ListUtil.groupList(minPriceReq.getHotelIdList(), 200);
                LongAdder count = new LongAdder();
                this.logger.info("红色加力count:{},酒店分组数量 {}", count, hotelIdList.size());
                this.asyncForListMinPriceHSJL(minPriceReq, key, runningKey, hotelIdList, count);
            } else {
                this.logger.info("红色加力最低价同步任务已经在执行 。。。。。");
            }
        } catch (Exception e) {
            this.logger.info("红色加力最低价同步异常", e);
        } finally {
            if (lock) {
                try {
                    rLock.unlock();
                } catch (Exception e) {
                    this.logger.info("### 红色加力协议同步最低价_解锁异常,", e);
                }
            }
        }

        return success();
    }

    public void asyncForListMinPriceHSJL(MinPriceReq minPriceReq, String key, String runningKey, List<List<Long>> hotelIdList, LongAdder count) {
        /*hotelIdList.stream().forEach(item -> {
            try {
                exceteListMinPriceHSJL(minPriceReq, key, runningKey, count, item);
            } catch (Exception e) {
                logger.error("任务执行失败", e);
            }
        });*/

        List<CompletableFuture<?>> completableFutures = hotelIdList.stream().map(item -> {
            return CompletableFuture.runAsync(() -> {
                try {
                    MinPriceReq req = new MinPriceReq();
                    req.setCheckOutDate(minPriceReq.getCheckOutDate());
                    req.setCheckInDate(minPriceReq.getCheckInDate());
                    List<List<Long>> lists = MyTools.splitList(item, this.batchCount);
                    for (List<Long> hotelIds : lists) {
                        long startTime = System.currentTimeMillis();
                        req.setHotelIdList(hotelIds);
                        this.execteListMinPriceHSJL(req, count);
                        this.logger.info("红色加力同步100个酒店耗时：{}", (System.currentTimeMillis() - startTime));
                    }
                } catch (Exception e) {
                    this.logger.error("红色加力同步最低价任务执行失败", e);
                }
            }, this.threadPoolTaskExecutor).thenRunAsync(() -> count.increment());
        }).collect(Collectors.toList());

        CompletableFuture<?> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));

        try {
            allOf.get();
        } catch (Exception e) {
            this.logger.error("### JdJdbHsjlCorpServiceImpl_dealWithJdXjScoreInterfacePlatsFutures_任务执行失败:{}", e.getMessage(), e);
        }
    }

    public void limiterData(String key, long second, long num, Consumer<Integer> consumer) {
        try {
            int tryNum = 1;
            RRateLimiter rRateLimiter = this.limiter.getLimiter(key, second, num);
            boolean tryAcquire = rRateLimiter.tryAcquire(1);
            while (!tryAcquire) {
                this.logger.info("当前供应商渠道【{}】限流.......睡眠100ms", key);
                try {
                    Thread.sleep(200);
                } catch (InterruptedException ex) {
                    this.logger.info("睡眠异常。。。。。。。。。。");
                }
                tryNum++;
                tryAcquire = rRateLimiter.tryAcquire(1);
            }
            consumer.accept(tryNum);
        } catch (Exception ex) {
            this.logger.info("限流异常。。。。。。。。。。{}", ex.getMessage(), ex);
        }
    }

    private HSJSResponse reCallHsjlXy(String key, MinPriceReq minPriceReq) {
        HSJSResponse hsjsResponse = null;
        try {
            hsjsResponse = (HSJSResponse) this.fetchDataFunction(PlatEnum.PLAT_HSJL_XY.getValue() + key, 60, 80,
                    (result) -> JSONObject.parseObject(HsjlxyApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class));
            this.logger.info("红色加力协议协议最低价请求参数:{}返回参数: {}", JSONObject.toJSONString(minPriceReq), JSONObject.toJSONString(hsjsResponse));
            while (null != hsjsResponse) {
                if (StringUtils.equals(hsjsResponse.getReturnCode(), "009")) {
                    this.logger.info("红色加力协议最低价请求失败，原因：{}", hsjsResponse.getRetrunMsg());
                    try {
                        this.logger.info("红色加力协议开始睡眠20s");
                        Thread.sleep(10000);
                    } catch (Exception e) {
                        this.logger.error("睡眠20s异常 errorMessage:{}", e.getMessage(), e);
                    }
                    hsjsResponse = (HSJSResponse) this.fetchDataFunction(PlatEnum.PLAT_HSJL_XY.getValue() + key, 60, 80,
                            (result) -> JSONObject.parseObject(HsjlxyApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class));
                    this.logger.info("红色加力协议最低价再次请求返回报文 {}", JSONObject.toJSONString(hsjsResponse));
                } else {
                    this.logger.info("红色加力协议最低价请求成功 responseCode:{}", hsjsResponse.getReturnCode());
                    return hsjsResponse;
                }
            }

        } catch (Exception e) {
            log.error("红色加力协议最低价返回异常", e);
        }

        return hsjsResponse;
    }

    private HSJSResponse reCallHsjl(String key, MinPriceReq minPriceReq) {
        /*HSJSResponse hsjsResponse = (HSJSResponse) fetchDataFunction(PlatEnum.PLAT_HSJL.getValue() + key, 1, 1,
            (result) -> JSONObject.parseObject(HsjlxyApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class));*/
        HSJSResponse hsjsResponse = JSONObject.parseObject(HsjlApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class);
        this.logger.info("红色加力最低价返回 {}", JSONObject.toJSONString(hsjsResponse));
        while (StringUtils.equals(hsjsResponse.getReturnCode(), "009")) {
            try {
                this.logger.info("睡眠20s。。。。。。。。。。。。。。。");
                Thread.sleep(20000);
            } catch (InterruptedException e) {
                this.logger.error("睡眠20s异常。。。。。。。。。。。。。。。");
            }
            hsjsResponse = JSONObject.parseObject(HsjlApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class);
            this.logger.info("红色加力最低价返回 {}", JSONObject.toJSONString(hsjsResponse));
        }
        return hsjsResponse;
    }

    private HSJSResponse reCallHsjlMinPrice(String key, MinPriceReq minPriceReq) {
        HSJSResponse hsjsResponse = (HSJSResponse) this.fetchDataFunction(PlatEnum.PLAT_HSJL.getValue() + key, 60, 90,
                (result) -> JSONObject.parseObject(HsjlApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class));
//        HSJSResponse hsjsResponse = JSONObject.parseObject(HsjlApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class);
        this.logger.info("红色加力最低价返回 {}", JSONObject.toJSONString(hsjsResponse));
        while (StringUtils.equals(hsjsResponse.getReturnCode(), "009")) {
            try {
                this.logger.info("睡眠20s。。。。。。。。。。。。。。。");
                Thread.sleep(20000);
            } catch (InterruptedException e) {
                this.logger.error("睡眠20s异常。。。。。。。。。。。。。。。");
            }
            hsjsResponse = JSONObject.parseObject(HsjlApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class);
            this.logger.info("红色加力最低价返回 {}", JSONObject.toJSONString(hsjsResponse));
        }
        return hsjsResponse;
    }

    private QtMinPriceResponse reCallQiantao(MinPriceReqV2 minPriceReq) {
        QtMinPriceResponse result = QiantaoApi.getMinPrice(minPriceReq);
        this.logger.info("### reCallQiantao_千淘最低价返回 {}", result);

        while (Objects.isNull(result)) {
            if (minPriceReq.getRetryCount() > 2) break;
            this.logger.warn("### reCallQiantao_千淘最低价返回null，重试次数：{}", minPriceReq.getRetryCount());
            try {
                this.logger.info("睡眠10s。。。。。。。。。。。。。。。");
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                this.logger.error("睡眠10s异常。。。。。。。。。。。。。。。");
            }
            result = this.reCallQiantao(minPriceReq);
            minPriceReq.setRetryCount(minPriceReq.getRetryCount() + 1);
            this.logger.info("### reCallQiantao重试第 {} 次", minPriceReq.getRetryCount());
        }
        return result;
    }

    public void execteListMinPriceHSJL(MinPriceReq minPriceReq, LongAdder count) {
        List<ZhJdJdbMinPrice> wysInsertList = new ArrayList<>();
        List<ZhJdJdbMinPrice> ysInsertList = new ArrayList<>();
        List<ZhJdJdbMinPrice> ysUpdateList = new ArrayList<>();

        this.logger.info("红色加力最低价请求参数 {},当前count:{}", JSONObject.toJSONString(minPriceReq), count);
        HSJSResponse hsjsResponse = this.reCallHsjlMinPrice("_queryHotelLowestPrice", minPriceReq);
        if (hsjsResponse.getReturnCode().equals("000")) {
            List<HotelLowestPrices> hotelLowestPrices = hsjsResponse.getBussinessResponse().getHotelLowestPrices();
            this.logger.info("红色加力返回总数 count:{},hotelLowestPrices:{}", count, hotelLowestPrices.size());
            for (HotelLowestPrices hotelLowestPrice : hotelLowestPrices) {
                //1.根据酒店id和PlatEnum.PLAT_HSJL_XY查询jd_jdb_mapping
                ZhJdJdbMapping mapping = new ZhJdJdbMapping();
                mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
                mapping.setPlatId(hotelLowestPrice.getHotelId());
                List<ZhJdJdbMapping> zhJdJdbMappingList = this.zhJdJdbMappingService.selectZhJdJdbMappingList(mapping);
                this.logger.info("红色加力酒店映射结果：size:{}", zhJdJdbMappingList.size());
                if (CollectionUtils.isNotEmpty(zhJdJdbMappingList)) {
                    ZhJdJdbMapping jdJdbMapping = zhJdJdbMappingList.get(0);
                    String localId = jdJdbMapping.getLocalId();
                    this.logger.info("红色加力酒店映射成功 local_id:{},hotel_id:{}", jdJdbMapping.getLocalId(), hotelLowestPrice.getHotelId());
                    //获取localId
                    List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                    for (PriceItems priceItem : priceItems) {
                        BigDecimal salePrice = new BigDecimal(priceItem.getSalePrice());
                        Date date = priceItem.getSaleDate();
                        ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                        zhJdJdbMinPrice.setJdid(localId);
                        zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                        zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL.getValue());
                        List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                        if (CollectionUtils.isEmpty(prices)) {
                            zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                            zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                            zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL.getValue());
                            ysInsertList.add(zhJdJdbMinPrice);
                            //zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                            this.logger.info("红色加力最低价插入成功 localId：{} sxsj：{}", localId, date);
                        } else {
                            this.logger.info("红色加力酒店最低价查询成功 localId：{} sxsj：{},size:{}", localId, date, prices.size());
                            zhJdJdbMinPrice = prices.get(0);
                            if (zhJdJdbMinPrice.getMinPrice().intValue() - salePrice.intValue() == 0) {
                                //不需要更新最低价
                                this.logger.info("红色加力当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), salePrice);

                            } else {
                                zhJdJdbMinPrice.setSxsj(date);
                                zhJdJdbMinPrice.setMinPrice(salePrice);
                                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL.getValue());
                                ysUpdateList.add(zhJdJdbMinPrice);
                                //zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                                this.logger.info("红色加力最低价更新成功 localId：{},plateId:{}, sxsj：{}", localId, hotelLowestPrice.getHotelId(), date);
                            }
                        }
                    }
                } else {
                    String hotelId = hotelLowestPrice.getHotelId();
                    String localId = PlatEnum.PLAT_HSJL.getValue() + hotelId;
     //               this.logger.info("红色加力未映射酒店数据最低价,不处理 localId:{} hotelId:{}", localId, hotelLowestPrice.getHotelId());
                    this.logger.info("红色加力未映射酒店数据最低价插入成功开始 localId:{} hotelId:{}", localId, hotelLowestPrice.getHotelId());
                    List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                    for (PriceItems priceItem : priceItems) {
                        ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                        zhJdJdbMinPrice.setJdid(localId);
                        zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                        zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                        zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL.getValue());
                        wysInsertList.add(zhJdJdbMinPrice);
                        //zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                        this.logger.info("红色加力未映射酒店数据最低价插入成功 local_id:{}", zhJdJdbMinPrice.getJdid());
                    }
                }

            }

            //批量插入
            if (CollectionUtils.isNotEmpty(ysInsertList)) {
                int ysCount = this.zhJdJdbMinPriceService.insertBatch(ysInsertList);
                if (ysCount > 0) this.logger.info("红色加力最低价批量插入成功");
            }
            if (CollectionUtils.isNotEmpty(wysInsertList)) {
                int wysCount = this.zhJdJdbMinPriceService.insertBatch(wysInsertList);
                if (wysCount > 0) this.logger.info("红色加力未映射酒店数据最低价批量插入成功");
            }
            //批量更新
            if (CollectionUtils.isNotEmpty(ysUpdateList)) {
                int ysUpdateCount = this.zhJdJdbMinPriceService.updateBatch(ysUpdateList);
                if (ysUpdateCount > 0) this.logger.info("红色加力最低价批量更新成功");
            }

        } else {
            this.logger.error("红色加力供应商最低价返回异常 {},当前count:{}", JSONObject.toJSONString(hsjsResponse), count);
        }
    }

    @Override
    public AjaxResult listMinPriceHSJLXY(MinPriceReq minPriceReq) {
        String dateKey = MyTools.getDateByCurr(noMappingDays);
        if (null != minPriceReq.getMappingDays()) {
            dateKey = MyTools.getDateByCurr(minPriceReq.getMappingDays());
        }

        String key = PROJECT_NAME + ":" + "jd_min_price_hsjlxy" + ":" + PlatEnum.PLAT_HSJL_XY.getValue() + ":" + dateKey;

        String runningKey = PROJECT_NAME + ":" + "jd_min_price" + ":" + PlatEnum.PLAT_HSJL_XY.getValue() + ":" + "running";
        String runningValue = this.stringRedisTemplate.opsForValue().get(runningKey);
        if (StringUtils.isNotEmpty(runningValue)) {
            this.logger.error("红色加力最低价正在更新,请勿重复操作");
            return AjaxResult.error("红色加力最低价正在更新,请勿重复操作");
        }
        if (StringUtils.isEmpty(minPriceReq.getUserName())) {
            minPriceReq.setUserName(getUsername());
        }
        this.stringRedisTemplate.opsForValue().set(runningKey, minPriceReq.getUserName());


        /**
         * {"bussinessResponse":{"hotelLowestPrices":[{"hotelId":1273455,"priceItems":[{"roomStatus":1,"saleDate":"2024-09-18","salePrice":22.0},{"roomStatus":1,"saleDate":"2024-09-19","salePrice":22.0}]},{"hotelId":561794,"priceItems":[{"roomStatus":1,"saleDate":"2024-09-18","salePrice":3.0},{"roomStatus":1,"saleDate":"2024-09-19","salePrice":3.0}]}]},"returnCode":"000","retrunMsg":"成功","returnCode":"000","returnMsg":"成功"}
         */
        if (CollectionUtils.isEmpty(minPriceReq.getHotelIdList())) {
            HotelInfoHsjlxy search = new HotelInfoHsjlxy();
            //search.setStatus(8);//未映射数据
            List<HotelInfoHsjlxy> list = this.hotelInfoHsjlxyService.selectHotelInfoHsjlxyList2(search);
            this.logger.info("红色加力协议未映射酒店数据数量：{}", list.size());
            List<Long> hotelIdList = list.stream().map(dto -> Long.parseLong(dto.getId())).collect(Collectors.toList());
            minPriceReq.setHotelIdList(hotelIdList);
        }

        if (org.apache.commons.lang3.StringUtils.isEmpty(minPriceReq.getCheckInDate())) {
            //明天
            String tomorrow = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String tenDays = LocalDateTime.now().plusDays(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            minPriceReq.setCheckInDate(tomorrow);
            minPriceReq.setCheckOutDate(tenDays);
        }
        this.threadPoolTaskExecutor.execute(() -> {
            long startTime = System.currentTimeMillis();
            this.logger.info("红色加力协议最低价开始执行 当前时间：{}", startTime);

            //hotelIdList按照每100个值进行分组
            List<List<Long>> hotelIdList = MyTools.splitList(minPriceReq.getHotelIdList(), 100);
            int count = 0;
            this.logger.info("红色加力协议count:{},酒店分组数量 {}", count, hotelIdList.size());
            for (List<Long> hotelIdList1 : hotelIdList) {
                count++;
                minPriceReq.setHotelIdList(hotelIdList1);
                this.logger.info("红色加力协议最低价参数 {},当前count:{}", JSONObject.toJSONString(minPriceReq), count);
                HSJSResponse hsjsResponse = JSONObject.parseObject(HsjlxyApi.queryHotelLowestPrice(minPriceReq), HSJSResponse.class);
                this.logger.info("红色加力协议最低价返回 {},当前count:{}", JSONObject.toJSONString(hsjsResponse), count);
                if (hsjsResponse.getReturnCode().equals("000")) {
                    List<HotelLowestPrices> hotelLowestPrices = hsjsResponse.getBussinessResponse().getHotelLowestPrices();
                    this.logger.info("红色加力协议返回总数 count:{},hotelLowestPrices:{}", count, hotelLowestPrices.size());
                    for (HotelLowestPrices hotelLowestPrice : hotelLowestPrices) {
                        //1.根据酒店id和PlatEnum.PLAT_HSJL_XY查询jd_jdb_mapping
                        ZhJdJdbMapping mapping = new ZhJdJdbMapping();
                        mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
                        mapping.setPlatId(hotelLowestPrice.getHotelId());
                        List<ZhJdJdbMapping> zhJdJdbMappingList = this.zhJdJdbMappingService.selectZhJdJdbMappingList(mapping);
                        if (CollectionUtils.isNotEmpty(zhJdJdbMappingList)) {
                            ZhJdJdbMapping jdJdbMapping = zhJdJdbMappingList.get(0);
                            String localId = jdJdbMapping.getLocalId();
                            this.logger.info("红色加力协议酒店映射成功 local_id:{}", jdJdbMapping.getLocalId());
                            //获取localId
                            List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                            for (PriceItems priceItem : priceItems) {
                                BigDecimal salePrice = new BigDecimal(priceItem.getSalePrice());
                                Date date = priceItem.getSaleDate();
                                ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                                zhJdJdbMinPrice.setJdid(localId);
                                zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                                List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                                if (CollectionUtils.isEmpty(prices)) {
                                    zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                                    zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                                    zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                                    this.zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                                    this.logger.info("红色加力协议最低价插入成功 localId：{} sxsj：{}", localId, date);
                                } else {
                                    zhJdJdbMinPrice = prices.get(0);
                                    if (zhJdJdbMinPrice.getMinPrice().intValue() - salePrice.intValue() == 0) {
                                        //不需要更新最低价
                                        this.logger.info("红色加力协议当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), salePrice);
                                    } else {
                                        zhJdJdbMinPrice.setSxsj(date);
                                        zhJdJdbMinPrice.setMinPrice(salePrice);
                                        this.zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                                        this.logger.info("红色加力协议最低价更新成功 localId：{} sxsj：{}", localId, date);
                                    }
                                }
                            }
                        } else {
                            String hotelId = hotelLowestPrice.getHotelId();
                            String localId = PlatEnum.PLAT_HSJL.getValue() + hotelId;
                            this.logger.info("红色加力协议未映射酒店数据最低价插入成功2 localId:{} hotelId:{}", localId, hotelLowestPrice.getHotelId());
                            List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                            for (PriceItems priceItem : priceItems) {
                                ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                                zhJdJdbMinPrice.setJdid(localId);
                                zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                                zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                                this.zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                                this.logger.info("红色加力协议最低价插入成功2 local_id:{}", zhJdJdbMinPrice.getJdid());
                            }
                        }

                    }
                } else {
                    this.logger.error("红色加力协议最低价返回异常 {},当前count:{}", JSONObject.toJSONString(hsjsResponse), count);
                }
                try {
                    Thread.sleep(1000);
                    this.logger.info("红色加力协议休眠完成 当前count:{},当前时间:{}", count, new Date());
                    if (count == hotelIdList.size()) {
                        this.stringRedisTemplate.opsForValue().set(key, "1");
                        this.deleteRunningKey(runningKey);
                        HotelUpdateStatus.statusUpdateMinPrice = false;
                        this.applicationContext.publishEvent(new InterfacePlatEvent(InterfacePlatEvent.class, PlatEnum.PLAT_HSJL_XY.getValue()));
                        this.logger.info("红色加力协议发布通知事件成功 总耗时为:{},当前count:{}", (System.currentTimeMillis() - startTime) / 1000, count);
                    }
                } catch (Exception e) {
                    this.stringRedisTemplate.opsForValue().set(key, "0");
                    this.deleteRunningKey(runningKey);
                    HotelUpdateStatus.statusUpdateMinPrice = false;
                    this.logger.error("红色加力协议最低价暂停异常 errorMessage:{}", e.getMessage(), e);
                }
            }
        });
        return success();
    }

    /**
     * 标价最低 HSJLXY 新
     *
     * @param minPriceReq
     * @return
     */
    public AjaxResult listMinPriceHSJLXYNewOld(MinPriceReq minPriceReq) {
        String dateKey = MyTools.getDateByCurr(noMappingDays);
        if (null != minPriceReq.getMappingDays()) {
            dateKey = MyTools.getDateByCurr(minPriceReq.getMappingDays());
        }

        String key = PROJECT_NAME + ":" + "jd_min_price_hsjlxy" + ":" + PlatEnum.PLAT_HSJL_XY.getValue() + ":" + dateKey;

        String runningKey = PROJECT_NAME + ":" + "jd_min_price" + ":" + PlatEnum.PLAT_HSJL_XY.getValue() + ":" + "running";
        String runningValue = this.stringRedisTemplate.opsForValue().get(runningKey);
        if (StringUtils.isNotEmpty(runningValue)) {
            this.logger.error("红色加力最低价正在更新,请勿重复操作");
//            return AjaxResult.error("红色加力最低价正在更新,请勿重复操作");
        }
        if (StringUtils.isEmpty(minPriceReq.getUserName())) {
            minPriceReq.setUserName(getUsername());
        }
        this.stringRedisTemplate.opsForValue().set(runningKey, minPriceReq.getUserName());


        /**
         * {"bussinessResponse":{"hotelLowestPrices":[{"hotelId":1273455,"priceItems":[{"roomStatus":1,"saleDate":"2024-09-18","salePrice":22.0},{"roomStatus":1,"saleDate":"2024-09-19","salePrice":22.0}]},{"hotelId":561794,"priceItems":[{"roomStatus":1,"saleDate":"2024-09-18","salePrice":3.0},{"roomStatus":1,"saleDate":"2024-09-19","salePrice":3.0}]}]},"returnCode":"000","retrunMsg":"成功","returnCode":"000","returnMsg":"成功"}
         */
        if (CollectionUtils.isEmpty(minPriceReq.getHotelIdList())) {
            HotelInfoHsjlxy search = new HotelInfoHsjlxy();
            //search.setStatus(8);//未映射数据
            List<HotelInfoHsjlxy> list = this.hotelInfoHsjlxyService.selectHotelInfoHsjlxyList2(search);
            this.logger.info("红色加力协议未映射酒店数据数量：{}", list.size());
            List<Long> hotelIdList = list.stream().map(dto -> Long.parseLong(dto.getId())).collect(Collectors.toList());
            minPriceReq.setHotelIdList(hotelIdList);
        }

        if (org.apache.commons.lang3.StringUtils.isEmpty(minPriceReq.getCheckInDate())) {
            //明天
            String tomorrow = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String tenDays = LocalDateTime.now().plusDays(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            minPriceReq.setCheckInDate(tomorrow);
            minPriceReq.setCheckOutDate(tenDays);
        }
        long startTime = System.currentTimeMillis();
        this.logger.info("红色加力协议最低价开始执行 当前时间：{}", startTime);

        //hotelIdList按照每100个值进行分组
        List<List<Long>> hotelIdList = MyTools.splitList(minPriceReq.getHotelIdList(), 100);
        LongAdder count = new LongAdder();
        this.logger.info("红色加力协议count:{},酒店分组数量 {}", count, hotelIdList.size());
        this.asyncForListMinPriceHSJLXY(minPriceReq, hotelIdList, count);
        try {
            this.logger.info("红色加力协议休眠完成 当前count:{},当前时间:{}", count, new Date());
            if (count.intValue() == hotelIdList.size()) {
                this.stringRedisTemplate.opsForValue().set(key, "1");
                this.deleteRunningKey(runningKey);
                HotelUpdateStatus.statusUpdateMinPrice = false;
                this.applicationContext.publishEvent(new InterfacePlatEvent(InterfacePlatEvent.class, PlatEnum.PLAT_HSJL_XY.getValue()));
                this.logger.info("红色加力协议发布通知事件成功 总耗时为:{},当前count:{}", (System.currentTimeMillis() - startTime) / 1000, count);
            }
        } catch (Exception e) {
            this.stringRedisTemplate.opsForValue().set(key, "0");
            this.deleteRunningKey(runningKey);
            HotelUpdateStatus.statusUpdateMinPrice = false;
            this.logger.error("红色加力协议最低价暂停异常 errorMessage:{}", e.getMessage(), e);
        }
        return success();
    }

    @Override
    public AjaxResult listMinPriceHSJLXYNew(MinPriceReq minPriceReq) {
        String dateKey = MyTools.getDateByCurr(Objects.nonNull(minPriceReq.getMappingDays()) ? minPriceReq.getMappingDays() : noMappingDays);
        String key = PROJECT_NAME + ":" + "jd_min_price_hsjlxy" + ":" + PlatEnum.PLAT_HSJL_XY.getValue() + ":" + dateKey;
        String runningKey = PROJECT_NAME + "_" + "jd_min_price" + "_" + PlatEnum.PLAT_HSJL_XY.getValue() + "_" + "running";
        RLock rLock = this.redissonClient.getLock(runningKey);
        Boolean lock = false;
        try {
            lock = rLock.tryLock(2L, 20, TimeUnit.SECONDS);
            if (lock) {
                if (CollectionUtils.isEmpty(minPriceReq.getHotelIdList())) {
                    List<HotelGnBase> list = this.hotelGnBaseService.getAllIdList(PlatEnum.PLAT_HSJL_XY);
                    this.logger.info("红色加力协议酒店数据数量：{}", list.size());
                    List<Long> hotelIdList = list.stream().map(dto -> Long.parseLong(dto.getId())).collect(Collectors.toList());
                    minPriceReq.setHotelIdList(hotelIdList);
                }
                if (StringUtils.isEmpty(minPriceReq.getCheckInDate())) {
                    String tomorrow = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    String tenDays = LocalDateTime.now().plusDays(10).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    minPriceReq.setCheckInDate(tomorrow);
                    minPriceReq.setCheckOutDate(tenDays);
                }
                long startTime = System.currentTimeMillis();
                this.logger.info("红色加力协议最低价开始执行 当前时间：{}", startTime);
                List<List<Long>> hotelIdList = ListUtil.groupList(minPriceReq.getHotelIdList(), 10);
                LongAdder count = new LongAdder();
                this.logger.info("红色加力协议count:{},酒店分组数量 {}", count, hotelIdList.size());
                this.asyncForListMinPriceHSJLXY(minPriceReq, hotelIdList, count);
            } else {
                this.logger.info("红色加力协议最低价同步任务已经在执行 。。。。。");
            }
        } catch (Exception e) {
            this.logger.info("红色加力协议最低价同步异常", e);
        } finally {
            if (lock) {
                try {
                    rLock.unlock();
                } catch (Exception e) {
                    this.logger.info("### 红色加力协议同步最低价_解锁异常,", e);
                }
            }
        }

        return success();
    }

    @Override
    public void dealListMinPriceHSJLNew(String localId, Long hotelId) {
        MinPriceReq minPriceReq = new MinPriceReq();
        minPriceReq.setHotelIdList(com.google.common.collect.Lists.newArrayList(hotelId));
        minPriceReq.setCheckInDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
        minPriceReq.setCheckOutDate(DateUtils.formatDate(DateUtils.addDays(new Date(), 30), "yyyy-MM-dd"));
        HSJSResponse hsjsResponse = this.reCallHsjl("_queryHotelLowestPrice-1", minPriceReq);
        if (hsjsResponse.getReturnCode().equals("000")) {
            List<HotelLowestPrices> hotelLowestPrices = hsjsResponse.getBussinessResponse().getHotelLowestPrices();
            List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsert = Lists.newArrayList();
            List<ZhJdJdbMinPrice> zhJdJdbMinPriceListUpdate = Lists.newArrayList();
            for (HotelLowestPrices hotelLowestPrice : hotelLowestPrices) {
                List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                for (PriceItems priceItem : priceItems) {
                    BigDecimal salePrice = new BigDecimal(priceItem.getSalePrice());
                    Date date = priceItem.getSaleDate();
                    ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                    zhJdJdbMinPrice.setJdid(localId);
                    zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                    zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL.getValue());
                    List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                    if (CollectionUtils.isEmpty(prices)) {
                        zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                        zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                        zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL.getValue());
                        zhJdJdbMinPriceListInsert.add(zhJdJdbMinPrice);
                    } else {
                        this.logger.info("红色加力酒店最低价查询成功 localId：{} sxsj：{},size:{}", localId, date, prices.size());
                        zhJdJdbMinPrice = prices.get(0);
                        if (zhJdJdbMinPrice.getMinPrice().intValue() - salePrice.intValue() == 0) {
                            //不需要更新最低价
                            this.logger.info("红色加力当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), salePrice);
                        } else {
                            zhJdJdbMinPrice.setSxsj(date);
                            zhJdJdbMinPrice.setMinPrice(salePrice);
                            zhJdJdbMinPriceListUpdate.add(zhJdJdbMinPrice);
                            this.logger.info("红色加力最低价更新成功 localId：{} sxsj：{}", localId, date);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsert)) {
                int zhJdJdbMinPriceResult = this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsert);
                if (zhJdJdbMinPriceResult != 0) this.logger.info("红色加力最低价插入成功");
            }

            if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListUpdate)) {
                int updateZhJdJdbMinPrice = this.zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(zhJdJdbMinPriceListUpdate);
                if (updateZhJdJdbMinPrice != 0) this.logger.info("红色加力最低价插入成功");
            }
        } else {
            this.logger.error("红色加力协议最低价返回异常 {}", JSONObject.toJSONString(hsjsResponse));
        }
    }

    @Override
    public void dealListMinPriceQtNew(String localId, String hotelCode) {
        List<ZhJdJdbMinPrice> insertList = Lists.newArrayList();
        List<ZhJdJdbMinPrice> uppdateList = Lists.newArrayList();

        for (int i = 1; i < 30; i++) {
            MinPriceReqV2 minPriceReqV2 = MinPriceReqV2.builder()
                    .hotelCode(hotelCode)
                    .checkInDate(i)
                    .checkOutDate(i + 1)
                    .retryCount(1)
                    .build();

            QtMinPriceResponse result = this.reCallQiantao(minPriceReqV2);
            if (ObjectUtils.isEmpty(result)
                    || ObjectUtils.isEmpty(result.getMinPrice())) {
                this.logger.error("### dealListMinPriceQtNew_千淘协议最低价返回异常 result:{}", result);
                continue;
            }

            Date date = DateUtils.dateTime(DateUtils.YYYY_MM_DD, ObjectUtils.isEmpty(result.getOpeningTime()) ? LocalDate.now().plusDays(i).toString() : result.getOpeningTime());

            ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
            zhJdJdbMinPrice.setJdid(localId);
            zhJdJdbMinPrice.setSxsj(date);
            zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_QT.getValue());
            List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
            if (CollectionUtils.isEmpty(prices)) {
                zhJdJdbMinPrice.setSxsj(date);
                zhJdJdbMinPrice.setJdid(localId);
                zhJdJdbMinPrice.setMinPrice(result.getMinPrice());
                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_QT.getValue());
                insertList.add(zhJdJdbMinPrice);
            } else {
                this.logger.info("### dealListMinPriceQtNew_千淘酒店最低价查询成功 localId：{} sxsj：{},size:{}", localId, date, prices.size());
                zhJdJdbMinPrice = prices.get(0);
                if (zhJdJdbMinPrice.getMinPrice().compareTo(result.getMinPrice()) == 0) {
                    //不需要更新最低价
                    this.logger.info("### dealListMinPriceQtNew_千淘当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), result.getMinPrice());
                } else {
                    zhJdJdbMinPrice.setSxsj(date);
                    zhJdJdbMinPrice.setMinPrice(result.getMinPrice());
                    uppdateList.add(zhJdJdbMinPrice);
                    this.logger.info("### dealListMinPriceQtNew_千淘最低价更新成功 localId：{} sxsj：{}", localId, date);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            int zhJdJdbMinPriceResult = this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(insertList);
            if (zhJdJdbMinPriceResult != 0) this.logger.info("### dealListMinPriceQtNew_千淘最低价插入成功");
        }

        if (CollectionUtils.isNotEmpty(uppdateList)) {
            int updateZhJdJdbMinPrice = this.zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(uppdateList);
            if (updateZhJdJdbMinPrice != 0) this.logger.info("### dealListMinPriceQtNew_千淘最低价修改成功");
        }


//        MinPriceReqV2 minPriceReqV2 = MinPriceReqV2.builder()
//                .hotelCode(hotelCode)
//                .checkInDate(1)
//                .checkOutDate(30)
//                .retryCount(1)
//                .build();
//        QtMinPriceResponse result = reCallQiantao(minPriceReqV2);
//        final Date[] sxsj = {null};
//        if (Objects.nonNull(result)&&Objects.nonNull(result.getMinPrice())) {
//            sxsj[0] = result.getOpeningTime() == null ? null : DateUtils.dateTime(DateUtils.YYYY_MM_DD, result.getOpeningTime());
//            Optional.ofNullable(result.getOpeningTime()).map(item->DateUtils.dateTime(DateUtils.YYYY_MM_DD, item)).ifPresent(i1-> sxsj[0] = i1);
//            ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
//            zhJdJdbMinPrice.setJdid(localId);
//            zhJdJdbMinPrice.setSxsj(sxsj[0]);
//            zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_QT.getValue());
//            List<ZhJdJdbMinPrice> prices = zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
//            List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsert = Lists.newArrayList();
//            List<ZhJdJdbMinPrice> zhJdJdbMinPriceListUpdate = Lists.newArrayList();
//            if (CollectionUtils.isEmpty(prices)) {
//                zhJdJdbMinPrice.setSxsj(sxsj[0]);
//                zhJdJdbMinPrice.setJdid(localId);
//                zhJdJdbMinPrice.setMinPrice(result.getMinPrice());
//                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_QT.getValue());
////                zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
//                zhJdJdbMinPriceListInsert.add(zhJdJdbMinPrice);
//            } else {
//                logger.info("### dealListMinPriceQtNew_千淘酒店最低价查询成功 localId：{} sxsj：{},size:{}", localId, sxsj[0], prices.size());
//                zhJdJdbMinPrice = prices.get(0);
//                if (zhJdJdbMinPrice.getMinPrice().intValue() - result.getMinPrice().intValue() == 0) {
//                    //不需要更新最低价
//                    logger.info("### dealListMinPriceQtNew_千淘当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), result.getMinPrice());
//                } else {
//                    zhJdJdbMinPrice.setSxsj(sxsj[0]);
//                    zhJdJdbMinPrice.setMinPrice(result.getMinPrice());
//                    zhJdJdbMinPriceListUpdate.add(zhJdJdbMinPrice);
////                    zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
//                    logger.info("### dealListMinPriceQtNew_千淘最低价更新成功 localId：{} sxsj：{}", localId, sxsj[0]);
//                }
//            }
//            if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsert)) {
//                int zhJdJdbMinPriceResult = zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsert);
//                if (zhJdJdbMinPriceResult!=0)   logger.info("### dealListMinPriceQtNew_千淘最低价插入成功");
//            }
//
//            if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListUpdate)) {
//                int updateZhJdJdbMinPrice = zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(zhJdJdbMinPriceListUpdate);
//                if (updateZhJdJdbMinPrice!=0)   logger.info("### dealListMinPriceQtNew_千淘最低价修改成功");
//            }
//        } else {
//            logger.error("### dealListMinPriceQtNew_千淘协议最低价返回异常 result:{}", result);
//        }
    }


    @Override
    public void dealListMinPriceMtNew(String localId, Long hotelId) {
        List<HotelLowestPrices> hotelLowestPrices = MeituanApi.hotelProductMinPrice(Collections.singletonList(hotelId), "1", "30");
        if (CollectionUtils.isNotEmpty(hotelLowestPrices)) {
            List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsert = Lists.newArrayList();
            List<ZhJdJdbMinPrice> zhJdJdbMinPriceListUpdate = Lists.newArrayList();
            hotelLowestPrices.stream().forEach(priceItems -> {
                List<PriceItems> priceItemsInner = priceItems.getPriceItems();
                for (PriceItems priceItem : priceItemsInner) {
                    BigDecimal salePrice = new BigDecimal(priceItem.getSalePrice());
                    Date date = priceItem.getSaleDate();
                    ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                    zhJdJdbMinPrice.setJdid(localId);
                    zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                    zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_MT.getValue());
                    List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                    if (CollectionUtils.isEmpty(prices)) {
                        zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
//                        zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                        zhJdJdbMinPriceListInsert.add(zhJdJdbMinPrice);
                    } else {
                        this.logger.info("### dealListMinPriceMtNew_美团酒店最低价查询成功 localId：{} sxsj：{},size:{}", localId, date, prices.size());
                        zhJdJdbMinPrice = prices.get(0);
                        if (zhJdJdbMinPrice.getMinPrice().intValue() - salePrice.intValue() == 0) {
                            //不需要更新最低价
                            this.logger.info("### dealListMinPriceMtNew_美团当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), salePrice);
                        } else {
                            zhJdJdbMinPrice.setJdid(localId);
                            zhJdJdbMinPrice.setSxsj(date);
                            zhJdJdbMinPrice.setMinPrice(salePrice);
//                            zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                            zhJdJdbMinPriceListUpdate.add(zhJdJdbMinPrice);
                            this.logger.info("### dealListMinPriceMtNew_美团最低价更新成功 localId：{} sxsj：{}", localId, date);
                        }
                    }
                }
            });
            if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsert)) {
                int zhJdJdbMinPriceResult = this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsert);
                if (zhJdJdbMinPriceResult != 0) this.logger.info("### dealListMinPriceMtNew_美团最低价插入成功");
            }

            if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListUpdate)) {
                int updateZhJdJdbMinPrice = this.zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(zhJdJdbMinPriceListUpdate);
                if (updateZhJdJdbMinPrice != 0) this.logger.info("### dealListMinPriceMtNew_美团最低价修改成功");
            }
        } else {
            this.logger.warn("### dealListMinPriceMtNew_美团最低价查询为空,localId:{}, hotelId:{}", localId, hotelId);
        }
    }

    public void asyncForListMinPriceHSJLXY(MinPriceReq minPriceReq, List<List<Long>> hotelIdList, LongAdder count) {
        /*hotelIdList.stream().forEach(item -> {
            try {
                this.dealListMinPriceHSJLXYNew(minPriceReq, item, count, key, runningKey);
            } catch (Exception e) {
                logger.error("任务执行失败", e);
            }
        });*/

        List<CompletableFuture<?>> completableFutures = hotelIdList.stream().map(item -> {
            return CompletableFuture.runAsync(() -> {
                try {
                    List<List<Long>> lists = MyTools.splitList(item, this.batchCount);
                    MinPriceReq req = new MinPriceReq();
                    req.setCheckInDate(minPriceReq.getCheckInDate());
                    req.setCheckOutDate(minPriceReq.getCheckOutDate());
                    for (List<Long> hotelIds : lists) {
                        long startTime = System.currentTimeMillis();
                        req.setHotelIdList(hotelIds);
                        this.dealListMinPriceHSJLXYNew(req, count);
                        this.logger.info("红色加力协议同步100个酒店耗时：{},处理城市数量:{}", (System.currentTimeMillis() - startTime), hotelIds.size());
                    }
                } catch (Exception e) {
                    this.logger.error("红色加力协议同步最低价任务执行失败", e);
                }
            }, this.threadPoolTaskExecutor).thenRunAsync(() -> count.increment());
        }).collect(Collectors.toList());

        CompletableFuture<?> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));

        try {
            allOf.get();
        } catch (Exception e) {
            this.logger.error("### JdJdbServiceImpl_asyncForListMinPriceHSJLXY_任务执行失败:{}", e.getMessage(), e);
        }
    }

    private void dealListMinPriceHSJLXYNew(MinPriceReq minPriceReq, LongAdder count) {
//        minPriceReq.setHotelIdList(hotelIdList);
        HSJSResponse hsjsResponse = this.reCallHsjlXy("_queryHotelLowestPrice-1", minPriceReq);
        if (null != hsjsResponse) {
            if (hsjsResponse.getReturnCode().equals("000")) {
                List<HotelLowestPrices> hotelLowestPrices = hsjsResponse.getBussinessResponse().getHotelLowestPrices();
                this.logger.info("红色加力协议返回总数 count:{},hotelLowestPrices:{}", count, hotelLowestPrices.size());
                List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsert = Lists.newArrayList();
                List<ZhJdJdbMinPrice> zhJdJdbMinPriceListUpdate = Lists.newArrayList();
                List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsertEnd = Lists.newArrayList();
                for (HotelLowestPrices hotelLowestPrice : hotelLowestPrices) {
                    //1.根据酒店id和PlatEnum.PLAT_HSJL_XY查询jd_jdb_mapping
                    ZhJdJdbMapping mapping = new ZhJdJdbMapping();
                    mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
                    mapping.setPlatId(hotelLowestPrice.getHotelId());
                    List<ZhJdJdbMapping> zhJdJdbMappingList = this.zhJdJdbMappingService.selectZhJdJdbMappingList(mapping);
                    if (CollectionUtils.isNotEmpty(zhJdJdbMappingList)) {
                        ZhJdJdbMapping jdJdbMapping = zhJdJdbMappingList.get(0);
                        String localId = jdJdbMapping.getLocalId();
                        this.logger.info("红色加力协议酒店映射成功 local_id:{}", jdJdbMapping.getLocalId());
                        //获取localId
                        List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                        for (PriceItems priceItem : priceItems) {
                            BigDecimal salePrice = new BigDecimal(priceItem.getSalePrice());
                            Date date = priceItem.getSaleDate();
                            ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                            zhJdJdbMinPrice.setJdid(localId);
                            zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                            zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                            List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                            if (CollectionUtils.isEmpty(prices)) {
                                zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                                zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                                zhJdJdbMinPriceListInsert.add(zhJdJdbMinPrice);
                            } else {
                                this.logger.info("红色加力协议酒店最低价查询成功 localId：{} sxsj：{},size:{}", localId, date, prices.size());
                                zhJdJdbMinPrice = prices.get(0);
                                if (zhJdJdbMinPrice.getMinPrice().intValue() - salePrice.intValue() == 0) {
                                    //不需要更新最低价
                                    this.logger.info("红色加力协议当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), salePrice);
                                } else {
                                    zhJdJdbMinPrice.setSxsj(date);
                                    zhJdJdbMinPrice.setMinPrice(salePrice);
                                    zhJdJdbMinPriceListUpdate.add(zhJdJdbMinPrice);
                                    this.logger.info("红色加力协议最低价更新成功 localId：{} sxsj：{}", localId, date);
                                }
                            }
                        }
                    } else {
                        String hotelId = hotelLowestPrice.getHotelId();
                        String localId = PlatEnum.PLAT_HSJL.getValue() + hotelId;
                        //this.logger.info("红色加力协议未映射酒店数据最低价,不处理 localId:{} hotelId:{}", localId, hotelLowestPrice.getHotelId());
                        this.logger.info("红色加力协议未映射酒店数据最低价插入成功2 localId:{} hotelId:{}", localId, hotelLowestPrice.getHotelId());
                        List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                        for (PriceItems priceItem : priceItems) {
                            ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                            zhJdJdbMinPrice.setJdid(localId);
                            zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                            zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                            zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                            //zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                            zhJdJdbMinPriceListInsertEnd.add(zhJdJdbMinPrice);
                            this.logger.info("红色加力协议最低价插入成功2 local_id:{}", zhJdJdbMinPrice.getJdid());
                        }
                    }

                }

                if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsert)) {
                    int zhJdJdbMinPriceResult = this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsert);
                    if (zhJdJdbMinPriceResult != 0) this.logger.info("红色加力协议最低价插入成功");
                }

                if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListUpdate)) {
                    int updateZhJdJdbMinPrice = this.zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(zhJdJdbMinPriceListUpdate);
                    if (updateZhJdJdbMinPrice != 0) this.logger.info("红色加力协议最低价插入成功");
                }

                if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsertEnd)) {
                    int insertZhJdJdbMinPrice = this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsertEnd);
                    if (insertZhJdJdbMinPrice != 0) this.logger.info("红色加力协议最低价插入成功END");
                    this.logger.info("红色加力协议最低价更新成功");
                }

            } else {
                this.logger.error("红色加力协议最低价返回异常 {},当前count:{}", JSONObject.toJSONString(hsjsResponse), count);
            }
        } else {
            this.logger.error("红色加力协议最低价返回为空 {}", JSONObject.toJSONString(minPriceReq));
        }

    }

    @Override
    public void dealListMinPriceHSJLXYNew(String localId, Long hotelId) {
        localId = AsciiUtils.convertStringToAscii(localId);
        MinPriceReq minPriceReq = new MinPriceReq();
        minPriceReq.setHotelIdList(com.google.common.collect.Lists.newArrayList(hotelId));
        minPriceReq.setCheckInDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd"));
        minPriceReq.setCheckOutDate(DateUtils.formatDate(DateUtils.addDays(new Date(), 30), "yyyy-MM-dd"));
        HSJSResponse hsjsResponse = this.reCallHsjl("_queryHotelLowestPrice-1", minPriceReq);
        if (hsjsResponse.getReturnCode().equals("000")) {
            List<HotelLowestPrices> hotelLowestPrices = hsjsResponse.getBussinessResponse().getHotelLowestPrices();
            List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsert = Lists.newArrayList();
            List<ZhJdJdbMinPrice> zhJdJdbMinPriceListUpdate = Lists.newArrayList();
            for (HotelLowestPrices hotelLowestPrice : hotelLowestPrices) {
                List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                for (PriceItems priceItem : priceItems) {
                    BigDecimal salePrice = new BigDecimal(priceItem.getSalePrice());
                    Date date = priceItem.getSaleDate();
                    ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                    zhJdJdbMinPrice.setJdid(localId);
                    zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                    zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                    List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                    if (CollectionUtils.isEmpty(prices)) {
                        zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
                        zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                        zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_HSJL_XY.getValue());
                        zhJdJdbMinPriceListInsert.add(zhJdJdbMinPrice);
                    } else {
                        this.logger.info("红色加力协议酒店最低价查询成功 localId：{} sxsj：{},size:{}", localId, date, prices.size());
                        zhJdJdbMinPrice = prices.get(0);
                        if (zhJdJdbMinPrice.getMinPrice().intValue() - salePrice.intValue() == 0) {
                            //不需要更新最低价
                            this.logger.info("红色加力协议当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), salePrice);
                        } else {
                            zhJdJdbMinPrice.setSxsj(date);
                            zhJdJdbMinPrice.setMinPrice(salePrice);
                            zhJdJdbMinPriceListUpdate.add(zhJdJdbMinPrice);
                            this.logger.info("红色加力协议最低价更新成功 localId：{} sxsj：{}", localId, date);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsert)) {
                int zhJdJdbMinPriceResult = this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsert);
                if (zhJdJdbMinPriceResult != 0) this.logger.info("红色加力协议最低价插入成功");
            }

            if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListUpdate)) {
                int updateZhJdJdbMinPrice = this.zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(zhJdJdbMinPriceListUpdate);
                if (updateZhJdJdbMinPrice != 0) this.logger.info("红色加力协议最低价插入成功");
            }
        } else {
            this.logger.error("红色加力协议最低价返回异常 {}", JSONObject.toJSONString(hsjsResponse));
        }
    }

    @Override
    public AjaxResult listMinPriceMT(MinPriceReq minPriceReq) {
        long startTime = System.currentTimeMillis();
        String runningKey = PROJECT_NAME + "_" + "jd_min_price" + "_" + PlatEnum.PLAT_MT.getValue() + "_" + "running";
        RLock rLock = this.redissonClient.getLock(runningKey);
        boolean lock = false;
        try {
            lock = rLock.tryLock(2L, 60 * 30, TimeUnit.SECONDS);
            if (lock) {
                Future<Boolean> future = this.threadPoolTaskExecutor2.submit(() -> {
                    Long successCount = 0L;
                    Long errorCount = 0L;
                    int pageNo = 0;
                    while (true) {
                        List<String> interfacePlatsList = new ArrayList<>();
                        interfacePlatsList.add(PlatEnum.PLAT_MT.getValue());//美团
                        List<String> dataList = this.jdJdbMapper.getThirdHotelId(0, interfacePlatsList, pageNo * 1000, 1000);
                        this.logger.info("美团当前表finalI:{},pageNo:{},读取数量：{},当前线程：{}", 0, pageNo, dataList.size(), Thread.currentThread().getName());
                        // 处理数据
                        if (CollectionUtils.isEmpty(dataList)) {
                            log.info("美团最低价执行完成 当前数据表为:{}", 0);
                            return Boolean.TRUE;
                        }
                        log.info("美团酒店dataList数量:{}", dataList.size());
                        List<Long> dataListLong = dataList.stream().map(Long::valueOf).collect(Collectors.toList());
                        try {
                            String checkInDate = minPriceReq.getCheckInDate();
                            String checkOutDate = minPriceReq.getCheckOutDate();
                            List<List<Long>> hotelIdList = MyTools.splitList(dataListLong, 10);
                            this.logger.info("美团低价查询日期：{}，酒店分组数量：{}", checkInDate, hotelIdList.size());
                            for (List<Long> hotelIdList1 : hotelIdList) {
                                this.fetchMeiTuanData(hotelIdList1, checkInDate, checkOutDate, successCount, errorCount);
                            }
                            this.logger.info("美团处理成功 当前线程:{},当前页数：{},successCount:{},errorCount:{}", Thread.currentThread().getName(), pageNo, successCount, errorCount);
                        } catch (Exception e) {
                            this.logger.error("美团低价更新异常 errorMessage:{}", e.getMessage(), e);
                            return Boolean.FALSE;
                        }
                        pageNo++;
                    }
                });
                try {
                    Boolean flag = (Boolean) future.get();
                    this.logger.info("美团最低价执行结果 flag:{}", flag);
                } catch (Exception e) {
                    this.logger.error("美团最低价数据同步任务执行异常", e);
                }

            } else {
                this.logger.info("美团最低价已经在执行 。。。。。。。");
            }
        } catch (Exception e) {
            this.logger.info("美团最低价异常", e);
        } finally {
            if (lock) {
                try {
                    rLock.unlock();
                } catch (Exception e) {
                    this.logger.error("### 美团同步最低价_解锁异常,", e);
                }
            }
        }

        this.logger.info("美团同步最低价 总耗时为:{}", (System.currentTimeMillis() - startTime) / 1000);
        return success();
    }

    private void fetchMeiTuanData(List<Long> hotelIdList, String checkInDate, String checkOutDate, Long successCount, Long errorCount) {
        //美团每秒40次
        List<HotelLowestPrices> hotelLowestPrices = (List<HotelLowestPrices>) this.fetchDataFunction(PlatEnum.PLAT_MT.getValue() + "_min_price", 1, 37, (result) -> {
            return MeituanApi.hotelProductMinPrice(hotelIdList, checkInDate, checkOutDate);
        });
//        List<HotelLowestPrices> hotelLowestPrices = MeituanApi.hotelProductMinPrice(hotelIdList, checkInDate, checkOutDate);
        this.logger.info("美团API低价查询数据返回：{},size:{}", JSON.toJSONString(hotelLowestPrices), hotelLowestPrices.size());
        List<ZhJdJdbMinPrice> mtInsertList = new ArrayList<>();
        List<ZhJdJdbMinPrice> mtUpdateList = new ArrayList<>();
        for (HotelLowestPrices hotelLowestPrice : hotelLowestPrices) {
            // 获取映射关系
            ZhJdJdbMapping mapping = new ZhJdJdbMapping();
            mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
            mapping.setPlatId(hotelLowestPrice.getHotelId());
            List<ZhJdJdbMapping> zhJdJdbMappingList = this.zhJdJdbMappingService.selectZhJdJdbMappingList(mapping);
            if (CollectionUtils.isEmpty(zhJdJdbMappingList)) {
                this.logger.error("美团API低价查询未找到映射关系，美团酒店ID：{}", hotelLowestPrice.getHotelId());
                errorCount++;
                continue;
            }
            ZhJdJdbMapping jdJdbMapping = zhJdJdbMappingList.get(0);
            // 映射不为空，比较API查询的数据和本地库数据的价格，取最低价
            String localId = jdJdbMapping.getLocalId();
            List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
            for (PriceItems priceItem : priceItems) {
                Date saleDate = priceItem.getSaleDate();
                BigDecimal salePrice = MyTools.convertToYuan(priceItem.getSalePrice());
                ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                zhJdJdbMinPrice.setJdid(localId);
                zhJdJdbMinPrice.setSxsj(saleDate);
                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_MT.getValue());
                List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                this.logger.info("查询最低价,localId：{},plat_id:{},saleDate:{},size:{}", localId, hotelLowestPrice.getHotelId(), saleDate, prices.size());
                if (CollectionUtils.isEmpty(prices)) {
                    successCount++;
                    zhJdJdbMinPrice.setSxsj(saleDate);
                    zhJdJdbMinPrice.setMinPrice(salePrice);
                    //zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                    mtInsertList.add(zhJdJdbMinPrice);
                    this.logger.info("美团API最低价插入成功 localId：{},plat_id:{} sxsj：{}", localId, hotelLowestPrice.getHotelId(), saleDate);
                } else {
                    this.logger.info("美团数据库中查到prices的长度:{}", prices.size());
                    zhJdJdbMinPrice = prices.get(0);
                    if (zhJdJdbMinPrice.getMinPrice().compareTo(salePrice) == 0) {
                        //不需要更新最低价
                        this.logger.info("美团API酒店不需要更新最低价 sxsj：{},localId：{},plat_id:{},数据库价格：{},接口返回价格:{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), hotelLowestPrice.getHotelId(), zhJdJdbMinPrice.getMinPrice(), salePrice);
                    } else {
                        successCount++;
                        zhJdJdbMinPrice.setSxsj(saleDate);
                        zhJdJdbMinPrice.setMinPrice(salePrice);
                        //zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                        mtUpdateList.add(zhJdJdbMinPrice);
                        this.logger.info("美团API最低价更新成功 localId：{},plate_id:{}, sxsj：{}", localId, hotelLowestPrice.getHotelId(), saleDate);
                    }
                }
            }
        }

        //批量插入
        if (CollectionUtils.isNotEmpty(mtInsertList)) {
            int mtCount = this.zhJdJdbMinPriceService.insertBatch(mtInsertList);
            if (mtCount > 0) this.logger.info("美团API最低价批量插入成功");
        }
        //批量更新
        if (CollectionUtils.isNotEmpty(mtUpdateList)) {
            int mtUpdateCount = this.zhJdJdbMinPriceService.updateBatch(mtUpdateList);
            if (mtUpdateCount > 0) this.logger.info("美团API最低价批量更新成功");
        }

    }

    @Override
    public AjaxResult listMinPriceTravelButler(MinPriceReq minPriceReq) {
        //设置数据处理范围（不算今天的后七天）
        if (null == minPriceReq.getCheckInDate()) {
            String tomorrow = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String sevenDays = LocalDateTime.now().plusDays(7).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            minPriceReq.setCheckInDate(tomorrow);
            minPriceReq.setCheckOutDate(sevenDays);
        }
        List<String> dateList = DateUtils.getDateList(minPriceReq.getCheckInDate(), 7);

        //根据平台编码获取差旅管家所有的城市映射数据
        HotelCityMapping hotelCityMapping = new HotelCityMapping();
        hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_CLGJ.getValue()));
        List<HotelCityMapping> hotelCityMappings = this.hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
        log.info("差旅管家拉取最低价-》城市总数量：{}", hotelCityMappings.size());

        //将hotelCityMappings按照200分组
//        List<List<HotelCityMapping>> hotelCityMappingGroup = hotelCityMappingService.groupByNum(hotelCityMappings, 200);
        List<List<HotelCityMapping>> hotelCityMappingGroup = ListUtil.groupList(hotelCityMappings, 200);
        LongAdder count = new LongAdder();
        List<CompletableFuture<?>> completableFutures = hotelCityMappingGroup.stream().map(item -> {
            return CompletableFuture.runAsync(() -> {
                try {
                    String treadName = Thread.currentThread().getName();
                    log.info("差旅管家拉取最低价-》当前线程:{} 执行城市总数：{}", treadName, item.size());
                    this.fetchChaiLvData(dateList, item);
                } catch (Exception e) {
                    this.logger.error("差旅管家拉取最低价任务执行失败", e);
                }
            }, this.threadPoolTaskExecutor2).thenRunAsync(() -> count.increment());
        }).collect(Collectors.toList());

        CompletableFuture<?> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));

        try {
            allOf.get();
        } catch (Exception e) {
            this.logger.error("### 拉取差旅管家底价_任务执行失败，", e);
        }

        this.logger.info("差旅管家低价任务执行数：{}", count.longValue());

        return success(count);
    }

    public void fetchChaiLvData(List<String> dateList, List<HotelCityMapping> mappingList) {
        //定义城市酒店总数量
        int totalCount = 0;
        int totalCountCL = 0;
        //单个城市酒店映射数据量
        int zhJdJdbMappingTotalCount = 0;
        //单个城市酒店映射去重后数据量
        int zhJdJdbMappingDistinctTotalCount = 0;
        //单个城市酒店最低价映射数据量
        int zhJdJdbMinPriceTotalCount = 0;
        //单个城市酒店最低价映射插入数据量
        int zhJdJdbMinPriceInsertTotalCount = 0;
        //单个城市酒店最低价映射更新数据量
        int zhJdJdbMinPriceUpdateTotalCount = 0;
        //单个城市酒店最低价映未处理射数据量
        int zhJdJdbMinPriceUnprocessedTotalCount = 0;


        if (ObjectUtils.isEmpty(mappingList)) {
            this.logger.error("未查询到城市映射信息");
            return;
        }
        List<String> cityIds = mappingList.stream().map(HotelCityMapping::getPlatNum).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(cityIds)) {
            this.logger.error("查询到城市映射信息中PlatNum为null");
            return;
        }

        //批量查询城市下的酒店数据
        List<HotelGnBase> hotelInfoChailvgjList = this.hotelGnBaseService.getByCityIds(PlatEnum.PLAT_CLGJ, cityIds);
        if (CollectionUtils.isEmpty(hotelInfoChailvgjList)) {
            this.logger.error("未查询到酒店信息信息，cityIds = {}", JSON.toJSONString(cityIds));
            return;
        }
        this.logger.info("差旅管家拉取最低价-》cityIds = {}, 酒店总数量：{}", cityIds, hotelInfoChailvgjList.size());
        totalCount += hotelInfoChailvgjList.size();

        //按照城市分组(差旅文档表明此接口只单个城市id的搜索)
        Map<String, List<HotelGnBase>> hotelInfoGroupByCityId = hotelInfoChailvgjList.stream().collect(Collectors.groupingBy(HotelGnBase::getCityId));

        //遍历分组
        for (Map.Entry<String, List<HotelGnBase>> item : hotelInfoGroupByCityId.entrySet()) {
            String k = item.getKey();
            List<HotelGnBase> v = item.getValue();

            //按照每组10条进行分组（差旅文档标明此接口分页参数PageSize最大支持20）
            List<List<HotelGnBase>> hotelInfoListList = ListUtils.partition(v, 20);

            //按照日期处理每天需拉去的最低价（差旅文档表明此接口只支持单天的搜索）
            for (String date : dateList) {
                Map<String, Integer> result = this.fetchHotelInfoByHotelIds(hotelInfoListList, date, k);


                //数量统计
                totalCountCL += result.get("totalCountCL");
                zhJdJdbMappingTotalCount += result.get("zhJdJdbMappingTotalCount");
                zhJdJdbMappingDistinctTotalCount += result.get("zhJdJdbMappingDistinctTotalCount");
                zhJdJdbMinPriceTotalCount += result.get("zhJdJdbMinPriceTotalCount");
                zhJdJdbMinPriceInsertTotalCount += result.get("zhJdJdbMinPriceInsertTotalCount");
                zhJdJdbMinPriceUpdateTotalCount += result.get("zhJdJdbMinPriceUpdateTotalCount");
                zhJdJdbMinPriceUnprocessedTotalCount += result.get("zhJdJdbMinPriceUnprocessedTotalCount");
            }
        }
        this.logger.info("差旅管家拉取最低价- 酒店总数量：{}, " +
                "差旅酒店数量：{}," +
                "城市酒店映射数据量:{}," +
                "酒店映射去重后数据量:{}," +
                "酒店最低价映射数据量:{}," +
                "酒店最低价映射插入数据量:{}," +
                "酒店最低价映射更新数据量:{}," +
                "酒店最低价映未处理射数据量:{}", totalCount, totalCountCL, zhJdJdbMappingTotalCount, zhJdJdbMappingDistinctTotalCount, zhJdJdbMinPriceTotalCount, zhJdJdbMinPriceInsertTotalCount, zhJdJdbMinPriceUpdateTotalCount, zhJdJdbMinPriceUnprocessedTotalCount);
    }

    public AjaxResult listMinPriceQTOld() {
        String dateKey = MyTools.getDateByCurr(noMappingDays);
        //校验今天是否已经执行
        String key = PROJECT_NAME + ":" + "jd_min_price_qt" + ":" + PlatEnum.PLAT_QT.getValue() + ":" + dateKey;
        this.logger.info("千淘最低价Key：{}", key);
//        String value = stringRedisTemplate.opsForValue().get(key);
//        if (StringUtils.isNotEmpty(value) && value.equals("1")) {
//            logger.info("千淘最低价已经执行,请勿重复执行");
//            return success("千淘最低价已经执行,请勿重复执行");
//        }

        String runningKey = PROJECT_NAME + ":" + "jd_min_price" + ":" + PlatEnum.PLAT_QT.getValue() + ":" + "running";
        String runningValue = this.stringRedisTemplate.opsForValue().get(runningKey);
        if (StringUtils.isNotEmpty(runningValue)) {
            this.logger.error("千淘最低价正在更新,请勿重复操作");
            return AjaxResult.error("千淘最低价正在更新,请勿重复操作");
        }

        this.stringRedisTemplate.opsForValue().set(runningKey, "admin");

        String unzipKey = PROJECT_NAME + ":" + "jd_min_price_unzip_url" + ":" + PlatEnum.PLAT_QT.getValue() + ":" + dateKey;
        String unzipValue = this.stringRedisTemplate.opsForValue().get(unzipKey);
        if (StringUtils.isEmpty(unzipValue)) {
            log.error("千淘最低价文件不存在,请下载后重试");
            return AjaxResult.error("千淘最低价文件不存在,请下载后重试");
        }
        String downloadFilePath = PropertiesUtil.getProp(QiantaoApi.fileDirKey);
        //下载千淘最低价文件，并且上传到服务器上
        String des = downloadFilePath + PropertiesUtil.getProp(clientIdKey) + "__" + dateKey + ".price";
        ZhJdJdbMapping jdJdbMapping = new ZhJdJdbMapping();
        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
        long begin = System.currentTimeMillis();
        this.logger.info("开始时间：{}", begin);
        List<ZhJdJdbMapping> mappings = this.zhJdJdbMappingService.selectZhJdJdbMappingList(jdJdbMapping);
        this.logger.info("千淘ZhJdJdbMapping总数：{},执行时间:{}", mappings.size(), System.currentTimeMillis() - begin);
        Map<String, String> platToLocal = new HashMap<>();
        Map<String, String> localToPlat = new HashMap<>();
        for (ZhJdJdbMapping mapping : mappings) {
            platToLocal.put(mapping.getPlatId(), mapping.getLocalId());
            localToPlat.put(mapping.getLocalId(), mapping.getPlatId());
        }
        this.logger.info("platToLocal:{},localToPlat:{}", platToLocal.size(), localToPlat.size());
//        JdJdb jdJdb0 = new JdJdb();
//        jdJdb0.setReserve2(PlatEnum.PLAT_QT.getValue());
//        List<JdJdb> jdJdbs = jdJdbMapper.selectJdJdbList(jdJdb0);
//        Map<String, Date> jdMap = new HashMap<>();
//        for (JdJdb jd : jdJdbs) {
//            String id = localToPlat.get(jd.getId());
//            if (id != null) {
//                jdMap.put(id, jd.getSavedate());
//            }
//        }
        ConcurrentHashMap<String, Boolean> executedTasks = new ConcurrentHashMap<>();
        IntStream.range(0, 10).forEach(suffix -> {
            String taskKey = "low_price_task_" + suffix;
            if (executedTasks.putIfAbsent(taskKey, true) == null) {
                this.threadPoolTaskExecutor.submit(() -> {
                    this.logger.info("拼接后服务器上des：{}", des);
                    this.logger.info("千淘开始读取最低价开始 当前时间:{},当前批次:{}", new Date(), suffix + 1);
                    List<ZhJdJdbMinPrice> qtInsertList = new ArrayList<>();
                    List<ZhJdJdbMinPrice> qtUpdateList = new ArrayList<>();
                    File file = new File(des + (suffix + 1));
                    if (file.exists()) {
                        try {
                            LineIterator it = FileUtils.lineIterator(file, "utf8");
                            HotelInfoQiantao hotel = new HotelInfoQiantao();

                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            JdJdb jdJdb = new JdJdb();
                            jdJdb.setReserve2(PlatEnum.PLAT_QT.getValue());

//                            List<String> dates = new ArrayList<>();
//                            for (int i = 0; i < noMappingDays; i++) {
//                                String date = MyTools.getDateByCurr(0 - i);
//                                dates.add(date);
//                            }
                            int successCount = 0;
                            int failCount = 0;
                            while (it.hasNext()) {
                                String line = it.nextLine();
                                JSONObject json = JSONObject.parseObject(line);
                                String date = json.getString("Date");
                                String HotelCode = json.getString("HotelCode");
//                                Date saveDate = jdMap.get(HotelCode);
//                                if (saveDate != null && dates.contains(sdf.format(saveDate))) {
//                                    continue;
//                                }
                                String minPrice = json.getString("MinPrice");
//                                hotel.setId(HotelCode);
//                                hotel.setReserve1(minPrice);
//                                hotelInfoQiantaoService.updateHotelInfoQiantao(hotel);

                                String localId = platToLocal.get(HotelCode);
                                if (StringUtils.isEmpty(localId)) {
                                    failCount++;
                                    this.logger.error("mapping中没有找到酒店HotelCode：{},localId:{}", HotelCode, localId);
                                    continue;
                                }
                                ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                                zhJdJdbMinPrice.setJdid(localId);
                                zhJdJdbMinPrice.setSxsj(sdf.parse(date));
                                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_QT.getValue());
                                List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                                this.logger.info("千淘最低价 ZhJdJdbMinPrice的数量:{} localId:{},plateId:{}", prices.size(), localId, HotelCode);
                                if (CollectionUtils.isEmpty(prices)) {
                                    zhJdJdbMinPrice.setSxsj(zhJdJdbMinPrice.getSxsj());
                                    zhJdJdbMinPrice.setMinPrice(new BigDecimal(minPrice));
                                    zhJdJdbMinPrice.setInterfacePlat((PlatEnum.PLAT_QT.getValue()));
                                    //zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                                    qtInsertList.add(zhJdJdbMinPrice);
                                    this.logger.info("千淘最低价插入成功 localId：{},plate_id:{} sxsj：{},minPrice:{}", localId, HotelCode, zhJdJdbMinPrice.getSxsj(), minPrice);
                                } else {
                                    zhJdJdbMinPrice = prices.get(0);
                                    if (zhJdJdbMinPrice.getMinPrice().intValue() - new BigDecimal(minPrice).intValue() == 0) {
                                        //不需要更新最低价
                                        this.logger.info("当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice());
                                    } else {
                                        zhJdJdbMinPrice.setSxsj(sdf.parse(date));
                                        zhJdJdbMinPrice.setMinPrice(new BigDecimal(minPrice));
                                        zhJdJdbMinPrice.setInterfacePlat((PlatEnum.PLAT_QT.getValue()));
                                        //zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                                        qtUpdateList.add(zhJdJdbMinPrice);
                                        this.logger.info("千淘最低价更新成功 localId：{},plate_id:{} sxsj：{}", localId, HotelCode, date);
                                    }
                                }
                                successCount++;
                                this.logger.info("千淘执行成功数量：{},执行失败数量：{},当前时间为:{},suffix:{}", successCount, failCount, new Date(), (suffix + 1));
                            }
                            this.stringRedisTemplate.opsForValue().set(key, "1", 1, TimeUnit.DAYS);
                        } catch (Exception e) {
                            this.stringRedisTemplate.opsForValue().set(key, "0", 1, TimeUnit.DAYS);
                            this.logger.error("千淘最低价异常 errorMessage:{}", e.getMessage(), e);
                        } finally {
                            this.deleteRunningKey(runningKey);
                            HotelUpdateStatus.statusUpdate = true;
                            HotelUpdateStatus.statusUpdateMinPrice = false;
                            this.logger.info("finally千淘最低价执行完成 当前时间为:{},当前suffix:{}", new Date(), (suffix + 1));
                        }
                    } else {
                        HotelUpdateStatus.statusUpdate = false;
                        HotelUpdateStatus.statusUpdateMinPrice = false;
                        this.logger.error("千淘最低价文件不存在 文件名字为：{}", des + (suffix + 1));
                    }

                    this.logger.info("qtInsertList数量：{},qtUpdateList:{}", qtInsertList.size(), qtUpdateList.size());
                    try {
                        //批量插入
                        if (CollectionUtils.isNotEmpty(qtInsertList)) {
                            int qtCount = this.zhJdJdbMinPriceService.insertBatch(qtInsertList);
                            if (qtCount > 0) this.logger.info("千淘最低价批量插入成功");
                        }
                        //批量更新
                        if (CollectionUtils.isNotEmpty(qtUpdateList)) {
                            int qtUpdateCount = this.zhJdJdbMinPriceService.updateBatch(qtUpdateList);
                            if (qtUpdateCount > 0) this.logger.info("千淘最低价批量更新成功");
                        }
                    } catch (Exception e) {
                        this.logger.error("千淘最低价插入失败 errorMessage:{}", e.getMessage(), e);
                    }

                });
            } else {
                this.logger.info("千淘最低价数据同步任务:{} ,已经执行过，跳过", taskKey);
            }
        });
        return success();
    }

    @Override
    public AjaxResult listMinPriceQT(String randomKey) {
        String runningKey = PROJECT_NAME + "_" + "jd_min_price" + "_" + PlatEnum.PLAT_QT.getValue() + "_" + "running";
        RLock rLock = this.redissonClient.getLock(runningKey);
        Boolean lock = false;
        try {
            lock = rLock.tryLock(5L, TimeUnit.SECONDS);
            if (lock) {
                String downloadFilePath = PropertiesUtil.getProp(QiantaoApi.fileDirKey);
                //下载千淘最低价文件，并且上传到服务器上
                String des = downloadFilePath + PropertiesUtil.getProp(clientIdKey) + "__" + randomKey + ".price";
                this.logger.info("listMinPriceQT_des:{}", des);
                ZhJdJdbMapping jdJdbMapping = new ZhJdJdbMapping();
                jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
                long begin = System.currentTimeMillis();
                this.logger.info("开始时间：{}", begin);
                List<ZhJdJdbMapping> mappings = this.zhJdJdbMappingService.selectZhJdJdbMappingList(jdJdbMapping);
                this.logger.info("千淘ZhJdJdbMapping总数：{},执行时间:{}", mappings.size(), System.currentTimeMillis() - begin);
                Map<String, String> platToLocal = new HashMap<>();
                Map<String, String> localToPlat = new HashMap<>();
                for (ZhJdJdbMapping mapping : mappings) {
                    platToLocal.put(mapping.getPlatId(), mapping.getLocalId());
                    localToPlat.put(mapping.getLocalId(), mapping.getPlatId());
                }
                this.logger.info("platToLocal:{},localToPlat:{}", platToLocal.size(), localToPlat.size());
                List<Future> futureList = new ArrayList<>();
                IntStream.range(0, 10).forEach(suffix -> {
                    Future<Boolean> future = this.threadPoolTaskExecutor.submit(() -> {
                        this.logger.info("千淘开始读取最低价开始 当前时间:{},当前批次:{}", new Date(), suffix + 1);
                        List<ZhJdJdbMinPrice> qtInsertList = new ArrayList<>();
                        List<ZhJdJdbMinPrice> qtUpdateList = new ArrayList<>();
                        File file = new File(des + (suffix + 1));
                        if (file.exists()) {
                            try {
                                LineIterator it = FileUtils.lineIterator(file, "utf8");
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                JdJdb jdJdb = new JdJdb();
                                jdJdb.setReserve2(PlatEnum.PLAT_QT.getValue());

                                int successCount = 0;
                                int failCount = 0;
                                while (it.hasNext()) {
                                    String line = it.nextLine();
                                    JSONObject json = JSONObject.parseObject(line);
                                    String date = json.getString("Date");
                                    String HotelCode = json.getString("HotelCode");
                                    String minPrice = json.getString("MinPrice");

                                    String localId = platToLocal.get(HotelCode);
                                    if (StringUtils.isEmpty(localId)) {
                                        failCount++;
                                        this.logger.error("mapping中没有找到酒店HotelCode：{},localId:{}", HotelCode, localId);
                                        localId =PlatEnum.PLAT_QT.getValue() +  HotelCode;
                                    }
                                    ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                                    zhJdJdbMinPrice.setJdid(localId);
                                    zhJdJdbMinPrice.setSxsj(sdf.parse(date));
                                    zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_QT.getValue());
                                    List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                                    this.logger.info("千淘最低价 ZhJdJdbMinPrice的数量:{} localId:{},plateId:{}", prices.size(), localId, HotelCode);
                                    if (CollectionUtils.isEmpty(prices)) {
                                        zhJdJdbMinPrice.setSxsj(zhJdJdbMinPrice.getSxsj());
                                        zhJdJdbMinPrice.setMinPrice(new BigDecimal(minPrice));
                                        zhJdJdbMinPrice.setInterfacePlat((PlatEnum.PLAT_QT.getValue()));
                                        //zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                                        qtInsertList.add(zhJdJdbMinPrice);
                                        this.logger.info("千淘最低价插入成功 localId：{},plate_id:{} sxsj：{},minPrice:{}", localId, HotelCode, zhJdJdbMinPrice.getSxsj(), minPrice);
                                    } else {
                                        zhJdJdbMinPrice = prices.get(0);
                                        if (zhJdJdbMinPrice.getMinPrice().intValue() - new BigDecimal(minPrice).intValue() == 0) {
                                            //不需要更新最低价
                                            this.logger.info("当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice());
                                        } else {
                                            zhJdJdbMinPrice.setSxsj(sdf.parse(date));
                                            zhJdJdbMinPrice.setMinPrice(new BigDecimal(minPrice));
                                            zhJdJdbMinPrice.setInterfacePlat((PlatEnum.PLAT_QT.getValue()));
                                            //zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                                            qtUpdateList.add(zhJdJdbMinPrice);
                                            this.logger.info("千淘最低价更新成功 localId：{},plate_id:{} sxsj：{}", localId, HotelCode, date);
                                        }
                                    }
                                    successCount++;
                                    this.logger.info("千淘执行成功数量：{},执行失败数量：{},当前时间为:{},suffix:{}", successCount, failCount, new Date(), (suffix + 1));
                                }
                            } catch (Exception e) {
                                this.logger.error("千淘最低价异常 errorMessage:{}", e.getMessage(), e);
                            } finally {
                                this.logger.info("finally千淘最低价执行完成 当前时间为:{},当前suffix:{}", new Date(), (suffix + 1));
                            }
                        } else {
                            this.logger.error("千淘最低价文件不存在 文件名字为：{}", des + (suffix + 1));
                        }

                        this.logger.info("qtInsertList数量：{},qtUpdateList:{}", qtInsertList.size(), qtUpdateList.size());
                        try {
                            //批量插入
                            if (CollectionUtils.isNotEmpty(qtInsertList)) {
                                int qtCount = this.zhJdJdbMinPriceService.insertBatch(qtInsertList);
                                if (qtCount > 0) this.logger.info("千淘最低价批量插入成功");
                            }
                            //批量更新
                            if (CollectionUtils.isNotEmpty(qtUpdateList)) {
                                int qtUpdateCount = this.zhJdJdbMinPriceService.updateBatch(qtUpdateList);
                                if (qtUpdateCount > 0) this.logger.info("千淘最低价批量更新成功");
                            }
                        } catch (Exception e) {
                            this.logger.error("千淘最低价插入失败 errorMessage:{}", e.getMessage(), e);
                        }
                        return Boolean.TRUE;
                    });
                    futureList.add(future);
                });

                futureList.forEach(future -> {
                    try {
                        Boolean flag = (Boolean) future.get();
                        this.logger.info("千淘最低价执行结果, flag: {}", flag);
                    } catch (Exception e) {
                        this.logger.error("千淘最低价数据同步任务执行异常, errorMessage: {}", e.getMessage(), e);
                    }
                });
            } else {
                this.logger.info("千淘最低价正在更新,请勿重复操作 。。。。。。");
                return AjaxResult.error("千淘最低价正在更新,请勿重复操作");
            }
        } catch (InterruptedException e) {
            this.logger.error("同步千淘最低价获取锁异常，", e);
            return AjaxResult.error("同步千淘最低价获取锁异常");
        } finally {
            if (lock) {
                try {
                    rLock.unlock();
                    this.logger.info("同步千淘最低价解锁锁成功");
                } catch (Exception e) {
                    this.logger.error("同步千淘最低价解锁锁异常", e);
                    return AjaxResult.error("同步千淘最低价解锁锁异常");
                }
            }
        }

        return success();
    }

    @Override
    public void updateAllImageData(List<Optional<HotelCheckImageDto>> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (Optional<HotelCheckImageDto> optional : list) {
            optional.ifPresent(hotelCheckImageDtoOptional -> this.jdJdbMapper.updateImageData(hotelCheckImageDtoOptional));
        }
    }

    public void downloadFileOld(MinPriceReq minPriceReq) {
        //获取30天后的日期
        String dateKey = MyTools.getDateByCurr(noMappingDays);
        //2.获取下载最低价URL
        String urlKey = PROJECT_NAME + ":" + "jd_min_price_url" + ":" + PlatEnum.PLAT_QT.getValue() + ":" + dateKey;
        RLock rLock = this.redissonClient.getLock(urlKey);
        boolean lock = false;
        try {
            lock = rLock.tryLock(2L, 60 * 30, TimeUnit.SECONDS);
            if (lock) {
                String urlValue = null;
//                String urlValue = stringRedisTemplate.opsForValue().get(urlKey);
//                if (StringUtils.isEmpty(urlValue)) {
                this.logger.info("千淘最低价URL开始获取");
                String urlPrice = QiantaoApi.getUrlPrice(noMappingDays, dateKey);
                this.logger.info("千淘最低价URL {}", urlPrice);
                JSONObject urlJSON = JSONObject.parseObject(urlPrice);
                Boolean flag = urlJSON.getBoolean("IsSuccess");
                if (flag) {
                    urlValue = urlJSON.getString("ReturnJson");
//                        stringRedisTemplate.opsForValue().set(urlKey, urlValue, 1, TimeUnit.DAYS);
                } else {
                    throw new RuntimeException("获取千淘最低价下载地址失败");
                }
//                }
                this.logger.info("千淘最低价URL成功 URL:{},当前时间:{}", urlValue, new Date());
                //3. 下载文件
                String downloadedFilePath = null;
                String downloadFilePath = PropertiesUtil.getProp(QiantaoApi.fileDirKey);
                boolean downloadFlag = false;
                while (!downloadFlag) {
                    try {
                        String downLoadKey = PROJECT_NAME + ":" + "jd_min_price_download_url" + ":" + PlatEnum.PLAT_QT.getValue() + ":" + dateKey;
                        String downLoadValue = this.stringRedisTemplate.opsForValue().get(downLoadKey);
                        if (StringUtils.isEmpty(downLoadValue)) {
                            downloadedFilePath = DownloadAndUnzip.downloadFile(urlValue, downloadFilePath);
                            this.stringRedisTemplate.opsForValue().set(downLoadKey, downloadedFilePath, 1, TimeUnit.DAYS);
                            this.logger.info("第一次下载后路径为：{}", downloadedFilePath);
                            this.logger.info("千淘最低价文件下载完成");
                        } else {
                            downloadedFilePath = downLoadValue;
                            this.logger.info("从缓存中获取服务器上下载路径：{}", downloadedFilePath);
                        }
                        downloadFlag = true;
                        this.logger.info("千淘最低价downloadFlag：{}", downloadFlag);
                    } catch (Exception e) {
                        this.logger.error("千淘最低价数据下载中或者解析失败 errorMessage:{}", e.getMessage(), e);
                        long sleepTime = 5000 * 60;
                        try {
                            this.logger.info("千淘休眠开始 等待三方上传数据中.. 当前时间为:{}", new Date());
                            Thread.sleep(sleepTime);
                            this.logger.info("千淘休眠完成 继续执行下载任务.. 当前时间为:{}", new Date());
                        } catch (InterruptedException ex) {
                            this.logger.error("千淘等待下载休眠异常 errorMessage:{}", ex.getMessage(), ex);
                        }
                    }
                }

                //4.解压文件
                String unzipKey = PROJECT_NAME + ":" + "jd_min_price_unzip_url" + ":" + PlatEnum.PLAT_QT.getValue() + ":" + dateKey;
                String unzipValue = this.stringRedisTemplate.opsForValue().get(unzipKey);
                if (StringUtils.isEmpty(unzipValue)) {
                    // 解压文件
                    Boolean unZipFile = null;
                    try {
                        unZipFile = DownloadAndUnzip.unZipFile(downloadedFilePath, downloadFilePath);
                        this.stringRedisTemplate.opsForValue().set(unzipKey, unZipFile ? "1" : "0", 1, TimeUnit.DAYS);
                        //文件分批次
                        String filename = downloadFilePath + PropertiesUtil.getProp(clientIdKey) + "__" + dateKey + ".price";
                        int numberOfParts = 10; // 根据需要调整
                        FileSplitter.splitFile(filename, numberOfParts);
                    } catch (Exception e) {
                        this.logger.error("解压文件失败 errorMessage:{}", e.getMessage(), e);
                        throw new RuntimeException(e);
                    }
                }
                this.logger.info("千淘最低价文件解压成功 unzipValue:{}", unzipValue);
            } else {
                this.logger.info("downloadFile已经在执行 。。。。。。。");
            }
        } catch (InterruptedException e) {
            this.logger.info("### downloadFile异常", e);
        } finally {
            if (lock) {
                try {
                    rLock.unlock();
                } catch (Exception e) {
                    this.logger.error("### downloadFile_解锁异常,key:{}", urlKey);
                }
            }
        }
    }

    @Override
    public String downloadFile(MinPriceReq minPriceReq) {
        //获取30天后的日期
        String dateKey = MyTools.getDateByCurr(noMappingDays);
        String randomKey = null;
        //2.获取下载最低价URL
        String urlKey = PROJECT_NAME + "_" + "jd_min_price_url" + "_" + PlatEnum.PLAT_QT.getValue() + "_" + dateKey;
        RLock rLock = this.redissonClient.getLock(urlKey);
        boolean lock = false;
        try {
            lock = rLock.tryLock(2L, 60 * 30, TimeUnit.SECONDS);
            if (lock) {
                String urlValue = null;
                this.logger.info("千淘最低价URL开始获取");
                Random random = new Random();
                int nextInt = random.nextInt(10000);
                randomKey = dateKey + "_" + nextInt;
                this.logger.info("千淘最低价URL开始获取_randomKey:{}", randomKey);
                String urlPrice = QiantaoApi.getUrlPrice(noMappingDays, randomKey);
                this.logger.info("千淘最低价URL {}", urlPrice);
                JSONObject urlJSON = JSONObject.parseObject(urlPrice);
                Boolean flag = urlJSON.getBoolean("IsSuccess");
                if (flag) {
                    urlValue = urlJSON.getString("ReturnJson");
                } else {
                    throw new RuntimeException("获取千淘最低价下载地址失败");
                }
                this.logger.info("千淘最低价URL成功 URL:{},当前时间:{}", urlValue, new Date());
                //3. 下载文件
                String downloadedFilePath = null;
                String downloadFilePath = PropertiesUtil.getProp(QiantaoApi.fileDirKey);
                boolean downloadFlag = false;
                while (!downloadFlag) {
                    try {
                        downloadedFilePath = DownloadAndUnzip.downloadFile(urlValue, downloadFilePath);
                        downloadFlag = true;
                        this.logger.info("千淘最低价downloadFlag：{}", downloadFlag);
                    } catch (Exception e) {
                        this.logger.error("千淘最低价数据下载中或者解析失败 errorMessage:{}", e.getMessage(), e);
                        long sleepTime = 5000 * 60;
                        try {
                            this.logger.info("千淘休眠开始 等待三方上传数据中.. 当前时间为:{}", new Date());
                            Thread.sleep(sleepTime);
                            this.logger.info("千淘休眠完成 继续执行下载任务.. 当前时间为:{}", new Date());
                        } catch (InterruptedException ex) {
                            this.logger.error("千淘等待下载休眠异常 errorMessage:{}", ex.getMessage(), ex);
                        }
                    }
                }

                //4.解压文件
                try {
                    DownloadAndUnzip.unZipFile(downloadedFilePath, downloadFilePath);
                    //文件分批次
                    String filename = downloadFilePath + PropertiesUtil.getProp(clientIdKey) + "__" + randomKey + ".price";
                    int numberOfParts = 10; // 根据需要调整
                    FileSplitter.splitFile(filename, numberOfParts);
                } catch (Exception e) {
                    this.logger.error("解压文件失败 errorMessage:{}", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
                this.logger.info("千淘最低价文件解压成功。。。。。。。。");
            } else {
                this.logger.info("downloadFile已经在执行 。。。。。。。");
            }
        } catch (InterruptedException e) {
            this.logger.info("### downloadFile异常", e);
        } finally {
            if (lock) {
                try {
                    rLock.unlock();
                } catch (Exception e) {
                    this.logger.error("### downloadFile_解锁异常,key:{}", urlKey);
                }
            }
        }
        return randomKey;
    }

    @Override
    public void deleteFile() {
        String downloadFilePath = PropertiesUtil.getProp(QiantaoApi.fileDirKey);
        this.logger.info("删除千淘最低价文件开始 downloadFilePath:{}", downloadFilePath);
        try {
            // 验证路径是否合法
            if (downloadFilePath == null || !Paths.get(downloadFilePath).isAbsolute()) {
                throw new IllegalArgumentException("Invalid file directory path: " + downloadFilePath);
            }

            // 获取downloadFilePath下的文件
            Path dirPath = Paths.get(downloadFilePath);
            if (Files.exists(dirPath) && Files.isDirectory(dirPath)) {
                try (Stream<Path> stream = Files.list(dirPath)) {
                    stream.forEach(path -> {
                        try {
                            //判断文明名称是否包含 PropertiesUtil.getProp(clientIdKey)
                            if (path.getFileName().toString().contains(PropertiesUtil.getProp(clientIdKey))) {
                                Files.deleteIfExists(path);
                                this.logger.info("删除文件 {} 成功", path);
                            }
                        } catch (IOException e) {
                            this.logger.error("删除文件 {} 异常", path, e);
                        }
                    });
                }
            }
        } catch (IllegalArgumentException | IOException e) {
            this.logger.error("删除文件异常，路径: {}", downloadFilePath, e);
        }
    }

    @Override
    public List<String> selectInIds(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return this.jdJdbMapper.selectInIds(idList);
    }

    @Override
    public List<String> selectAllJson(int i, int pageSize) {
        return this.jdJdbMapper.selectAllJson(i, pageSize);
    }


    @Override
    public List<String> selectAllJsonForScore(int i, int pageSize) {
        return jdJdbMapper.selectAllJsonForScore(i, pageSize);

    }

    @Override
    public List<String> selectIdList(String minId, int pageNumber) {

        return this.jdJdbMapper.selectIdList(minId, pageNumber);
    }

    public Map<String, Integer> fetchHotelInfoByHotelIds(List<List<HotelGnBase>> hotelInfoGroup, String date, String cityId) {
        //定义处理数据、成功及失败等数量
        //单个城市总酒店数据量（差旅酒店返回）
        int totalCountCL = 0;
        //单个城市酒店映射数据量
        int zhJdJdbMappingTotalCount = 0;
        //单个城市酒店映射去重后数据量
        int zhJdJdbMappingDistinctTotalCount = 0;
        //单个城市酒店最低价映射数据量
        int zhJdJdbMinPriceTotalCount = 0;
        //单个城市酒店最低价映射插入数据量
        int zhJdJdbMinPriceInsertTotalCount = 0;
        //单个城市酒店最低价映射更新数据量
        int zhJdJdbMinPriceUpdateTotalCount = 0;
        //单个城市酒店最低价映未处理射数据量
        int zhJdJdbMinPriceUnprocessedTotalCount = 0;


        Date saleDate = DateUtils.parseDate(date);
        //定义该城市下所有差旅酒店接口返回集合
        List<ChaiLlvHotelInfo> chaiLlvHotelInfoList = new ArrayList<>();

        //循环处理数据
        for (List<HotelGnBase> hotelInfoChailvgjList : hotelInfoGroup) {

            //获取主键id集合
            List<String> ids = hotelInfoChailvgjList.stream().map(HotelGnBase::getId).collect(Collectors.toList());

            JSONArray filterList = new JSONArray();
            //组装调用差旅接口的参数（因为只查询酒店id）
            for (String id : ids) {
                JSONObject filter = new JSONObject();
                filter.put("Type", 15);
                filter.put("Value", id);
                filterList.add(filter);
            }

            //调用差旅酒店搜索接口
            long start = System.currentTimeMillis();
            String hotelSearchResponse = (String) this.fetchDataFunction(PlatEnum.PLAT_CLGJ.getValue() + "_resource_Search", 1, 7, (result) -> ChailvgjApi.hotelSearch(Integer.valueOf(cityId), date, filterList, 1, 20));
            this.logger.info("差旅管家拉取最低价-》调用差旅酒店搜索接口，cityId = {}， date = {}， filterList = {}， hotelSearchResponse = {}， 耗时 = {}", cityId, date, JSON.toJSONString(filterList), hotelSearchResponse, System.currentTimeMillis() - start);

            //解析返回值
            Response<SearchModel> response = JSON.parseObject(hotelSearchResponse, new TypeReference<Response<SearchModel>>() {
            });
            if (ObjectUtils.isEmpty(response)
                    || !"200".equals(response.getCode())
                    || ObjectUtils.isEmpty(response.getData())
                    || CollectionUtils.isEmpty(response.getData().getHotelInfoList())) {
                continue;
            }

            List<ChaiLlvHotelInfo> hotelInfoList = response.getData().getHotelInfoList();
            //接口返回结果放集合统一处理，批量处理减少耗时
            chaiLlvHotelInfoList.addAll(hotelInfoList);

        }

        //处理所有差旅酒店搜索接口返回的数据
        if (CollectionUtils.isEmpty(chaiLlvHotelInfoList)) {

            HashMap<String, Integer> countResult = new HashMap<>();
            countResult.put("totalCountCL", totalCountCL);
            countResult.put("zhJdJdbMappingTotalCount", zhJdJdbMappingTotalCount);
            countResult.put("zhJdJdbMappingDistinctTotalCount", zhJdJdbMappingDistinctTotalCount);
            countResult.put("zhJdJdbMinPriceTotalCount", zhJdJdbMinPriceTotalCount);
            countResult.put("zhJdJdbMinPriceInsertTotalCount", zhJdJdbMinPriceInsertTotalCount);
            countResult.put("zhJdJdbMinPriceUpdateTotalCount", zhJdJdbMinPriceUpdateTotalCount);
            countResult.put("zhJdJdbMinPriceUnprocessedTotalCount", zhJdJdbMinPriceUnprocessedTotalCount);
            return countResult;
        }

        totalCountCL = chaiLlvHotelInfoList.size();
        //按500条分组批量处理
        List<List<ChaiLlvHotelInfo>> hotelInfoPartition = ListUtils.partition(chaiLlvHotelInfoList, 1000);

        //处理酒店映射信息表数据
        for (List<ChaiLlvHotelInfo> itemList : hotelInfoPartition) {
            //定义插入、更新集合
            List<ZhJdJdbMinPrice> insertList = new ArrayList<>();
            List<ZhJdJdbMinPrice> updateList = new ArrayList<>();

            List<Long> hotelIdList = itemList.stream().map(ChaiLlvHotelInfo::getHotelId).collect(Collectors.toList());
            //根据HotelId批量查询酒店映射信息表
            long start1 = System.currentTimeMillis();
            List<ZhJdJdbMapping> zhJdJdbMappingList = this.zhJdJdbMappingService.findByPlateIdsAndInterfacePlat(hotelIdList, PlatEnum.PLAT_CLGJ.getValue());
            this.logger.info("差旅管家拉取最低价-》查询本地酒店映射表，hotelIdList = {},总条数 = {}, 耗时 = {}", JSON.toJSONString(hotelIdList), zhJdJdbMappingList.size(), System.currentTimeMillis() - start1);
            zhJdJdbMappingTotalCount += zhJdJdbMappingList.size();

            //如果存在脏数据，PlatId相同的有多条，只取第一条
            zhJdJdbMappingList = new ArrayList<>(zhJdJdbMappingList.stream().collect(Collectors.toMap(
                            ZhJdJdbMapping::getPlatId,
                            mapping -> mapping,
                            (existing, replacement) -> existing))
                    .values());
            this.logger.info("差旅管家拉取最低价-》本地酒店映射表去重后，hotelIdList = {},总条数 = {}", JSON.toJSONString(hotelIdList), zhJdJdbMappingList.size());
            zhJdJdbMappingDistinctTotalCount += zhJdJdbMappingList.size();

            if (CollectionUtils.isEmpty(zhJdJdbMappingList)) {
                continue;
            }

            //获取映射表localId集合
            List<String> localIdList = zhJdJdbMappingList.stream().map(ZhJdJdbMapping::getLocalId).collect(Collectors.toList());

            //批量查询酒店最低价信息
            ZhJdJdbMinPriceParam zhJdJdbMinPriceParam = new ZhJdJdbMinPriceParam();
            zhJdJdbMinPriceParam.setJdids(localIdList);
            zhJdJdbMinPriceParam.setSxsj(saleDate);
            zhJdJdbMinPriceParam.setInterfacePlat(PlatEnum.PLAT_CLGJ.getValue());
            List<ZhJdJdbMinPrice> zhJdJdbMinPriceList = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceListByJdids(zhJdJdbMinPriceParam);
            //如果存在脏数据，Jdid相同的有多条，只取第一条
            zhJdJdbMinPriceList = new ArrayList<>(zhJdJdbMinPriceList.stream().collect(Collectors.toMap(
                            ZhJdJdbMinPrice::getJdid,
                            mapping -> mapping,
                            (existing, replacement) -> existing))
                    .values());
            //循环酒店映射表数据
            for (ZhJdJdbMapping zhJdJdbMapping : zhJdJdbMappingList) {
                //获取对应查询出来的差旅酒店信息(肯定有)
                ChaiLlvHotelInfo hotelInfo = itemList.stream().filter(item -> item.getHotelId().toString().equals(zhJdJdbMapping.getPlatId())).findFirst().orElse(null);
                //获取对应酒店最低价数据
                ZhJdJdbMinPrice zhJdJdbMinPrice = zhJdJdbMinPriceList.stream().filter(item -> zhJdJdbMapping.getLocalId().equals(item.getJdid())).findFirst().orElse(null);
                //如果为null，组装数据插入
                if (ObjectUtils.isEmpty(zhJdJdbMinPrice)) {
                    ZhJdJdbMinPrice temp = new ZhJdJdbMinPrice();
                    temp.setMinPrice(hotelInfo.getMinPrice());
                    temp.setJdid(zhJdJdbMapping.getLocalId());
                    temp.setSxsj(saleDate);
                    temp.setInterfacePlat(PlatEnum.PLAT_CLGJ.getValue());
                    insertList.add(temp);
                    this.logger.info("差旅管家插入最低价_sxsj：{},生效时间：{},拉取最低价:{}", temp.getJdid(), temp.getSxsj(), hotelInfo.getMinPrice());
                } else {
                    zhJdJdbMinPriceTotalCount++;
                    //不为null,判断酒店最低价比较
                    //不相等：更新
                    if (zhJdJdbMinPrice.getMinPrice().compareTo(hotelInfo.getMinPrice()) != 0) {
                        zhJdJdbMinPrice.setMinPrice(hotelInfo.getMinPrice());
                        updateList.add(zhJdJdbMinPrice);
                        zhJdJdbMinPriceUpdateTotalCount++;
                        this.logger.info("差旅管家最低价更新成功_localId：{} sxsj：{},平台编码：{}", zhJdJdbMapping.getLocalId(), saleDate, PlatEnum.PLAT_CLGJ.getValue());
                        continue;
                    }
                    //如果相等：不处理
                    zhJdJdbMinPriceUnprocessedTotalCount++;
                    this.logger.info("差旅管家不需要更新最低价_sxsj：{},酒店ID：{},最低价：{},拉取最低价:{}", zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getMinPrice(), hotelInfo.getMinPrice());
                }
            }


            //处理最低价映射表
//            for (ZhJdJdbMapping zhJdJdbMapping : zhJdJdbMappingList) {
//                //获取对应查询出来的差旅酒店信息(肯定有)
//                ChaiLlvHotelInfo hotelInfo = itemList.stream().filter(item -> item.getHotelId().toString().equals(zhJdJdbMapping.getPlatId())).findFirst().orElse(null);
//
//                //查询酒店最低价表信息
//                ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
//                zhJdJdbMinPrice.setJdid(zhJdJdbMapping.getLocalId());
//                zhJdJdbMinPrice.setSxsj(saleDate);
//                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_CLGJ.getValue());
//                List<ZhJdJdbMinPrice> prices = zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
//                //不存在：插入
//                if (CollectionUtils.isEmpty(prices)) {
//                    zhJdJdbMinPrice.setSxsj(saleDate);
//                    zhJdJdbMinPrice.setMinPrice(hotelInfo.getMinPrice());
//                    zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_CLGJ.getValue());
//                    insertList.add(zhJdJdbMinPrice);
//                } else {
//                    zhJdJdbMinPriceTotalCount++;
//                    //存在
//                    zhJdJdbMinPrice = prices.get(0);
//                    //比较表中最低价与差旅最新接口返回作比较
//                    //相同：不处理
//                    if (zhJdJdbMinPrice.getMinPrice().compareTo(hotelInfo.getMinPrice()) == 0) {
//                        //不需要更新最低价
//                        zhJdJdbMinPriceUnprocessedTotalCount++;
//                        logger.info("差旅管家不需要更新最低价_sxsj：{},酒店ID：{},最低价：{},拉取最低价:{}", zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getMinPrice(), hotelInfo.getMinPrice());
//                    } else {
//                        //不同：更新
//                        zhJdJdbMinPrice.setSxsj(saleDate);
//                        zhJdJdbMinPrice.setMinPrice(hotelInfo.getMinPrice());
//                        zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_CLGJ.getValue());
//                        zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
//                        logger.info("差旅管家最低价更新成功_localId：{} sxsj：{},平台编码：{}", zhJdJdbMapping.getLocalId(), saleDate, PlatEnum.PLAT_CLGJ.getValue());
//                        zhJdJdbMinPriceUpdateTotalCount++;
//                    }
//                }
//            }

            //插入批量处理
            if (CollectionUtils.isNotEmpty(insertList)) {
                long startTime = System.currentTimeMillis();
                log.info("差旅管家批量插入开始时间：{}", startTime);
                this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(insertList);
                log.info("差旅管家批量插入完成 耗时：{},数量:{}", (System.currentTimeMillis() - startTime), insertList.size());
                zhJdJdbMinPriceInsertTotalCount += insertList.size();
            }

            //更新批量处理
            if (CollectionUtils.isNotEmpty(updateList)) {
                long startTime = System.currentTimeMillis();
                log.info("差旅管家批量更新开始时间：{}", startTime);
                this.zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(updateList);
                log.info("差旅管家批量更新完成 耗时：{},数量:{}", (System.currentTimeMillis() - startTime), updateList.size());
                zhJdJdbMinPriceUpdateTotalCount += updateList.size();
            }

        }


        //数量统计返回
        HashMap<String, Integer> countResult = new HashMap<>();
        countResult.put("totalCountCL", totalCountCL);
        countResult.put("zhJdJdbMappingTotalCount", zhJdJdbMappingTotalCount);
        countResult.put("zhJdJdbMappingDistinctTotalCount", zhJdJdbMappingDistinctTotalCount);
        countResult.put("zhJdJdbMinPriceTotalCount", zhJdJdbMinPriceTotalCount);
        countResult.put("zhJdJdbMinPriceInsertTotalCount", zhJdJdbMinPriceInsertTotalCount);
        countResult.put("zhJdJdbMinPriceUpdateTotalCount", zhJdJdbMinPriceUpdateTotalCount);
        countResult.put("zhJdJdbMinPriceUnprocessedTotalCount", zhJdJdbMinPriceUnprocessedTotalCount);

        return countResult;
    }

    public Object fetchDataFunction(String key, long second, long num, Function<Integer, Object> function) {
        int tryNum = 1;
        RRateLimiter rRateLimiter = this.limiter.getLimiter(key, second, num);
        boolean tryAcquire = rRateLimiter.tryAcquire(1);
        while (!tryAcquire) {
            this.logger.info("当前供应商渠道【{}】限流.......睡眠500ms", key);
            try {
                Thread.sleep(500);
            } catch (InterruptedException ex) {
                this.logger.info("睡眠异常。。。。。。。。。。", ex);
                Thread.currentThread().interrupt();
            }
//            if (tryNum > 5) {
//                break;
//            }
            tryNum++;
            tryAcquire = rRateLimiter.tryAcquire(1);
        }
        return function.apply(tryNum);
    }


    public void fetchData(String key, long second, long num, Consumer<Integer> consumer) {
        int tryNum = 1;
        RRateLimiter rRateLimiter = this.limiter.getLimiter(key, second, num);
        boolean tryAcquire = rRateLimiter.tryAcquire(1);
        while (!tryAcquire) {
            this.logger.info("当前供应商渠道【{}】限流.......睡眠100ms", key);
            try {
                Thread.sleep(100);
            } catch (InterruptedException ex) {
                this.logger.info("睡眠异常。。。。。。。。。。");
            }
            tryNum++;
            tryAcquire = rRateLimiter.tryAcquire(1);
        }
        consumer.accept(tryNum);
    }

    private void deleteRunningKey(String runningKey) {
        Boolean delete = this.stringRedisTemplate.delete(runningKey);
        if (Boolean.TRUE.equals(delete)) {
            this.logger.info("runningKey删除成功");
        } else {
            this.logger.info("runningKey删除失败");
        }
    }

    @Override
    public List<JdJdb> selectJdJdbByPlaId(String plat, String platId) {
        return this.jdJdbMapper.selectJdJdbByPlaId(plat, platId);
    }

    @Override
    public void dealListMinPriceCLGJNew(String localId, Long hotelCode) {

        //定义插入、更新集合
        List<ZhJdJdbMinPrice> insertList = new ArrayList<>();
        List<ZhJdJdbMinPrice> updateList = new ArrayList<>();

        //查询差旅管家酒店信息
        HotelInfoChailvgj hotelInfoChailvgj = this.hotelInfoChailvgjService.selectHotelInfoChailvgjById(hotelCode.toString());
        if (ObjectUtils.isEmpty(hotelInfoChailvgj)) {
            log.error("未查询到供应商酒店信息， hotelCode = {}", hotelCode);
            return;
        }

        //组装调用差旅接口的参数（因为只查询酒店id）
        JSONArray filterList = new JSONArray();
        JSONObject filter = new JSONObject();
        filter.put("Type", 15);
        filter.put("Value", hotelCode);
        filterList.add(filter);

        //30天时间遍历
        for (int i = 0; i < 30; i++) {
            Date saleDate = DateUtils.parseDate(LocalDate.now().plusDays(i));
            String dateStr = LocalDate.now().plusDays(i).toString();
            String dateStrAfter = LocalDate.now().plusDays(i + 1).toString();

            //调用差旅管家api
            String hotelSearchResponse = (String) this.fetchDataFunction(PlatEnum.PLAT_CLGJ.getValue() + "_resource_Search", 1, 7, (result) -> ChailvgjApi.hotelSearch(Integer.valueOf(hotelInfoChailvgj.getCityId()), dateStr, dateStrAfter, filterList, null, 1, 20));

            //解析返回值
            Response<SearchModel> response = JSON.parseObject(hotelSearchResponse, new TypeReference<Response<SearchModel>>() {
            });
            if (ObjectUtils.isEmpty(response)
                    || !"200".equals(response.getCode())
                    || ObjectUtils.isEmpty(response.getData())
                    || CollectionUtils.isEmpty(response.getData().getHotelInfoList())) {
                log.error("调用差旅管家api失败，hotelCode = {}, dateStr = {}, response = {}", hotelCode, dateStr, response);
                continue;
            }
            List<ChaiLlvHotelInfo> hotelInfoList = response.getData().getHotelInfoList();
            if (CollectionUtils.isEmpty(hotelInfoList)) {
                log.error("调用差旅管家api返回数据为空，hotelCode = {}, dateStr = {}, response = {}", hotelCode, dateStr, response);
                continue;
            }

            ChaiLlvHotelInfo chaiLlvHotelInfo = hotelInfoList.get(0);

            //查询最低价信息
            ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
            zhJdJdbMinPrice.setJdid(localId);
            zhJdJdbMinPrice.setSxsj(saleDate);
            zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_CLGJ.getValue());
            List<ZhJdJdbMinPrice> minPrices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
            //插入
            if (CollectionUtils.isEmpty(minPrices)) {
                zhJdJdbMinPrice.setMinPrice(chaiLlvHotelInfo.getMinPrice());
                insertList.add(zhJdJdbMinPrice);
            } else {
                ZhJdJdbMinPrice temp = minPrices.get(0);
                //比较最小金额是否相同
                if (temp.getMinPrice().compareTo(chaiLlvHotelInfo.getMinPrice()) != 0) {
                    //更新
                    temp.setMinPrice(chaiLlvHotelInfo.getMinPrice());
                    updateList.add(temp);
                }
            }
        }

        //插入批量处理
        if (CollectionUtils.isNotEmpty(insertList)) {
            this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(insertList);
        }

        //更新批量处理
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(updateList);
        }

    }


    /**
     * 此方法调用jdJdbMapper中的相应方法，以获取在数据库中通过酒店名称和地址筛选出的重复项列表
     * 主要用于数据去重或检查是否存在重复的酒店记录
     *
     * @return 返回一个JdJdbRepeatDTO对象列表，包含通过酒店名称和地址筛选出的重复项
     */
    @Override
    public List<JdJdbRepeatDTO> selectJdJdbRepeatByJdmcAndJddz(JdJdbRepeatDTO jdJdbRepeatDTO) {
        return this.jdJdbMapper.selectJdJdbRepeatByJdmcAndJddz(jdJdbRepeatDTO);
    }

    @Override
    public List<JdJdb> selectJdJdbByIds(String[] ids) {
        if (ids == null || ids.length == 0) return new ArrayList<>();
        return this.jdJdbMapper.getJdJdbByIdsNew(Arrays.stream(ids).collect(Collectors.toList()));
    }

    private void handlerJdmc(JdJdb jdJdb) {
        if (jdJdb == null) {
            return;
        }
        if (StringUtils.isNotBlank(jdJdb.getJdmc())) {// 酒店名称中文括号替换成英文
            String jdmc = jdJdb.getJdmc();
            jdmc = jdmc.replaceAll("（", "(");
            jdmc = jdmc.replaceAll("）", ")");
            jdJdb.setJdmc(jdmc);
        }
    }

    /**
     * 更新科坦最低价格
     *
     * @param minPriceReq
     * @return
     */
    @Override
    public AjaxResult updateLowPriceOfCozyTime(MinPriceReq minPriceReq) {
        String runningKey = PROJECT_NAME + "_" + "jd_min_price" + "_" + PlatEnum.PLAT_KT.getValue() + "_" + "running";
        RLock rLock = this.redissonClient.getLock(runningKey);
        Boolean lock = false;
        try {
            lock = rLock.tryLock();
            if (lock) {
                // 如果时间段有一个为空就是全量更新
                if (StringUtils.isBlank(minPriceReq.getCheckInDate()) || StringUtils.isBlank(minPriceReq.getCheckOutDate())) {

                    ///////////////////////////////////

                    List<HotelGnBase> allIdList = hotelGnBaseService.getAllIdList(PlatEnum.PLAT_KT);
                    if(CollectionUtils.isEmpty(allIdList)){
                        this.logger.warn("科坦最低价获取酒店Ids为空,不执行");
                        return success();
                    }
                    List<Long> idList = allIdList.stream().mapToLong(base -> Long.valueOf(base.getId())).boxed().collect(Collectors.toList());
                    List<List<Long>> hotelIdList = ListUtil.groupList(idList, 100);

                    this.logger.info("科坦最低价全量拉取总共 {} 数据", allIdList.size());
                    long startTime = System.currentTimeMillis();
                    this.logger.info("科坦最低价开始执行 当前时间：{}", startTime);
                    LongAdder count = new LongAdder();
                    this.logger.info("科坦count:{},酒店分组数量 {}", count, hotelIdList.size());
                    this.asyncUpdateLowPriceOfCozyTime(1, hotelIdList, count);

                    ///////////////////////////////////


                } else {

                    CozyTimeHotelApi20HotelLowestPriceIncrementRequest request = new CozyTimeHotelApi20HotelLowestPriceIncrementRequest();
                    request.setStartDate(minPriceReq.getCheckInDate());
                    request.setEndDate(minPriceReq.getCheckOutDate());
                    CozyTimeHotelApi20HotelLowestPriceIncrementResponse response = this.cozyTimeSdkApi.execute(request);
                    if (response.success()) {
                        minPriceReq.setHotelIdList(response.getHotelIdList());
                        if (CollectionUtils.isEmpty(minPriceReq.getHotelIdList())) {
                            this.logger.warn("科坦最低价需要执行酒店Ids为空,不执行");
                            return success();
                        }

                        long startTime = System.currentTimeMillis();
                        this.logger.info("科坦最低价开始执行 当前时间：{}", startTime);
                        List<List<Long>> hotelIdList = ListUtil.groupList(minPriceReq.getHotelIdList(), 100);
                        LongAdder count = new LongAdder();
                        this.logger.info("科坦count:{},酒店分组数量 {}", count, hotelIdList.size());
                        this.asyncUpdateLowPriceOfCozyTime(1, hotelIdList, count);
                    }
                }

            } else {
                this.logger.info("科坦最低价同步任务已经在执行 。。。。。");
            }
        } catch (Exception e) {
            this.logger.info("科坦最低价同步异常", e);
        } finally {
            if (lock) {
                try {
                    rLock.unlock();
                } catch (Exception e) {
                    this.logger.info("### 科坦同步最低价_解锁异常,", e);
                }
            }
        }

        return success();
    }

    public void asyncUpdateLowPriceOfCozyTime(int page, List<List<Long>> hotelIdList, LongAdder count) {
        List<CompletableFuture<?>> completableFutures = hotelIdList.stream().map(item -> {
            return CompletableFuture.runAsync(() -> {
                try {
                    List<List<Long>> lists = MyTools.splitList(item, this.batchCount);
                    MinPriceReq req = new MinPriceReq();
                    for (List<Long> hotelIds : lists) {
                        long startTime = System.currentTimeMillis();
                        req.setHotelIdList(hotelIds);
                        this.dealListMinPriceCozyTime(req, count);
                        this.logger.info("科坦同步第{}页数据{}个酒店耗时:{}ms", page, hotelIds.size(), (System.currentTimeMillis() - startTime));
                    }
                } catch (Exception e) {
                    this.logger.error("科坦同步最低价任务执行失败", e);
                }
            }, this.cozytimePriceExecutor).thenRunAsync(() -> count.increment());
        }).collect(Collectors.toList());

        CompletableFuture<?> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));

        try {
            allOf.get();
        } catch (Exception e) {
            this.logger.error("### JdJdbServiceImpl_asyncUpdateLowPriceOfCozyTime_任务执行失败:{}", e.getMessage(), e);
        }
    }

    @Override
    public void dealListMinPriceCozyTime(MinPriceReq minPriceReq, LongAdder count) {
        MinPriceReqV2 minPriceReqV2 = MinPriceReqV2.builder()
                .ids(minPriceReq.getHotelIdList())
                .retryCount(1)
                .build();
        CozyTimeHotelApi20HotelLowestPriceResponse cozyTimeResp = this.reCallCozyTime(minPriceReqV2);
        if (cozyTimeResp != null) {
            if (cozyTimeResp.success()) {
                List<CozyTimeHotelApi20HotelLowestPriceResponseLowestPrice> hotelLowestPrices = cozyTimeResp.getLowestPrices();
                this.logger.info("科坦返回总数 count:{},hotelLowestPrices:{}", count, hotelLowestPrices.size());
                List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsert = Lists.newArrayList();
                List<ZhJdJdbMinPrice> zhJdJdbMinPriceListUpdate = Lists.newArrayList();
                List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsertEnd = Lists.newArrayList();
                for (CozyTimeHotelApi20HotelLowestPriceResponseLowestPrice hotelLowestPrice : hotelLowestPrices) {
                    //1.根据酒店id和PlatEnum.查询zh_jd_jdb_mapping
                    ZhJdJdbMapping mapping = new ZhJdJdbMapping();
                    mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_KT.getValue()));
                    mapping.setPlatId(String.valueOf(hotelLowestPrice.getHotelId()));
                    List<ZhJdJdbMapping> zhJdJdbMappingList = this.zhJdJdbMappingService.selectZhJdJdbMappingList(mapping);
                    if (CollectionUtils.isNotEmpty(zhJdJdbMappingList)) {
                        ZhJdJdbMapping jdJdbMapping = zhJdJdbMappingList.get(0);
                        String localId = jdJdbMapping.getLocalId();
                        this.logger.info("科坦酒店映射成功 local_id:{}", jdJdbMapping.getLocalId());
                        //获取localId
                        List<CozyTimeHotelApi20HotelLowestPriceResponsePriceItem> priceItems = hotelLowestPrice.getPriceItems();
                        for (CozyTimeHotelApi20HotelLowestPriceResponsePriceItem priceItem : priceItems) {
                            try {
                                BigDecimal salePrice = new BigDecimal(priceItem.getSalePrice());
                                Date date = DateUtils.parseDate(priceItem.getSaleDate(), DateUtils.YYYY_MM_DD);
                                ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                                zhJdJdbMinPrice.setJdid(localId);
                                zhJdJdbMinPrice.setSxsj(date);
                                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_KT.getValue());
                                List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                                if (CollectionUtils.isEmpty(prices)) {
                                    zhJdJdbMinPrice.setSxsj(date);
                                    zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                                    zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_KT.getValue());
                                    zhJdJdbMinPriceListInsert.add(zhJdJdbMinPrice);
                                } else {
                                    this.logger.info("科坦酒店最低价查询成功 localId：{} sxsj：{},size:{}", localId, date, prices.size());
                                    zhJdJdbMinPrice = prices.get(0);
                                    if (zhJdJdbMinPrice.getMinPrice().intValue() - salePrice.intValue() == 0) {
                                        //不需要更新最低价
                                        this.logger.info("科坦当前酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), salePrice);
                                    } else {
                                        zhJdJdbMinPrice.setSxsj(date);
                                        zhJdJdbMinPrice.setMinPrice(salePrice);
                                        zhJdJdbMinPriceListUpdate.add(zhJdJdbMinPrice);
                                        this.logger.info("科坦最低价更新成功 localId：{} sxsj：{}", localId, date);
                                    }
                                }
                            } catch (Exception e) {
                                this.logger.error("科坦最低价梳理数据异常local_id:{}, priceItem:{}", localId, JSON.toJSONString(priceItem), e);

                            }
                        }
                    } else {
                        String hotelId = String.valueOf(hotelLowestPrice.getHotelId());
                        String localId = PlatEnum.PLAT_KT.getValue() + hotelId;
                        //    this.logger.info("科坦未映射酒店数据最低价暂不处理 localId:{} hotelId:{}", localId, hotelLowestPrice.getHotelId());
                        //todo 只有20版本的存储没有映射关系，后期不需要
                        this.logger.info("科坦未映射酒店数据最低价插入成功2 localId:{} hotelId:{}", localId, hotelLowestPrice.getHotelId());
                        List<CozyTimeHotelApi20HotelLowestPriceResponsePriceItem> priceItems = hotelLowestPrice.getPriceItems();
                        for (CozyTimeHotelApi20HotelLowestPriceResponsePriceItem priceItem : priceItems) {

                            ZhJdJdbMinPrice zhJdJdbMinPrice = null;
                            try {
                                Date date = DateUtils.parseDate(priceItem.getSaleDate(), DateUtils.YYYY_MM_DD);
                                zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                                zhJdJdbMinPrice.setJdid(localId);
                                zhJdJdbMinPrice.setMinPrice(new BigDecimal(priceItem.getSalePrice()));
                                zhJdJdbMinPrice.setSxsj(date);
                                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_KT.getValue());
                            } catch (Exception e) {
                                this.logger.error("科坦最低价梳理数据异常local_id:{}, priceItem:{}", localId, JSON.toJSONString(priceItem), e);
                            }
                            //zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                            zhJdJdbMinPriceListInsertEnd.add(zhJdJdbMinPrice);
                            this.logger.info("科坦最低价插入成功2 local_id:{}", zhJdJdbMinPrice.getJdid());
                        }
                    }

                }

                if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsert)) {
                    int zhJdJdbMinPriceResult = this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsert);
                    if (zhJdJdbMinPriceResult != 0) this.logger.info("科坦最低价插入成功");
                }

                if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListUpdate)) {
                    int updateZhJdJdbMinPrice = this.zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(zhJdJdbMinPriceListUpdate);
                    if (updateZhJdJdbMinPrice != 0) this.logger.info("科坦最低价插入成功");
                }

                if (CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsertEnd)) {
                    int insertZhJdJdbMinPrice = this.zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsertEnd);
                    if (insertZhJdJdbMinPrice != 0) this.logger.info("科坦最低价插入成功END");
                    this.logger.info("科坦最低价更新成功");
                }

            } else {
                this.logger.error("科坦最低价返回异常 {},当前count:{}", JSONObject.toJSONString(cozyTimeResp), count);
            }
        } else {
            this.logger.error("科坦最低价返回为空 {}", JSONObject.toJSONString(minPriceReq));
        }

    }

    @Override
    public List<JdJdb> selectAll(int i, int pageSize) {
        return jdJdbMapper.selectAll(i, pageSize);
    }

    @Override
    public List<String> selectCityId() {
        return jdJdbMapper.selectCityId();
    }

    @Override
    public List<JdJdb> selectByCityId(String cityId, Date date) {
      return  jdJdbMapper.selectByCityId(cityId,date);
    }

    @Override
    public int count() {
      return   jdJdbMapper.count();
    }

    @Override
    public List<JdJdb> listByIds(List<String> ids) {
        return jdJdbMapper.listByIds(ids);
    }

    @Override
    public void updateKnowledgeIdByCityIdAndDate(String cityId, String knowledgeId, Date date) {
        jdJdbMapper.updateKnowledgeIdByCityIdAndDate(cityId,knowledgeId,date);
    }

    private CozyTimeHotelApi20HotelLowestPriceResponse reCallCozyTime(MinPriceReqV2 minPriceReq) {
        CozyTimeHotelApi20HotelLowestPriceRequest request = new CozyTimeHotelApi20HotelLowestPriceRequest();
        request.setHotelIds(minPriceReq.getIds());
        CozyTimeHotelApi20HotelLowestPriceResponse result = null;
        try {
            result = this.cozyTimeSdkApi.execute(request);
            this.logger.info("### reCallCozyTime_科坦最低价返回 {}", result);
        } catch (Exception e) {
            this.logger.error("### reCallCozyTime_科坦最低价返回异常 {}", result, e);
            return null;
        }

        while ("429".equals(result.getResultCode())) {// 请求次数超过阀值
            if (minPriceReq.getRetryCount() > 2) break;
            this.logger.warn("### reCallCozyTime_科坦最低价返回null，重试次数：{}", minPriceReq.getRetryCount());
            try {
                this.logger.info("睡眠10s。。。。。。。。。。。。。。。");
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                this.logger.error("睡眠10s异常。。。。。。。。。。。。。。。");
            }
            result = this.reCallCozyTime(minPriceReq);
            minPriceReq.setRetryCount(minPriceReq.getRetryCount() + 1);
            this.logger.info("### reCallCozyTime重试第 {} 次", minPriceReq.getRetryCount());
        }
        return result;
    }

}
