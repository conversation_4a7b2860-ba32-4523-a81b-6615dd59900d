package com.ltgj.ivw.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/6/13
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HotelGnGeoMappingDTO {

    /**
     * 平台编码
     */
    private String platformId;
    /**
     * 平台商圈id
     */
    private String geoId;
    /**
     * 平台商圈名称
     */
    private String geoName;
    /**
     * 平台城市id (至少传)
     */
    private String cityId;
    /**
     * 基础城市id (优先传)
     */
    private String baseCityId;
}
