package com.ltgj.supplier.cozyTime;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.supplier.common.dto.KetanCityDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.context.annotation.Configuration;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPInputStream;

/**
 * 科坦API调用类
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Slf4j
@Configuration
public class KetanApi {

    private static final String BASE_URL = "cozyTime.url";
    private static final String PARTNER_CODE = "cozyTime.partnerCode";
    private static final String SECRET_KEY = "cozyTime.secretKey";
    private static final String CONNECT_TIMEOUT = "cozyTime.connectTimeout";
    private static final String READ_TIMEOUT = "cozyTime.readTimeout";
    
    /**
     * HTTP请求方法枚举
     */
    public enum HttpMethod {
        GET, POST
    }

    private static OkHttpClient okHttpClient;

    static {
        int connectTimeout = Integer.parseInt(PropertiesUtil.getProp(CONNECT_TIMEOUT, "60000"));
        int readTimeout = Integer.parseInt(PropertiesUtil.getProp(READ_TIMEOUT, "60000"));
        
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(readTimeout, TimeUnit.MILLISECONDS)
                .build();
    }

    /**
     * 获取科坦城市列表
     *
     * @return 城市数据列表
     */
    public static KetanCityDTO getCityList() {
        // 尝试使用GET方法获取城市列表
        String responseBody = sendRequest("/hotel_content_api/2.0/cities", "城市列表", "", HttpMethod.GET);
        if (StringUtils.isBlank(responseBody)) {
            // 如果GET失败，尝试使用POST方法
            responseBody = sendRequest("/hotel_content_api/2.0/cities", "城市列表", "", HttpMethod.POST);
            if (StringUtils.isBlank(responseBody)) {
                return null;
            }
        }
        return JSON.parseObject(responseBody, KetanCityDTO.class);
    }
    
    /**
     * 使用指定方法获取科坦城市列表
     *
     * @param method HTTP请求方法
     * @return 城市数据列表
     */
    public static KetanCityDTO getCityList(HttpMethod method) {
        String responseBody = sendRequest("/hotel_content_api/2.0/cities", "城市列表", "", method);
        if (StringUtils.isBlank(responseBody)) {
            return null;
        }
        return JSON.parseObject(responseBody, KetanCityDTO.class);
    }

    /**
     * 发送请求到科坦API（默认使用POST方法）
     *
     * @param urlPath    API路径
     * @param methodDesc 方法描述（用于日志）
     * @param requestBody 请求体
     * @return 响应数据
     */
    private static String sendRequest(String urlPath, String methodDesc, String requestBody) {
        return sendRequest(urlPath, methodDesc, requestBody, HttpMethod.POST);
    }
    
    /**
     * 发送请求到科坦API
     *
     * @param urlPath    API路径
     * @param methodDesc 方法描述（用于日志）
     * @param requestBody 请求体
     * @param httpMethod HTTP请求方法
     * @return 响应数据
     */
    private static String sendRequest(String urlPath, String methodDesc, String requestBody, HttpMethod httpMethod) {
        try {
            // 通过OkHttpClient调用请求得到Call
            final Call call = okHttpClient.newCall(buildRequest(urlPath, requestBody, httpMethod));
            
            // 执行同步请求，获取Response对象
            Response response = call.execute();
            ResponseBody body = response.body();
            
            if (Objects.isNull(body)) {
                log.info("[科坦酒店-{}], 查询请求: 响应参数=null", methodDesc);
                return null;
            }
            
            // 获取内容类型
            String contentEncoding = response.header("Content-Encoding");
            byte[] responseBytes = body.bytes();
            String resultStr;
            
            // 处理GZIP压缩
            if ("gzip".equalsIgnoreCase(contentEncoding)) {
                resultStr = decompressGzip(responseBytes);
            } else {
                resultStr = new String(responseBytes, StandardCharsets.UTF_8);
            }
            
            log.info("[科坦酒店-{}], 查询请求: 响应参数={}", methodDesc, resultStr);
            
            // 检查响应状态码
            if (!response.isSuccessful()) {
                log.info("[科坦酒店-{}], 请求失败 状态码={}, 错误信息={}",
                       methodDesc, response.code(), resultStr);
                return null;
            }
            
            if (StringUtils.isNotBlank(resultStr)) {
                try {
                    JSONObject json = JSON.parseObject(resultStr);
                    if (json.containsKey("code") && json.getIntValue("code") != 200) {
                        log.info("[科坦酒店-{}], 请求失败：{}", methodDesc, resultStr);
                        return null;
                    }
                    if (json.containsKey("data")) {
                        return json.getString("data");
                    }
                    // 如果响应不包含code和data字段，则直接返回整个响应内容
                    return resultStr;
                } catch (Exception e) {
                    // 如果不是JSON格式，直接返回响应内容
                    log.info("[科坦酒店-{}], 响应不是JSON格式，直接返回响应内容", methodDesc);
                    return resultStr;
                }
            }
            return null;
        } catch (Exception e) {
            log.error("[科坦酒店-{}] 数据异常 errorMessage:{}, error:{}", methodDesc, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 解压GZIP数据
     *
     * @param compressedData 压缩的字节数据
     * @return 解压后的字符串
     */
    private static String decompressGzip(byte[] compressedData) {
        try (ByteArrayInputStream bis = new ByteArrayInputStream(compressedData);
             GZIPInputStream gis = new GZIPInputStream(bis);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }
            
            return bos.toString(StandardCharsets.UTF_8.name());
        } catch (IOException e) {
            log.error("GZIP解压缩失败", e);
            // 如果解压失败，尝试直接以UTF-8解析
            return new String(compressedData, StandardCharsets.UTF_8);
        }
    }

    /**
     * 构建请求对象（默认使用POST方法）
     *
     * @param urlPath API路径
     * @param body    请求体
     * @return Request对象
     */
    private static Request buildRequest(String urlPath, String body) {
        return buildRequest(urlPath, body, HttpMethod.POST);
    }
    
    /**
     * 构建请求对象
     *
     * @param urlPath API路径
     * @param body    请求体
     * @param httpMethod HTTP请求方法
     * @return Request对象
     */
    private static Request buildRequest(String urlPath, String body, HttpMethod httpMethod) {
        long timestamp = System.currentTimeMillis() / 1000;
        String partnerCode = PropertiesUtil.getProp(PARTNER_CODE);
        
        // 构建请求头
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("timestamp", String.valueOf(timestamp));
        headerMap.put("partnerCode", partnerCode);
        headerMap.put("x-Signature", generateSignature(partnerCode, timestamp));
        headerMap.put("Accept", "application/json");
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        
        String url = PropertiesUtil.getProp(BASE_URL) + urlPath;
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .headers(Headers.of(headerMap));
                
        // 根据HTTP方法类型设置请求方式
        if (httpMethod == HttpMethod.POST) {
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody requestBody = RequestBody.create(mediaType, StringUtils.isBlank(body) ? "{}" : body);
            requestBuilder.post(requestBody);
        } else {
            requestBuilder.get();
        }
        
        log.info("科坦酒店, 请求方法={}, 请求url={}, 请求header={}, 请求参数={}",
                httpMethod, url, JSON.toJSONString(headerMap), body);
                
        return requestBuilder.build();
    }

    /**
     * 生成签名
     *
     * @param partnerCode 合作商编码
     * @param timestamp   时间戳
     * @return 签名字符串
     */
    public static String generateSignature(String partnerCode, long timestamp) {
        String secretKey = PropertiesUtil.getProp(SECRET_KEY);
        String signStr = partnerCode + secretKey + timestamp;
        log.info("签名原始字符串: {}", signStr);
        
        try {
            // 使用SHA-512进行哈希
            MessageDigest md = MessageDigest.getInstance("SHA-512");
            byte[] messageDigest = md.digest(signStr.getBytes(StandardCharsets.UTF_8));
            
            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            String signature = hexString.toString();
            log.info("生成的签名: {}", signature);
            return signature;
        } catch (Exception e) {
            log.error("生成签名异常", e);
            return null;
        }
    }
} 