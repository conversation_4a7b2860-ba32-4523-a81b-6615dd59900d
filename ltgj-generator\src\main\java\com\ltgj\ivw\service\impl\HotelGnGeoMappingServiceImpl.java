package com.ltgj.ivw.service.impl;

import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.GeoCityGeo;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.dto.HotelGnGeoMappingDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IGeoCityGeoService;
import com.ltgj.ivw.service.IHotelCityMappingService;
import com.ltgj.ivw.service.IHotelGnGeoMappingService;
import com.ltgj.ivw.vo.HotelGnGeoMappingVO;
import com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping;
import com.ltgj.supplier.common.gn.domain.HotelGnGeoMappingExample;
import com.ltgj.supplier.common.gn.mapper.HotelGnGeoMappingMapper;
import com.ltgj.supplier.common.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/13
 * @description: 商圈映射数据服务实现
 */
@Service("hotelGnGeoMappingService")
@Slf4j
public class HotelGnGeoMappingServiceImpl implements IHotelGnGeoMappingService {

    @Autowired
    private HotelGnGeoMappingMapper hotelGnGeoMappingMapper;

    @Autowired
    private IGeoCityGeoService geoCityGeoService;

    @Autowired
    private IHotelCityMappingService hotelCityMappingService;

    private IdUtil sequence = new IdUtil();

    @Override
    public int batchInsertOrUpdate(List<HotelGnGeoMapping> records) {
        return hotelGnGeoMappingMapper.batchInsertOrUpdate(records);
    }

    /**
     * 创建商圈映射数据
     *
     * @param dto 商圈映射数据传输对象
     * @return 受影响的数据库行数
     */
    @Override
    public HotelGnGeoMappingVO createGnGeoMapping(HotelGnGeoMappingDTO dto) {
        log.info("开始创建商圈映射数据，平台ID: {}, 城市ID: {}", dto.getPlatformId(), dto.getCityId());

        // 1. 校验输入数据
        checkData(dto);

        // 2. 查询商圈数据
        List<GeoCityGeo> geoCityGeos = queryGeoCityGeos(dto);
        if (CollectionUtils.isEmpty(geoCityGeos)) {
            log.warn("未找到匹配的商圈数据，cityId: {}, geoId: {}, geoName: {}",
                    dto.getCityId(), dto.getGeoId(), dto.getGeoName());
            return null;
        }

        // 3. 构建映射数据（只取第一条商圈记录）
        GeoCityGeo geo = geoCityGeos.get(0);
        HotelGnGeoMapping mapping = buildHotelGnGeoMapping(dto, geo);
        // 4. 批量插入或更新
        int result = batchInsertOrUpdate(Collections.singletonList(mapping));

        log.info("商圈映射数据创建完成，受影响行数: {}", result);

        if (result > 0) {
            return HotelGnGeoMappingVO.builder().baseGeoId(geo.getGeoId()).baseGeoName(geo.getGeoName()).build();
        }

        log.warn("商圈映射数据创建失败,dto:{}",dto);

        return null;
    }

    @Override
    public HotelGnGeoMappingVO selectGnGeoMapping(HotelGnGeoMappingDTO hotelGnGeoMappingDTO) {
        List<HotelGnGeoMapping> hotelGnGeoMappings = null;
        HotelGnGeoMappingExample hotelGnGeoMappingExample = new HotelGnGeoMappingExample();
        HotelGnGeoMappingExample.Criteria criteria = hotelGnGeoMappingExample.createCriteria();
        if (hotelGnGeoMappingDTO.getGeoId() != null) {
            criteria.andPlatformIdEqualTo(hotelGnGeoMappingDTO.getPlatformId())
                    .andPlatformCityIdEqualTo(hotelGnGeoMappingDTO.getGeoName())
                    .andPlatformGeoIdEqualTo(hotelGnGeoMappingDTO.getGeoId());
            hotelGnGeoMappings = hotelGnGeoMappingMapper.selectByExample(hotelGnGeoMappingExample);
        }


        if (CollectionUtils.isEmpty(hotelGnGeoMappings)) {
            hotelGnGeoMappingExample.clear();
            criteria.andPlatformIdEqualTo(hotelGnGeoMappingDTO.getPlatformId())
                    .andPlatformCityIdEqualTo(hotelGnGeoMappingDTO.getGeoName())
                    .andPlatformGeoNameEqualTo(hotelGnGeoMappingDTO.getGeoName());
            hotelGnGeoMappings = hotelGnGeoMappingMapper.selectByExample(hotelGnGeoMappingExample);
        }

        if (CollectionUtils.isEmpty(hotelGnGeoMappings)) {
            //
            return createGnGeoMapping(hotelGnGeoMappingDTO);
        }
        return HotelGnGeoMappingVO.builder()
                .baseGeoId(hotelGnGeoMappings.get(0).getMappingGeoId())
                .baseGeoName(hotelGnGeoMappings.get(0).getMappingGeoName())
                .build();
    }

    /**
     * 校验输入数据
     *
     * @param dto 商圈映射数据传输对象
     */
    private void checkData(HotelGnGeoMappingDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("商圈映射数据不能为空");
        }
        if (dto.getPlatformId() == null) {
            throw new IllegalArgumentException("平台ID不能为空");
        }
        if (dto.getCityId() == null) {
            throw new IllegalArgumentException("城市ID不能为空");
        }
        if (dto.getGeoName() == null) {
            throw new IllegalArgumentException("商圈名称不能为空");
        }
    }

    /**
     * 查询商圈数据，优先使用 geoId，若无结果或 geoId 为空则使用 geoName
     *
     * @param dto 商圈映射数据传输对象
     * @return 商圈数据列表
     */
    private List<GeoCityGeo> queryGeoCityGeos(HotelGnGeoMappingDTO dto) {
        String baseCityIdOut = dto.getBaseCityId();

        if (StringUtils.isEmpty(baseCityIdOut)) {
            HotelCityMapping hotelCityMapping = new HotelCityMapping();
            hotelCityMapping.setPlatNum(dto.getCityId());
            hotelCityMapping.setInterfacePlat(Long.valueOf(dto.getPlatformId()));
            // 通过平台城市id和平台编码拿到打底城市id
            List<HotelCityMapping> hotelCityMappings = hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
            baseCityIdOut = CollectionUtils.isNotEmpty(hotelCityMappings) ? hotelCityMappings.get(0).getLocalId() : StringUtils.EMPTY;
        }
        // 优先使用 geoId 查询
        if (dto.getGeoId() != null) {
            GeoCityGeo condition = GeoCityGeo.builder()
                    .cityId(baseCityIdOut)
                    .geoId(dto.getGeoId())
                    .build();
            List<GeoCityGeo> results = geoCityGeoService.selectGeoCityGeoListBybase(condition);
            if (!results.isEmpty()) {
                return results;
            }
            log.info("未找到 geoId {} 的商圈数据，尝试使用 geoName 查询", dto.getGeoId());
        }

        // 回退到使用 geoName 查询
        GeoCityGeo condition = GeoCityGeo.builder()
                .cityId(baseCityIdOut)
                .geoName(dto.getGeoName())
                .build();
        return geoCityGeoService.selectGeoCityGeoListBybase(condition);
    }

    /**
     * 构建商圈映射对象
     *
     * @param dto 商圈映射数据传输对象
     * @param geo 商圈数据
     * @return 商圈映射对象
     */
    private HotelGnGeoMapping buildHotelGnGeoMapping(HotelGnGeoMappingDTO dto, GeoCityGeo geo) {
        return HotelGnGeoMapping.builder()
                .id(dto.getCityId() + "_" + sequence.nextId())
                .platformId(dto.getPlatformId())
                .platformCityId(dto.getCityId())
                .platformGeoId(StringUtils.defaultString(dto.getGeoId(),dto.getCityId() + sequence.nextId()))
                .platformGeoName(dto.getGeoName())
                .mappingPlatformId(PlatEnum.PLAT_BASE.getValue())
                .mappingCityId(geo.getCityId())
                .mappingGeoId(geo.getGeoId())
                .mappingGeoName(geo.getGeoName())
                .remark(String.format("%s-打底商圈映射数据", dto.getPlatformId()))
                .createBy("system")
                .createTime(DateUtils.getNowDate())
                .updateBy("system")
                .isDelete(Boolean.FALSE)
                .build();
    }
}