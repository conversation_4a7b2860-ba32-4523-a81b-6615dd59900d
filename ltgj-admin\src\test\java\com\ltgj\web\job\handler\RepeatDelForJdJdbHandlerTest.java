package com.ltgj.web.job.handler;

import com.ltgj.RuoYiApplication;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * <AUTHOR>
 * @Date 2025/2/14
 * @Description RepeatDelForJdJdbHandler 集成测试 - 实际调用业务流程
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = RuoYiApplication.class)
public class RepeatDelForJdJdbHandlerTest {

    @Autowired
    private RepeatDelForJdJdbHandler repeatDelForJdJdbHandler;

    @Test
    public void testExecuteRealBusiness() throws Exception {
        log.info("开始执行RepeatDelForJdJdbHandler真实业务流程测试");
        
        // 实际调用业务方法 - 不传参数，让业务逻辑处理所有数据
        ReturnT<String> result = repeatDelForJdJdbHandler.execute("周同志公寓(迎新路分店)");
        
        // 验证返回结果
        assertNotNull("返回结果不能为空", result);
        assertEquals("业务执行应该成功", ReturnT.SUCCESS, result);
        
        log.info("RepeatDelForJdJdbHandler业务流程执行完成，结果: {}", result);
    }

    @Test
    public void testExecuteWithSpecificHotel() throws Exception {
        log.info("开始执行RepeatDelForJdJdbHandler指定酒店测试");
        
        // 传入具体的酒店名称参数，测试特定酒店的重复数据处理
        String hotelName = "测试酒店";
        ReturnT<String> result = repeatDelForJdJdbHandler.execute(hotelName);
        
        // 验证返回结果
        assertNotNull("返回结果不能为空", result);
        assertEquals("业务执行应该成功", ReturnT.SUCCESS, result);
        
        log.info("RepeatDelForJdJdbHandler指定酒店({})处理完成，结果: {}", hotelName, result);
    }

    @Test
    public void testExecuteWithEmptyParam() throws Exception {
        log.info("开始执行RepeatDelForJdJdbHandler空参数测试");
        
        // 传入空字符串参数
        ReturnT<String> result = repeatDelForJdJdbHandler.execute("");
        
        // 验证返回结果
        assertNotNull("返回结果不能为空", result);
        assertEquals("业务执行应该成功", ReturnT.SUCCESS, result);
        
        log.info("RepeatDelForJdJdbHandler空参数处理完成，结果: {}", result);
    }
} 