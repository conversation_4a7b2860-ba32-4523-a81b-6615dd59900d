package com.ltgj.ivw.controller;

import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.poi.ExcelUtil;
import com.ltgj.ivw.dto.*;
import com.ltgj.ivw.service.IGeoHierarchyService;
import com.ltgj.ivw.service.IHotelCityGeoService;
import com.ltgj.ivw.service.IHotelInfoElongService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 地理位置层级控制器
 * 提供国家、省份、城市、区县的四级联动查询功能
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/ivw/geoHierarchy")
@Slf4j
public class GeoHierarchyController extends BaseController {
    
    @Autowired
    private IGeoHierarchyService geoHierarchyService;

    @Autowired
    private IHotelInfoElongService hotelInfoElongService;

    @Autowired
    private IHotelCityGeoService hotelCityGeoService;
    
    /**
     * 获取完整的四级联动数据
     * 适用于一次性加载所有层级数据的场景
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getGeoHierarchy
     * 
     * @return 完整的四级联动树形结构数据
     */
    @GetMapping("/getGeoHierarchy")
    public AjaxResult getGeoHierarchy() {
        log.info("接收到获取完整四级联动数据请求");
        return geoHierarchyService.getGeoHierarchy();
    }
    
    /**
     * 分级查询地理位置数据 - POST方式
     * 根据级别和上级ID获取对应的下级数据
     * 
     * 使用方式：
     * POST /ivw/geoHierarchy/getGeoDataByLevel
     * Content-Type: application/json
     * 
     * 请求体示例：
     * {
     *   "level": "city",           // 查询级别：country/province/city/district
     *   "parentId": "110000",      // 上级ID（可选）
     *   "includeChildren": false,  // 是否包含子级数据（可选）
     *   "geoType": "2",           // 地区类型：1-商圈 2-行政区 3-标志物（仅对district有效）
     *   "pageNum": 1,             // 页码（可选）
     *   "pageSize": 20,           // 每页大小（可选）
     *   "orderBy": "name ASC"     // 排序（可选）
     * }
     * 
     * @param request 查询请求参数
     * @return 分级数据列表
     */
    @PostMapping("/getGeoDataByLevel")
    public AjaxResult getGeoDataByLevel(@RequestBody GeoLevelQueryReq request) {
        log.info("接收到分级查询地理位置数据请求(POST)，参数：{}", request);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 分级查询地理位置数据 - GET方式
     * 通过查询参数方式传递条件，便于浏览器直接访问
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getGeoDataByLevel?level=city&parentId=110000&includeChildren=true&geoType=2
     * 
     * @param level 查询级别：country/province/city/district（必填）
     * @param parentId 上级ID，根据级别确定上级类型（可选）
     * @param includeChildren 是否包含子级数据，默认false（可选）
     * @param geoType 地区类型：1-商圈 2-行政区 3-标志物，仅对district级别有效（可选）
     * @param pageNum 页码，默认1（可选）
     * @param pageSize 每页大小，默认20（可选）
     * @param orderBy 排序字段，如"name ASC"（可选）
     * @return 分级数据列表
     */
    @GetMapping("/getGeoDataByLevel")
    public AjaxResult getGeoDataByLevelGet(
            @RequestParam(value = "level", required = true) String level,
            @RequestParam(value = "parentId", required = false) String parentId,
            @RequestParam(value = "includeChildren", required = false, defaultValue = "false") Boolean includeChildren,
            @RequestParam(value = "geoType", required = false) String geoType,
            @RequestParam(value = "pageNum", required = false) Integer pageNum,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "orderBy", required = false) String orderBy) {
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel(level);
        request.setParentId(parentId);
        request.setIncludeChildren(includeChildren);
        request.setGeoType(geoType);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        request.setOrderBy(orderBy);
        
        log.info("接收到分级查询地理位置数据请求(GET)，参数：{}", request);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 获取所有国家列表
     * 快捷方法，等同于不传任何参数的分级查询
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getCountries
     * 
     * @return 国家列表数据
     */
    @GetMapping("/getCountries")
    public AjaxResult getCountries() {
        log.info("接收到获取国家列表请求");
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("country");
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 根据国家ID获取省份列表
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getProvincesByCountry?countryId=CN
     * 
     * @param countryId 国家ID（必填）
     * @return 省份列表数据
     */
    @GetMapping("/getProvincesByCountry")
    public AjaxResult getProvincesByCountry(@RequestParam("countryId") String countryId) {
        log.info("接收到根据国家获取省份列表请求，国家ID：{}", countryId);
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("province");
        request.setParentId(countryId);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 根据国家ID获取省份列表 - 路径参数方式
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getProvinces/CN
     * 
     * @param countryId 国家ID（必填）
     * @return 省份列表数据
     */
    @GetMapping("/getProvinces/{countryId}")
    public AjaxResult getProvinces(@PathVariable("countryId") String countryId) {
        log.info("接收到根据国家获取省份列表请求(路径参数)，国家ID：{}", countryId);
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("province");
        request.setParentId(countryId);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 根据省份ID获取城市列表
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getCitiesByProvince?provinceId=110000
     * 
     * @param provinceId 省份ID（必填）
     * @return 城市列表数据
     */
    @GetMapping("/getCitiesByProvince")
    public AjaxResult getCitiesByProvince(@RequestParam("provinceId") String provinceId) {
        log.info("接收到根据省份获取城市列表请求，省份ID：{}", provinceId);
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("city");
        request.setParentId(provinceId);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 根据省份ID获取城市列表 - 路径参数方式
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getCities/110000
     * 
     * @param provinceId 省份ID（必填）
     * @return 城市列表数据
     */
    @GetMapping("/getCities/{provinceId}")
    public AjaxResult getCities(@PathVariable("provinceId") String provinceId) {
        log.info("接收到根据省份获取城市列表请求(路径参数)，省份ID：{}", provinceId);
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("city");
        request.setParentId(provinceId);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 根据城市ID获取区县列表
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getDistrictsByCity?cityId=110100
     * 
     * @param cityId 城市ID（必填）
     * @return 区县列表数据
     */
    @GetMapping("/getDistrictsByCity")
    public AjaxResult getDistrictsByCity(@RequestParam("cityId") String cityId) {
        log.info("接收到根据城市获取区县列表请求，城市ID：{}", cityId);
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("district");
        request.setParentId(cityId);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 根据城市ID获取区县列表 - 路径参数方式
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getDistricts/110100
     * 
     * @param cityId 城市ID（必填）
     * @return 区县列表数据
     */
    @GetMapping("/getDistricts/{cityId}")
    public AjaxResult getDistricts(@PathVariable("cityId") String cityId) {
        log.info("接收到根据城市获取区县列表请求(路径参数)，城市ID：{}", cityId);
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("district");
        request.setParentId(cityId);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 根据区县类型获取区县列表
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getDistrictsByType?cityId=110100&geoType=2
     * 
     * @param cityId 城市ID（必填）
     * @param geoType 地区类型：1-商圈 2-行政区 3-标志物（必填）
     * @return 指定类型的区县列表数据
     */
    @GetMapping("/getDistrictsByType")
    public AjaxResult getDistrictsByType(
            @RequestParam("cityId") String cityId, 
            @RequestParam("geoType") String geoType) {
        log.info("接收到根据城市和类型获取区县列表请求，城市ID：{}，地区类型：{}", cityId, geoType);
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("district");
        request.setParentId(cityId);
        request.setGeoType(geoType);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 根据城市ID和区县类型获取区县列表 - 路径参数方式
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getDistrictsByType/110100/2
     * 
     * @param cityId 城市ID（必填）
     * @param geoType 地区类型：1-商圈 2-行政区 3-标志物（必填）
     * @return 指定类型的区县列表数据
     */
    @GetMapping("/getDistrictsByType/{cityId}/{geoType}")
    public AjaxResult getDistrictsByTypeWithPath(
            @PathVariable("cityId") String cityId, 
            @PathVariable("geoType") String geoType) {
        log.info("接收到根据城市和类型获取区县列表请求(路径参数)，城市ID：{}，地区类型：{}", cityId, geoType);
        
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("district");
        request.setParentId(cityId);
        request.setGeoType(geoType);
        return geoHierarchyService.getGeoDataByLevel(request);
    }
    
    /**
     * 获取上级行政区划信息 - POST方式
     * 根据子级ID和类型查询其上级行政区划
     * 
     * 使用方式：
     * POST /ivw/geoHierarchy/getParentInfo
     * Content-Type: application/json
     * 
     * 请求体示例：
     * {
     *   "childId": "110101",      // 子级ID（必填）
     *   "childType": "district"   // 子级类型：province/city/district（必填）
     * }
     * 
     * @param request 上级查询请求参数
     * @return 上级行政区划信息
     */
    @PostMapping("/getParentInfo")
    public AjaxResult getParentInfo(@RequestBody GeoParentLookupReq request) {
        log.info("接收到获取上级行政区划请求(POST)，参数：{}", request);
        return geoHierarchyService.getParentInfo(request);
    }
    
    /**
     * 通过城市ID获取上级省份信息 - GET方式便捷接口
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getProvinceByCity/110100
     * 
     * @param cityId 城市ID（必填）
     * @return 省份信息
     */
    @GetMapping("/getProvinceByCity/{cityId}")
    public AjaxResult getProvinceByCity(@PathVariable("cityId") String cityId) {
        log.info("接收到通过城市获取省份请求，城市ID：{}", cityId);
        
        try {
            GeoParentLookupReq request = new GeoParentLookupReq();
            request.setChildId(cityId);
            request.setChildType("city");
            
            return geoHierarchyService.getParentInfo(request);
        } catch (Exception e) {
            log.error("通过城市获取省份异常，城市ID：{}，错误信息：{}", cityId, e.getMessage(), e);
            return AjaxResult.error("获取省份信息异常：" + e.getMessage());
        }
    }
    
    /**
     * 通过区县ID获取上级城市信息 - GET方式便捷接口
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getCityByDistrict/110101
     * 
     * @param districtId 区县ID（必填）
     * @return 城市信息
     */
    @GetMapping("/getCityByDistrict/{districtId}")
    public AjaxResult getCityByDistrict(@PathVariable("districtId") String districtId) {
        log.info("接收到通过区县获取城市请求，区县ID：{}", districtId);
        
        try {
            GeoParentLookupReq request = new GeoParentLookupReq();
            request.setChildId(districtId);
            request.setChildType("district");
            
            return geoHierarchyService.getParentInfo(request);
        } catch (Exception e) {
            log.error("通过区县获取城市异常，区县ID：{}，错误信息：{}", districtId, e.getMessage(), e);
            return AjaxResult.error("获取城市信息异常：" + e.getMessage());
        }
    }
    
    /**
     * 通过省份ID获取上级国家信息 - GET方式便捷接口
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getCountryByProvince/110000
     * 
     * @param provinceId 省份ID（必填）
     * @return 国家信息
     */
    @GetMapping("/getCountryByProvince/{provinceId}")
    public AjaxResult getCountryByProvince(@PathVariable("provinceId") String provinceId) {
        log.info("接收到通过省份获取国家请求，省份ID：{}", provinceId);
        
        try {
            GeoParentLookupReq request = new GeoParentLookupReq();
            request.setChildId(provinceId);
            request.setChildType("province");
            
            return geoHierarchyService.getParentInfo(request);
        } catch (Exception e) {
            log.error("通过省份获取国家异常，省份ID：{}，错误信息：{}", provinceId, e.getMessage(), e);
            return AjaxResult.error("获取国家信息异常：" + e.getMessage());
        }
    }
    
    /**
     * 根据任意层级的ID获取其完整的四级联动层次结构 - POST方式
     * 支持传入国家、省份、城市、区县任意层级的ID
     * 返回该ID对应节点的完整层次结构数据
     * 
     * 🔥 核心原则：只返回与当前节点直接相关的层次路径，不包含兄弟节点
     * 
     * 示例说明：
     * - 查询"大同市"：返回 中国→山西省→大同市→[大同市的所有区县]
     * - 查询"天镇县"：返回 中国→山西省→大同市→天镇县
     * - 不会返回山西省的其他城市，也不会返回大同市的其他区县（除非天镇县是查询目标）
     * 
     * 使用方式：
     * POST /ivw/geoHierarchy/getGeoStructureById
     * Content-Type: application/json
     * 
     * 请求体示例：
     * {
     *   "id": "110100",                    // 要查询的ID（必填）
     *   "idType": "city",                  // ID类型：country/province/city/district（可选，不填会自动识别）
     *   "includeFullChildren": true,       // 是否包含当前节点的完整子级数据（可选，默认true）
     *   "includeParents": true,            // 是否包含上级节点路径（可选，默认true）
     *   "geoType": "2"                     // 地区类型：1-商圈 2-行政区 3-标志物（可选，仅对区县查询有效）
     * }
     * 
     * @param request 层次结构查询请求参数
     * @return 精确的层次结构数据，只包含与查询ID相关的路径
     */
    @PostMapping("/getGeoStructureById")
    public AjaxResult<GeoHierarchyNode> getGeoStructureById(@RequestBody GeoStructureQueryReq request) {
        log.info("接收到根据ID获取地理位置层次结构请求(POST)，参数：{}", request);
        
        try {
            return geoHierarchyService.getGeoStructureById(request);
        } catch (Exception e) {
            log.error("根据ID获取地理位置层次结构异常，参数：{}，错误信息：{}", request, e.getMessage(), e);
            return AjaxResult.errorFor("获取地理位置层次结构异常：" + e.getMessage());
        }
    }
    
    /**
     * 根据任意层级的ID获取其完整的四级联动层次结构 - GET方式
     * 通过查询参数方式传递条件，便于浏览器直接访问
     * 
     * 🔥 核心原则：只返回与当前节点直接相关的层次路径，不包含兄弟节点
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getGeoStructureById?id=110100&idType=city&includeFullChildren=true&includeParents=true&geoType=2
     * 
     * @param id 要查询的ID（必填）
     * @param idType ID类型：country/province/city/district（可选，不填会自动识别）
     * @param includeFullChildren 是否包含当前节点的完整子级数据，默认true（可选）
     * @param includeParents 是否包含上级节点路径，默认true（可选）
     * @param geoType 地区类型：1-商圈 2-行政区 3-标志物（可选，仅对区县查询有效）
     * @return 精确的层次结构数据，只包含与查询ID相关的路径
     */
    @GetMapping("/getGeoStructureById")
    public AjaxResult<GeoHierarchyNode> getGeoStructureByIdGet(
            @RequestParam("id") String id,
            @RequestParam(value = "idType", required = false) String idType,
            @RequestParam(value = "includeFullChildren", required = false, defaultValue = "true") Boolean includeFullChildren,
            @RequestParam(value = "includeParents", required = false, defaultValue = "true") Boolean includeParents,
            @RequestParam(value = "geoType", required = false) String geoType) {
        
        GeoStructureQueryReq request = new GeoStructureQueryReq();
        request.setId(id);
        request.setIdType(idType);
        request.setIncludeFullChildren(includeFullChildren);
        request.setIncludeParents(includeParents);
        request.setGeoType(geoType);
        
        log.info("接收到根据ID获取地理位置层次结构请求(GET)，参数：{}", request);
        
        try {
            return geoHierarchyService.getGeoStructureById(request);
        } catch (Exception e) {
            log.error("根据ID获取地理位置层次结构异常(GET)，参数：{}，错误信息：{}", request, e.getMessage(), e);
            return AjaxResult.errorFor("获取地理位置层次结构异常：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取层次结构 - 路径参数方式（简化版）
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/structure/110100
     * 
     * @param id 要查询的ID（必填）
     * @return 完整的四级联动层次结构数据（默认包含所有上下级）
     */
    @GetMapping("/structure/{id}")
    public AjaxResult getStructureById(@PathVariable("id") String id) {
        log.info("接收到根据ID获取地理位置层次结构请求(路径参数)，ID：{}", id);
        
        try {
            GeoStructureQueryReq request = new GeoStructureQueryReq();
            request.setId(id);
            request.setIncludeFullChildren(true);
            request.setIncludeParents(true);
            
            return geoHierarchyService.getGeoStructureById(request);
        } catch (Exception e) {
            log.error("根据ID获取地理位置层次结构异常(路径参数)，ID：{}，错误信息：{}", id, e.getMessage(), e);
            return AjaxResult.error("获取地理位置层次结构异常：" + e.getMessage());
        }
    }
    
    /**
     * 选择性清除缓存 - 新增接口
     * 支持按缓存类型选择性清除，提供更精细的缓存管理
     * 
     * 使用方式：
     * POST /ivw/geoHierarchy/clearCacheSelective
     * Content-Type: application/json
     * 
     * 请求体示例：
     * {
     *   "cacheTypes": ["hierarchy", "level"]
     * }
     * 
     * 支持的缓存类型说明：
     * - "hierarchy": 完整四级联动数据缓存（getGeoHierarchy方法的缓存）
     * - "level": 分级查询数据缓存（getGeoDataByLevel方法的缓存）
     * - "parent": 上级查询缓存（getParentInfo方法的缓存）
     * - "structure": 层次结构查询缓存（getGeoStructureById方法的缓存）
     * - "all": 清除所有缓存（等同于传统clearCache接口）
     * 
     * 使用场景：
     * 1. 只清除完整四级联动缓存：["hierarchy"]
     * 2. 只清除分级查询缓存：["level"]
     * 3. 清除查询相关缓存：["level", "parent", "structure"]
     * 4. 清除所有缓存：["all"] 或 ["hierarchy", "level", "parent", "structure"]
     * 
     * @param request 缓存清除请求，包含要清除的缓存类型列表
     * @return 操作结果，包含详细的清除信息
     */
    @Log(title = "地理位置层级", businessType = BusinessType.CLEAN)
    @PostMapping("/clearCacheSelective")
    public AjaxResult clearCacheSelective(@RequestBody CacheClearRequest request) {
        log.info("接收到选择性清除地理位置缓存请求，缓存类型：{}", request.getCacheTypes());
        
        try {
            // 参数校验
            if (request == null || request.getCacheTypes() == null || request.getCacheTypes().isEmpty()) {
                return AjaxResult.error("缓存类型参数不能为空");
            }
            
            // 执行选择性缓存清除
            String result = geoHierarchyService.clearCacheSelective(request.getCacheTypes());
            
            return AjaxResult.success("选择性缓存清除完成", result);
            
        } catch (Exception e) {
            log.error("选择性清除缓存异常，缓存类型：{}，错误信息：{}", request.getCacheTypes(), e.getMessage(), e);
            return AjaxResult.error("选择性清除缓存异常：" + e.getMessage());
        }
    }
    
    /**
     * 清除缓存 - GET方式（便捷接口）
     * 支持通过查询参数指定要清除的缓存类型
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/clearCacheSelective?cacheTypes=hierarchy,level,parent
     * 
     * @param cacheTypesParam 缓存类型参数，用逗号分隔，如："hierarchy,level,parent"
     * @return 操作结果
     */
    @Log(title = "地理位置层级", businessType = BusinessType.CLEAN)
    @GetMapping("/clearCacheSelective")
    public AjaxResult clearCacheSelectiveGet(@RequestParam("cacheTypes") String cacheTypesParam) {
        log.info("接收到选择性清除地理位置缓存请求(GET)，缓存类型参数：{}", cacheTypesParam);
        
        try {
            // 参数校验和转换
            if (StringUtils.isEmpty(cacheTypesParam)) {
                return AjaxResult.error("缓存类型参数不能为空");
            }
            
            // 解析缓存类型参数
            String[] cacheTypeArray = cacheTypesParam.split(",");
            List<String> cacheTypes = new ArrayList<>();
            for (String type : cacheTypeArray) {
                String trimmedType = type.trim();
                if (StringUtils.isNotEmpty(trimmedType)) {
                    cacheTypes.add(trimmedType);
                }
            }
            
            if (cacheTypes.isEmpty()) {
                return AjaxResult.error("缓存类型参数解析失败，请检查参数格式");
            }
            
            // 执行选择性缓存清除
            String result = geoHierarchyService.clearCacheSelective(cacheTypes);
            
            return AjaxResult.success("选择性缓存清除完成", result);
            
        } catch (Exception e) {
            log.error("选择性清除缓存异常(GET)，缓存类型参数：{}，错误信息：{}", cacheTypesParam, e.getMessage(), e);
            return AjaxResult.error("选择性清除缓存异常：" + e.getMessage());
        }
    }
    
    /**
     * 导出地理位置数据
     * 
     * 使用方式：
     * POST /ivw/geoHierarchy/export
     * Content-Type: application/json
     * 
     * 请求体示例：
     * {
     *   "level": "city",
     *   "parentId": "110000"
     * }
     * 
     * @param response HTTP响应对象
     * @param request 查询请求参数
     */
    @Log(title = "地理位置层级", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody GeoLevelQueryReq request) {
        log.info("接收到导出地理位置数据请求，参数：{}", request);
        
        AjaxResult result = geoHierarchyService.getGeoDataByLevel(request);
        
        if (result.get("code").equals(200)) {
            @SuppressWarnings("unchecked")
            List<GeoHierarchyNode> list = (List<GeoHierarchyNode>) result.get("data");
            ExcelUtil<GeoHierarchyNode> util = new ExcelUtil<>(GeoHierarchyNode.class);
            util.exportExcel(response, list, "地理位置数据");
        }
    }

    /**
     * 通过条件获取所有区域结构
     *
     * @param cityLevelQueryReq
     * @return
     */
    @PostMapping("/getAllRegionByCondition")
    public AjaxResult getAllRegionByCondition(@RequestBody CityLevelQueryReq cityLevelQueryReq) {
        log.info("接收到分级查询城市数据请求，参数：{}", cityLevelQueryReq);

        try {
            return hotelInfoElongService.getAllRegionByCondition(cityLevelQueryReq);
        } catch (Exception e) {
            log.error("，参数：{}，错误信息：{}", cityLevelQueryReq, e.getMessage(), e);
            return AjaxResult.error("查询城市数据异常：" + e.getMessage());
        }
    }
    
    /**
     * 从缓存中获取地理位置层次结构 - POST方式
     * 功能与 getGeoStructureById 相同，但从预加载的Redis缓存中检索数据
     * 提供更快的查询性能，适用于高频访问场景
     * 
     * 🚀 性能优势：从内存缓存读取，响应速度更快
     * ⚠️ 限制：只支持查询国家、省、市三级，不支持区县级别
     * 
     * 使用方式：
     * POST /ivw/geoHierarchy/getGeoStructureByIdFromCache
     * Content-Type: application/json
     * 
     * 请求体示例：
     * {
     *   "id": "110100",                    // 要查询的ID（必填，只支持国家/省/市ID）
     *   "idType": "city",                  // ID类型：country/province/city（可选，不填会自动识别）
     *   "includeFullChildren": true,       // 是否包含当前节点的完整子级数据（可选，默认true）
     *   "includeParents": true,            // 是否包含上级节点路径（可选，默认true）
     *   "geoType": "2"                     // 此参数已废弃，缓存不包含区县数据
     * }
     * 
     * @param request 层次结构查询请求参数
     * @return 精确的层次结构数据（最多到市级），只包含与查询ID相关的路径
     */
    @PostMapping("/getGeoStructureByIdFromCache")
    public AjaxResult<GeoHierarchyNode> getGeoStructureByIdFromCache(@RequestBody GeoStructureQueryReq request) {
        log.info("接收到从缓存获取地理位置层次结构请求(POST)，参数：{}", request);
        
        try {
            return hotelCityGeoService.getGeoStructureByIdFromCache(request);
        } catch (Exception e) {
            log.error("从缓存获取地理位置层次结构异常，参数：{}，错误信息：{}", request, e.getMessage(), e);
            return AjaxResult.errorFor("从缓存获取地理位置层次结构异常：" + e.getMessage());
        }
    }
    
    /**
     * 从缓存中获取地理位置层次结构 - GET方式
     * 功能与 getGeoStructureById 相同，但从预加载的Redis缓存中检索数据
     * 
     * 🚀 性能优势：从内存缓存读取，响应速度更快
     * ⚠️ 限制：只支持查询国家、省、市三级，不支持区县级别
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/getGeoStructureByIdFromCache?id=110100&idType=city&includeFullChildren=true&includeParents=true
     * 
     * @param id 要查询的ID（必填，只支持国家/省/市ID）
     * @param idType ID类型：country/province/city（可选，不填会自动识别）
     * @param includeFullChildren 是否包含当前节点的完整子级数据，默认true（可选）
     * @param includeParents 是否包含上级节点路径，默认true（可选）
     * @param geoType 此参数已废弃，缓存不包含区县数据
     * @return 精确的层次结构数据（最多到市级），只包含与查询ID相关的路径
     */
    @GetMapping("/getGeoStructureByIdFromCache")
    public AjaxResult<GeoHierarchyNode> getGeoStructureByIdFromCacheGet(
            @RequestParam("id") String id,
            @RequestParam(value = "idType", required = false) String idType,
            @RequestParam(value = "includeFullChildren", required = false, defaultValue = "true") Boolean includeFullChildren,
            @RequestParam(value = "includeParents", required = false, defaultValue = "true") Boolean includeParents,
            @RequestParam(value = "geoType", required = false) String geoType) {
        
        GeoStructureQueryReq request = new GeoStructureQueryReq();
        request.setId(id);
        request.setIdType(idType);
        request.setIncludeFullChildren(includeFullChildren);
        request.setIncludeParents(includeParents);
        request.setGeoType(geoType);
        
        log.info("接收到从缓存获取地理位置层次结构请求(GET)，参数：{}", request);
        
        try {
            return hotelCityGeoService.getGeoStructureByIdFromCache(request);
        } catch (Exception e) {
            log.error("从缓存获取地理位置层次结构异常(GET)，参数：{}，错误信息：{}", request, e.getMessage(), e);
            return AjaxResult.errorFor("从缓存获取地理位置层次结构异常：" + e.getMessage());
        }
    }
    
    /**
     * 从缓存中获取层次结构 - 路径参数方式（简化版）
     * 
     * 使用方式：
     * GET /ivw/geoHierarchy/structureFromCache/110100
     * 
     * @param id 要查询的ID（必填）
     * @return 完整的四级联动层次结构数据（默认包含所有上下级）
     */
    @GetMapping("/structureFromCache/{id}")
    public AjaxResult<GeoHierarchyNode> getStructureByIdFromCache(@PathVariable("id") String id) {
        log.info("接收到从缓存获取地理位置层次结构请求(路径参数)，ID：{}", id);
        
        try {
            GeoStructureQueryReq request = new GeoStructureQueryReq();
            request.setId(id);
            request.setIncludeFullChildren(true);
            request.setIncludeParents(true);
            
            return hotelCityGeoService.getGeoStructureByIdFromCache(request);
        } catch (Exception e) {
            log.error("从缓存获取地理位置层次结构异常(路径参数)，ID：{}，错误信息：{}", id, e.getMessage(), e);
            return AjaxResult.errorFor("从缓存获取地理位置层次结构异常：" + e.getMessage());
        }
    }
    
    /**
     * 预加载地理层级数据到缓存
     * 手动触发缓存预加载，可用于系统维护或缓存更新场景
     * 
     * 使用方式：
     * POST /ivw/geoHierarchy/preloadCache
     * 
     * @return 操作结果
     */
    @Log(title = "地理位置层级", businessType = BusinessType.OTHER)
    @PostMapping("/preloadCache")
    public AjaxResult preloadGeoHierarchyCache() {
        log.info("接收到预加载地理层级缓存请求");
        
        try {
            hotelCityGeoService.preloadGeoHierarchyCache();
            return AjaxResult.success("地理层级缓存预加载成功");
        } catch (Exception e) {
            log.error("预加载地理层级缓存异常，错误信息：{}", e.getMessage(), e);
            return AjaxResult.error("预加载地理层级缓存异常：" + e.getMessage());
        }
    }

} 