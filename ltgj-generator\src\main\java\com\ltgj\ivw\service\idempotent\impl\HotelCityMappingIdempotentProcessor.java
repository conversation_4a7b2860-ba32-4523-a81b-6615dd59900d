package com.ltgj.ivw.service.idempotent.impl;

import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.mapper.HotelCityMappingMapper;
import com.ltgj.ivw.service.idempotent.AbstractIdempotentProcessor;
import com.ltgj.ivw.service.idempotent.dto.HotelCityMappingDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * HotelCityMapping表幂等性处理器
 */
@Slf4j
@Component
public class HotelCityMappingIdempotentProcessor extends AbstractIdempotentProcessor<HotelCityMapping, Long, HotelCityMappingDTO> {
    
    @Autowired
    private HotelCityMappingMapper hotelCityMappingMapper;
    
    @Override
    protected String getProcessorName() {
        return "HotelCityMapping幂等性处理器";
    }
    
    @Override
    public Long getEntityKey(HotelCityMapping entity) {
        return entity.getId();
    }

    @Override
    public HotelCityMappingDTO getEntityAttributeKey(HotelCityMapping entity) {
        return HotelCityMappingDTO.builder()
                .platNum(entity.getPlatNum())
                .interfacePlat(String.valueOf(entity.getInterfacePlat()))
                .localId(entity.getLocalId())
                .build();
    }

    @Override
    public boolean isEntityEqual(HotelCityMapping existing, HotelCityMapping incoming) {
        if (existing == null || incoming == null) {
            return false;
        }
        
        try {
            // 使用反射比较所有字段（排除时间字段）
            Field[] fields = HotelCityMapping.class.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                
                // 跳过时间字段和序列化字段
                String fieldName = field.getName();
                if (fieldName.equals("serialVersionUID") || 
                    fieldName.equals("createTime") ||
                    fieldName.equals("updateTime") ||
                    fieldName.equals("platMappings") ||
                        fieldName.equals("id")) {
                    continue;
                }
                
                Object existingValue = field.get(existing);
                Object incomingValue = field.get(incoming);
                
                if (!Objects.equals(existingValue, incomingValue)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("比较HotelCityMapping实体时发生错误", e);
            return false;
        }
    }
    
    @Override
    public String getChangedFields(HotelCityMapping existing, HotelCityMapping incoming) {
        if (existing == null || incoming == null) {
            return "实体为空";
        }
        
        List<String> changedFields = new ArrayList<>();
        
        try {
            Field[] fields = HotelCityMapping.class.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                
                String fieldName = field.getName();
                if (fieldName.equals("serialVersionUID") || 
                    fieldName.equals("createTime") || 
                    fieldName.equals("updateTime") ||
                    fieldName.equals("platMappings") ||
                    fieldName.equals("id")) {
                    continue;
                }
                
                Object existingValue = field.get(existing);
                Object incomingValue = field.get(incoming);
                
                if (!Objects.equals(existingValue, incomingValue)) {
                    changedFields.add(String.format("%s: [%s] -> [%s]", 
                        fieldName, existingValue, incomingValue));
                }
            }
        } catch (Exception e) {
            log.error("获取HotelCityMapping变更字段时发生错误", e);
            return "获取变更字段失败: " + e.getMessage();
        }
        
        return changedFields.isEmpty() ? "无变更" : String.join(", ", changedFields);
    }
    
    @Override
    public List<HotelCityMapping> batchQueryExisting(List<Long> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return new ArrayList<>();
        }
        
        try {
            // 分批查询，避免IN子句过长
            List<HotelCityMapping> result = new ArrayList<>();
            int batchSize = 1000;
            
            for (int i = 0; i < keys.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, keys.size());
                List<Long> batchKeys = keys.subList(i, endIndex);
                
                // 这里需要在HotelCityMappingMapper中添加批量查询方法
                List<HotelCityMapping> batchResult = hotelCityMappingMapper.selectByIds(batchKeys);
                result.addAll(batchResult);
            }
            
            return result;
        } catch (Exception e) {
            log.error("批量查询HotelCityMapping时发生错误，keys: {}", keys, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<HotelCityMapping> batchQueryExistingByIdempotentKey(List<HotelCityMappingDTO> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return new ArrayList<>();
        }

        try {
            // 分批查询，避免IN子句过长
            List<HotelCityMapping> result = new ArrayList<>();
            int batchSize = 1000;

            for (int i = 0; i < keys.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, keys.size());
                List<HotelCityMappingDTO> batchKeys = keys.subList(i, endIndex);

                // 这里需要在HotelCityMappingMapper中添加批量查询方法
                List<HotelCityMapping> batchResult = hotelCityMappingMapper.selectByAttribute(batchKeys);
                result.addAll(batchResult);
            }

            return result;
        } catch (Exception e) {
            log.error("批量查询HotelCityMapping时发生错误，keys: {}", keys, e);
            return new ArrayList<>();
        }
    }

    @Override
    public int batchInsert(List<HotelCityMapping> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        
        try {
            // 设置创建时间
            Date now = new Date();
            insertList.forEach(entity -> {
                entity.setCreateTime(now);
                entity.setUpdateTime(now);
            });
            
            return hotelCityMappingMapper.batchInsert(insertList);
        } catch (Exception e) {
            log.error("批量插入HotelCityMapping时发生错误，数量: {}", insertList.size(), e);
            return 0;
        }
    }
    
    @Override
    public int batchUpdate(List<HotelCityMapping> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return 0;
        }
        
        try {
            // 设置更新时间
            Date now = new Date();
            updateList.forEach(entity -> entity.setUpdateTime(now));
            
            // 逐个更新（如果需要批量更新，需要在Mapper中添加相应方法）
            int updateCount = 0;
            for (HotelCityMapping entity : updateList) {
                int result = hotelCityMappingMapper.updateHotelCityMappingByAttribute(entity);
                updateCount += result;
            }
            
            return updateCount;
        } catch (Exception e) {
            log.error("批量更新HotelCityMapping时发生错误，数量: {}", updateList.size(), e);
            return 0;
        }
    }
} 