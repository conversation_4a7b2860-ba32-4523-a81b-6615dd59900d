package com.ltgj.ivw.strategy.impl;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.BCity;
import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.mapper.BCityMapper;
import com.ltgj.ivw.strategy.GeoQueryStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 城市查询策略实现
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@Slf4j
public class CityQueryStrategy implements GeoQueryStrategy {
    
    @Autowired
    private BCityMapper bCityMapper;
    
    @Override
    public List<GeoHierarchyNode> execute(GeoLevelQueryReq request) {
        log.info("执行城市查询策略，参数：{}", request);
        
        // 查询指定省份下的城市
        List<BCity> cities = bCityMapper.selectCitiesByProvince(
                request.getParentId(), true);
        
        return cities.stream()
                .map(this::convertToNode)
                .collect(Collectors.toList());
    }
    
    @Override
    public String getSupportedLevel() {
        return "city";
    }
    
    /**
     * 转换为层级节点
     */
    private GeoHierarchyNode convertToNode(BCity city) {
        GeoHierarchyNode node = new GeoHierarchyNode();
        node.setId(city.getId());
        node.setName(city.getCityName());
        node.setEnName(city.geteName());
        node.setFullName(city.getCityNameLong());
        node.setType("city");
        node.setLevel(3);
        node.setParentId(city.getProvince());
        node.setParentName(city.getProvinceName());
        node.setLongitude(city.getLon());
        node.setLatitude(city.getLat());
        return node;
    }
    
} 