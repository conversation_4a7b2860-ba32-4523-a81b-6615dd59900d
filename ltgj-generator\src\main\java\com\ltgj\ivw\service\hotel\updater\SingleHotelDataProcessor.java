package com.ltgj.ivw.service.hotel.updater;

import com.alibaba.excel.read.metadata.ReadSheet;
import com.ltgj.ivw.domain.dto.HotelDTO;
import com.ltgj.ivw.domain.dto.HotelDetailDTO;
import com.ltgj.ivw.service.impl.HotelInfoChailvgjServiceImpl;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 单个酒店数据处理器(模板方法模式实现)
 *
 * <AUTHOR>
 */
@Slf4j
public class SingleHotelDataProcessor implements HotelDataProcessor {
    private final HotelInfoChailvgjServiceImpl service;
    private final HotelDTO hotel;
    
    /**
     * 构造方法
     * 
     * @param service 酒店服务实现
     * @param hotel 酒店数据
     */
    public SingleHotelDataProcessor(HotelInfoChailvgjServiceImpl service, HotelDTO hotel) {
        this.service = service;
        this.hotel = hotel;
    }
    
    @Override
    public ProcessResult process(String userName) {
        ProcessResult result = new ProcessResult(new AtomicInteger(1), new AtomicInteger(0), new AtomicInteger(0), new AtomicInteger(1));
        try {
            // 获取酒店详情
            HotelDetailDTO hotelDetail = service.getHotelDetail(hotel.getHotelId());
            if (hotelDetail != null) {
                // 保存酒店信息
                hotelDetail.setCurrentUserName(userName);
                result = service.saveHotelInfo(hotelDetail);
            }
        } catch (Exception e) {
            log.error("处理酒店[{}]详情异常", hotel.getHotelId(), e);
        }
        
        return result;
    }
} 