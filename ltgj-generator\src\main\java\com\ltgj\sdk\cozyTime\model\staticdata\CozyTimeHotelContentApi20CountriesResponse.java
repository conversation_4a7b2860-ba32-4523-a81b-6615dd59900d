package com.ltgj.sdk.cozyTime.model.staticdata;

import com.ltgj.sdk.cozyTime.base.CozyTimeBaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelContentApi20CountriesResponse extends CozyTimeBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<CozyTimeHotelContentApi20CountriesEntity> countries;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class CozyTimeHotelContentApi20CountriesEntity implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 国家编码
         */
        private String countryCode;

        /**
         * 国家英文名称
         */
        private String name;

        /**
         * 国家中文名称
         */
        private String nameCN;
    }


}