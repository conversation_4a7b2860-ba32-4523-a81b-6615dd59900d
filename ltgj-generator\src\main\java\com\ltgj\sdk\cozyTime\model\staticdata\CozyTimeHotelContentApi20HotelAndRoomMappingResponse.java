package com.ltgj.sdk.cozyTime.model.staticdata;

import com.ltgj.ivw.domain.ketan.HotelMapping;
import com.ltgj.ivw.domain.ketan.RoomMapping;
import com.ltgj.sdk.cozyTime.base.CozyTimeBaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelContentApi20HotelAndRoomMappingResponse extends CozyTimeBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<HotelMapping> hotelMappingList;


//    @Data
//    @AllArgsConstructor
//    @NoArgsConstructor
//    public static class HotelMapping implements Serializable {
//        private static final long serialVersionUID = 1L;
//        /**
//         * 酒店id
//         */
//        private Long hotelId;
//        /**
//         * 酒店名称
//         */
//        private String hotelName;
//        /**
//         * 艺龙酒店id
//         */
//        private String elongHotelId;
//        /**
//         * 艺龙酒店名称
//         */
//        private String elongHotelName;
//        /**
//         * 房间映射信息
//         */
//        private List<RoomMapping> roomMappingInfoList;
//    }
//
//    @Data
//    @AllArgsConstructor
//    @NoArgsConstructor
//    public static class RoomMapping implements Serializable {
//        private static final long serialVersionUID = 1L;
//
//        /**
//         * 房型ID
//         */
//        private Long roomId;
//        /**
//         * 房型名称
//         */
//        private String roomName;
//        /**
//         * 艺龙房型ID
//         */
//        private String elongRoomId;
//        /**
//         * 艺龙房型名称
//         */
//        private String elongRoomName;
//    }


}