package com.ltgj.supplier.common.service;

import com.ltgj.supplier.common.dto.CustomerTypeDTO;
import com.ltgj.supplier.common.dto.EnumCategoryDTO;
import com.ltgj.supplier.common.dto.EnumItemDTO;
import com.ltgj.supplier.common.dto.HotelFacilityDetailDTO;
import com.ltgj.supplier.common.gn.enums.HotelGnCustomerTypeEnum;
import com.ltgj.supplier.common.gn.enums.HotelGnFacilityDetail;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 枚举数据服务类
 * 负责处理各种枚举数据的转换和封装
 */
@Service
public class EnumDataService {

    /**
     * 获取所有枚举分类信息
     */
    public Map<String, Object> getAllEnumCategories() {
        Map<String, Object> result = new HashMap<>();
        
        // 客户类型政策信息
        result.put("policyInfo", getCustomerTypePolicyInfo());
        
        // 酒店设施详细配置信息
        result.put("facilitiesInfo", getHotelFacilityDetailInfo());
        
        return result;
    }

    /**
     * 获取客户类型政策信息
     */
    public Map<String, Object> getCustomerTypePolicyInfo() {
        Map<String, Object> policyInfo = new HashMap<>();
        policyInfo.put("categoryKey", "customerType");
        policyInfo.put("categoryName", "客户类型政策信息");
        policyInfo.put("categoryDesc", "酒店接待客人类型的政策规定信息");
        policyInfo.put("items", getCustomerTypeList());
        
        return policyInfo;
    }



    /**
     * 获取客户类型列表（按照前端需求格式：customerType 和 customerDesc）
     */
    public List<CustomerTypeDTO> getCustomerTypeList() {
        return Arrays.stream(HotelGnCustomerTypeEnum.values())
                .filter(customerType -> customerType != HotelGnCustomerTypeEnum.NONE) // 过滤掉 NONE
                .map(customerType -> new CustomerTypeDTO(
                        customerType.getCode(),
                        customerType.getDescription(),
                        customerType.name()
                ))
                .collect(Collectors.toList());
    }



    /**
     * 根据分类key获取对应的枚举数据
     */
    public Object getEnumDataByCategory(String categoryKey) {
        switch (categoryKey.toLowerCase()) {
            case "customertype":
            case "policyinfo":
                return getCustomerTypePolicyInfo();
            case "facility":
            case "facilityinfo":
            case "facilitiesinfo":
            case "facilitydetail":
            case "facilitydetailinfo":
                return getHotelFacilityDetailInfo();
            default:
                return null;
        }
    }

    /**
     * 通用方法：获取枚举分类DTO格式的数据
     */
    public EnumCategoryDTO getEnumCategoryDTO(String categoryKey) {
        switch (categoryKey.toLowerCase()) {
            case "customertype":
            case "policyinfo":
                List<EnumItemDTO> customerItems = Arrays.stream(HotelGnCustomerTypeEnum.values())
                        .filter(customerType -> customerType != HotelGnCustomerTypeEnum.NONE)
                        .map(customerType -> new EnumItemDTO(
                                customerType.getCode(),
                                customerType.getDescription(),
                                customerType.name()
                        ))
                        .collect(Collectors.toList());
                
                return new EnumCategoryDTO(
                        "customerType",
                        "客户类型政策信息",
                        "酒店接待客人类型的政策规定信息",
                        customerItems
                );
                
            case "facility":
            case "facilityinfo":
            case "facilitiesinfo":
            case "facilitydetail":
            case "facilitysinfo":
                List<EnumItemDTO> facilityDetailItems = Arrays.stream(HotelGnFacilityDetail.values())
                        .map(facility -> new EnumItemDTO(
                                facility.getId(),
                                facility.getName(),
                                facility.getKey()
                        ))
                        .collect(Collectors.toList());
                
                return new EnumCategoryDTO(
                        "facilityDetail",
                        "酒店设施详细信息",
                        "酒店设施的完整配置信息，包括是否有无、是否收费等属性",
                        facilityDetailItems
                );
                
            default:
                return null;
        }
    }

    /**
     * 获取酒店设施详细信息列表（包含完整属性配置）
     */
    public List<HotelFacilityDetailDTO> getHotelFacilityDetailList() {
        return Arrays.stream(HotelGnFacilityDetail.values())
                .map(facility -> new HotelFacilityDetailDTO(
                        facility.getId(),
                        facility.getName(),
                        facility.getKey(),
                        facility.isSupportIsHave(),
                        facility.isSupportIsCharge()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 获取酒店设施详细信息（带分类信息）
     */
    public Map<String, Object> getHotelFacilityDetailInfo() {
        Map<String, Object> facilityDetailInfo = new HashMap<>();
        facilityDetailInfo.put("categoryKey", "facilityDetail");
        facilityDetailInfo.put("categoryName", "酒店设施详细信息");
        facilityDetailInfo.put("categoryDesc", "酒店设施的完整配置信息，包括是否有无、是否收费等属性");
        facilityDetailInfo.put("items", getHotelFacilityDetailList());
        
        return facilityDetailInfo;
    }

    /**
     * 根据设施ID获取设施详细信息
     */
    public HotelFacilityDetailDTO getFacilityDetailById(int facilityId) {
        HotelGnFacilityDetail facility = HotelGnFacilityDetail.getById(facilityId);
        if (facility == null) {
            return null;
        }
        
        return new HotelFacilityDetailDTO(
                facility.getId(),
                facility.getName(),
                facility.getKey(),
                facility.isSupportIsHave(),
                facility.isSupportIsCharge()
        );
    }

    /**
     * 根据设施key获取设施详细信息
     */
    public HotelFacilityDetailDTO getFacilityDetailByKey(String facilityKey) {
        HotelGnFacilityDetail facility = HotelGnFacilityDetail.getByKey(facilityKey);
        if (facility == null) {
            return null;
        }
        
        return new HotelFacilityDetailDTO(
                facility.getId(),
                facility.getName(),
                facility.getKey(),
                facility.isSupportIsHave(),
                facility.isSupportIsCharge()
        );
    }

    /**
     * 创建设施配置模板（供前端初始化使用）
     */
    public List<HotelFacilityDetailDTO> createFacilityConfigTemplate() {
        return Arrays.stream(HotelGnFacilityDetail.values())
                .map(facility -> new HotelFacilityDetailDTO(
                        facility.getId(),
                        facility.getName(),
                        facility.getKey(),
                        facility.isSupportIsHave(),
                        facility.isSupportIsCharge(),
                        null, // isHave 默认为null，由前端设置
                        null, // isCharge 默认为null，由前端设置
                        null  // remark 默认为null
                ))
                .collect(Collectors.toList());
    }
} 