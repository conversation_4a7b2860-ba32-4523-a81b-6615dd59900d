package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.dto.CityLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.GeoStructureQueryReq;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IGeoDataQueryService;
import com.ltgj.ivw.service.IGeoHierarchyService;
import com.ltgj.ivw.service.IHotelCityGeoService;
import com.ltgj.ivw.service.IHotelCityMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 独立的地理数据查询服务实现类
 * 使用REQUIRES_NEW事务传播级别，确保可以在独立事务中切换数据源
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@Service
public class GeoDataQueryServiceImpl implements IGeoDataQueryService {
    
    @Autowired
    private IHotelCityMappingService hotelCityMappingService;
    
    @Autowired
    private IGeoHierarchyService geoHierarchyService;

    @Autowired
    private IHotelCityGeoService hotelCityGeoService;

    @Value("${geo.data.way:1}")
    private String dataSwitch;

    /**
     * 通过条件获取所有区域结构（独立事务）
     * 使用REQUIRES_NEW事务传播级别，会暂停当前事务，开启新事务
     * 这样可以确保地理查询服务能够正确切换到devyibase数据源
     * 
     * @param cityLevelQueryReq 查询条件
     * @return 地理层次结构
     */
    @Override
    public AjaxResult<GeoHierarchyNode> getAllRegionByConditionNewTx(CityLevelQueryReq cityLevelQueryReq) {
        AjaxResult<GeoHierarchyNode> result = null;
        try {
            log.info("独立事务地理查询开始，参数：platformId={}, cityId={}", 
                cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
            
            if (StringUtils.isEmpty(cityLevelQueryReq.getPlatformId()) || 
                StringUtils.isEmpty(cityLevelQueryReq.getCityId())) {
                log.warn("参数为空，platformId：{}，cityId：{}", 
                    cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
                return AjaxResult.errorFor("参数为空");
            }
            
            // 查询城市映射关系（使用master数据源）
            HotelCityMapping hotelCityMapping = hotelCityMappingService.getHotelCityByCondition(
                cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
                
            if (Objects.isNull(hotelCityMapping)) {
                log.warn("未找到对应的城市映射关系，platformId：{}，cityId：{}", 
                    cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
                return AjaxResult.errorFor("未找到对应城市映射关系");
            }
            
            // 构建地理结构查询请求
            GeoStructureQueryReq geoStructureQueryReq = GeoStructureQueryReq.builder()
                .id(hotelCityMapping.getLocalId())
                .idType("city")
                .includeParents(Boolean.TRUE)
                .includeFullChildren(Boolean.FALSE)
                .build();
            // 调用地理数据服务 走总缓存
            if (StringUtils.equals("1", dataSwitch)) {
                result = hotelCityGeoService.getGeoStructureByIdFromCache(geoStructureQueryReq);
            }else {
                // 调用地理层次服务（这里会切换到devyibase数据源） 分级缓存
                result = geoHierarchyService.getGeoStructureById(geoStructureQueryReq);
            }

            log.info("独立事务地理查询完成，结果：{}", JSONObject.toJSONString(result));
            
            return result;
            
        } catch (Exception e) {
            log.error("获取地理位置层次结构异常，参数：{}，错误信息：{}", 
                cityLevelQueryReq, e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
    }
} 