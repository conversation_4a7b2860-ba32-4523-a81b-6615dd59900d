package com.ltgj.sdk.cozyTime.model.staticdata;

import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据国家编码查询城市信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelContentApi20CitiesRequest implements CozyTimeRequestInterface<CozyTimeHotelContentApi20CitiesResponse> {
    @Override
    public String getRequestMethod() {
        return "GET";
    }

    @Override
    public String getEndpoint() {
        return "/hotel_content_api/2.0/cities";
    }

    @Override
    public Class<CozyTimeHotelContentApi20CitiesResponse> getResponseClass() {
        return CozyTimeHotelContentApi20CitiesResponse.class;
    }

    /**
     * 国家编码国家编码
     * 必填: true
     */
    private String countryCode;
}
