<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.HotelGnSupplierMapper">
  <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.HotelGnSupplier">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 16:16.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_en" jdbcType="VARCHAR" property="nameEn" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="open_date" jdbcType="VARCHAR" property="openDate" />
    <result column="decoration_date" jdbcType="VARCHAR" property="decorationDate" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="country_id" jdbcType="VARCHAR" property="countryId" />
    <result column="country_name" jdbcType="VARCHAR" property="countryName" />
    <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="area_id" jdbcType="VARCHAR" property="areaId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="business_district_id" jdbcType="VARCHAR" property="businessDistrictId" />
    <result column="business_district_name" jdbcType="VARCHAR" property="businessDistrictName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="address_en" jdbcType="VARCHAR" property="addressEn" />
    <result column="lon_google" jdbcType="DECIMAL" property="lonGoogle" />
    <result column="lat_google" jdbcType="DECIMAL" property="latGoogle" />
    <result column="lon_baidu" jdbcType="DECIMAL" property="lonBaidu" />
    <result column="lat_baidu" jdbcType="DECIMAL" property="latBaidu" />
    <result column="lon_gaode" jdbcType="DECIMAL" property="lonGaode" />
    <result column="lat_gaode" jdbcType="DECIMAL" property="latGaode" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="brand_id" jdbcType="VARCHAR" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="star" jdbcType="VARCHAR" property="star" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="score" jdbcType="VARCHAR" property="score" />
    <result column="synopsis" jdbcType="VARCHAR" property="synopsis" />
    <result column="sparkle" jdbcType="VARCHAR" property="sparkle" />
    <result column="policy_info" jdbcType="VARCHAR" property="policyInfo" />
    <result column="facilities_info" jdbcType="VARCHAR" property="facilitiesInfo" />
    <result column="round_info" jdbcType="VARCHAR" property="roundInfo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="increment_status" jdbcType="BIT" property="incrementStatus" />
    <result column="increment_time" jdbcType="DATE" property="incrementTime" />
    <result column="increment_type" jdbcType="BIT" property="incrementType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="reserve0" jdbcType="VARCHAR" property="reserve0" />
    <result column="reserve1" jdbcType="VARCHAR" property="reserve1" />
    <result column="reserve2" jdbcType="VARCHAR" property="reserve2" />
    <result column="reserve3" jdbcType="VARCHAR" property="reserve3" />
    <result column="reserve4" jdbcType="VARCHAR" property="reserve4" />
    <result column="reserve5" jdbcType="VARCHAR" property="reserve5" />
    <result column="reserve6" jdbcType="VARCHAR" property="reserve6" />
    <result column="reserve7" jdbcType="VARCHAR" property="reserve7" />
    <result column="reserve8" jdbcType="VARCHAR" property="reserve8" />
    <result column="reserve9" jdbcType="VARCHAR" property="reserve9" />
  </resultMap>
  <sql id="Base_Column_List">
    id, name, name_en, type_id, open_date, decoration_date, phone, country_id, country_name,
    province_id, province_name, city_id, city_name, area_id, area_name, business_district_id,
    business_district_name, address, address_en, lon_google, lat_google, lon_baidu, lat_baidu,
    lon_gaode, lat_gaode, group_id, group_name, brand_id, brand_name, star, image, score,
    synopsis, sparkle, policy_info, facilities_info, round_info,
    status, is_delete, remark, increment_status, increment_time, increment_type, create_time,
    update_time, create_by, update_by, reserve0, reserve1, reserve2, reserve3, reserve4,
    reserve5, reserve6, reserve7, reserve8, reserve9
  </sql>

  <sql id="Base_Column_Increase_List">
    id, create_time
  </sql>

  <select id="selectList" parameterType="com.ltgj.ivw.dto.HotelGnSupplierReq" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_Increase_List" />
    from ${tableName}
    <include refid="sqlWhereSearch"/>
  </select>
  <select id="selectCount" parameterType="com.ltgj.ivw.dto.HotelGnSupplierReq" resultType="int">
    select count(*) from ${tableName}
    <include refid="sqlWhereSearch"/>
  </select>
  <select id="selectSupplierCount" resultType="java.lang.Integer"
          parameterType="com.ltgj.ivw.dto.HotelGnSupplierReq">
    select count(*) from ${tableName}
    <include refid="sqlWhereSearch"/>
  </select>
  <select id="selectSuccessCount" resultType="java.lang.Integer"
          parameterType="com.ltgj.ivw.dto.HotelGnSupplierReq">
    SELECT COUNT(DISTINCT plat_id) FROM ${tableName} m
    INNER JOIN zh_jd_jdb_mapping n
    ON CONVERT(m.id USING utf8mb4) COLLATE utf8mb4_unicode_ci = n.plat_id
    AND n.interface_plat = #{interfacePlat}
    <include refid="sqlWhereSearch"/>
  </select>
  <!-- 查询条件 -->
  <sql id="sqlWhereSearch">
    <where>
      <if test="createTime !=null">
        AND (create_time <![CDATA[>=]]> STR_TO_DATE(#{createTime}, '%Y-%m-%d')
        AND create_time <![CDATA[<]]> DATE_ADD(STR_TO_DATE(#{createTime}, '%Y-%m-%d'), INTERVAL 1 DAY))
      </if>
      <if test="offset !=null and limit!=null">
        limit #{offset}, #{limit}
      </if>
    </where>
  </sql>
</mapper>
