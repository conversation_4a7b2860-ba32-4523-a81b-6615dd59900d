package com.ltgj.ivw.service.idempotent;

import java.util.List;

/**
 * 幂等性处理器接口
 * 用于处理数据的幂等性校验和操作
 * 
 * @param <T> 实体类型
 * @param <K> 主键类型
 */
public interface IdempotentProcessor<T, K,O> {
    
    /**
     * 处理数据的幂等性操作
     * 
     * @param dataList 待处理的数据列表
     * @return 处理结果统计
     */
    IdempotentResult<T> processIdempotent(List<T> dataList);
    
    /**
     * 获取实体的唯一标识
     * 
     * @param entity 实体对象
     * @return 唯一标识
     */
    K getEntityKey(T entity);

    /**
     * 获取实体的唯一标识 (属性组合)
     *
     * @param entity 实体对象
     * @return 唯一标识
     */
    O getEntityAttributeKey(T entity);
    
    /**
     * 比较两个实体是否相等（所有字段）
     * 
     * @param existing 现有实体
     * @param incoming 新实体
     * @return 是否相等
     */
    boolean isEntityEqual(T existing, T incoming);
    
    /**
     * 获取变更的字段信息
     * 
     * @param existing 现有实体
     * @param incoming 新实体
     * @return 变更字段描述
     */
    String getChangedFields(T existing, T incoming);
    
    /**
     * 批量查询现有数据
     * 
     * @param keys 主键列表
     * @return 现有数据映射
     */
    List<T> batchQueryExisting(List<K> keys);

    /**
     * 批量查询现有数据,根据幂等键
     *
     * @param keys 主键列表
     * @return 现有数据映射
     */
    List<T> batchQueryExistingByIdempotentKey(List<O> keys);

    /**
     * 批量插入新数据
     * 
     * @param insertList 待插入数据
     * @return 插入数量
     */
    int batchInsert(List<T> insertList);
    
    /**
     * 批量更新数据
     * 
     * @param updateList 待更新数据
     * @return 更新数量
     */
    int batchUpdate(List<T> updateList);
} 