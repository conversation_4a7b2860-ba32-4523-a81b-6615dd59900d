package com.ltgj.sdk.cozyTime.model.price;

import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 根据酒店ID列表，批量查询酒店30天（国内）和60天（国际）的每日起价信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelApi20HotelLowestPriceRequest implements CozyTimeRequestInterface<CozyTimeHotelApi20HotelLowestPriceResponse> {
    @Override
    public String getRequestMethod() {
        return "POST";
    }

    @Override
    public String getEndpoint() {
        return "/hotel_api/2.0/hotelLowestPrice";
    }

    @Override
    public Class<CozyTimeHotelApi20HotelLowestPriceResponse> getResponseClass() {
        return CozyTimeHotelApi20HotelLowestPriceResponse.class;
    }

    /**
     * 酒店Id
     * 单次最多查100个酒店
     * 必填：true
     */
    private List<Long> hotelIds;
}
