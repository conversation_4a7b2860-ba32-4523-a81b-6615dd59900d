package com.ltgj.ivw.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.annotation.Log;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.*;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.ivw.utils.hotelApi.QiantaoApi;
import com.ltgj.sdk.qiantao.model.staticdata.QiantaoQueryCityResponse;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnQiantao;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.qiantao.HotelQiantaoSupplierService;
import com.ltgj.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 千淘酒店数据Controller
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@RestController
@RequestMapping("/ivw/qiantao")
@Slf4j
public class HotelInfoQiantaoController extends AbstractIvwController<HotelGnBase, HotelGnQiantao, IHotelInfoQiantaoService> {
    @Autowired
    private IHotelInfoQiantaoService hotelInfoQiantaoService;
    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private IJdJdbMappingService jdJdbMappingService;
    @Autowired
    private IHotelCityMappingService hotelCityMappingService;
    @Autowired
    private IHotelUpdateRecodeService hotelUpdateRecodeService;
    @Autowired
    private ISysDictDataService sysDictDataService;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;
    @Autowired
    private HotelInfoQiantaoElongIdService hotelInfoQiantaoElongIdService;
    @Autowired
    private IHotelInfoElongService hotelInfoElongService;
    @Autowired
    private HotelQiantaoSupplierService hotelQiantaoSupplierService;
    @Autowired
    private HotelGnBaseService hotelGnBaseService;
    @Autowired
    private RedissonClient redissonClient;


    //获取酒店更新状态信息
    @PreAuthorize("@ss.hasPermi('ivw:qiantao:query')")
    @GetMapping("/getInfoStatus")
    public AjaxResult getInfoStatus() throws InterruptedException {
        Map<String, Object> map = new HashMap<>();
        String stepName = "";
        switch (HotelUpdateStatus.statusQT) {
            case 0:
                stepName = "初始状态";
                break;
            case 1:
                stepName = "文件下载中";
                break;
            case 2:
                stepName = "文件下载完成";
                break;
            case 3:
                stepName = "文件解压中";
                break;
            case 4:
                stepName = "文件解压完成";
                break;
            case 5:
                stepName = "酒店信息更新中";
                break;
            case 6:
                stepName = "酒店信息更新完成";
                break;
            case 11:
                stepName = "酒店映射中";
                break;
            case 12:
                stepName = "酒店映射完成";
                break;
        }
        map.put("stepName", stepName);
        if (HotelUpdateStatus.statusQT > 0) {
            if (HotelUpdateStatus.qtFileSize > 0) {
                long downPoint = HotelUpdateStatus.qtFileSizeGet * 100l / HotelUpdateStatus.qtFileSize;
                if (downPoint > 100) {
                    downPoint = 100;
                }
                map.put("downPoint", downPoint);
            }
            if (HotelUpdateStatus.statusQT > 2 && HotelUpdateStatus.qtUnzipFileSize > 0) {
                long unzipPoint = HotelUpdateStatus.qtUnzipFileSizeGet / HotelUpdateStatus.qtUnzipFileSize;
                if (unzipPoint > 100) {
                    unzipPoint = 100;
                }
                map.put("unzipPoint", unzipPoint);
            }
        }
        map.put("add", HotelUpdateStatus.qtDetailCountAdd);
        map.put("updateJS", HotelUpdateStatus.qtDetailCountUpdate);
        map.put("deleteJS", HotelUpdateStatus.qtDeleteJS);
        map.put("mapping", HotelUpdateStatus.qtMappingCount);
        map.put("mappingOK", HotelUpdateStatus.qtMappingCounted);
        map.put("mappingNO", HotelUpdateStatus.qtMappingCountFail);
        return AjaxResult.success(map);
    }

    @PreAuthorize("@ss.hasPermi('ivw:qiantao:query')")
    @GetMapping("/updateData")
    public void updateData() throws Exception {
        this.updateQiantaoAll();
    }

    @PreAuthorize("@ss.hasPermi('ivw:qiantao:query')")
    @GetMapping("/resetStatus")
    public void resetStatus() {
        this.doResetStatus();
    }
    public void doResetStatus() {
        HotelUpdateStatus.statusQT = 0;
        HotelUpdateStatus.qtUnzipFileSize = 0;
        HotelUpdateStatus.qtFileSize = 0;
        HotelUpdateStatus.qtFileSizeGet = 0;
        HotelUpdateStatus.qtUnzipFileSizeGet = 0;
        HotelUpdateStatus.status = 0;
        HotelUpdateStatus.qtDetailCountAdd = 0;
        HotelUpdateStatus.qtDetailCountUpdate = 0;
        HotelUpdateStatus.qtMappingCount = 0;
        HotelUpdateStatus.qtMappingCounted = new AtomicInteger(0);
        HotelUpdateStatus.qtMappingCountFail = new AtomicInteger(0);
    }


    public HotelCityMapping getMapping(String platId) {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
        mapping.setPlatNum(platId);
        List<HotelCityMapping> list = this.hotelCityMappingService.selectHotelCityMappingList(mapping);
        if (list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    /**
     * 平台酒店导入本地
     */
    @PreAuthorize("@ss.hasPermi('ivw:qiantao:remove')")
    @Log(title = "千淘酒店数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult platToLocal(@PathVariable String[] ids) throws Exception {
        return hotelService.platToLocal(ids);
    }

    //全量千淘酒店更新
    // updateQiantaoAllNew() 替代
    @Deprecated
    @RequestMapping("/updateQiantaoAllOld")
    public void updateQiantaoAll() throws Exception {
        if (HotelUpdateStatus.status == 1) {
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        HotelUpdateStatus.status = 1;
        HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
        hotelUpdateRecode.setYl2("2000010");
        hotelUpdateRecode.setId("QT" + MyTools.getTimesNo());
        hotelUpdateRecode.setTimeStart(new Date());
        hotelUpdateRecode.setUpdateBy(this.getUsername());
        while (true) {
            if (HotelUpdateStatus.statusQT == 0) {
                new Thread(() -> this.downAllHotelQT(1)).start();
            }
            if (HotelUpdateStatus.statusQT == 2) {
                new Thread(() -> this.unzipAllHotelQT(3, 4)).start();
            }
            if (HotelUpdateStatus.statusQT == 4) {
                new Thread(() -> {
                    try {
                        this.insertHotelQT(hotelUpdateRecode, 5, 6);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }).start();
            }
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                log.error("千淘睡眠失败: ", e);
            }
            System.err.println("等待10秒执行下一轮操作！当前状态" + HotelUpdateStatus.statusQT);
        }
    }
    //全量千淘酒店国内更新

    /**
     * 代替this.updateQiantaoAll()
     * 不写表hotel_info_qiantao
     * 改写 hotel_gn_qiantao
     *
     * @throws Exception
     */
    @RequestMapping("/updateQiantaoAll")
    public void updateQiantaoAllNew() throws Exception {

        RLock rLock = redissonClient.getLock("hotel_gn_qitantao_update_data_new");
        if (!rLock.tryLock(10, TimeUnit.SECONDS)) {
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        try {
            // 重置状态 ，job任务执行
            this.doResetStatus();

            //如果是job调用 getUsername()异常
            String userName = "admin";
            try {
                userName = this.getUsername();
            } catch (Exception e) {
            }

            HotelUpdateStatus.status = 1;
            HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
            hotelUpdateRecode.setYl2("2000010");
            hotelUpdateRecode.setId("QT" + MyTools.getTimesNo());
            hotelUpdateRecode.setTimeStart(new Date());
            hotelUpdateRecode.setUpdateBy(userName);
            while (true) {
                if (HotelUpdateStatus.statusQT == 0) {
                    new Thread(() -> this.downAllHotelQT(1)).start();
                }
                if (HotelUpdateStatus.statusQT == 2) {
                    new Thread(() -> this.unzipAllHotelQT(3, 4)).start();
                }
                if (HotelUpdateStatus.statusQT == 4) {
                    new Thread(() -> {
                        try {
                            this.insertHotelGnQT(hotelUpdateRecode, 5, 6);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }finally {
                            HotelUpdateStatus.statusQT = 6;
                        }
                    }).start();
                }

                if (HotelUpdateStatus.statusQT == 6) {
                       break;
                }

                try {
                    Thread.sleep(10000);
                } catch (InterruptedException e) {
                    log.error("千淘睡眠失败: ", e);
                }
                System.err.println("等待10秒执行下一轮操作！当前状态" + HotelUpdateStatus.statusQT);
            }
        } catch (Exception e) {
           log.error("拉取千淘酒店信息, 执行失败: ", e);
        } finally {

            try {
                rLock.unlock();
            } catch (Exception e) {
            }
        }
    }

    //千淘酒店数据
    //下载千淘酒店数据
    @RequestMapping("downAllHotelQT")
    public void downAllHotelQT(int start) {
        // 先删除文件
        String zipPath= ConstantList.DOWN_PATH + "Qiantao.zip";
        String hotelstz1= ConstantList.DOWN_PATH + "hotels-tz1.temp";
        String hotelstz2= ConstantList.DOWN_PATH + "hotels-tz2.temp";
        String hotelstz3= ConstantList.DOWN_PATH + "hotels-tz3.temp";
        String hotelstz4= ConstantList.DOWN_PATH + "hotels-tz4.temp";// 当前没有
        if(FileUtils.deleteQuietly(new File(zipPath))){
            log.info("删除千淘酒店信息压缩文件成功：{}",zipPath);
        }
        if(FileUtils.deleteQuietly(new File(hotelstz1))){
            log.info("删除千淘酒店信息hotelstz1文件成功：{}",hotelstz1);
        }
        if(FileUtils.deleteQuietly(new File(hotelstz2))){
            log.info("删除千淘酒店信息hotelstz2文件成功：{}",hotelstz2);
        }
        if(FileUtils.deleteQuietly(new File(hotelstz3))){
            log.info("删除千淘酒店信息hotelstz3文件成功：{}",hotelstz3);
        }
        if(FileUtils.deleteQuietly(new File(hotelstz4))){
            log.info("删除千淘酒店信息hotelstz4文件成功：{}",hotelstz4);
        }

        HotelUpdateStatus.qtFileSize = ResumeDownload.getRemoteFileSize(QiantaoApi.downAllUrl);
        if (HotelUpdateStatus.qtFileSize > 0) {
            HotelUpdateStatus.statusQT = start;
            new Thread(() -> {
                ResumeDownload.downFile(QiantaoApi.downAllUrl, "Qiantao");
            }).start();
        }
    }

    //解压千淘酒店数据
    @RequestMapping("unzipAllHotelQT")
    public void unzipAllHotelQT(int start, int end) {
        HotelUpdateStatus.statusQT = start;
        try {
            FileInputStream fis = new FileInputStream(ConstantList.DOWN_PATH + "Qiantao.zip");
            ZipInputStream zis = new ZipInputStream(fis);
            ZipEntry entry = zis.getNextEntry();
            while (entry != null) {
                HotelUpdateStatus.qtUnzipFileSize = entry.getSize();
                String fileName = entry.getName();
                File newFile = new File(ConstantList.DOWN_PATH + fileName);
                // 写入文件到目录中
                FileOutputStream fos = new FileOutputStream(newFile);
                byte[] buffer = new byte[1024];
                int len;
                long i = 0;
                while ((len = zis.read(buffer)) > 0) {
                    i++;
                    fos.write(buffer, 0, len);
                    HotelUpdateStatus.qtUnzipFileSizeGet = i * 1024 * 88;
                }
                fos.close();
                zis.closeEntry(); // 关闭ZipEntry并准备读取下一个条目
                entry = zis.getNextEntry();
            }
            zis.close(); // 关闭ZipInputStream以释放任何系统资源。
        } catch (Exception e) {

        }
        HotelUpdateStatus.statusQT = end;
    }

    //插入千淘酒店数据
    @RequestMapping("insertHotelQT")
    public void insertHotelQT(HotelUpdateRecode recode, int start, int end) throws IOException {
        log.info("准备插入千淘酒店数据: ....");
        HotelUpdateStatus.statusQT = start;
        List<String> idsAll = new ArrayList<>();
        File file0 = new File(ConstantList.DOWN_PATH);
        File[] files = file0.listFiles();
        String filePath;
        List<HotelInfoQiantao> list = this.hotelInfoQiantaoService.selectHotelInfoQiantaoList2(null);
        Map<String, HotelInfoQiantao> qtMap = new HashMap<>();
        for (HotelInfoQiantao hotelInfoQiantao : list) {
            qtMap.put(hotelInfoQiantao.getId(), hotelInfoQiantao);
        }
        if (files != null) {
            Map<String, String> map = new HashMap<>();

            // TODO 魏广甫 抓取千淘可售酒店列表
            String pricehotels = QiantaoApi.pricehotels();
            List<String> priceHotelList = new ArrayList<>();
            if (StringUtils.isNotBlank(pricehotels)) {
                String[] priceHotelArr = pricehotels.split("\n");
                priceHotelList = Arrays.stream(priceHotelArr).map(String::trim).collect(Collectors.toList());
            }

            for (File f : files) {
                if (f.getName().contains(".temp")) {
                    log.info("准备插入千淘酒店数据文件: {}", f.getName());
                    filePath = ConstantList.DOWN_PATH + f.getName();
                    File file = new File(filePath);
                    if (file.exists()) {
                        HotelInfoQiantao hotelInfoQiantao = new HotelInfoQiantao();
                        LineIterator it = FileUtils.lineIterator(file, "utf8");
                        try {
                            while (it.hasNext()) {
                                String line = it.nextLine();
                                try {
                                    JSONObject json = JSONObject.parseObject(line);
                                    hotelInfoQiantao.setId(json.getString("HotelCode"));
                                    hotelInfoQiantao.setName(json.getString("CnName"));
                                    hotelInfoQiantao.setBrandId(json.getString("BrandCode"));
                                    hotelInfoQiantao.setCityId(json.getString("CityCode"));
                                    hotelInfoQiantao.setStarInfo(json.getString("StarCode"));
                                    HotelInfoQiantao hiq = qtMap.get(json.getString("HotelCode"));
                                    if (hiq != null && priceHotelList.contains(hiq.getId())) {
                                        idsAll.add(json.getString("HotelCode"));
                                        if (!hiq.getName().equals(json.getString("CnName")) ||
                                                !hiq.getAddressLine().equals(json.getString("AddrCN"))) {
                                            hiq.setName(json.getString("CnName"));
                                            hiq.setAddressLine(json.getString("AddrCN"));
                                            hiq.setReserve9("2");
                                            this.hotelInfoQiantaoService.updateHotelInfoQiantao(hiq);
                                            HotelUpdateStatus.qtDetailCountUpdate++;
                                        }
                                        continue;
                                    }
                                    if (!priceHotelList.contains(json.getString("HotelCode"))) {
                                        continue;
                                    }
                                    if (StringUtils.isEmpty(json.getString("GovZone"))) {
                                        continue;
                                    }
                                    if (map.containsKey(json.getString("CnName") + json.getString("AddrCN"))) {
                                        continue;
                                    }
                                    hotelInfoQiantao.setAreaId(json.getString("GovZone"));
                                    hotelInfoQiantao.setNoticeInfo(json.getString("HotelTips"));
                                    hotelInfoQiantao.setImage(json.getString("MainPicture"));
                                    String address = json.getString("AddrCN");
                                    if (address.length() > 40) {
                                        address = address.substring(0, 40);
                                    }
                                    hotelInfoQiantao.setAddressLine(address);
                                    hotelInfoQiantao.setBusinessDistricts(json.getString("BusinessZone"));
                                    hotelInfoQiantao.setPhone(json.getString("PhoneNum"));
                                    hotelInfoQiantao.setOpenDate(json.getString("OpeningTime"));
                                    hotelInfoQiantao.setDecorationDate(json.getString("FixTime"));
                                    hotelInfoQiantao.setFacilitiesInfo(json.getString("HotelFacilities"));
                                    hotelInfoQiantao.setDescription(json.getString("Description"));
                                    hotelInfoQiantao.setGroupId(json.getString("HotelGroup"));
                                    try {
                                        BigDecimal lonGd = json.getBigDecimal("GDLongitude");
                                        lonGd = BigUtils.getBigDecimal(lonGd, 999);
                                        BigDecimal latGd = json.getBigDecimal("GDLatitude");
                                        latGd = BigUtils.getBigDecimal(latGd, 99);
                                        hotelInfoQiantao.setLonGd(lonGd);
                                        hotelInfoQiantao.setLatGd(latGd);
                                        hotelInfoQiantao.setStatus(1);
                                        hotelInfoQiantao.setReserve9("1");
                                        this.hotelInfoQiantaoService.insertHotelInfoQiantao(hotelInfoQiantao);
                                        HotelUpdateStatus.qtDetailCountAdd++;
                                        map.put(json.getString("CnName") + json.getString("AddrCN"), json.getString("CnName") + json.getString("AddrCN"));
                                    } catch (Exception e) {
                                        log.error("插入千淘酒店进异常了: ", e);
                                        TXTUtil.writeTXT(new Date() + " 插入千淘酒店进异常了 " + e, ConstantList.LOG_PATH,
                                                "log.txt");
                                        continue;
                                    }
                                } catch (Exception e) {
                                    log.error("处理千淘酒店数据异常: {}", line);
                                    log.error("处理千淘酒店数据异常: ", e);
                                }
                            }
                        } finally {
                            LineIterator.closeQuietly(it);
                        }
                    }
                }
            }
            //删除已失效酒店及映射
            for (HotelInfoQiantao hotel : list) { //遍历key
                if (!idsAll.contains(hotel.getId())) {
                    this.hotelInfoQiantaoService.deleteHotelInfoQiantaoById(hotel.getId());
                    HotelUpdateStatus.qtDeleteJS++;
                    JdJdbMapping queryMapping = new JdJdbMapping();
                    queryMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
                    queryMapping.setPlatId(hotel.getId());
                    for (JdJdbMapping jdJdbMapping : this.jdJdbMappingService.selectJdJdbMappingList(queryMapping)) {
                        this.jdJdbMappingService.deleteJdJdbMappingByIdV1(jdJdbMapping.getId());
                    }
                    ZhJdJdbMapping zhqueryMapping = new ZhJdJdbMapping();
                    zhqueryMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
                    zhqueryMapping.setPlatId(hotel.getId());
                    List<ZhJdJdbMapping> zhJdJdbMappingList = this.zhJdJdbMappingService.selectZhJdJdbMappingList(zhqueryMapping);
                    List<Long> idList = new ArrayList<>();
                    for (int i = 0; i < zhJdJdbMappingList.size(); i++) {
                        ZhJdJdbMapping zhjdJdbMapping = zhJdJdbMappingList.get(i);
                        if (i % 100 == 0) {
                            this.zhJdJdbMappingService.deleteByIds(idList);
                            idList = new ArrayList<>();
                        } else {
                            idList.add(zhjdJdbMapping.getId());
                        }
                    }
                    if (!CollectionUtils.isEmpty(idList)) {
                        this.zhJdJdbMappingService.deleteByIds(idList);
                    }
                }
            }
        }
        HotelUpdateStatus.statusQT = end;
        HotelUpdateStatus.status = 0;
        recode.setCountFail(HotelUpdateStatus.qtDetailCountUpdate);
        recode.setCountAdd(HotelUpdateStatus.qtDetailCountAdd);
        recode.setCountDel(HotelUpdateStatus.qtDeleteJS);
        recode.setTimeEnd(new Date());
        this.hotelUpdateRecodeService.insertHotelUpdateRecode(recode);
    }

    //插入千淘酒店国内数据
    @RequestMapping("insertHotelGnQT")
    public void insertHotelGnQT(HotelUpdateRecode recode, int start, int end) throws IOException {
        log.info("准备插入千淘酒店国内数据: ....");
        HotelUpdateStatus.statusQT = start;
        List<String> idsAll = new ArrayList<>();
        File file0 = new File(ConstantList.DOWN_PATH);
        File[] files = file0.listFiles();
        String filePath;
        List<HotelGnBase> list = this.hotelGnBaseService.getAllIdList(PlatEnum.PLAT_QT);
        //List<HotelInfoQiantao> list = this.hotelInfoQiantaoService.selectHotelInfoQiantaoList2(null);
        Map<String, HotelGnBase> qtMap = new HashMap<>();
        for (HotelGnBase hotelInfoQiantao : list) {
            qtMap.put(hotelInfoQiantao.getId(), null);
        }
        list = null;
        if (files != null) {
            // TODO 魏广甫 抓取千淘可售酒店列表
            String pricehotels = QiantaoApi.pricehotels();
            List<String> priceHotelList = new ArrayList<>();
            if (StringUtils.isNotBlank(pricehotels)) {
                String[] priceHotelArr = pricehotels.split("\n");
                priceHotelList = Arrays.stream(priceHotelArr).map(String::trim).collect(Collectors.toList());
            }

            Map<String, String> map = new HashMap<>();


            for (File f : files) {
                if (f.getName().contains(".temp")) {
                    log.info("准备插入千淘酒店国内数据文件: {}", f.getName());
                    filePath = ConstantList.DOWN_PATH + f.getName();
                    File file = new File(filePath);
                    if (file.exists()) {
                        LineIterator it = FileUtils.lineIterator(file, "utf8");
                        try {
                            while (it.hasNext()) {
                                String line = it.nextLine();
                                try {
                                    JSONObject json = JSONObject.parseObject(line);
                                    if (map.containsKey(json.getString("CnName") + json.getString("AddrCN"))) {
                                        continue;
                                    }
                                    // 写国内酒店千淘
                                    int result = this.hotelInfoQiantaoService.saveOrUpdateHotelGnQianTao(json);
                                    HotelGnBase hiq = qtMap.get(json.getString("HotelCode"));
                                    if (result > 0) {
                                        map.put(json.getString("CnName") + json.getString("AddrCN"), "");
                                        // 修改
                                        if (hiq != null && priceHotelList.contains(hiq.getId())) {
                                            idsAll.add(json.getString("HotelCode"));
                                            HotelUpdateStatus.qtDetailCountUpdate++;
                                        } else {// 添加
                                            HotelUpdateStatus.qtDetailCountAdd++;
                                        }
                                    }

                                } catch (Exception e) {
                                    log.error("处理千淘酒店数据异常: {}", line);
                                    log.error("处理千淘酒店数据异常: ", e);
                                }
                            }
                        } finally {
                            LineIterator.closeQuietly(it);
                        }
                    }
                }
            }
            //删除已失效酒店及映射
//            for (HotelGnBase hotel : list) { //遍历key
//                if (!idsAll.contains(hotel.getId())) {
//                    //this.hotelInfoQiantaoService.deleteHotelInfoQiantaoById(hotel.getId());
//                    this.hotelGnBaseService.deleteHotelGnById(PlatEnum.PLAT_QT, hotel.getId());
//                    HotelUpdateStatus.qtDeleteJS++;
////                    JdJdbMapping queryMapping = new JdJdbMapping();
////                    queryMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
////                    queryMapping.setPlatId(hotel.getId());
////                    for (JdJdbMapping jdJdbMapping : this.jdJdbMappingService.selectJdJdbMappingList(queryMapping)) {
////                        this.jdJdbMappingService.deleteJdJdbMappingByIdV1(jdJdbMapping.getId());
////                    }
////                    ZhJdJdbMapping zhqueryMapping = new ZhJdJdbMapping();
////                    zhqueryMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
////                    zhqueryMapping.setPlatId(hotel.getId());
////                    List<ZhJdJdbMapping> zhJdJdbMappingList = this.zhJdJdbMappingService.selectZhJdJdbMappingList(zhqueryMapping);
////                    List<Long> idList = new ArrayList<>();
////                    for (int i = 0; i < zhJdJdbMappingList.size(); i++) {
////                        ZhJdJdbMapping zhjdJdbMapping = zhJdJdbMappingList.get(i);
////                        if (i % 100 == 0) {
////                            this.zhJdJdbMappingService.deleteByIds(idList);
////                            idList = new ArrayList<>();
////                        } else {
////                            idList.add(zhjdJdbMapping.getId());
////                        }
////                    }
////                    if (!CollectionUtils.isEmpty(idList)) {
////                        this.zhJdJdbMappingService.deleteByIds(idList);
////                    }
//                }
//            }
        }
        HotelUpdateStatus.statusQT = end;
        HotelUpdateStatus.status = 0;
        recode.setCountFail(HotelUpdateStatus.qtDetailCountUpdate);
        recode.setCountAdd(HotelUpdateStatus.qtDetailCountAdd);
        recode.setCountDel(HotelUpdateStatus.qtDeleteJS);
        recode.setTimeEnd(new Date());
        this.hotelUpdateRecodeService.insertHotelUpdateRecode(recode);

    }


    public List<HotelCityMapping> getCitys2(String interfacePlat) {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setInterfacePlat(Long.valueOf(interfacePlat));
        return this.hotelCityMappingService.selectHotelCityMappingList(mapping);
    }

    public List<String> getCityStr() {
        HotelCityMapping mapping = new HotelCityMapping();
        mapping.setLocalData(1);
        List<HotelCityMapping> mappingList = this.hotelCityMappingService.selectHotelCityMappingList(mapping);
        List<String> cityStr = new ArrayList<>();
        for (HotelCityMapping hotelCityMapping : mappingList) {
            cityStr.add(hotelCityMapping.getCityName());
        }
        return cityStr;
    }

    /**
     * 千淘酒店数据映射
     *
     * @return
     * @throws Exception
     */
    @RequestMapping("/mapping/old")
    public AjaxResult mappingOld() throws Exception {
        if (HotelUpdateStatus.status == 1) {
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }

        Map<String, JdJdbMapping> jdjdbmappingMap = new HashMap<>();
        HotelUpdateStatus.status = 1;
        HotelUpdateStatus.statusQT = 11;
        List<HotelCityMapping> citys = this.getCitys2(PlatEnum.PLAT_QT.getValue());
        log.info("千淘酒店城市映射 citys:{}", citys.size());
        Map<String, HotelCityMapping> mapCity = new HashedMap();
        for (HotelCityMapping city : citys) {
            mapCity.put(city.getPlatNum(), city);
        }
        HotelInfoQiantao search = new HotelInfoQiantao();
        search.setStatus(1);
        List<HotelInfoQiantao> list = this.hotelInfoQiantaoService.selectHotelInfoQiantaoList2(search);
        log.info("千淘酒店数据总数量：" + list.size());
        HotelUpdateStatus.qtMappingCount = list.size();
        if (HotelUpdateStatus.jdMap.get("10119") == null) {
            HotelCityMapping city = new HotelCityMapping();
            city.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_EL.getValue()));
            List<HotelCityMapping> elongCityList = this.hotelCityMappingService.selectHotelCityMappingList(city);
            log.info("elong酒店城市数量：" + elongCityList.size());
            JdJdb jdJdb = new JdJdb();
            for (HotelCityMapping cityMapping : elongCityList) {
                jdJdb.setCityId(cityMapping.getLocalId());
                HotelUpdateStatus.jdMap.put(cityMapping.getLocalId(), this.jdJdbService.selectJdJdbList(jdJdb));
                log.info("elong酒店城市：{}", cityMapping.getCityName());
            }
        }
        List<String> cityStr = this.getCityStr();

        int tc = Integer.valueOf(this.sysDictDataService.selectDictLabel("hotel_params", "mapping_thread_count"));
        AtomicInteger threadSize = new AtomicInteger(tc);
        log.info("mapping_thread_count：{}", tc);

        int count = list.size();
        int tcount = count / tc;

        for (int t = 0; t < tc; t++) {
            List<HotelInfoQiantao> listSub0;
            if (t == tc - 1) {
                listSub0 = list.subList(t * tcount, count);
            } else {
                listSub0 = list.subList(t * tcount, (t + 1) * tcount);
            }
            List<HotelInfoQiantao> listSub = listSub0;
            new Thread(() -> {
                log.info("千淘线程开始: {}", Thread.currentThread().getName());
                try {
                    HotelCityMapping hotelCityMapping = new HotelCityMapping();
                    hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
                    for (int j = 0; j < listSub.size(); j++) {
                        HotelInfoQiantao noys = listSub.get(j);
                        log.info("开始处理千淘酒店数据: hotelId={}, hotelName={}", noys.getId(), noys.getName());
                        hotelCityMapping.setPlatNum(noys.getCityId());
                        List<HotelCityMapping> mappings = this.hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
                        log.info("mappings：{},cityId:{}", mappings.size(), noys.getCityId());
                        if (mappings.size() == 0) {
                            HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                            noys.setStatus(9);
                            this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys);
                            continue;
                        }
                        log.info("本地城市localId:{}", mappings.get(0).getLocalId());
                        List<JdJdb> jdList = HotelUpdateStatus.jdMap.get(mappings.get(0).getLocalId());
                        log.info("jdList:{}", jdList);
                        if (jdList == null || jdList.size() == 0) {
                            HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                            noys.setStatus(9);
                            this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys);
                            continue;
                        }
                        String namePlat = MatchRate.getOk(noys.getName());
                        String addressPlat = MatchRate.getOk(noys.getAddressLine());
                        String telPlat = MatchRate.getOk(noys.getPhone());
                        BigDecimal lonGDPlat = noys.getLonGd();
                        BigDecimal latGDPlat = noys.getLatGd();
                        BigDecimal lonBDPlat = noys.getLonBd();
                        BigDecimal latBDPlat = noys.getLatBd();
                        JdJdbMapping jdJdbMapping = new JdJdbMapping();
                        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
                        for (int i = 0; i < jdList.size(); i++) {
                            JdJdb baseys = jdList.get(i);
                            String name = MatchRate.getOk(baseys.getJdmc());
                            String address = MatchRate.getOk(baseys.getJddz());
                            String tel = MatchRate.getOk(baseys.getJddh());
                            BigDecimal lonGD = baseys.getLonGaode();
                            BigDecimal latGD = baseys.getLatGaode();
                            BigDecimal lonBD = baseys.getLonBaidu();
                            BigDecimal latBD = baseys.getLatBaidu();

                            int matchRateAddr = MatchRate.getMatchRate(null, address, addressPlat) * 2;
                            int matchRateName = MatchRate.getMatchRate2(cityStr, name, namePlat) * 5;
                            String name0 = name.replace("酒店", "").replace("店", "");
                            String namePlat0 = namePlat.replace("酒店", "").replace("店", "");
                            if (name0.equals(namePlat0)) {
                                matchRateName = 700;
                            }
                            int matchRate = matchRateAddr + matchRateName;

                            if (StringUtils.isNotEmpty(tel) && StringUtils.isNotEmpty(telPlat)) {
                                if (tel.contains(telPlat) || telPlat.contains(tel)) {
                                    matchRate += 200;
                                }
                            } else {
                                matchRate += 100;
                            }

                            int dist = 9999;
                            if (latGDPlat != null && lonGDPlat != null && lonGD != null && latGD != null) {
                                int dist2 = GetDistance.calculateDistance(lonGD.doubleValue(), latGD.doubleValue(),
                                        lonGDPlat.doubleValue(), latGDPlat.doubleValue());
                                if (dist2 < dist) {
                                    dist = dist2;
                                }
                            }
                            if (latBDPlat != null && lonBDPlat != null && lonBD != null && latBD != null) {
                                int dist3 = GetDistance.calculateDistance(lonBD.doubleValue(), latBD.doubleValue(),
                                        lonBDPlat.doubleValue(), latBDPlat.doubleValue());
                                if (dist3 < dist) {
                                    dist = dist3;
                                }
                            }
                            if (dist < 50) {
                                matchRate += 100;
                            }

                            if (matchRate > 799) {
                                log.info("mmatchRate > 799 开始");
                                JdJdbMapping jdJdbMapping2 = jdjdbmappingMap.get(baseys.getId());
                                if (jdJdbMapping2 != null) {
                                    log.info("千淘jdJdbMapping2 {}", JSONObject.toJSONString(jdJdbMapping2));
                                    if (Integer.valueOf(jdJdbMapping2.getReserve0()) < matchRate) {
                                        HotelInfoQiantao noys2 = new HotelInfoQiantao();
                                        noys2.setId(jdJdbMapping2.getPlatId());
                                        noys2.setStatus(1);
                                        this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys2);
                                        jdJdbMapping2.setReserve0(matchRate + "");
                                        jdJdbMapping2.setPlatId(noys.getId());
                                        jdjdbmappingMap.put(baseys.getId(), jdJdbMapping2);
                                        HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                                        log.info("千淘主表状态更新为1");
                                        break;
                                    } else {
                                        log.info("千淘jdJdbMapping2为空");
                                        if (i == jdList.size() - 1) {
                                            HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                                            noys.setStatus(9);
                                            this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys);
                                            log.info("千淘主表状态更新为9");
                                            break;
                                        }
                                        continue;
                                    }
                                }

                                jdJdbMapping.setPlatId(noys.getId());
                                jdJdbMapping.setLocalId(baseys.getId());
                                jdJdbMapping.setReserve0(matchRate + "");
                                jdJdbMapping.setJdName(baseys.getJdmc());
                                jdJdbMapping.setReserve3(noys.getName());
                                jdjdbmappingMap.put(baseys.getId(), jdJdbMapping);
                                HotelUpdateStatus.qtMappingCounted.incrementAndGet();
                                log.info("千淘jdjdbmappingMap数据执行完成 {}", baseys.getId());

                                break;
                            }

                            if (i == jdList.size() - 1) {
                                HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                                noys.setStatus(9);
                                this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys);
                                log.info("千淘最后一条数据执行完成");
                            }
                        }
                        log.info("处理千淘酒店数据结束: hotelId={}, hotelName={}", noys.getId(), noys.getName());
                    }
                } catch (Exception e) {
                    log.error("千淘数据映射异常 errorMessage：{}", e.getMessage(), e);
                }
                log.info("千淘线程准备结束: {}", Thread.currentThread().getName());
                threadSize.decrementAndGet();
            }).start();
        }
        while (true) {
            if (threadSize.get() == 0) {
                log.info("千淘准备插入酒店映射表...");
                Set<Map.Entry<String, JdJdbMapping>> entries = jdjdbmappingMap.entrySet();
                HotelInfoQiantao hotel = new HotelInfoQiantao();
                hotel.setStatus(8);
                for (Map.Entry<String, JdJdbMapping> entry : entries) {
//                    log.info("千淘插入酒店映射表: {}", JSONObject.toJSONString(entry));
                    try {
                        JdJdbMapping value = entry.getValue();
                        //插入前校验重复
                        log.info("千淘jdjdbmapping localId {},interfacePlat {}", value.getLocalId(), value.getInterfacePlat());
                        if (null == this.jdJdbMappingService.selectJdJdbMappingByLocalIdAndInterfacePlat(value.getLocalId(), value.getInterfacePlat())) {
                            this.jdJdbMappingService.insertJdJdbMapping(value);
                        }
//                    jdJdbMappingService.insertJdJdbMapping(value);
                        hotel.setId(value.getPlatId());
                        this.hotelInfoQiantaoService.updateHotelInfoQiantao(hotel);

                        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                        zhJdJdbMapping.setLocalId(value.getLocalId());
                        zhJdJdbMapping.setInterfacePlat(value.getInterfacePlat());
                        zhJdJdbMapping.setPlatId(value.getPlatId());
                        zhJdJdbMapping.setJdName(value.getJdName());
                        zhJdJdbMapping.setStatus(0);
                        zhJdJdbMapping.setSaveDate(new Date());
                        zhJdJdbMapping.setIsGnGj(1);
                        this.logger.info("千淘酒店zh_jd_jdb_mapping插入前数据 {}", JSONObject.toJSONString(zhJdJdbMapping));
                        //插入前校验重复
                        log.info("千淘酒店zh_jd_jdb_mapping localId {},interfacePlat {}", zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat());
                        if (null == this.zhJdJdbMappingService.findByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat())) {
                            this.zhJdJdbMappingService.insert(zhJdJdbMapping);
                            ;
                        }
                    } catch (Exception e) {
                        log.error("千淘插入映射表失败: {}", JSONObject.toJSONString(entry));
                        log.error("千淘插入映射表失败: ", e);
                    }
                }
                HotelUpdateStatus.statusQT = 12;
                HotelUpdateStatus.status = 0;
                break;
            }
            Thread.sleep(10000);
            System.out.println("千淘酒店 10秒巡逻一次，检测完成");
        }
        return AjaxResult.success();
    }

    /**
     * 千淘酒店数据映射
     *
     * @return
     * @throws Exception
     */
    @RequestMapping("/mapping")
    public AjaxResult mapping() throws Exception {
        if (HotelUpdateStatus.status == 1) {
            throw new Exception("有不可并行任务进行中，不可开启请等待任务完成！");
        }
        Map<String, JdJdbMapping> jdjdbmappingMap = new ConcurrentHashMap<>();
        HotelUpdateStatus.status = 1;
        HotelUpdateStatus.statusQT = 11;

        try {
            this.refreshRelationElongIdList();
        } catch (Exception e) {
            log.error("处理千淘与艺龙ID对应关系失败", e);
            HotelUpdateStatus.status = 1;
            return AjaxResult.error(e.getMessage());
        }

//        List<HotelCityMapping> citys = getCitys2(PlatEnum.PLAT_QT.getValue());
//        log.info("千淘酒店城市映射 citys:{}",citys.size());
//        Map<String, HotelCityMapping> mapCity = new HashedMap();
//        for (HotelCityMapping city : citys) {
//            mapCity.put(city.getPlatNum(), city);
//        }
        HotelInfoQiantao search = new HotelInfoQiantao();
        search.setStatus(1);
        List<HotelInfoQiantao> list = this.hotelInfoQiantaoService.selectHotelInfoQiantaoList2(search);
        log.info("千淘酒店数据总数量：" + list.size());
        HotelUpdateStatus.qtMappingCount = list.size();
        List<HotelInfoQiantao> newList = new ArrayList<>();
        List<List<HotelInfoQiantao>> lists = ListUtil.subList(list, 1000);
        for (List<HotelInfoQiantao> l : lists) {
            List<String> idList = l.stream().map(HotelInfoQiantao::getId).collect(Collectors.toList());
            List<HotelInfoQiantaoElongId> containIdList = this.hotelInfoQiantaoElongIdService.selectIdInStatus(idList);
            if (!CollectionUtils.isEmpty(containIdList)) {
                Map<String, HotelInfoQiantaoElongId> relationMap
                        = containIdList.stream().collect(Collectors.toMap(HotelInfoQiantaoElongId::getId, Function.identity()));
                List<String> jdJdbInList = this.jdJdbService.selectInIds(containIdList.stream().map(e -> PlatEnum.PLAT_EL.getValue() + e.getElongId()).collect(Collectors.toList()));
                List<String> updateSuccessList = new ArrayList<>();
                for (HotelInfoQiantao hotelInfoQiantao : l) {
                    HotelInfoQiantaoElongId hotelInfoQiantaoElongId = relationMap.get(hotelInfoQiantao.getId());
                    if (Objects.nonNull(hotelInfoQiantaoElongId) && jdJdbInList.contains(PlatEnum.PLAT_EL.getValue() + hotelInfoQiantaoElongId.getElongId())) {
                        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                        zhJdJdbMapping.setLocalId(PlatEnum.PLAT_EL.getValue() + hotelInfoQiantaoElongId.getElongId());
                        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
                        zhJdJdbMapping.setPlatId(hotelInfoQiantao.getId());
                        zhJdJdbMapping.setJdName(hotelInfoQiantao.getName());
                        zhJdJdbMapping.setStatus(0);
                        zhJdJdbMapping.setSaveDate(new Date());
                        zhJdJdbMapping.setIsGnGj(1);
                        this.logger.info("千淘酒店zh_jd_jdb_mapping插入前数据 {}", JSONObject.toJSONString(zhJdJdbMapping));
                        //插入前校验重复
                        log.info("千淘酒店zh_jd_jdb_mapping localId {},interfacePlat {}", zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat());
                        if (null == this.zhJdJdbMappingService.findByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat())) {
                            this.zhJdJdbMappingService.insert(zhJdJdbMapping);
                            updateSuccessList.add(hotelInfoQiantao.getId());
                        }
                        HotelUpdateStatus.qtMappingCounted.incrementAndGet();
                    } else {
                        newList.add(hotelInfoQiantao);
                    }
                }
                if (!CollectionUtils.isEmpty(updateSuccessList)) {
                    this.hotelInfoQiantaoService.updateSuccess(updateSuccessList);
                }
            }
        }
        list = newList;

        if (HotelUpdateStatus.jdMap.get("10119") == null) {
            HotelCityMapping city = new HotelCityMapping();
            city.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_EL.getValue()));
            List<HotelCityMapping> elongCityList = this.hotelCityMappingService.selectHotelCityMappingList(city);
            log.info("elong酒店城市数量：" + elongCityList.size());
            JdJdb jdJdb = new JdJdb();
            for (HotelCityMapping cityMapping : elongCityList) {
                jdJdb.setCityId(cityMapping.getLocalId());
                HotelUpdateStatus.jdMap.put(cityMapping.getLocalId(), this.jdJdbService.selectJdJdbList(jdJdb));
                log.info("elong酒店城市：{}", cityMapping.getCityName());
            }
        }
        List<String> cityStr = this.getCityStr();

        int tc = Integer.valueOf(this.sysDictDataService.selectDictLabel("hotel_params", "mapping_thread_count"));
        AtomicInteger threadSize = new AtomicInteger(tc);
        log.info("mapping_thread_count：{}", tc);

        int count = list.size();
        int tcount = count / tc;

        for (int t = 0; t < tc; t++) {
            List<HotelInfoQiantao> listSub0;
            if (t == tc - 1) {
                listSub0 = list.subList(t * tcount, count);
            } else {
                listSub0 = list.subList(t * tcount, (t + 1) * tcount);
            }
            List<HotelInfoQiantao> listSub = listSub0;
            new Thread(() -> {
                log.info("千淘线程开始: {}", Thread.currentThread().getName());
                try {
                    HotelCityMapping hotelCityMapping = new HotelCityMapping();
                    hotelCityMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
                    for (int j = 0; j < listSub.size(); j++) {
                        HotelInfoQiantao noys = listSub.get(j);
                        log.info("开始处理千淘酒店数据: hotelId={}, hotelName={}", noys.getId(), noys.getName());
                        hotelCityMapping.setPlatNum(noys.getCityId());
                        List<HotelCityMapping> mappings = this.hotelCityMappingService.selectHotelCityMappingList(hotelCityMapping);
                        log.info("mappings：{},cityId:{}", mappings.size(), noys.getCityId());
                        if (mappings.size() == 0) {
                            HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                            noys.setStatus(9);
                            this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys);
                            continue;
                        }
                        log.info("本地城市localId:{}", mappings.get(0).getLocalId());
                        List<JdJdb> jdList = HotelUpdateStatus.jdMap.get(mappings.get(0).getLocalId());
//                        log.info("jdList:{}",jdList);
                        if (jdList == null || jdList.size() == 0) {
                            HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                            noys.setStatus(9);
                            this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys);
                            continue;
                        }
                        String namePlat = MatchRate.getOk(noys.getName());
                        String addressPlat = MatchRate.getOk(noys.getAddressLine());
                        String telPlat = MatchRate.getOk(noys.getPhone());
                        BigDecimal lonGDPlat = noys.getLonGd();
                        BigDecimal latGDPlat = noys.getLatGd();
                        BigDecimal lonBDPlat = noys.getLonBd();
                        BigDecimal latBDPlat = noys.getLatBd();
                        JdJdbMapping jdJdbMapping = new JdJdbMapping();
                        jdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
                        for (int i = 0; i < jdList.size(); i++) {
                            JdJdb baseys = jdList.get(i);
                            String name = MatchRate.getOk(baseys.getJdmc());
                            String address = MatchRate.getOk(baseys.getJddz());
                            String tel = MatchRate.getOk(baseys.getJddh());
                            BigDecimal lonGD = baseys.getLonGaode();
                            BigDecimal latGD = baseys.getLatGaode();
                            BigDecimal lonBD = baseys.getLonBaidu();
                            BigDecimal latBD = baseys.getLatBaidu();

                            int matchRateAddr = MatchRate.getMatchRate(null, address, addressPlat) * 2;
                            int matchRateName = MatchRate.getMatchRate2(cityStr, name, namePlat) * 5;
                            String name0 = name.replace("酒店", "").replace("店", "");
                            String namePlat0 = namePlat.replace("酒店", "").replace("店", "");
                            if (name0.equals(namePlat0)) {
                                matchRateName = 700;
                            }
                            int matchRate = matchRateAddr + matchRateName;

                            if (StringUtils.isNotEmpty(tel) && StringUtils.isNotEmpty(telPlat)) {
                                if (tel.contains(telPlat) || telPlat.contains(tel)) {
                                    matchRate += 200;
                                }
                            } else {
                                matchRate += 100;
                            }

                            int dist = 9999;
                            if (latGDPlat != null && lonGDPlat != null && lonGD != null && latGD != null) {
                                int dist2 = GetDistance.calculateDistance(lonGD.doubleValue(), latGD.doubleValue(),
                                        lonGDPlat.doubleValue(), latGDPlat.doubleValue());
                                if (dist2 < dist) {
                                    dist = dist2;
                                }
                            }
                            if (latBDPlat != null && lonBDPlat != null && lonBD != null && latBD != null) {
                                int dist3 = GetDistance.calculateDistance(lonBD.doubleValue(), latBD.doubleValue(),
                                        lonBDPlat.doubleValue(), latBDPlat.doubleValue());
                                if (dist3 < dist) {
                                    dist = dist3;
                                }
                            }
                            if (dist < 50) {
                                matchRate += 100;
                            }

                            if (matchRate > 799) {
                                log.info("mmatchRate > 799 开始");
                                JdJdbMapping jdJdbMapping2 = jdjdbmappingMap.get(baseys.getId());
                                if (jdJdbMapping2 != null) {
                                    log.info("千淘jdJdbMapping2 {}", JSONObject.toJSONString(jdJdbMapping2));
                                    if (Integer.valueOf(jdJdbMapping2.getReserve0()) < matchRate) {
                                        HotelInfoQiantao noys2 = new HotelInfoQiantao();
                                        noys2.setId(jdJdbMapping2.getPlatId());
                                        noys2.setStatus(1);
                                        this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys2);
                                        jdJdbMapping2.setReserve0(matchRate + "");
                                        jdJdbMapping2.setPlatId(noys.getId());
                                        jdjdbmappingMap.put(baseys.getId(), jdJdbMapping2);
                                        HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                                        log.info("千淘主表状态更新为1");
                                        break;
                                    } else {
                                        log.info("千淘jdJdbMapping2为空");
                                        if (i == jdList.size() - 1) {
                                            HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                                            noys.setStatus(9);
                                            this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys);
                                            log.info("千淘主表状态更新为9");
                                            break;
                                        }
                                        continue;
                                    }
                                }

                                jdJdbMapping.setPlatId(noys.getId());
                                jdJdbMapping.setLocalId(baseys.getId());
                                jdJdbMapping.setReserve0(matchRate + "");
                                jdJdbMapping.setJdName(baseys.getJdmc());
                                jdJdbMapping.setReserve3(noys.getName());
                                jdjdbmappingMap.put(baseys.getId(), jdJdbMapping);
                                HotelUpdateStatus.qtMappingCounted.incrementAndGet();
                                log.info("千淘jdjdbmappingMap数据执行完成 {}", baseys.getId());

                                break;
                            }

                            if (i == jdList.size() - 1) {
                                HotelUpdateStatus.qtMappingCountFail.incrementAndGet();
                                noys.setStatus(9);
                                this.hotelInfoQiantaoService.updateHotelInfoQiantao(noys);
                                log.info("千淘最后一条数据执行完成");
                            }
                        }
                        log.info("处理千淘酒店数据结束: hotelId={}, hotelName={}", noys.getId(), noys.getName());
                    }
                } catch (Exception e) {
                    log.error("千淘数据映射异常 errorMessage：{}", e.getMessage(), e);
                }
                log.info("千淘线程准备结束: {}", Thread.currentThread().getName());
                threadSize.decrementAndGet();
            }).start();
        }
        while (true) {
            if (threadSize.get() == 0) {
                log.info("千淘准备插入酒店映射表...");
                Set<Map.Entry<String, JdJdbMapping>> entries = jdjdbmappingMap.entrySet();
                HotelInfoQiantao hotel = new HotelInfoQiantao();
                hotel.setStatus(8);
                for (Map.Entry<String, JdJdbMapping> entry : entries) {
//                    log.info("千淘插入酒店映射表: {}", JSONObject.toJSONString(entry));
                    try {
                        JdJdbMapping value = entry.getValue();
                        //插入前校验重复
                        log.info("千淘jdjdbmapping localId {},interfacePlat {}", value.getLocalId(), value.getInterfacePlat());
                        if (null == this.jdJdbMappingService.selectJdJdbMappingByLocalIdAndInterfacePlat(value.getLocalId(), value.getInterfacePlat())) {
                            this.jdJdbMappingService.insertJdJdbMapping(value);
                        }
//                    jdJdbMappingService.insertJdJdbMapping(value);
                        hotel.setId(value.getPlatId());
                        this.hotelInfoQiantaoService.updateHotelInfoQiantao(hotel);

                        ZhJdJdbMapping zhJdJdbMapping = new ZhJdJdbMapping();
                        zhJdJdbMapping.setLocalId(value.getLocalId());
                        zhJdJdbMapping.setInterfacePlat(value.getInterfacePlat());
                        zhJdJdbMapping.setPlatId(value.getPlatId());
                        zhJdJdbMapping.setJdName(value.getJdName());
                        zhJdJdbMapping.setStatus(0);
                        zhJdJdbMapping.setSaveDate(new Date());
                        zhJdJdbMapping.setIsGnGj(1);
                        this.logger.info("千淘酒店zh_jd_jdb_mapping插入前数据 {}", JSONObject.toJSONString(zhJdJdbMapping));
                        //插入前校验重复
                        log.info("千淘酒店zh_jd_jdb_mapping localId {},interfacePlat {}", zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat());
                        if (null == this.zhJdJdbMappingService.findByLocalIdAndInterfacePlat(zhJdJdbMapping.getLocalId(), zhJdJdbMapping.getInterfacePlat())) {
                            this.zhJdJdbMappingService.insert(zhJdJdbMapping);
                            ;
                        }
                    } catch (Exception e) {
                        log.error("千淘插入映射表失败: {}", JSONObject.toJSONString(entry));
                        log.error("千淘插入映射表失败: ", e);
                    }
                }
                HotelUpdateStatus.statusQT = 12;
                HotelUpdateStatus.status = 0;
                break;
            }
            Thread.sleep(10000);
            System.out.println("千淘酒店 10秒巡逻一次，检测完成");
        }
        return AjaxResult.success();
    }


    @PreAuthorize("@ss.hasPermi('ivw:chailvgj:edit')")
    @PostMapping("downloadLowPrice")
    public AjaxResult downloadLowPrice(@RequestBody MinPriceReq minPriceReq) {
        this.logger.info("差旅管家最低价文件下载开始");
        return AjaxResult.success(this.jdJdbService.downloadFile(minPriceReq));
    }


    @PostMapping("deleteLowPriceFile")
    public AjaxResult deleteLowPriceFile() {
        this.logger.info("差旅管家最低价文件删除开始");
        this.jdJdbService.deleteFile();
        return AjaxResult.success();
    }

    /**
     * 刷新艺龙与千淘ID对应关系
     *
     * @return
     */
    public void refreshRelationElongIdList() throws Exception {
        // 重置千淘映射状态表的状态为0
        this.hotelInfoQiantaoElongIdService.updateInitStatus();
        String elongHotelsfileName = "QiantaoElongHotels";
        QiantaoApi.getElongHotels(elongHotelsfileName);
        List<String> fileNameList = QiantaoApi.unzipElongHotels(elongHotelsfileName);
        // 读取千淘映射text数据
        if (CollectionUtils.isEmpty(fileNameList)) {
            throw new Exception("解压文件失败");
        }
        for (String fileName : fileNameList) {
            List<String> qiantaoElongIdList = QiantaoApi.readElongHotels(fileName);
            // 循环批处理映射关系
            Date now = new Date();
            if (!CollectionUtils.isEmpty(qiantaoElongIdList)) {
                List<List<String>> subList = ListUtil.subList(qiantaoElongIdList, 2000);
                for (List<String> list : subList) {
                    List<HotelInfoQiantaoElongId> hotelInfoQiantaoElongIdList = new ArrayList<>();
                    for (String line : list) {
                        if (StringUtils.isBlank(line)) {
                            continue;
                        }
                        String[] s = line.split(" ");
                        if (s.length < 2) {
                            continue;
                        }
                        String elongHotelId = s[0].trim();
                        String qiantaoHotelId = s[1].trim();
                        HotelInfoQiantaoElongId hotelInfoQiantaoElongId = new HotelInfoQiantaoElongId();
                        hotelInfoQiantaoElongId.setId(qiantaoHotelId);
                        hotelInfoQiantaoElongId.setElongId(elongHotelId);
                        hotelInfoQiantaoElongIdList.add(hotelInfoQiantaoElongId);
                    }
                    List<String> idList = hotelInfoQiantaoElongIdList.stream().map(HotelInfoQiantaoElongId::getId).collect(Collectors.toList());
                    List<String> containIdList = this.hotelInfoQiantaoElongIdService.selectIdIn(idList);
                    idList.removeAll(containIdList);
                    // 查询艺龙id是否有清除没有的艺龙ID
                    List<String> elongIds = hotelInfoQiantaoElongIdList.stream().map(HotelInfoQiantaoElongId::getElongId).collect(Collectors.toList());
                    List<String> elongIdList = this.hotelInfoElongService.selectIdIn(elongIds);
                    if (!CollectionUtils.isEmpty(idList)) {
                        List<HotelInfoQiantaoElongId> insertList = hotelInfoQiantaoElongIdList.stream()
                                .filter(e -> idList.contains(e.getId()))
                                .filter(e -> elongIdList.contains(e.getElongId()))
                                .peek(e -> e.setStatus(1))
                                .peek(e -> e.setCreateTime(now))
                                .peek(e -> e.setUpdateTime(now))
                                .collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(insertList)) {
                            this.hotelInfoQiantaoElongIdService.insertBatch(insertList);
                        }
                    }
                    if (!CollectionUtils.isEmpty(containIdList)) {
                        List<String> collect = hotelInfoQiantaoElongIdList.stream()
                                .filter(e -> containIdList.contains(e.getId()))
                                .filter(e -> elongIdList.contains(e.getElongId()))
                                .map(HotelInfoQiantaoElongId::getId)
                                .collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(collect)) {
                            this.hotelInfoQiantaoElongIdService.updateStatus(collect);
                        }
                    }
                }
            }
        }
        this.hotelInfoQiantaoElongIdService.delInitAllStatus();
    }

    /**
     * 单独处理千淘城市数据
     */
    @PostMapping("/processCityData")
    public AjaxResult processCityData(@RequestBody QiantaoQueryCityResponse citiesResponse,
                                      @RequestParam(defaultValue = "1") String idempotent) {
        try {
            int count = this.hotelQiantaoSupplierService.processCityData(citiesResponse, idempotent);
            return AjaxResult.success("处理成功，共处理千淘城市数据: " + count + "条");
        } catch (Exception e) {
            log.error("处理千淘城市数据异常", e);
            return AjaxResult.error("处理失败: " + e.getMessage());
        }
    }

    /**
     * 单独获取千淘城市数据
     */
    @GetMapping("/getCityData")
    public AjaxResult getCityData() {
        try {
            String cityListJson = QiantaoApi.QueryCity();
            if (StringUtils.isNotEmpty(cityListJson)) {
                JSONObject jsonObject = JSONObject.parseObject(cityListJson);
                QiantaoQueryCityResponse response = new QiantaoQueryCityResponse();
                response.setIsSuccess(jsonObject.getBoolean("IsSuccess"));
                response.setReturnJson(jsonObject.getJSONArray("ReturnJson").toJavaList(QiantaoQueryCityResponse.City.class));
                return AjaxResult.success("获取千淘城市数据成功", response);
            } else {
                return AjaxResult.error("获取千淘城市数据失败，返回为空");
            }
        } catch (Exception e) {
            log.error("获取千淘城市数据异常", e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }


}
