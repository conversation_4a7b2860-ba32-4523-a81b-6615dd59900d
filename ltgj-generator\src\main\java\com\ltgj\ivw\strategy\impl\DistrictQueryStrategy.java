package com.ltgj.ivw.strategy.impl;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.GeoCityGeo;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.mapper.GeoCityGeoMapper;
import com.ltgj.ivw.strategy.GeoQueryStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 区县查询策略实现
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@Slf4j
public class DistrictQueryStrategy implements GeoQueryStrategy {
    
    @Autowired
    private GeoCityGeoMapper geoCityGeoMapper;
    
    @Override
    @DataSource(DataSourceType.DEVYIXBASE)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<GeoHierarchyNode> execute(GeoLevelQueryReq request) {
        log.info("执行区县查询策略，参数：{}", request);
        
        // 转换geoType参数
        Long geoType = null;
        if (request.getGeoType() != null) {
            try {
                geoType = Long.valueOf(request.getGeoType());
            } catch (NumberFormatException e) {
                log.warn("geoType转换失败：{}", request.getGeoType());
            }
        }
        
        // 查询指定城市下的区县/商圈/标志物
        List<GeoCityGeo> districts = geoCityGeoMapper.selectDistrictsByCity(
                request.getParentId(), geoType, true);
        
        return districts.stream()
                .map(this::convertToNode)
                .collect(Collectors.toList());
    }
    
    @Override
    public String getSupportedLevel() {
        return "district";
    }
    
    /**
     * 转换为层级节点
     */
    private GeoHierarchyNode convertToNode(GeoCityGeo district) {
        GeoHierarchyNode node = new GeoHierarchyNode();
        node.setId(district.getGeoId());
        node.setName(district.getGeoName());
        node.setType("district");
        node.setLevel(4);
        node.setParentId(district.getCityId());
        node.setParentName(district.getCityName());
        node.setGeoType(district.getGeoType());
        
        // 转换坐标
        if (district.getLonBaidu() != null) {
            node.setLongitude(district.getLonBaidu().toString());
        }
        if (district.getLatBaidu() != null) {
            node.setLatitude(district.getLatBaidu().toString());
        }
        
        return node;
    }
    
} 