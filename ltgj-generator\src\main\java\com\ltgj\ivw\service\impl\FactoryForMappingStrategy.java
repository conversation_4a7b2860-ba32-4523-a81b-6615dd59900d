package com.ltgj.ivw.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2024-12-03
 */
@Service
public class FactoryForMappingStrategy {

    @Autowired
    Map<String, MappingStrategy> strategys = new ConcurrentHashMap<>(1);

    public MappingStrategy getStrategy(String component) {
        MappingStrategy mappingStrategy = strategys.get(component);
        if(ObjectUtils.isEmpty(mappingStrategy)) {
            throw new RuntimeException("no mappingStrategy defined");
        }
        return mappingStrategy;
    }
}
