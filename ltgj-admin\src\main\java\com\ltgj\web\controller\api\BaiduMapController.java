package com.ltgj.web.controller.api;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.supplier.map.SearchHttpAK;
import com.ltgj.supplier.map.request.BaiduMapPlaceSearchRequest;
import com.ltgj.supplier.map.response.BaiduMapApiResponse;
import com.ltgj.supplier.map.response.BaiduMapPlaceSearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Slf4j
@RestController
@RequestMapping("/api/map")
public class BaiduMapController {

    /**
     * 搜索
     *
     * @param region 区域
     * @param query  查询
     * @return {@link AjaxResult }
     */
    @GetMapping("/search")
    public AjaxResult search(String region, String query) {
        try {
            SearchHttpAK snCal = new SearchHttpAK();
            BaiduMapPlaceSearchRequest request = new BaiduMapPlaceSearchRequest();
            request.setQuery(query);
            request.setRegion(region);
            BaiduMapApiResponse<List<BaiduMapPlaceSearchResponse>> listBaiduMapApiResponse = snCal.searchPlace2(request);
            if (listBaiduMapApiResponse.isSuccess()){
                List<BaiduMapPlaceSearchResponse> result = listBaiduMapApiResponse.getResults();
                return AjaxResult.success(result);
            }else {
                return AjaxResult.error(listBaiduMapApiResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("查询百度地图数据异常", e);
            return AjaxResult.error("查询百度地图数据失败: " + e.getMessage());
        }
    }

}