package com.ltgj.ivw.mapper;

import com.ltgj.ivw.domain.HotelGnSupplier;
import com.ltgj.ivw.dto.HotelGnSupplierReq;

import java.time.LocalDate;
import java.util.List;

public interface HotelGnSupplierMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_ketan
     *
     * @mbg.generated 2025/06/06 16:16
     */
    public List<HotelGnSupplier> selectList(HotelGnSupplierReq record);

    public int selectCount(HotelGnSupplierReq record);

    public int selectSupplierCount(HotelGnSupplierReq queryReq);

    public int selectSuccessCount(HotelGnSupplierReq queryReq);
}
