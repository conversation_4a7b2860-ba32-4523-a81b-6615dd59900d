package com.ltgj.ivw.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.ltgj.common.exception.ServiceException;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelCityAll;
import com.ltgj.ivw.dto.HotelSupplierSearchCityReq;
import com.ltgj.ivw.enums.PlatEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import com.ltgj.ivw.mapper.HotelCityMapper;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.service.IHotelCityService;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 酒店城市管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Service
@Slf4j
public class HotelCityServiceImpl implements IHotelCityService
{
    @Autowired
    private HotelCityMapper hotelCityMapper;

    private static Map<String, Map<String, HotelCity>> cityCacheMap = new HashMap<>();

    private static Map<String, Map<String, HotelCity>> areaCacheMap = new HashMap<>();

    /**
     * 查询酒店城市管理
     *
     * @param id 酒店城市管理主键
     * @return 酒店城市管理
     */
    @Override
    public HotelCity selectHotelCityById(Integer id)
    {
        return hotelCityMapper.selectHotelCityById(id);
    }

    @Override
    public HotelCity selectHotelCityByCityId(String cityId) {
        return hotelCityMapper.selectHotelCityByCityId(cityId);
    }

    @Override
    public HotelCity selectHotelCityByLocationId(String locationId) {
        return hotelCityMapper.selectHotelCityByLocationId(locationId);
    }

    /**
     * 查询酒店城市管理列表
     *
     * @param hotelCity 酒店城市管理
     * @return 酒店城市管理
     */
    @Override
    public List<HotelCity> selectHotelCityList(HotelCity hotelCity)
    {
        return hotelCityMapper.selectHotelCityList(hotelCity);
    }
    /**
     * 查询酒店城市管理列表符合条件的总数
     * @return
     */
    @Override
    public Long selectHotelCityCount(HotelCity hotelCity)
    {
        return hotelCityMapper.selectHotelCityCount(hotelCity);
    }

    /**
     * 新增酒店城市管理
     *
     * @param hotelCity 酒店城市管理
     * @return 结果
     */
    @Override
    public int insertHotelCity(HotelCity hotelCity)
    {
        return hotelCityMapper.insertHotelCity(hotelCity);
    }

    /**
     * 批量插入酒店城市信息
     *
     * @param hotelCity 酒店城市列表
     * @return 插入成功的条数
     */
    @Override
    public int batchInsertHotelCity(List<HotelCity> hotelCity) {
        int result = 0;

        List<List<HotelCity>> partition = Lists.partition(hotelCity, 200);

        for (List<HotelCity> hotelCities : partition) {
            try {
                int insertCount = hotelCityMapper.batchInsertHotelCity(hotelCities);
                result += insertCount;
            } catch (Exception e) {
                // 记录异常日志
                log.error("批量插入酒店城市信息时发生错误: {}", e.getMessage(), e);
                // 根据业务需求决定是否抛出异常或继续执行
                throw new RuntimeException("批量插入酒店城市信息失败", e);
            }
        }

        return result;
    }

    @Override
    public int insertHotelCityAll(HotelCityAll hotelCity)
    {
        return hotelCityMapper.insertHotelCityAll(hotelCity);
    }

    /**
     * 修改酒店城市管理
     *
     * @param hotelCity 酒店城市管理
     * @return 结果
     */
    @Override
    public int updateHotelCity(HotelCity hotelCity)
    {
        return hotelCityMapper.updateHotelCity(hotelCity);
    }
    /**
     * 修改酒店城市管理通过城市id（批量更新）
     *
     * @param hotelCitys 酒店城市管理列表
     * @return 结果
     */
    @Override
    public int updateHotelCityByCityId(List<HotelCity> hotelCitys)
    {
        if (CollectionUtils.isEmpty(hotelCitys)) {
            log.warn("批量更新酒店城市数据为空");
            return 0;
        }

        int totalUpdated = 0;
        int batchSize = 100; // 每批处理100条记录
        
        // 分页处理，避免一次性更新过多数据
        List<List<HotelCity>> partitions = Lists.partition(hotelCitys, batchSize);
        
        log.info("开始批量更新酒店城市数据，总数量：{}，分{}批处理，每批{}条", 
                hotelCitys.size(), partitions.size(), batchSize);
        
        for (int i = 0; i < partitions.size(); i++) {
            List<HotelCity> batch = partitions.get(i);
            try {
                log.debug("正在处理第{}批数据，数量：{}", i + 1, batch.size());
                
                // 执行批量更新
                int batchResult = hotelCityMapper.updateHotelCityByCityId(batch);
                totalUpdated += batchResult;
                
                log.debug("第{}批数据更新完成，更新数量：{}", i + 1, batchResult);
                
            } catch (Exception e) {
                log.error("第{}批酒店城市数据更新失败，数量：{}，错误信息：{}", 
                         i + 1, batch.size(), e.getMessage(), e);
                // 根据业务需求决定是否继续执行还是抛出异常
                throw new RuntimeException("批量更新酒店城市数据失败", e);
            }
        }
        
        log.info("批量更新酒店城市数据完成，总更新数量：{}", totalUpdated);
        return totalUpdated;
    }

    /**
     * 批量删除酒店城市管理
     *
     * @param ids 需要删除的酒店城市管理主键
     * @return 结果
     */
    @Override
    public int deleteHotelCityByIds(Integer[] ids)
    {
        return hotelCityMapper.deleteHotelCityByIds(ids);
    }

    /**
     * 删除酒店城市管理信息
     *
     * @param id 酒店城市管理主键
     * @return 结果
     */
    @Override
    public int deleteHotelCityById(Integer id)
    {
        return hotelCityMapper.deleteHotelCityById(id);
    }

    /**
     * 导入城市数据
     *
     * @param cityList 城市数据列表
     * @return 结果
     */
    @Override
    public String importCity(List<HotelCity> cityList)
    {
        if (StringUtils.isNull(cityList) || cityList.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (HotelCity city : cityList)
        {
            try {
                hotelCityMapper.insertHotelCity(city);
                successNum++;
                successMsg.append("<br/>" + successNum + "、账号 " + city.getCityName() + " 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、城市 " + city.getCityName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<HotelCity> selectHotelCityListByName(HotelCity hotelCityQuery) {
        return hotelCityMapper.selectHotelCityListByName(hotelCityQuery);
    }

    @Override
    public HotelCity selectByHsjlLocation(String locationId) {
        return hotelCityMapper.selectByHsjlLocation(locationId);
    }

    @Override
    public List<HotelCity> selectByCityName(HotelSupplierSearchCityReq hotelCity) {
        return hotelCityMapper.selectByCityName(hotelCity);
    }

    public HotelCity getByCityIdAndPlatIdForCache(PlatEnum platEnum, String cityId) {
        try {
            if (platEnum == null || StringUtils.isEmpty(cityId)) {
                return null;
            }
            Map<String, HotelCity> platCityMap =  cityCacheMap.get(platEnum.getValue());
            if (platCityMap == null || platCityMap.isEmpty()) {
                List<HotelCity> cityList = hotelCityMapper.selectByPlatId(platEnum.getValue());
                if (CollectionUtils.isEmpty(cityList)) {
                    return null;
                }
                platCityMap = cityList.stream().collect(Collectors.toMap(HotelCity::getCityId, Function.identity(), (k1, k2) -> k1));
                cityCacheMap.put(platEnum.getValue(), platCityMap);
            }
            return platCityMap.get(cityId);
        } catch (Exception e) {
            log.error("{}-获取城市异常, cityId:{}, error:{}", platEnum.getName(), cityId, e.getStackTrace());
            e.printStackTrace();
        }
        return null;
    }

    public HotelCity getByAreaIdAndPlatIdForCache(PlatEnum platEnum, String areaId) {
        try {
            if (platEnum == null || StringUtils.isEmpty(areaId)) {
                return null;
            }
            Map<String, HotelCity> platCityMap =  areaCacheMap.get(platEnum.getValue());
            if (platCityMap == null || platCityMap.isEmpty()) {
                List<HotelCity> cityList = hotelCityMapper.selectByPlatId(platEnum.getValue());
                if (CollectionUtils.isEmpty(cityList)) {
                    return null;
                }
                platCityMap = cityList.stream().collect(Collectors.toMap(HotelCity::getLocationId, Function.identity(), (k1, k2) -> k1));
                areaCacheMap.put(platEnum.getValue(), platCityMap);
            }
            return platCityMap.get(areaId);
        } catch (Exception e) {
            log.error("{}-获取城市异常, cityId:{}, error:{}", platEnum.getName(), areaId, e.getStackTrace());
            e.printStackTrace();
        }
        return null;
    }

    @Scheduled(cron = "0 1/30 * * * ?")
    public void clearCityCache() {
        cityCacheMap.clear();
        log.info("清除城市缓存完成");
    }
}
