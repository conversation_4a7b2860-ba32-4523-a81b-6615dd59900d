package com.ltgj.supplier.chailvgj;

import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelCityService;
import com.ltgj.ivw.utils.hotelApi.ChailvgjApi;
import com.ltgj.sdk.chailvgj.model.staticdata.ChailvgjCityListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 差旅管家酒店供应商服务类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class HotelChailvgjSupplierService {

    @Autowired
    private IHotelCityService hotelCityService;

    /**
     * 差旅管家供应商编码
     */
    private static final String SUPPLIER_CODE_CHAILVGJ = "CHAILVGJ";

    /**
     * 同步差旅管家城市数据
     *
     * @param idempotent 是否幂等
     * @return 处理结果
     */
    public String syncCityData(String idempotent) {
        try {
            // 1. 调用API获取差旅管家城市数据
            String cityListJson = ChailvgjApi.getCityList();
            if (StringUtils.isEmpty(cityListJson)) {
                return "获取差旅管家城市数据失败";
            }

            // 2. 解析JSON响应
            JSONObject jsonResponse = JSONObject.parseObject(cityListJson);
            ChailvgjCityListResponse citiesResponse = jsonResponse.toJavaObject(ChailvgjCityListResponse.class);
            
            if (citiesResponse == null) {
                return "解析差旅管家城市数据失败";
            }

            // 3. 处理城市数据
            int cityCount = this.processCityData(citiesResponse, idempotent);

            return String.format("差旅管家城市数据同步完成，共处理城市数据: %d条", cityCount);

        } catch (Exception e) {
            log.error("同步差旅管家城市数据异常", e);
            return "同步失败: " + e.getMessage();
        }
    }

    /**
     * 处理差旅管家城市数据
     *
     * @param citiesResponse 城市响应数据
     * @param idempotent     是否幂等
     * @return 处理的城市数量
     */
    public int processCityData(ChailvgjCityListResponse citiesResponse, String idempotent) {
        int count = 0;
        if (citiesResponse == null || citiesResponse.getData() == null || 
            CollectionUtils.isEmpty(citiesResponse.getData().getCityList())) {
            return count;
        }

        List<ChailvgjCityListResponse.City> cities = citiesResponse.getData().getCityList();
        
        for (ChailvgjCityListResponse.City city : cities) {
            try {
                HotelCity hotelCity = this.createHotelCity(city);
                // 检查是否已存在相同的城市数据（根据平台和城市ID）
                HotelCity existingQuery = new HotelCity();
                existingQuery.setReserve1(PlatEnum.PLAT_CLGJ.getValue());
                existingQuery.setCityId(hotelCity.getCityId());
                List<HotelCity> existingCities = this.hotelCityService.selectHotelCityList(existingQuery);
                
                if (StringUtils.equals(idempotent, "1") && !existingCities.isEmpty()) {
                    // 幂等模式下，如果已存在则跳过
                    log.debug("差旅管家城市数据已存在，跳过: {}", city.getCity());
                    continue;
                }
                
                if (existingCities.isEmpty()) {
                    this.hotelCityService.insertHotelCity(hotelCity);
                    count++;
                    log.debug("插入差旅管家城市数据: {}", city.getCity());
                } else {
                    log.debug("差旅管家城市数据已存在: {}", city.getCity());
                }
            } catch (Exception e) {
                log.error("处理差旅管家城市数据异常: {}", city.getCity(), e);
            }
        }

        return count;
    }

    /**
     * 创建城市对象
     *
     * @param city 差旅管家城市数据
     * @return 酒店城市对象
     */
    private HotelCity createHotelCity(ChailvgjCityListResponse.City city) {
        HotelCity hotelCity = new HotelCity();
        
        // 设置平台标识
        hotelCity.setReserve1(PlatEnum.PLAT_CLGJ.getValue());

        // 设置国家信息
        hotelCity.setCountryId(StringUtils.defaultString(city.getCountryId(),"CN"));
        hotelCity.setCountryName(StringUtils.defaultString(city.getCountry(),"中国"));
        hotelCity.setCountryNameEn(StringUtils.defaultString(city.getCountry(),"CHINA"));

        // 设置省份信息
        if (city.getProvinceId() != null) {
            hotelCity.setProvinceId(String.valueOf(city.getProvinceId()));
        }
        hotelCity.setProvinceName(city.getProvince());
        hotelCity.setProvinceNameEn(city.getProvince());

        // 设置城市信息
        if (city.getCityId() != null) {
            hotelCity.setCityId(String.valueOf(city.getCityId()));
        }
        hotelCity.setParentCityId(String.valueOf(city.getParentCityId()));
        hotelCity.setCityName(city.getCity());
        hotelCity.setCityNameEn(city.getCityNameEn());


        return hotelCity;
    }
} 