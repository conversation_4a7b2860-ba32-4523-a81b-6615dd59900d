package com.ltgj.web.job.handler;

import com.ltgj.ivw.service.HotelPreMappingManager;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@JobHandler(value = "platHotelSecondV2MappingHandler")
public class PlatHotelSecondV2MappingHandler extends IJobHandler {

    @Resource
    private HotelPreMappingManager hotelPreMappingManager;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try{
            log.info("平台酒店SecondV2预映射处理任务平台: {} 开始", s);
            hotelPreMappingManager.handleSecondPreMapping(s);
        }catch (Exception e) {
            log.error("平台酒店SecondV2预映射处理任务异常：{}", e);
        }
        return ReturnT.SUCCESS;
    }
}
