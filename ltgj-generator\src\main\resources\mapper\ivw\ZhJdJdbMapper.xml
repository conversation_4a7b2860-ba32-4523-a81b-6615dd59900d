<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.ZhJdJdbMapper">

    <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.ZhJdJdb">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="interfacePlat" jdbcType="VARCHAR" property="interfacePlat" />
        <result column="jdmc" jdbcType="VARCHAR" property="jdmc" />
        <result column="jdmc_en" jdbcType="VARCHAR" property="jdmcEn" />
        <result column="jdlx" jdbcType="VARCHAR" property="jdlx" />
        <result column="jddz" jdbcType="VARCHAR" property="jddz" />
        <result column="jddz_en" jdbcType="VARCHAR" property="jddzEn" />
        <result column="jddh" jdbcType="VARCHAR" property="jddh" />
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="lon_google" jdbcType="DECIMAL" property="lonGoogle" />
        <result column="lat_google" jdbcType="DECIMAL" property="latGoogle" />
        <result column="lon_baidu" jdbcType="DECIMAL" property="lonBaidu" />
        <result column="lat_baidu" jdbcType="DECIMAL" property="latBaidu" />
        <result column="lon_gaode" jdbcType="DECIMAL" property="lonGaode" />
        <result column="lat_gaode" jdbcType="DECIMAL" property="latGaode" />
        <result column="city_id" jdbcType="VARCHAR" property="cityId" />
        <result column="city_name" jdbcType="VARCHAR" property="cityName" />
        <result column="brand_id" jdbcType="VARCHAR" property="brandId" />
        <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
        <result column="district" jdbcType="VARCHAR" property="district" />
        <result column="district_name" jdbcType="VARCHAR" property="districtName" />
        <result column="business_zone" jdbcType="VARCHAR" property="businessZone" />
        <result column="business_zone_name" jdbcType="VARCHAR" property="businessZoneName" />
        <result column="jdxj" jdbcType="TINYINT" property="jdxj" />
        <result column="score" jdbcType="DECIMAL" property="score" />
        <result column="jtid" jdbcType="VARCHAR" property="jtid" />
        <result column="jtmc" jdbcType="VARCHAR" property="jtmc" />
        <result column="kysj" jdbcType="VARCHAR" property="kysj" />
        <result column="zhzxsj" jdbcType="VARCHAR" property="zhzxsj" />
        <result column="rank" jdbcType="INTEGER" property="rank" />
        <result column="createdate" jdbcType="TIMESTAMP" property="createdate" />
        <result column="mender" jdbcType="VARCHAR" property="mender" />
        <result column="savedate" jdbcType="TIMESTAMP" property="savedate" />
        <result column="min_price" jdbcType="DECIMAL" property="minPrice" />
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.ltgj.ivw.domain.ZhJdJdb">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        <result column="notice_info" jdbcType="LONGVARCHAR" property="noticeInfo" />
        <result column="policy_info" jdbcType="LONGVARCHAR" property="policyInfo" />
        <result column="facilities_info" jdbcType="LONGVARCHAR" property="facilitiesInfo" />
    </resultMap>
    <resultMap id="BaseResultMapMapping" type="com.ltgj.ivw.domain.ZhJdJdbMapping">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="interface_plat" jdbcType="VARCHAR" property="interfacePlat" />
        <result column="local_id" jdbcType="VARCHAR" property="localId" />
        <result column="plat_id" jdbcType="VARCHAR" property="platId" />
        <result column="is_gn_gj" jdbcType="TINYINT" property="isGnGj" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="jd_name" jdbcType="VARCHAR" property="jdName" />
        <result column="savedate" jdbcType="TIMESTAMP" property="saveDate" />
        <result column="plat_jd_name" jdbcType="VARCHAR" property="platJdName" />
        <result column="mapping_score" jdbcType="DECIMAL" property="mappingScore" />
    </resultMap>

    <sql id="Base_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        id, interfacePlat, jdmc, jdmc_en, jdlx, jddz, jddz_en, jddh, img_url, `status`, lon_google,
        lat_google, lon_baidu, lat_baidu, lon_gaode, lat_gaode, city_id, city_name, brand_id,
        brand_name, district, district_name, business_zone, business_zone_name, jdxj, score,
        jtid, jtmc, kysj, zhzxsj, `rank`, createdate, mender, savedate, min_price
    </sql>
    <sql id="Blob_Column_List">
        <!-- @mbg.generated: generated automatically, do not modify! -->
        notice_info, policy_info, facilities_info
    </sql>


    <sql id="selectZhJdJdbMappingVo">
        select id, interface_plat, local_id, plat_id, is_gn_gj, status, jd_name, savedate,plat_jd_name,mapping_score from zh_jd_jdb_mapping
    </sql>

    <select id="selectZhJdJdbMappingByIds" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        <include refid="selectZhJdJdbMappingVo"/>
        where id in
        <foreach item="item" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCount" resultType="java.lang.Long">
        select count(0) from zh_jd_jdb_mapping
    </select>


    <update id="batchUpdateZhJdJdbMapping" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update zh_jd_jdb_mapping
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.mappingScore != null and item.mappingScore != ''"> mapping_score = #{item.mappingScore},</if>
                <if test="item.platJdName != null and item.platJdName != ''"> plat_jd_name = #{item.platJdName},</if>
            </trim>
            where id = #{item.id}
        </foreach>
    </update>

    <insert id="insert" parameterType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        insert into zh_jd_jdb_mapping
        (interface_plat,local_id,plat_id,is_gn_gj,status,savedate,jd_name,plat_jd_name,mapping_score)
        values
        (#{interfacePlat},#{localId},#{platId},#{isGnGj},#{status},now(),#{jdName},#{platJdName},#{mappingScore})
    </insert>

    <insert id="insertZhJdJdb">
        INSERT INTO zh_jd_jdb_${suffix}
            (`id`, `interfacePlat`, `jdmc`, `jdmc_en`, `jdlx`, `jddz`, `jddz_en`, `jddh`, `img_url`, `status`, `lon_google`, `lat_google`, `lon_baidu`, `lat_baidu`, `lon_gaode`, `lat_gaode`,
            `city_id`, `city_name`, `brand_id`, `brand_name`, `district`, `district_name`, `business_zone`, `business_zone_name`, `jdxj`, `score`, `jtid`, `jtmc`,
            `kysj`, `zhzxsj`, `rank`, `createdate`, `mender`, `savedate`, `min_price`)
        VALUES
            (#{jdJdb.id}, #{jdJdb.interfacePlat}, #{jdJdb.jdmc}, #{jdJdb.jdmcEn}, #{jdJdb.jdlx}, #{jdJdb.jddz}, #{jdJdb.jddzEn}, #{jdJdb.jddh},
            #{jdJdb.imgUrl}, #{jdJdb.status}, #{jdJdb.lonGoogle}, #{jdJdb.latGoogle}, #{jdJdb.lonBaidu}, #{jdJdb.latBaidu}, #{jdJdb.lonGaode}, #{jdJdb.latGaode},
            #{jdJdb.cityId}, #{jdJdb.cityName}, #{jdJdb.brandId}, #{jdJdb.brandName}, #{jdJdb.district}, #{jdJdb.districtName}, #{jdJdb.businessZone}, #{jdJdb.businessZoneName},
            #{jdJdb.jdxj}, #{jdJdb.score}, #{jdJdb.jtid}, #{jdJdb.jtmc}, #{jdJdb.kysj}, #{jdJdb.zhzxsj}, #{jdJdb.rank}, #{jdJdb.createdate}, #{jdJdb.mender},
            now(), 0.00)
    </insert>

    <update id="updateById">
        update zh_jd_jdb_mapping
        <set>
            <if test="interfacePlat != null">interface_plat=#{interfacePlat},</if>
            <if test="localId != null">local_id=#{localId},</if>
            <if test="platId != null">plat_id=#{platId},</if>
            <if test="isGnGj != null">is_gn_gj=#{isGnGj},</if>
            <if test="status != null">status=#{status},</if>
            <if test="platJdName != null">plat_jd_name=#{platJdName},</if>
            <if test="mappingScore != null">mapping_score=#{mappingScore},</if>
        </set>
    </update>

    <update id="updateImageData">
        update zh_jd_jdb_${dto.dbNum}
        <set>
            <if test="dto.imgUrl != null and dto.imgUrl != ''">
                img_url = #{dto.imgUrl},
            </if>
            <if test="dto.brandName != null and dto.brandName != ''">
                brand_name = #{dto.brandName},
            </if>
        </set>
        where id = #{dto.id}
    </update>

    <delete id="deleteByIds">
        update zh_jd_jdb_mapping set status = 1 where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteById">
        delete from zh_jd_jdb_mapping where id = #{id}
    </delete>

    <delete id="deleteThoroughByIds">
        delete from zh_jd_jdb_mapping where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByLocalIds">
        update zh_jd_jdb_mapping set status = 1 where interface_plat = #{interfacePlat} and local_id in
        <foreach item="id" collection="localIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByLocalIdAndInterfacePlat">
        delete from zh_jd_jdb_mapping where local_id=#{localId} and interface_plat=#{interfacePlat}
    </delete>
    <select id="findByLocalId" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        select * from zh_jd_jdb_mapping where local_id=#{localId}
    </select>

    <select id="findCountByLocalId" resultType="java.lang.Integer">
        select count(1) from zh_jd_jdb_mapping where local_id=#{localId}
    </select>

    <select id="findByLocalIdAndInterfacePlat" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        select * from zh_jd_jdb_mapping where local_id=#{localId} and interface_plat=#{interfacePlat} limit 1
    </select>

    <select id="selectJdJdbMappingList" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        <include refid="selectZhJdJdbMappingVo"/>
        <where>
            <if test="interfacePlat != null "> and interface_plat = #{interfacePlat}</if>
            <if test="localId != null  and localId != ''"> and local_id = #{localId}</if>
            <if test="platId != null  and platId != ''"> and plat_id = #{platId}</if>
            <if test="isGnGj != null "> and is_gn_gj = #{isGnGj}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="jdName != null  and jdName != ''"> and jd_name like concat('%', #{jdName}, '%')</if>
            <if test="platJdName != null"> and plat_jd_name like concat('%', #{platJdName}, '%'),</if>
            <if test="mappingScore != null"> and mapping_score &lt;= #{mappingScore}</if>
        </where>
    </select>

    <select id="findByPlateIdAndInterfacePlat" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        select * from zh_jd_jdb_mapping where plat_id=#{platId} and interface_plat=#{interfacePlat}
    </select>

    <select id="findByPlateIdAndInterfacePlatAndLocalId" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        select * from zh_jd_jdb_mapping where plat_id=#{platId} and interface_plat=#{interfacePlat} and local_id=#{localId}
    </select>

    <select id="findByInterfacePlatAndPlatId" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        select * from zh_jd_jdb_mapping where interface_plat=#{interfacePlat} and plat_id=#{platId} order by savedate desc, id desc
    </select>

    <select id="selectAllImageDto" resultType="com.ltgj.ivw.dto.HotelCheckImageDto">
        SELECT
            a.id as id, a.img_url as imgUrl, a.brand_name as 'brandName',
            GROUP_CONCAT( CONCAT(m.interface_plat,':',m.plat_id) SEPARATOR ',' ) AS relationInfo
        FROM
            zh_jd_jdb_${dbNum} a
        LEFT JOIN zh_jd_jdb_mapping m ON a.id = m.local_id
        where
        interface_plat in
        <foreach collection="interfacePlatList" item="interfacePlat" open="(" separator="," close=")">
            #{interfacePlat}
        </foreach>
            GROUP BY a.id
            ORDER BY a.id
            LIMIT #{s}, #{e}
    </select>

    <select id="selectByCityIdAndHotelId" resultType="java.lang.Long">
        select count(0) from zh_jd_jdb_${suffix} where id = #{hotelId}
    </select>

    <select id="findByPlateIdsAndInterfacePlat" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        select
            *
        from zh_jd_jdb_mapping
        where
            interface_plat = #{interfacePlat}
            and plat_id in
            <foreach collection="hotelIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>

    <update id="updateZhJdJdb">
        update zh_jd_jdb_${suffix}
        set interfacePlat = #{jdJdb.interfacePlat,jdbcType=VARCHAR},
            jdmc = #{jdJdb.jdmc,jdbcType=VARCHAR},
            jdmc_en = #{jdJdb.jdmcEn,jdbcType=VARCHAR},
            jdlx = #{jdJdb.jdlx,jdbcType=VARCHAR},
            jddz = #{jdJdb.jddz,jdbcType=VARCHAR},
            jddz_en = #{jdJdb.jddzEn,jdbcType=VARCHAR},
            jddh = #{jdJdb.jddh,jdbcType=VARCHAR},
            img_url = #{jdJdb.imgUrl,jdbcType=VARCHAR},
            `status` = #{jdJdb.status,jdbcType=TINYINT},
            lon_google = #{jdJdb.lonGoogle,jdbcType=DECIMAL},
            lat_google = #{jdJdb.latGoogle,jdbcType=DECIMAL},
            lon_baidu = #{jdJdb.lonBaidu,jdbcType=DECIMAL},
            lat_baidu = #{jdJdb.latBaidu,jdbcType=DECIMAL},
            lon_gaode = #{jdJdb.lonGaode,jdbcType=DECIMAL},
            lat_gaode = #{jdJdb.latGaode,jdbcType=DECIMAL},
            city_id = #{jdJdb.cityId,jdbcType=VARCHAR},
            city_name = #{jdJdb.cityName,jdbcType=VARCHAR},
            brand_id = #{jdJdb.brandId,jdbcType=VARCHAR},
            brand_name = #{jdJdb.brandName,jdbcType=VARCHAR},
            district = #{jdJdb.district,jdbcType=VARCHAR},
            district_name = #{jdJdb.districtName,jdbcType=VARCHAR},
            business_zone = #{jdJdb.businessZone,jdbcType=VARCHAR},
            business_zone_name = #{jdJdb.businessZoneName,jdbcType=VARCHAR},
            jdxj = #{jdJdb.jdxj,jdbcType=TINYINT},
            score = #{jdJdb.score,jdbcType=DECIMAL},
            jtid = #{jdJdb.jtid,jdbcType=VARCHAR},
            jtmc = #{jdJdb.jtmc,jdbcType=VARCHAR},
            kysj = #{jdJdb.kysj,jdbcType=VARCHAR},
            zhzxsj = #{jdJdb.zhzxsj,jdbcType=VARCHAR},
            `rank` = #{jdJdb.rank,jdbcType=INTEGER},
            createdate = #{jdJdb.createdate,jdbcType=TIMESTAMP},
            mender = #{jdJdb.mender,jdbcType=VARCHAR},
            savedate = #{jdJdb.savedate,jdbcType=TIMESTAMP},
            min_price = #{jdJdb.minPrice,jdbcType=DECIMAL},
            notice_info = #{jdJdb.noticeInfo,jdbcType=LONGVARCHAR},
            policy_info = #{jdJdb.policyInfo,jdbcType=LONGVARCHAR},
            facilities_info = #{jdJdb.facilitiesInfo,jdbcType=LONGVARCHAR}
        where id = #{jdJdb.id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteZhJdJdb">
        delete from zh_jd_jdb_${suffix}
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteZhJdJdbMappingByLocalId">
        delete from zh_jd_jdb_mapping
        where local_id = #{localId,jdbcType=VARCHAR}
    </delete>

    <select id="selectZhJdJdbByLocalId" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from jd_jdb
        where id = #{localId,jdbcType=VARCHAR}
    </select>

    <select id="countByPlateIdAndInterfacePlat" resultType="int">
        select count(0) from zh_jd_jdb_mapping where interface_plat=#{interfacePlat} and plat_id=#{platId};
    </select>


    <select id="findDataByLocalIdsAndJdmc" resultMap="BaseResultMapMapping">
        <include refid="selectZhJdJdbMappingVo"/>
        where local_id in
        <foreach collection="localIds" item="localId" open="(" close=")" separator=",">
            #{localId}
        </foreach>
        <if test="jdName != null ">
            and jd_name = #{jdName}
         </if>
    </select>

    <update id="updatePlatStatus">
        update ${tableName} set status = #{platStatus} where id = #{platId}
    </update>

    <insert id="batchInsert">
        INSERT INTO zh_jd_jdb_mapping (
        interface_plat,
        local_id,
        plat_id,
        is_gn_gj,
        status,
        jd_name,
        savedate,
        plat_jd_name,
        mapping_score
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.interfacePlat},
            #{item.localId},
            #{item.platId},
            #{item.isGnGj},
            #{item.status},
            #{item.jdName},
            NOW(),
            #{item.platJdName},
            #{item.mappingScore}
            )
        </foreach>

    </insert>

    <select id="selectByLocalIdsAndInterfacePlat" resultType="com.ltgj.ivw.domain.ZhJdJdbMapping">
        <include refid="selectZhJdJdbMappingVo"/>
        WHERE local_id IN
        <foreach collection="localIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND interface_plat = #{interfacePlat}
    </select>

    <select id="selectCountByPlatIdsAndInterfacePlat" resultType="int">
        select COUNT(DISTINCT plat_id) from zh_jd_jdb_mapping
        WHERE plat_id IN
        <foreach collection="platIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND interface_plat = #{interfacePlat}
    </select>
</mapper>
