package com.ltgj.ivw.mapper;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.GeoCityGeo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行政区基础数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-18
 */
public interface GeoCityGeoMapper 
{
    /**
     * 查询行政区基础数据
     * 
     * @param id 行政区基础数据主键
     * @return 行政区基础数据
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    public GeoCityGeo selectGeoCityGeoById(String id);

    /**
     * 查询行政区基础数据列表
     * 
     * @param geoCityGeo 行政区基础数据
     * @return 行政区基础数据集合
     */
    public List<GeoCityGeo> selectGeoCityGeoList(GeoCityGeo geoCityGeo);

    /**
     * 根据商圈名称查询
     *
     * @param geoName 商圈名称
     * @return 行政区基础数据集合
     */
    public String selectGeoCityByGeoName(String geoName);

    /**
     * 根据城市ID查询区县/商圈/标志物列表
     *
     * @param cityId 城市ID
     * @param geoType 地区类型
     * @param validOnly 是否只查询有效数据
     * @return 区县信息集合
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    public List<GeoCityGeo> selectDistrictsByCity(@Param("cityId") String cityId, @Param("geoType") Long geoType, @Param("validOnly") Boolean validOnly);

    /**
     * 根据地区类型查询区县/商圈/标志物列表
     *
     * @param geoType 地区类型
     * @param cityId 城市ID（可选）
     * @return 区县信息集合
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    public List<GeoCityGeo> selectDistrictsByType(@Param("geoType") Long geoType, @Param("cityId") String cityId);

    /**
     * 新增行政区基础数据
     * 
     * @param geoCityGeo 行政区基础数据
     * @return 结果
     */
    public int insertGeoCityGeo(GeoCityGeo geoCityGeo);

    /**
     * 修改行政区基础数据
     * 
     * @param geoCityGeo 行政区基础数据
     * @return 结果
     */
    public int updateGeoCityGeo(GeoCityGeo geoCityGeo);

    /**
     * 删除行政区基础数据
     * 
     * @param id 行政区基础数据主键
     * @return 结果
     */
    public int deleteGeoCityGeoById(String id);

    /**
     * 批量删除行政区基础数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGeoCityGeoByIds(String[] ids);
}
