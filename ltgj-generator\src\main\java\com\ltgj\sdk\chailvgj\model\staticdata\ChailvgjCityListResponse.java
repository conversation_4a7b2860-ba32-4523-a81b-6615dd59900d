package com.ltgj.sdk.chailvgj.model.staticdata;



import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 差旅管家城市列表查询响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChailvgjCityListResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 响应代码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 城市数据
     */
    private Data data;

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 城市列表
         */
        private List<City> cityList;
    }

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class City implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 国家编码
         */
        private String CountryId;
        /**
         * 国家名称
         */
        private String Country;
        /**
         * 国家名称英文
         */
        private String CountryEn;
        /**
         * 省份ID
         */
        private Integer ProvinceId;
        /**
         * 省份名称
         */
        private String Province;
        /**
         * 省份名称英文
         */
        private String ProvinceEn;
        /**
         * 城市ID
         */
        private Integer CityId;
        /**
         * 城市ID
         */
        private Integer ParentCityId;
        /**
         * 城市名称
         */
        private String City;
        /**
         * 城市英文名称
         */
        private String CityNameEn;
    }
} 