package com.ltgj.web.job.handler;

import com.ltgj.common.utils.ExceptionUtil;
import com.ltgj.ivw.service.AiMappingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "formatHotelDataHandle")
public class FormatHotelDataHandle extends IJobHandler {
    @Autowired
    private AiMappingService aiMappingService;
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
            log.info("开始执行酒店基础数据格式化");
            aiMappingService.formatJdjdb();
        } catch (Exception e) {
            log.error("酒店基础数据格式化失败");
            log.error(ExceptionUtils.getStackTrace(e));
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
