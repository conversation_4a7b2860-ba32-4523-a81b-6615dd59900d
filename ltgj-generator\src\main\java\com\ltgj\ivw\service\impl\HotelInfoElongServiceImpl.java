package com.ltgj.ivw.service.impl;

import com.google.common.collect.Lists;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.dto.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelInfoElongMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.supplier.cozyTime.HotelCozyTimeSupplierService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 艺龙酒店数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-24
 */
@Service("hotelInfoElongService")
@Slf4j
public class HotelInfoElongServiceImpl implements IHotelInfoElongService, IHotelInfoService
{
    @Autowired
    private HotelInfoElongMapper hotelInfoElongMapper;

    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private ISupplierElongCityMappingService supplierElongCityMappingService;

    @Autowired
    private IHotelCityMappingService hotelCityMappingService;

    @Autowired
    private IGeoHierarchyService geoHierarchyService;

    @Override
    public HotelInfo selectHotelInfoById(String id)
    {
        return hotelInfoElongMapper.selectHotelInfoElongById(id);
    }

    @Override
    public int updateHotelInfo(HotelInfo hotelInfo) {
        return hotelInfoElongMapper.updateHotelInfoElong((HotelInfoElong) hotelInfo);
    }

    /**
     * 查询艺龙酒店数据
     *
     * @param id 艺龙酒店数据主键
     * @return 艺龙酒店数据
     */
    @Override
    public HotelInfoElong selectHotelInfoElongById(String id)
    {
        return hotelInfoElongMapper.selectHotelInfoElongById(id);
    }

    /**
     * 查询艺龙酒店数据列表
     *
     * @param hotelInfoElong 艺龙酒店数据
     * @return 艺龙酒店数据
     */
    @Override
    public List<HotelInfoElong> selectHotelInfoElongList(HotelInfoElong hotelInfoElong)
    {
        return hotelInfoElongMapper.selectHotelInfoElongList(hotelInfoElong);
    }

    @Override
    public List<HotelInfoElong> selectHotelInfoElongList2(HotelInfoElong hotelInfoElong)
    {
        return hotelInfoElongMapper.selectHotelInfoElongList2(hotelInfoElong);
    }

    /**
     * 新增艺龙酒店数据
     *
     * @param hotelInfoElong 艺龙酒店数据
     * @return 结果
     */
    @Override
    public int insertHotelInfoElong(HotelInfoElong hotelInfoElong)
    {
        hotelInfoElong.setCreateTime(DateUtils.getNowDate());
        return hotelInfoElongMapper.insertHotelInfoElong(hotelInfoElong);
    }

    @Override
    public int insertHotelInfoElongs(List<HotelInfoElong> hotelInfoElongList) {
        return hotelInfoElongMapper.insertHotelInfoElongs(hotelInfoElongList);
    }

    /**
     * 修改艺龙酒店数据
     *
     * @param hotelInfoElong 艺龙酒店数据
     * @return 结果
     */
    @Override
    public int updateHotelInfoElong(HotelInfoElong hotelInfoElong)
    {
        hotelInfoElong.setUpdateTime(DateUtils.getNowDate());
        return hotelInfoElongMapper.updateHotelInfoElong(hotelInfoElong);
    }

    @Override
    public void initElongData() {
        hotelInfoElongMapper.initElongData();
    }

    /**
     * 批量删除艺龙酒店数据
     *
     * @param ids 需要删除的艺龙酒店数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoElongByIds(String[] ids)
    {
        return hotelInfoElongMapper.deleteHotelInfoElongByIds(ids);
    }

    /**
     * 删除艺龙酒店数据信息
     *
     * @param id 艺龙酒店数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoElongById(String id)
    {
        return hotelInfoElongMapper.deleteHotelInfoElongById(id);
    }

    @Override
    public List<String> selectIdIn(List<String> idList) {
        return hotelInfoElongMapper.selectIdIn(idList);
    }

    @Override
    public AjaxResult updateCityInfo() {
        long startTime = System.currentTimeMillis();
        log.info("开始更新艺龙城市信息...");
        
        try {
            // 1.查询所有科坦的城市数据
            log.info("步骤1：查询科坦平台的城市数据");
            List<HotelCity> hotelCities = hotelCityService.selectHotelCityList(
                    HotelCity.builder().reserve1(PlatEnum.PLAT_KT.getValue()).build());
            
            if (CollectionUtils.isEmpty(hotelCities)) {
                log.warn("科坦城市数据为空，无法继续处理");
                return AjaxResult.error("科坦城市数据为空");
            }
            log.info("查询到科坦城市数据数量：{}", hotelCities.size());
            
            // 1.1通过supplier_elong_city_mapping表拿到科坦和艺龙的城市id关系
            log.info("步骤2：查询科坦和艺龙的城市映射关系");
            List<SupplierElongCityMapping> supplierElongCityMappings = 
                    supplierElongCityMappingService.selectBySupplierAndElongCityCode(
                            HotelCozyTimeSupplierService.SUPPLIER_CODE_KETAN, null);
            
            if (CollectionUtils.isEmpty(supplierElongCityMappings)) {
                log.warn("科坦和艺龙的城市id映射关系为空，无法继续处理");
                return AjaxResult.error("科坦和艺龙的城市id关系为空");
            }
            log.info("查询到科坦和艺龙城市映射关系数量：{}", supplierElongCityMappings.size());
            
            // 1.2通过科坦和艺龙的城市id关系构建艺龙城市数据
            log.info("步骤3：构建艺龙城市数据");
            List<HotelCity> hotelCityList = Lists.newCopyOnWriteArrayList();
            int mappedCount = 0;
            int skippedCount = 0;
            
            for (SupplierElongCityMapping supplierElongCityMapping : supplierElongCityMappings) {
                String elongCityCode = supplierElongCityMapping.getElongCityCode();
                String supplierCityCode = supplierElongCityMapping.getSupplierCityCode();
                
                // 查找对应的科坦城市数据
                HotelCity sourceCity = hotelCities.stream()
                        .filter(item -> StringUtils.equals(item.getCityId(), supplierCityCode))
                        .findFirst()
                        .orElse(null);
                
                if (sourceCity != null) {
                    // 构建艺龙城市数据
                    HotelCity elongCity = HotelCity.builder()
                            .reserve1(PlatEnum.PLAT_EL.getValue())
                            .cityId(elongCityCode)
                            .countryId(Optional.ofNullable(sourceCity.getCountryId()).orElse("CN"))
                            .countryName(Optional.ofNullable(sourceCity.getCountryName()).orElse("中国"))
                            .countryNameEn(Optional.ofNullable(sourceCity.getCountryNameEn()).orElse("CHINA"))
                            .provinceId(sourceCity.getProvinceId())
                            .provinceName(sourceCity.getProvinceName())
                            .provinceNameEn(sourceCity.getProvinceNameEn())
                            .build();
                    
                    hotelCityList.add(elongCity);
                    mappedCount++;
                } else {
                    log.debug("未找到科坦城市数据，supplierCityCode：{}", supplierCityCode);
                    skippedCount++;
                }
            }
            
            log.info("艺龙城市数据构建完成，成功映射：{}条，跳过：{}条", mappedCount, skippedCount);

            // 2.批量更新艺龙城市信息
            if (CollectionUtils.isNotEmpty(hotelCityList)) {

                log.info("步骤4：开始批量更新艺龙城市信息，数量：{}", hotelCityList.size());
                
                int updateResult = 0;
                
                if (hotelCityList.size() > 1000) {
                    log.warn("批量更新艺龙城市信息数量超过1000条（{}条），启用二级分页处理", hotelCityList.size());
                    
                    // 1.3二级分页处理：先分成1000条的大批次，每个大批次内部还会分成100条的小批次
                    List<List<HotelCity>> largeBatches = Lists.partition(hotelCityList, 1000);
                    log.info("数据分为{}个大批次进行处理，每批最多1000条", largeBatches.size());
                    
                    for (int i = 0; i < largeBatches.size(); i++) {
                        List<HotelCity> largeBatch = largeBatches.get(i);
                        try {
                            // 计算进度百分比
                            double progressPercent = ((double)(i + 1) / largeBatches.size()) * 100;
                            log.info("正在处理第{}/{}个大批次（进度：{:.1f}%），数量：{}条", 
                                    i + 1, largeBatches.size(), progressPercent, largeBatch.size());
                            
                            long batchStartTime = System.currentTimeMillis();
                            
                            // 每个大批次内部会自动分成100条的小批次处理
                            int batchResult = hotelCityService.updateHotelCityByCityId(largeBatch);
                            updateResult += batchResult;
                            
                            long batchEndTime = System.currentTimeMillis();
                            long batchDuration = batchEndTime - batchStartTime;
                            
                            log.info("第{}/{}个大批次处理完成，耗时：{}ms，更新数量：{}条，累计更新：{}条", 
                                    i + 1, largeBatches.size(), batchDuration, batchResult, updateResult);
                            
                        } catch (Exception e) {
                            log.error("第{}/{}个大批次处理失败，数量：{}条，错误信息：{}", 
                                     i + 1, largeBatches.size(), largeBatch.size(), e.getMessage(), e);
                            // 继续处理下一批次，避免全部失败
                        }
                    }
                } else {
                    log.info("数据量在合理范围内（{}条），直接进行批量更新", hotelCityList.size());
                    // 执行批量更新（Service层内部实现100条/批的分页处理）
                    updateResult = hotelCityService.updateHotelCityByCityId(hotelCityList);
                }
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                
                log.info("艺龙城市信息更新完成！总耗时：{}ms，更新数量：{}", duration, updateResult);
                
                return AjaxResult.success("艺龙城市信息更新成功")
                        .put("totalMappings", supplierElongCityMappings.size())
                        .put("successMapped", mappedCount)
                        .put("skipped", skippedCount)
                        .put("updated", updateResult)
                        .put("duration", duration + "ms");
            } else {
                log.warn("没有需要更新的艺龙城市数据");
                return AjaxResult.error("没有需要更新的城市数据");
            }
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("更新艺龙城市信息失败，耗时：{}ms，错误信息：{}", duration, e.getMessage(), e);
            return AjaxResult.error("更新艺龙城市信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取四级联动城市数据（国家->省份->城市->区县）
     * 
     * @param platformId 平台ID（可选，用于筛选特定平台的数据）
     * @return 四级联动数据结构
     */
    @Override
    @Cacheable(value = "getCityHierarchy", keyGenerator = "keyGenerator", cacheManager = "cacheManager")
    public AjaxResult getCityHierarchy(String platformId) {
        long startTime = System.currentTimeMillis();
        log.info("开始获取四级联动城市数据，平台ID：{}", platformId);
        
        try {
            // 1. 查询条件构建
            HotelCity queryCondition = new HotelCity();
            if (StringUtils.isNotEmpty(platformId)) {
                queryCondition.setReserve1(platformId);
                log.info("步骤1：查询平台{}的城市数据", platformId);
            } else {
                log.info("步骤1：查询所有平台的城市数据");
            }
            
            // 2. 查询所有城市数据
            List<HotelCity> allCities = hotelCityService.selectHotelCityList(queryCondition);
            if (CollectionUtils.isEmpty(allCities)) {
                log.warn("未查询到城市数据");
                return AjaxResult.error("未查询到城市数据");
            }
            log.info("查询到城市数据总数：{}", allCities.size());
            
            // 3. 构建四级联动数据结构
            log.info("步骤2：开始构建四级联动数据结构");
            List<CountryNode> hierarchyData = buildFourLevelCityHierarchy(allCities);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("四级联动城市数据构建完成！总耗时：{}ms，国家数量：{}", 
                    duration, hierarchyData.size());
            
            return AjaxResult.success("获取四级联动城市数据成功")
                    .put("data", hierarchyData)
                    .put("totalCities", allCities.size())
                    .put("countryCount", hierarchyData.size())
                    .put("duration", duration + "ms");
                    
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("获取四级联动城市数据失败，耗时：{}ms，错误信息：{}", duration, e.getMessage(), e);
            return AjaxResult.error("获取四级联动城市数据失败：" + e.getMessage());
        }
    }

    /**
     * 分级查询城市数据
     * 根据传入的参数层级返回下一级数据
     * 
     * @param request 查询请求参数
     * @return 下一级数据
     */
    @Override
    @Cacheable(value = "getCityDataByLevel", keyGenerator = "keyGenerator", cacheManager = "cacheManager")
    public AjaxResult getCityDataByLevel(CityLevelQueryReq request) {
        long startTime = System.currentTimeMillis();
        log.info("开始分级查询城市数据，参数：{}", request);
        
        try {
            // 构建查询条件
            HotelCity queryCondition = new HotelCity();
            if (StringUtils.isNotEmpty(request.getPlatformId())) {
                queryCondition.setReserve1(request.getPlatformId());
            }
            
            // 根据传入参数确定查询层级并构建条件
            String queryLevel = determineQueryLevel(request);
            log.info("查询层级：{}", queryLevel);
            
            AjaxResult result;
            switch (queryLevel) {
                case "country":
                    result = getCountryList(queryCondition);
                    break;
                case "province":
                    queryCondition.setCountryId(request.getCountryId());
                    result = getProvinceList(queryCondition);
                    break;
                case "city":
                    queryCondition.setCountryId(request.getCountryId());
                    queryCondition.setProvinceId(request.getProvinceId());
                    result = getCityList(queryCondition);
                    break;
                case "district":
                    queryCondition.setCountryId(request.getCountryId());
                    queryCondition.setProvinceId(request.getProvinceId());
                    queryCondition.setCityId(request.getCityId());
                    result = getDistrictList(queryCondition);
                    break;
                default:
                    return AjaxResult.error("无效的查询参数");
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("分级查询城市数据完成！层级：{}，总耗时：{}ms", queryLevel, duration);
            result.put("queryLevel", queryLevel);
            result.put("duration", duration + "ms");
            
            return result;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.error("分级查询城市数据失败，耗时：{}ms，错误信息：{}", duration, e.getMessage(), e);
            return AjaxResult.error("分级查询城市数据失败：" + e.getMessage());
        }
    }

    /**
     * 确定查询层级
     */
    private String determineQueryLevel(CityLevelQueryReq request) {
        if (StringUtils.isEmpty(request.getCountryId())) {
            return "country";
        }
        if (StringUtils.isEmpty(request.getProvinceId())) {
            return "province";
        }
        if (StringUtils.isEmpty(request.getCityId())) {
            return "city";
        }
        return "district";
    }
    
    /**
     * 获取国家列表
     */
    private AjaxResult getCountryList(HotelCity queryCondition) {
        List<HotelCity> allCities = hotelCityService.selectHotelCityList(queryCondition);
        
        Map<String, CountryNode> countryMap = new LinkedHashMap<>();
        for (HotelCity city : allCities) {
            if (StringUtils.isNotEmpty(city.getCountryId()) && StringUtils.isNotEmpty(city.getCountryName())) {
                countryMap.computeIfAbsent(city.getCountryId(), k -> {
                    CountryNode node = new CountryNode();
                    node.setId(city.getCountryId());
                    node.setName(city.getCountryName());
                    node.setNameEn(city.getCountryNameEn());
                    return node;
                });
            }
        }
        
        List<CountryNode> countries = countryMap.values().stream()
                .sorted((c1, c2) -> Optional.ofNullable(c1.getName()).orElse("").compareTo(
                    Optional.ofNullable(c2.getName()).orElse("")))
                .collect(Collectors.toList());
        
        return AjaxResult.success("获取国家列表成功")
                .put("data", countries)
                .put("total", countries.size());
    }
    
    /**
     * 获取省份列表
     */
    private AjaxResult getProvinceList(HotelCity queryCondition) {
        List<HotelCity> allCities = hotelCityService.selectHotelCityList(queryCondition);
        
        Map<String, ProvinceNode> provinceMap = new LinkedHashMap<>();
        for (HotelCity city : allCities) {
            String provinceKey = Optional.ofNullable(city.getProvinceName()).orElse("其他");
            if (StringUtils.isNotEmpty(provinceKey)) {
                provinceMap.computeIfAbsent(provinceKey, k -> {
                    ProvinceNode node = new ProvinceNode();
                    node.setId(city.getProvinceId());
                    node.setName(provinceKey);
                    node.setNameEn(city.getProvinceNameEn());
                    return node;
                });
            }
        }
        
        List<ProvinceNode> provinces = provinceMap.values().stream()
                .sorted((p1, p2) -> Optional.ofNullable(p1.getName()).orElse("").compareTo(
                    Optional.ofNullable(p2.getName()).orElse("")))
                .collect(Collectors.toList());
        
        return AjaxResult.success("获取省份列表成功")
                .put("data", provinces)
                .put("total", provinces.size());
    }
    
    /**
     * 获取城市列表
     */
    private AjaxResult getCityList(HotelCity queryCondition) {
        List<HotelCity> allCities = hotelCityService.selectHotelCityList(queryCondition);
        
        Map<String, CityNode> cityMap = new LinkedHashMap<>();
        for (HotelCity city : allCities) {
            if (StringUtils.isNotEmpty(city.getCityId()) && StringUtils.isNotEmpty(city.getCityName())) {
                cityMap.computeIfAbsent(city.getCityId(), k -> {
                    CityNode node = new CityNode();
                    node.setId(city.getCityId());
                    node.setName(city.getCityName());
                    node.setNameEn(city.getCityNameEn());
                    return node;
                });
            }
        }
        
        List<CityNode> cities = cityMap.values().stream()
                .sorted((c1, c2) -> Optional.ofNullable(c1.getName()).orElse("").compareTo(
                    Optional.ofNullable(c2.getName()).orElse("")))
                .collect(Collectors.toList());
        
        return AjaxResult.success("获取城市列表成功")
                .put("data", cities)
                .put("total", cities.size());
    }
    
    /**
     * 获取区县列表
     */
    private AjaxResult getDistrictList(HotelCity queryCondition) {
        List<HotelCity> allCities = hotelCityService.selectHotelCityList(queryCondition);
        
        Map<String, DistrictNode> districtMap = new LinkedHashMap<>();
        for (HotelCity city : allCities) {
            if (StringUtils.isNotEmpty(city.getLocationId()) && StringUtils.isNotEmpty(city.getLocationName())) {
                districtMap.computeIfAbsent(city.getLocationId(), k -> {
                    DistrictNode node = DistrictNode.builder()
                            .locationId(city.getLocationId())
                            .locationName(city.getLocationName())
                            .locationNameEn(city.getLocationNameEn())
                            .platformId(city.getReserve1())
                            .build();
                    return node;
                });
            }
        }
        
        List<DistrictNode> districts = districtMap.values().stream()
                .sorted((d1, d2) -> Optional.ofNullable(d1.getLocationName()).orElse("").compareTo(
                    Optional.ofNullable(d2.getLocationName()).orElse("")))
                .collect(Collectors.toList());
        
        return AjaxResult.success("获取区县列表成功")
                .put("data", districts)
                .put("total", districts.size());
    }
    
    /**
     * 构建四级联动数据结构
     * 
     * @param allCities 所有城市数据
     * @return 四级联动数据结构
     */
    private List<CountryNode> buildFourLevelCityHierarchy(List<HotelCity> allCities) {
        log.info("开始构建四级联动数据结构...");
        
        // 使用Map来组织数据，提高构建效率
        Map<String, CountryNode> countryMap = new LinkedHashMap<>();
        
        int processedCount = 0;
        int validDataCount = 0;
        
        for (HotelCity city : allCities) {
            processedCount++;
            
            // 数据校验
            if (StringUtils.isEmpty(city.getCountryId()) || StringUtils.isEmpty(city.getCountryName())) {
                continue;
            }
            
            validDataCount++;
            String countryKey = city.getCountryId();
            String provinceKey = Optional.ofNullable(city.getProvinceName()).orElse("其他");
            String cityKey = Optional.ofNullable(city.getCityId()).orElse("unknown");
            
            // 获取或创建国家节点
            CountryNode countryNode = countryMap.computeIfAbsent(countryKey, k -> {
                CountryNode node = new CountryNode();
                node.setId(city.getCountryId());
                node.setName(city.getCountryName());
                node.setNameEn(city.getCountryNameEn());
                node.setProvinces(new LinkedHashMap<>());
                return node;
            });
            
            // 获取或创建省份节点
            ProvinceNode provinceNode = countryNode.getProvinces().computeIfAbsent(provinceKey, k -> {
                ProvinceNode node = new ProvinceNode();
                node.setId(city.getProvinceId());
                node.setName(provinceKey);
                node.setNameEn(city.getProvinceNameEn());
                node.setCities(new ArrayList<>());
                return node;
            });
            
            // 查找或创建城市节点
            CityNode cityNode = provinceNode.getCities().stream()
                    .filter(c -> Objects.equals(c.getId(), cityKey))
                    .findFirst()
                    .orElseGet(() -> {
                        CityNode newCity = new CityNode();
                        newCity.setId(cityKey);
                        newCity.setName(city.getCityName());
                        newCity.setNameEn(city.getCityNameEn());
                        newCity.setDistricts(new ArrayList<>());
                        provinceNode.getCities().add(newCity);
                        return newCity;
                    });
            
            // 添加区县数据
            if (StringUtils.isNotEmpty(city.getLocationId()) && StringUtils.isNotEmpty(city.getLocationName())) {
                DistrictNode districtNode = DistrictNode.builder()
                        .locationId(city.getLocationId())
                        .locationName(city.getLocationName())
                        .locationNameEn(city.getLocationNameEn())
                        .platformId(city.getReserve1())
                        .build();
                
                // 避免重复区县（基于locationId去重）
                boolean districtExists = cityNode.getDistricts().stream()
                        .anyMatch(d -> Objects.equals(d.getLocationId(), city.getLocationId()));
                
                if (!districtExists) {
                    cityNode.getDistricts().add(districtNode);
                }
            }
            
            // 每处理1000条记录输出一次日志
            if (processedCount % 1000 == 0) {
                log.debug("已处理{}条记录，有效数据{}条", processedCount, validDataCount);
            }
        }
        
        // 转换Map为List，并排序
        List<CountryNode> result = countryMap.values().stream()
                .peek(country -> {
                    // 将provinces Map转换为List并排序
                    List<ProvinceNode> provinceList = country.getProvinces().values().stream()
                            .peek(province -> {
                                // 对城市列表进行排序
                                province.getCities().sort((c1, c2) -> 
                                    Optional.ofNullable(c1.getName()).orElse("").compareTo(
                                        Optional.ofNullable(c2.getName()).orElse("")));
                                
                                // 对每个城市的区县列表进行排序
                                province.getCities().forEach(city -> {
                                    if (city.getDistricts() != null) {
                                        city.getDistricts().sort((d1, d2) -> 
                                            Optional.ofNullable(d1.getLocationName()).orElse("").compareTo(
                                                Optional.ofNullable(d2.getLocationName()).orElse("")));
                                    }
                                });
                            })
                            .sorted((p1, p2) -> 
                                Optional.ofNullable(p1.getName()).orElse("").compareTo(
                                    Optional.ofNullable(p2.getName()).orElse("")))
                            .collect(Collectors.toList());
                    
                    country.setProvinceList(provinceList);
                })
                .sorted((c1, c2) -> 
                    Optional.ofNullable(c1.getName()).orElse("").compareTo(
                        Optional.ofNullable(c2.getName()).orElse("")))
                .collect(Collectors.toList());
        
        log.info("四级联动数据结构构建完成，处理记录：{}条，有效数据：{}条，国家：{}个", 
                processedCount, validDataCount, result.size());
        
        return result;
    }
    
    /**
     * 构建三级联动数据结构（保留原方法用于兼容）
     * 
     * @param allCities 所有城市数据
     * @return 三级联动数据结构
     */
    private List<CountryNode> buildCityHierarchy(List<HotelCity> allCities) {
        log.info("开始构建层级数据结构...");
        
        // 使用Map来组织数据，提高构建效率
        Map<String, CountryNode> countryMap = new LinkedHashMap<>();
        
        int processedCount = 0;
        int validDataCount = 0;
        
        for (HotelCity city : allCities) {
            processedCount++;
            
            // 数据校验
            if (StringUtils.isEmpty(city.getCountryId()) || StringUtils.isEmpty(city.getCountryName())) {
                continue;
            }
            
            validDataCount++;
            String countryKey = city.getCountryId();
            String provinceKey = Optional.ofNullable(city.getProvinceName()).orElse("其他");
            
            // 获取或创建国家节点
            CountryNode countryNode = countryMap.computeIfAbsent(countryKey, k -> {
                CountryNode node = new CountryNode();
                node.setId(city.getCountryId());
                node.setName(city.getCountryName());
                node.setNameEn(city.getCountryNameEn());
                node.setProvinces(new LinkedHashMap<>());
                return node;
            });
            
            // 获取或创建省份节点
            ProvinceNode provinceNode = countryNode.getProvinces().computeIfAbsent(provinceKey, k -> {
                ProvinceNode node = new ProvinceNode();
                node.setId(city.getProvinceId());
                node.setName(provinceKey);
                node.setNameEn(city.getProvinceNameEn());
                node.setCities(new ArrayList<>());
                return node;
            });
            
            // 创建城市节点
            CityNode cityNode = new CityNode();
            cityNode.setId(city.getCityId());
            cityNode.setName(city.getCityName());
            cityNode.setNameEn(city.getCityNameEn());
            
            // 避免重复城市（基于cityId去重）
            boolean cityExists = provinceNode.getCities().stream()
                    .anyMatch(existingCity -> Objects.equals(existingCity.getId(), city.getCityId()));
            
            if (!cityExists) {
                provinceNode.getCities().add(cityNode);
            }
            
            // 每处理1000条记录输出一次日志
            if (processedCount % 1000 == 0) {
                log.debug("已处理{}条记录，有效数据{}条", processedCount, validDataCount);
            }
        }
        
        // 转换Map为List，并排序
        List<CountryNode> result = countryMap.values().stream()
                .peek(country -> {
                    // 将provinces Map转换为List并排序
                    List<ProvinceNode> provinceList = country.getProvinces().values().stream()
                            .peek(province -> {
                                // 对城市列表进行排序
                                province.getCities().sort((c1, c2) -> 
                                    Optional.ofNullable(c1.getName()).orElse("").compareTo(
                                        Optional.ofNullable(c2.getName()).orElse("")));
                            })
                            .sorted((p1, p2) -> 
                                Optional.ofNullable(p1.getName()).orElse("").compareTo(
                                    Optional.ofNullable(p2.getName()).orElse("")))
                            .collect(Collectors.toList());
                    
                    country.setProvinceList(provinceList);
                })
                .sorted((c1, c2) -> 
                    Optional.ofNullable(c1.getName()).orElse("").compareTo(
                        Optional.ofNullable(c2.getName()).orElse("")))
                .collect(Collectors.toList());
        
        log.info("层级数据结构构建完成，处理记录：{}条，有效数据：{}条，国家：{}个", 
                processedCount, validDataCount, result.size());
        
        return result;
    }

    @Override
    @Cacheable(value = "getParentInfo", keyGenerator = "keyGenerator", cacheManager = "cacheManager")
    public AjaxResult getParentInfo(ParentLookupReq request) {
        log.info("开始获取上级行政区划信息，参数：{}", request);
        
        try {
            // 构建查询条件
            HotelCity queryCondition = new HotelCity();
            queryCondition.setCountryId(request.getCountryId());
            
            // 如果指定了平台ID，则添加平台过滤条件
            if (StringUtils.isNotEmpty(request.getPlatformId())) {
                queryCondition.setReserve1(request.getPlatformId());
            }
            
            List<HotelCity> allCities = hotelCityService.selectHotelCityList(queryCondition);
            
            if (CollectionUtils.isEmpty(allCities)) {
                log.warn("未找到对应的城市数据，查询条件：{}", request);
                return AjaxResult.error("未找到对应的城市数据");
            }
            
            log.info("查询到城市数据数量：{}", allCities.size());
            
            if ("city".equals(request.getChildType())) {
                // 通过城市ID查询所属省份
                return getProvinceByCity(request.getChildId(), allCities);
            } else if ("district".equals(request.getChildType())) {
                // 通过区县ID查询所属城市
                return getCityByDistrict(request.getChildId(), allCities);
            } else {
                return AjaxResult.error("不支持的子级类型：" + request.getChildType());
            }
            
        } catch (Exception e) {
            log.error("获取上级行政区划信息异常，参数：{}，错误信息：{}", request, e.getMessage(), e);
            return AjaxResult.error("获取上级行政区划信息异常：" + e.getMessage());
        }
    }
    
    /**
     * 通过城市ID获取所属省份信息
     */
    private AjaxResult getProvinceByCity(String cityId, List<HotelCity> allCities) {
        log.info("通过城市ID获取省份信息，城市ID：{}", cityId);
        
        // 查找指定城市的省份信息
        Optional<HotelCity> targetCity = allCities.stream()
                .filter(city -> Objects.equals(city.getCityId(), cityId))
                .findFirst();
        
        if (!targetCity.isPresent()) {
            log.warn("未找到指定的城市，城市ID：{}", cityId);
            return AjaxResult.error("未找到指定的城市");
        }
        
        HotelCity city = targetCity.get();
        
        // 构建省份信息
        Map<String, Object> provinceInfo = new HashMap<>();
        provinceInfo.put("provinceId", city.getProvinceId());
        provinceInfo.put("provinceName", city.getProvinceName());
        provinceInfo.put("provinceNameEn", city.getProvinceNameEn());
        provinceInfo.put("countryId", city.getCountryId());
        provinceInfo.put("countryName", city.getCountryName());
        provinceInfo.put("countryNameEn", city.getCountryNameEn());
        
        log.info("找到省份信息：{}", provinceInfo);
        return AjaxResult.success("查询成功", provinceInfo);
    }
    
    /**
     * 通过区县ID获取所属城市信息
     */
    private AjaxResult getCityByDistrict(String districtId, List<HotelCity> allCities) {
        log.info("通过区县ID获取城市信息，区县ID：{}", districtId);
        
        // 查找指定区县所属的城市信息
        Optional<HotelCity> targetDistrict = allCities.stream()
                .filter(city -> Objects.equals(city.getLocationId(), districtId))
                .findFirst();
        
        if (!targetDistrict.isPresent()) {
            log.warn("未找到指定的区县，区县ID：{}", districtId);
            return AjaxResult.error("未找到指定的区县");
        }
        
        HotelCity district = targetDistrict.get();
        
        // 构建城市信息
        Map<String, Object> cityInfo = new HashMap<>();
        cityInfo.put("cityId", district.getCityId());
        cityInfo.put("cityName", district.getCityName());
        cityInfo.put("cityNameEn", district.getCityNameEn());
        cityInfo.put("provinceId", district.getProvinceId());
        cityInfo.put("provinceName", district.getProvinceName());
        cityInfo.put("provinceNameEn", district.getProvinceNameEn());
        cityInfo.put("countryId", district.getCountryId());
        cityInfo.put("countryName", district.getCountryName());
        cityInfo.put("countryNameEn", district.getCountryNameEn());
        
        log.info("找到城市信息：{}", cityInfo);
        return AjaxResult.success("查询成功", cityInfo);
    }

    @Override
    @CacheEvict(value = "getCityDataByLevel", allEntries = true, cacheManager = "cacheManager")
    public void clearCache() {
        log.info("清除缓存...");
    }

    @Override
    @CacheEvict(value = "getParentInfo", allEntries = true, cacheManager = "cacheManager")
    public void clearCacheForParent() {
        log.info("清除向上查询缓存...");
    }

    @Override
    public AjaxResult<List<HotelCity>> getHotelCityByCondition(CityLevelQueryReq cityLevelQueryReq) {
        if (StringUtils.isEmpty(cityLevelQueryReq.getPlatformId()) || StringUtils.isEmpty(cityLevelQueryReq.getCityId())) {
            log.warn("参数为空，platformId：{}，cityId：{}", cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
            return AjaxResult.errorFor("参数为空");
        }
        HotelCityMapping hotelCityMapping = hotelCityMappingService.getHotelCityByCondition(cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
        if (Objects.isNull(hotelCityMapping)) {
            log.warn("未找到对应的城市映射关系，platformId：{}，cityId：{}", cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
            return AjaxResult.errorFor("未找到对应城市映射关系");
        }
        HotelCity hotelCitySearch = HotelCity.builder().reserve1(PlatEnum.PLAT_EL.getValue()).cityId(hotelCityMapping.getLocalId()).build();
        List<HotelCity> hotelCities = hotelCityService.selectHotelCityList(hotelCitySearch);
        return AjaxResult.successWith("查询成功",hotelCities);
    }

    /**
     * 通过条件获取所有区域结构
     * @param cityLevelQueryReq
     * @return
     */
    @Override
    public AjaxResult<GeoHierarchyNode> getAllRegionByCondition(CityLevelQueryReq cityLevelQueryReq) {
        try {
            if (StringUtils.isEmpty(cityLevelQueryReq.getPlatformId()) || StringUtils.isEmpty(cityLevelQueryReq.getCityId())) {
                log.warn("参数为空，platformId：{}，cityId：{}", cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
                return AjaxResult.errorFor("参数为空");
            }
            HotelCityMapping hotelCityMapping = hotelCityMappingService.getHotelCityByCondition(cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
            if (Objects.isNull(hotelCityMapping)) {
                log.warn("未找到对应的城市映射关系，platformId：{}，cityId：{}", cityLevelQueryReq.getPlatformId(), cityLevelQueryReq.getCityId());
                return AjaxResult.errorFor("未找到对应城市映射关系");
            }
            GeoStructureQueryReq geoStructureQueryReq = GeoStructureQueryReq.builder().id(hotelCityMapping.getLocalId()).idType("city").includeParents(Boolean.TRUE).includeFullChildren(Boolean.FALSE).build();
            return geoHierarchyService.getGeoStructureById(geoStructureQueryReq);
        } catch (Exception e) {
            log.error("获取地理位置层次结构异常，参数：{}，错误信息：{}", cityLevelQueryReq, e.getMessage(), e);
            return AjaxResult.errorFor("获取地理位置层次结构异常：" + e.getMessage());
        }
    }
}
