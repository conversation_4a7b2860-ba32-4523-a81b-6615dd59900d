package com.ltgj.ivw.service;

import java.util.List;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.HotelCityMapping;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 城市映射管理Service接口
 *
 * <AUTHOR>
 * @date 2024-03-28
 */
public interface IHotelCityMappingService
{
    /**
     * 查询城市映射管理
     *
     * @param id 城市映射管理主键
     * @return 城市映射管理
     */
    public HotelCityMapping selectHotelCityMappingById(Long id);

    /**
     * 查询城市映射管理列表
     *
     * @param hotelCityMapping 城市映射管理
     * @return 城市映射管理集合
     */
    public List<HotelCityMapping> selectHotelCityMappingList(HotelCityMapping hotelCityMapping);

    /**
     * 查询城市映射管理列表
     *
     * @param hotelCityMapping 城市映射管理
     * @return 城市映射管理集合
     */
    public List<HotelCityMapping> selectListLikeLocaltionName(HotelCityMapping hotelCityMapping);

    /**
     * 新增城市映射管理
     *
     * @param hotelCityMapping 城市映射管理
     * @return 结果
     */
    public int insertHotelCityMapping(HotelCityMapping hotelCityMapping);

    /**
     * 修改城市映射管理
     *
     * @param hotelCityMapping 城市映射管理
     * @return 结果
     */
    public int updateHotelCityMapping(HotelCityMapping hotelCityMapping);

    /**
     * 批量删除城市映射管理
     *
     * @param ids 需要删除的城市映射管理主键集合
     * @return 结果
     */
    public int deleteHotelCityMappingByIds(Long[] ids);

    /**
     * 删除城市映射管理信息
     *
     * @param id 城市映射管理主键
     * @return 结果
     */
    public int deleteHotelCityMappingById(Long id);

    List<List<HotelCityMapping>> groupByNum(List<HotelCityMapping> hotelCityMappings, int num);

    HotelCityMapping getHotelCityByCondition(String platformId, String cityId);
}
