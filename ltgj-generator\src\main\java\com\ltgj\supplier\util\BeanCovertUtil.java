package com.ltgj.supplier.util;

import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.es.domain.Result;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.dto.CityLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.HotelGnGeoMappingDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IGeoDataQueryService;
import com.ltgj.ivw.service.IHotelGnGeoMappingService;
import com.ltgj.ivw.service.IHotelInfoElongService;
import com.ltgj.ivw.vo.HotelGnGeoMappingVO;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.enums.HotelGnTypeEnum;
import com.ltgj.supplier.common.gn.manager.HotelGnGBManager;
import com.ltgj.supplier.common.gn.vo.req.AddAndSaveMappingGBReq;
import com.ltgj.supplier.common.gn.vo.resp.MappingGBResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 实体类转换
 * 
 * <AUTHOR>
 * @date 2024-06-28
 */
@Slf4j
@Component
public class BeanCovertUtil {

    @Autowired
    private IHotelInfoElongService hotelInfoElongService;

    @Autowired
    private HotelGnGBManager hotelGnGBManager;

    @Autowired
    private IHotelGnGeoMappingService hotelGnGeoMappingService;
    
    @Autowired
    private IGeoDataQueryService geoDataQueryService;

    public JdJdb platCovertToJdJdb(PlatEnum platEnum, HotelGnBase hotelInfo, String targetHotelId) {
        JdJdb jdJdb = new JdJdb();
        if (ObjectUtils.isEmpty(hotelInfo)){
            return jdJdb;
        }
        //国家，省，市转换
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(hotelInfo.getCityId())){
            CityLevelQueryReq cityLevelQueryReq = new CityLevelQueryReq();
            cityLevelQueryReq.setCityId(hotelInfo.getCityId());
            cityLevelQueryReq.setPlatformId(platEnum.getValue());
            // 使用新的独立事务地理查询服务，避免事务传播导致的数据源切换失效
            AjaxResult<GeoHierarchyNode> geoHierarchyNodeRes = geoDataQueryService.getAllRegionByConditionNewTx(cityLevelQueryReq);
            log.info("获取地理位置层次结构结果：{}", JSONObject.toJSONString(geoHierarchyNodeRes));
            if (!ObjectUtils.isEmpty(geoHierarchyNodeRes) && !ObjectUtils.isEmpty(geoHierarchyNodeRes.getData())){
                GeoHierarchyNode geoHierarchyNode = geoHierarchyNodeRes.getData();

                jdJdb.setCountryId(geoHierarchyNode.getId());
                jdJdb.setCountryName(geoHierarchyNode.getName());

                List<GeoHierarchyNode> provinceChildren = geoHierarchyNode.getChildren();
                if (!ObjectUtils.isEmpty(provinceChildren)) {
                    GeoHierarchyNode provinceGeoHierarchyNode = provinceChildren.get(0);

                    jdJdb.setProvinceId(provinceGeoHierarchyNode.getId());
                    jdJdb.setProvinceName(provinceGeoHierarchyNode.getName());

                    List<GeoHierarchyNode> cityChildren = provinceGeoHierarchyNode.getChildren();
                    if (!ObjectUtils.isEmpty(cityChildren)) {
                        GeoHierarchyNode cityGeoHierarchyNode = cityChildren.get(0);

                        jdJdb.setCityId(cityGeoHierarchyNode.getId());
                        jdJdb.setCityName(cityGeoHierarchyNode.getName());
                    }
                }
            }
        }

        hotelInfo.setBusinessDistrictId(null);
        hotelInfo.setBusinessDistrictName(null);

        //集团/品牌
        if (!ObjectUtils.isEmpty(hotelInfo.getGroupId())){
            AddAndSaveMappingGBReq addAndSaveMappingGBReq = new AddAndSaveMappingGBReq();
            addAndSaveMappingGBReq.setPlatform(platEnum);
            addAndSaveMappingGBReq.setGroupId(hotelInfo.getGroupId());
            addAndSaveMappingGBReq.setGroupName(hotelInfo.getGroupName());
            addAndSaveMappingGBReq.setBrandId(hotelInfo.getBrandId());
            addAndSaveMappingGBReq.setBrandName(hotelInfo.getBrandName());
            Result<MappingGBResp> mappingGBRespResult = hotelGnGBManager.saveBaseGBInfo(addAndSaveMappingGBReq);
            log.info("保存集团品牌映射结果：{}", JSONObject.toJSONString(mappingGBRespResult));
            if (Result.isSuccess(mappingGBRespResult)){
                MappingGBResp data = mappingGBRespResult.getData();
                if (data != null){
                    jdJdb.setJtid(data.getGroupId());
                    jdJdb.setJtmc(data.getGroupName());
                    jdJdb.setBrandId(data.getBrandId());
                    jdJdb.setBrandName(data.getBrandName());
                }
            }
        }

        //商圈
        if (StringUtils.isNotBlank(hotelInfo.getCityId()) && StringUtils.isNotBlank(hotelInfo.getBusinessDistrictName())){
            HotelGnGeoMappingDTO hotelGnGeoMappingDTO = HotelGnGeoMappingDTO.builder()
                    .geoId(hotelInfo.getBusinessDistrictId())
                    .geoName(hotelInfo.getBusinessDistrictName())
                    .cityId(hotelInfo.getCityId())
                    .platformId(platEnum.getValue())
                    .build();
            HotelGnGeoMappingVO hotelGnGeoMappingVO = hotelGnGeoMappingService.selectGnGeoMapping(hotelGnGeoMappingDTO);
            log.info("查询地理位置映射结果：{}", JSONObject.toJSONString(hotelGnGeoMappingVO));
            if (hotelGnGeoMappingVO != null){
                jdJdb.setBusinessZone(hotelGnGeoMappingVO.getBaseGeoId());
                jdJdb.setBusinessZoneName(hotelGnGeoMappingVO.getBaseGeoName());
            } else {
                jdJdb.setBusinessZone(null);
                jdJdb.setBusinessZoneName(null);
                log.info("没有查询到地理商圈位置映射结果");
            }
        }

        jdJdb.setId(targetHotelId);
        jdJdb.setInterfacePlat(platEnum.getValue());
        jdJdb.setJdmc(hotelInfo.getName());
        jdJdb.setJdmcEn(hotelInfo.getNameEn());
        jdJdb.setJdlx(String.valueOf(HotelGnTypeEnum.getByPlatTypeId(hotelInfo.getTypeId(), platEnum).getId()));
        jdJdb.setJddz(hotelInfo.getAddress());
        jdJdb.setJddzEn(hotelInfo.getAddressEn());
        jdJdb.setJddh(hotelInfo.getPhone());
        jdJdb.setImgUrl(hotelInfo.getImage());
        jdJdb.setStatus(0);
        jdJdb.setLonGoogle(hotelInfo.getLonGoogle());
        jdJdb.setLatGoogle(hotelInfo.getLatGoogle());
        jdJdb.setLonBaidu(hotelInfo.getLonBaidu());
        jdJdb.setLatBaidu(hotelInfo.getLatBaidu());
        jdJdb.setLonGaode(hotelInfo.getLonGaode());
        jdJdb.setLatGaode(hotelInfo.getLatGaode());
//        jdJdb.setBrandId(hotelInfo.getBrandId());
//        jdJdb.setBrandName(hotelInfo.getBrandName());
//        jdJdb.setDistrict(hotelInfo.getAreaId());
//        jdJdb.setDistrictName(hotelInfo.getAreaName());
//        jdJdb.setBusinessZone(hotelInfo.getBusinessDistrictId());
//        jdJdb.setBusinessZoneName(hotelInfo.getBusinessDistrictName());
        jdJdb.setJdxj(hotelInfo.getStar());
        jdJdb.setScore(ObjectUtils.isEmpty(hotelInfo.getScore())?BigDecimal.ZERO:new BigDecimal(hotelInfo.getScore()));
//        jdJdb.setJtid(hotelInfo.getGroupId());
//        jdJdb.setJtmc(hotelInfo.getGroupName());
        jdJdb.setKysj(hotelInfo.getOpenDate());
        jdJdb.setZhzxsj(hotelInfo.getDecorationDate());
        jdJdb.setRank(0L);
        jdJdb.setCreatedate(new Date());
        jdJdb.setMender(null);
        jdJdb.setSavedate(new Date());
        jdJdb.setMinPrice(BigDecimal.ZERO);
        jdJdb.setReserve1(hotelInfo.getSynopsis());
        jdJdb.setReserve2(null);
        jdJdb.setNoticeInfo(null);
        jdJdb.setPolicyInfo(hotelInfo.getPolicyInfo());
        jdJdb.setFacilitiesInfo(hotelInfo.getFacilitiesInfo());
        jdJdb.setReserve6(null);
        jdJdb.setReserve7(null);
        jdJdb.setReserve8(null);
        jdJdb.setReserve9(null);
        jdJdb.setReserve0("0");
        jdJdb.setReserve3(null);
        jdJdb.setReserve4(null);
        jdJdb.setReserve5(null);
        jdJdb.setOriginId(null);
        jdJdb.setRecommendLevel(0);
        jdJdb.setSparkle(hotelInfo.getSparkle());
        jdJdb.setAiDocumentId(null);
        return jdJdb;
    }
}