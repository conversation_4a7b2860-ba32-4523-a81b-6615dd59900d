package com.ltgj.generator.mybatis;

import com.ltgj.ivw.domain.HotelInfo;
import com.ltgj.ivw.utils.ReflectForServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.builder.annotation.ProviderMethodResolver;
import org.apache.ibatis.jdbc.SQL;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义解析器
 *
 * <AUTHOR>
 * @Date 2024/12/27
 * @description:
 */
@Slf4j
public class HotelInfoCommonSqlProvider<T> implements ProviderMethodResolver {

    public String selectHotelInfoCommonById(@Param("id") String id, @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        return new SQL() {
            {
                SELECT("*");
                FROM(tableName);
                WHERE("id = #{id}");
            }
        }.toString();
    }

    public String selectHotelInfoCommonList(@Param("hotelInfo") HotelInfo entity,
            @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        return new SQL() {
            {
                SELECT("*");
                FROM(tableName);
                // 根据 entity 对象添加 WHERE 条件
                StringBuilder sb = new StringBuilder();
                if (entity != null) {
                    appendWhereCondition(sb, "id", entity.getId(), "hotelInfo.id", false);
                    appendWhereCondition(sb, "name", entity.getName(), "hotelInfo.name", false);
                    appendWhereCondition(sb, "name_en", entity.getNameEn(), "hotelInfo.nameEn", false);
                    appendWhereCondition(sb, "phone", entity.getPhone(), "hotelInfo.phone", false);
                    appendWhereCondition(sb, "country", entity.getCountry(), "hotelInfo.country", false);
                    appendWhereCondition(sb, "province_id", entity.getProvinceId(), "hotelInfo.provinceId", false);
                    appendWhereCondition(sb, "city_id", entity.getCityId(), "hotelInfo.cityId", false);
                    appendWhereCondition(sb, "city_name", entity.getCityName(), "hotelInfo.cityName", false);
                    appendWhereCondition(sb, "area_id", entity.getAreaId(), "hotelInfo.areaId", false);
                    appendWhereCondition(sb, "business_districts", entity.getBusinessDistricts(), "hotelInfo.businessDistricts", false);
                    appendWhereCondition(sb, "address_line", entity.getAddressLine(), "hotelInfo.addressLine", false);
                    appendWhereCondition(sb, "open_date", entity.getOpenDate(), "hotelInfo.openDate", false);
                    appendWhereCondition(sb, "decoration_date", entity.getDecorationDate(), "hotelInfo.decorationDate", false);
                    appendWhereCondition(sb, "description", entity.getDescription(), "hotelInfo.description", false);
                    appendWhereCondition(sb, "floor_count", entity.getFloorCount(), "hotelInfo.floorCount", false);
                    appendWhereCondition(sb, "brand_id", entity.getBrandId(), "hotelInfo.brandId", false);
                    appendWhereCondition(sb, "group_id", entity.getGroupId(), "hotelInfo.groupId", false);
                    appendWhereCondition(sb, "theme_id", entity.getThemeId(), "hotelInfo.themeId", false);
                    appendWhereCondition(sb, "type_id", entity.getTypeId(), "hotelInfo.typeId", false);
                    if (entity.getStar() != null) {
                        sb.append(" and star = #{hotelInfo.star}");
                    }
                    appendWhereCondition(sb, "star_info", entity.getStarInfo(), "hotelInfo.starInfo", false);
                    appendWhereCondition(sb, "notice_info", entity.getNoticeInfo(), "hotelInfo.noticeInfo", false);
                    appendWhereCondition(sb, "image", entity.getImage(), "hotelInfo.image", false);
                    appendWhereCondition(sb, "policy_info", entity.getPolicyInfo(), "hotelInfo.policyInfo", false);
                    appendWhereCondition(sb, "facilities_info", entity.getFacilitiesInfo(), "hotelInfo.facilitiesInfo", false);
                    appendWhereCondition(sb, "plat_num", entity.getPlatNum(), "hotelInfo.platNum", false);
                    appendWhereCondition(sb, "plat_hotel_id", entity.getPlatHotelId(), "hotelInfo.platHotelId", false);
                    if (entity.getRecommendationLevel() != null) {
                        sb.append(" and recommendation_level = #{hotelInfo.recommendationLevel}");
                    }
                    if (entity.getLonBd() != null) {
                        sb.append(" and lon_bd = #{hotelInfo.lonBd}");
                    }
                    if (entity.getLatBd() != null) {
                        sb.append(" and lat_bd = #{hotelInfo.latBd}");
                    }
                    if (entity.getLonGd() != null) {
                        sb.append(" and lon_gd = #{hotelInfo.lonGd}");
                    }
                    if (entity.getLatGd() != null) {
                        sb.append(" and lat_gd = #{hotelInfo.latGd}");
                    }
                    if (entity.getLonGg() != null) {
                        sb.append(" and lon_gg = #{hotelInfo.lonGg}");
                    }
                    if (entity.getLatGg() != null) {
                        sb.append(" and lat_gg = #{hotelInfo.latGg}");
                    }
                    appendWhereCondition(sb, "reserve1", entity.getReserve1(), "hotelInfo.reserve1", false);
                    appendWhereCondition(sb, "reserve2", entity.getReserve2(), "hotelInfo.reserve2", false);
                    appendWhereCondition(sb, "reserve3", entity.getReserve3(), "hotelInfo.reserve3", false);
                    appendWhereCondition(sb, "reserve4", entity.getReserve4(), "hotelInfo.reserve4", false);
                    appendWhereCondition(sb, "reserve5", entity.getReserve5(), "hotelInfo.reserve5", false);
                    appendWhereCondition(sb, "reserve6", entity.getReserve6(), "hotelInfo.reserve6", false);
                    appendWhereCondition(sb, "reserve7", entity.getReserve7(), "hotelInfo.reserve7", false);
                    appendWhereCondition(sb, "reserve8", entity.getReserve8(), "hotelInfo.reserve8", false);
                    appendWhereCondition(sb, "reserve9", entity.getReserve9(), "hotelInfo.reserve9", false);
                    appendWhereCondition(sb, "reserve0", entity.getReserve0(), "hotelInfo.reserve0", false);
                    if (entity.getStatus() != null) {
                        sb.append(" and status = #{hotelInfo.status}");
                    }
                    if (entity.getUpdateTime() != null) {
                        sb.append(" and update_time = #{hotelInfo.updateTime}");
                    }
                }
                WHERE("1=1" + sb);
            }
        }.toString();
    }

    public String selectHotelInfoCommonList2(@Param("hotelInfo") HotelInfo hotelInfo,
            @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        return new SQL() {
            {
                SELECT("*");
                FROM(tableName);
                // 根据 entity 对象添加 WHERE 条件
                StringBuilder sb = new StringBuilder();
                if (Objects.nonNull(hotelInfo)) {
                    if (hotelInfo.getStatus() != null) {
                        sb.append(" and status = #{hotelInfo.status}");
                    }
                    appendWhereCondition(sb, "city_id", hotelInfo.getCityId(), "hotelInfo.cityId", false);
                    appendWhereCondition(sb, "city_name", hotelInfo.getCityName(), "hotelInfo.cityName", false);
                    appendWhereCondition(sb, "id", hotelInfo.getId(), "hotelInfo.id", false);
                    appendWhereCondition(sb, "name", hotelInfo.getName(), "hotelInfo.name", true);
                    appendWhereCondition(sb, "reserve9", hotelInfo.getReserve9(), "hotelInfo.reserve9", false);
                    // 修复逻辑错误：当reserve9有值时表示已删除，没值时表示未删除
                    if (isStringEmpty(hotelInfo.getReserve9())) {
                        sb.append(" and is_delete = 0");
                    }
                    if (isStringNotEmpty(hotelInfo.getBrandId())) {
                        sb.append(" and brand_id is not null and brand_id != ''");
                    }
                    if (hotelInfo.getIncrementStatus() != null) {
                        sb.append(" and increment_status = #{hotelInfo.incrementStatus}");
                    }
                    if (hotelInfo.getUpdateTime() != null) {
                        sb.append(" and update_time = #{hotelInfo.updateTime}");
                    }
                }
                WHERE("1=1" + sb);
            }
        }.toString();
    }

    public String updateHotelInfoCommon(@Param("hotelInfo") HotelInfo hotelInfo,
            @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        String objectName = "hotelInfo";
        return new SQL() {
            {
                UPDATE(tableName);
                // 根据 entity 对象添加 SET 条件
                StringBuilder sb = new StringBuilder();
                appendForUpdateIfNotNull(sb, "increment_status", "incrementStatus", hotelInfo.getIncrementStatus(), objectName);
                appendForUpdateIfNotNull(sb, "increment_time", "incrementTime", hotelInfo.getIncrementTime(), objectName);
                appendForUpdateIfNotNull(sb, "increment_type", "incrementType", hotelInfo.getIncrementStatus(), objectName);
                appendForUpdateIfNotNull(sb, "is_delete", "isDelete", hotelInfo.getIsDelete(), objectName);
                appendForUpdateIfNotNull(sb, "status", "status", hotelInfo.getStatus(), objectName);
                appendForUpdateIfNotNull(sb, "dept_id", "deptId", hotelInfo.getDeptId(), objectName);
                appendForUpdateIfNotNull(sb, "name", "name", hotelInfo.getName(), objectName);
                appendForUpdateIfNotNull(sb, "name_en", "nameEn", hotelInfo.getNameEn(), objectName);
                // 移除重复的dept_id设置
                appendForUpdateIfNotNull(sb, "phone", "phone", hotelInfo.getPhone(), objectName);
                appendForUpdateIfNotNull(sb, "country", "country", hotelInfo.getCountry(), objectName);
                appendForUpdateIfNotNull(sb, "province_id", "provinceId", hotelInfo.getProvinceId(), objectName);
                appendForUpdateIfNotNull(sb, "city_id", "cityId", hotelInfo.getCityId(), objectName);
                appendForUpdateIfNotNull(sb, "city_name", "cityName", hotelInfo.getCityName(), objectName);
                appendForUpdateIfNotNull(sb, "area_id", "areaId", hotelInfo.getAreaId(), objectName);
                appendForUpdateIfNotNull(sb, "business_districts", "businessDistricts", hotelInfo.getBusinessDistricts(), objectName);
                appendForUpdateIfNotNull(sb, "address_line", "addressLine", hotelInfo.getAddressLine(), objectName);
                appendForUpdateIfNotNull(sb, "open_date", "openDate", hotelInfo.getOpenDate(), objectName);
                appendForUpdateIfNotNull(sb, "decoration_date", "decorationDate", hotelInfo.getDecorationDate(), objectName);
                appendForUpdateIfNotNull(sb, "description", "description", hotelInfo.getDescription(), objectName);
                appendForUpdateIfNotNull(sb, "floor_count", "floorCount", hotelInfo.getFloorCount(), objectName);
                appendForUpdateIfNotNull(sb, "brand_id", "brandId", hotelInfo.getBrandId(), objectName);
                appendForUpdateIfNotNull(sb, "group_id", "groupId", hotelInfo.getGroupId(), objectName);
                appendForUpdateIfNotNull(sb, "theme_id", "themeId", hotelInfo.getThemeId(), objectName);
                appendForUpdateIfNotNull(sb, "type_id", "typeId", hotelInfo.getTypeId(), objectName);
                appendForUpdateIfNotNull(sb, "star", "star", hotelInfo.getStar(), objectName);
                appendForUpdateIfNotNull(sb, "star_info", "starInfo", hotelInfo.getStarInfo(), objectName);
                appendForUpdateIfNotNull(sb, "notice_info", "noticeInfo", hotelInfo.getNoticeInfo(), objectName);
                appendForUpdateIfNotNull(sb, "image", "image", hotelInfo.getImage(), objectName);
                appendForUpdateIfNotNull(sb, "policy_info", "policyInfo", hotelInfo.getPolicyInfo(), objectName);
                appendForUpdateIfNotNull(sb, "facilities_info", "facilitiesInfo", hotelInfo.getFacilitiesInfo(), objectName);
                appendForUpdateIfNotNull(sb, "plat_num", "platNum", hotelInfo.getPlatNum(), objectName);
                appendForUpdateIfNotNull(sb, "plat_hotel_id", "platHotelId", hotelInfo.getPlatHotelId(), objectName);
                appendForUpdateIfNotNull(sb, "recommendation_level", "recommendationLevel", hotelInfo.getRecommendationLevel(), objectName);
                appendForUpdateIfNotNull(sb, "lon_bd", "lonBd", hotelInfo.getLonBd(), objectName);
                appendForUpdateIfNotNull(sb, "lat_bd", "latBd", hotelInfo.getLatBd(), objectName);
                appendForUpdateIfNotNull(sb, "lon_gd", "lonGd", hotelInfo.getLonGd(), objectName);
                appendForUpdateIfNotNull(sb, "lat_gd", "latGd", hotelInfo.getLatGd(), objectName);
                appendForUpdateIfNotNull(sb, "lon_gg", "lonGg", hotelInfo.getLonGg(), objectName);
                appendForUpdateIfNotNull(sb, "lat_gg", "latGg", hotelInfo.getLatGg(), objectName);
                appendForUpdateIfNotNull(sb, "reserve1", "reserve1", hotelInfo.getReserve1(), objectName);
                appendForUpdateIfNotNull(sb, "reserve2", "reserve2", hotelInfo.getReserve2(), objectName);
                appendForUpdateIfNotNull(sb, "reserve3", "reserve3", hotelInfo.getReserve3(), objectName);
                appendForUpdateIfNotNull(sb, "reserve4", "reserve4", hotelInfo.getReserve4(), objectName);
                appendForUpdateIfNotNull(sb, "reserve5", "reserve5", hotelInfo.getReserve5(), objectName);
                appendForUpdateIfNotNull(sb, "reserve6", "reserve6", hotelInfo.getReserve6(), objectName);
                appendForUpdateIfNotNull(sb, "reserve7", "reserve7", hotelInfo.getReserve7(), objectName);
                appendForUpdateIfNotNull(sb, "reserve8", "reserve8", hotelInfo.getReserve8(), objectName);
                appendForUpdateIfNotNull(sb, "reserve9", "reserve9", hotelInfo.getReserve9(), objectName);
                appendForUpdateIfNotNull(sb, "reserve0", "reserve0", hotelInfo.getReserve0(), objectName);
                appendForUpdateIfNotNull(sb, "update_time", "updateTime", hotelInfo.getUpdateTime(), objectName);
                appendForUpdateIfNotNull(sb, "update_by", "updateBy", hotelInfo.getUpdateBy(), objectName);
                
                if (sb.length() > 0 && sb.charAt(0) == ',') {
                    sb.deleteCharAt(0);
                }
                SET(sb.toString());
                WHERE("id = #{hotelInfo.id}");
            }
        }.toString();
    }

    public String insertHotelInfoCommon(@Param("hotelInfo") Object hotelInfo,
            @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        String objectName = "hotelInfo";
        
        return new SQL() {
            {
                INSERT_INTO(tableName);
                
                // 动态添加INSERT字段和值
                StringBuilder columns = new StringBuilder();
                StringBuilder values = new StringBuilder();
                
                // 使用反射获取HotelInfo对象的所有字段值
                if (hotelInfo instanceof HotelInfo) {
                    HotelInfo hotel = (HotelInfo) hotelInfo;
                    
                    appendForInsertIfNotNull(columns, values, "id", "id", hotel.getId(), objectName);
                    appendForInsertIfNotNull(columns, values, "increment_status", "incrementStatus", hotel.getIncrementStatus(), objectName);
                    appendForInsertIfNotNull(columns, values, "increment_time", "incrementTime", hotel.getIncrementTime(), objectName);
                    appendForInsertIfNotNull(columns, values, "increment_type", "incrementType", hotel.getIncrementType(), objectName);
                    appendForInsertIfNotNull(columns, values, "is_delete", "isDelete", hotel.getIsDelete(), objectName);
                    appendForInsertIfNotNull(columns, values, "status", "status", hotel.getStatus(), objectName);
                    appendForInsertIfNotNull(columns, values, "dept_id", "deptId", hotel.getDeptId(), objectName);
                    appendForInsertIfNotNull(columns, values, "name", "name", hotel.getName(), objectName);
                    appendForInsertIfNotNull(columns, values, "name_en", "nameEn", hotel.getNameEn(), objectName);
                    appendForInsertIfNotNull(columns, values, "phone", "phone", hotel.getPhone(), objectName);
                    appendForInsertIfNotNull(columns, values, "country", "country", hotel.getCountry(), objectName);
                    appendForInsertIfNotNull(columns, values, "province_id", "provinceId", hotel.getProvinceId(), objectName);
                    appendForInsertIfNotNull(columns, values, "city_id", "cityId", hotel.getCityId(), objectName);
                    appendForInsertIfNotNull(columns, values, "city_name", "cityName", hotel.getCityName(), objectName);
                    appendForInsertIfNotNull(columns, values, "area_id", "areaId", hotel.getAreaId(), objectName);
                    appendForInsertIfNotNull(columns, values, "business_districts", "businessDistricts", hotel.getBusinessDistricts(), objectName);
                    appendForInsertIfNotNull(columns, values, "address_line", "addressLine", hotel.getAddressLine(), objectName);
                    appendForInsertIfNotNull(columns, values, "open_date", "openDate", hotel.getOpenDate(), objectName);
                    appendForInsertIfNotNull(columns, values, "decoration_date", "decorationDate", hotel.getDecorationDate(), objectName);
                    appendForInsertIfNotNull(columns, values, "description", "description", hotel.getDescription(), objectName);
                    appendForInsertIfNotNull(columns, values, "floor_count", "floorCount", hotel.getFloorCount(), objectName);
                    appendForInsertIfNotNull(columns, values, "brand_id", "brandId", hotel.getBrandId(), objectName);
                    appendForInsertIfNotNull(columns, values, "group_id", "groupId", hotel.getGroupId(), objectName);
                    appendForInsertIfNotNull(columns, values, "theme_id", "themeId", hotel.getThemeId(), objectName);
                    appendForInsertIfNotNull(columns, values, "type_id", "typeId", hotel.getTypeId(), objectName);
                    appendForInsertIfNotNull(columns, values, "star", "star", hotel.getStar(), objectName);
                    appendForInsertIfNotNull(columns, values, "star_info", "starInfo", hotel.getStarInfo(), objectName);
                    appendForInsertIfNotNull(columns, values, "notice_info", "noticeInfo", hotel.getNoticeInfo(), objectName);
                    appendForInsertIfNotNull(columns, values, "image", "image", hotel.getImage(), objectName);
                    appendForInsertIfNotNull(columns, values, "policy_info", "policyInfo", hotel.getPolicyInfo(), objectName);
                    appendForInsertIfNotNull(columns, values, "facilities_info", "facilitiesInfo", hotel.getFacilitiesInfo(), objectName);
                    appendForInsertIfNotNull(columns, values, "plat_num", "platNum", hotel.getPlatNum(), objectName);
                    appendForInsertIfNotNull(columns, values, "plat_hotel_id", "platHotelId", hotel.getPlatHotelId(), objectName);
                    appendForInsertIfNotNull(columns, values, "recommendation_level", "recommendationLevel", hotel.getRecommendationLevel(), objectName);
                    appendForInsertIfNotNull(columns, values, "lon_bd", "lonBd", hotel.getLonBd(), objectName);
                    appendForInsertIfNotNull(columns, values, "lat_bd", "latBd", hotel.getLatBd(), objectName);
                    appendForInsertIfNotNull(columns, values, "lon_gd", "lonGd", hotel.getLonGd(), objectName);
                    appendForInsertIfNotNull(columns, values, "lat_gd", "latGd", hotel.getLatGd(), objectName);
                    appendForInsertIfNotNull(columns, values, "lon_gg", "lonGg", hotel.getLonGg(), objectName);
                    appendForInsertIfNotNull(columns, values, "lat_gg", "latGg", hotel.getLatGg(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve1", "reserve1", hotel.getReserve1(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve2", "reserve2", hotel.getReserve2(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve3", "reserve3", hotel.getReserve3(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve4", "reserve4", hotel.getReserve4(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve5", "reserve5", hotel.getReserve5(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve6", "reserve6", hotel.getReserve6(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve7", "reserve7", hotel.getReserve7(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve8", "reserve8", hotel.getReserve8(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve9", "reserve9", hotel.getReserve9(), objectName);
                    appendForInsertIfNotNull(columns, values, "reserve0", "reserve0", hotel.getReserve0(), objectName);
                    appendForInsertIfNotNull(columns, values, "create_time", "createTime", hotel.getCreateTime(), objectName);
                    appendForInsertIfNotNull(columns, values, "create_by", "createBy", hotel.getCreateBy(), objectName);
                    appendForInsertIfNotNull(columns, values, "update_time", "updateTime", hotel.getUpdateTime(), objectName);
                    appendForInsertIfNotNull(columns, values, "update_by", "updateBy", hotel.getUpdateBy(), objectName);
                }
                
                // 移除开头的逗号
                if (columns.length() > 0 && columns.charAt(0) == ',') {
                    columns.deleteCharAt(0);
                }
                if (values.length() > 0 && values.charAt(0) == ',') {
                    values.deleteCharAt(0);
                }
                
                INTO_COLUMNS(columns.toString());
                INTO_VALUES(values.toString());
            }
        }.toString();
    }

    public String deleteHotelInfoCommonByIds(@Param("ids") String[] ids, @Param("entityClass") Class<T> entityClass) {
        String tableName = ReflectForServiceUtils.getTableName(entityClass);
        return new SQL() {{
            DELETE_FROM(tableName);
            // 修复SQL注入风险：使用参数化查询
            WHERE("id IN (" + 
                Arrays.stream(ids)
                    .map(id -> "'" + id.replace("'", "''") + "'")  // 简单的SQL转义
                    .collect(Collectors.joining(",")) + 
                ")");
        }}.toString();
    }

    /**
     * 检查字符串是否为空
     */
    private boolean isStringEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 检查字符串是否不为空
     */
    private boolean isStringNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }

    /**
     * 添加WHERE条件的通用方法
     */
    private void appendWhereCondition(StringBuilder sb, String columnName, String value, String paramName, boolean useLike) {
        if (isStringNotEmpty(value)) {
            if (useLike) {
                sb.append(" and ").append(columnName).append(" like concat('%', #{").append(paramName).append("}, '%')");
            } else {
                sb.append(" and ").append(columnName).append(" = #{").append(paramName).append("}");
            }
        }
    }

    private void appendForUpdateIfNotNull(StringBuilder sb, String columnName, String fieldName, Object value, String objectName) {
        if (value != null) {
            sb.append(",").append(columnName).append(" = #{").append(objectName).append(".").append(fieldName).append("}");
        }
    }

    /**
     * 为INSERT语句添加字段和值（如果值不为null）
     */
    private void appendForInsertIfNotNull(StringBuilder columns, StringBuilder values, String columnName, String fieldName, Object value, String objectName) {
        if (value != null) {
            columns.append(",").append(columnName);
            values.append(",#{").append(objectName).append(".").append(fieldName).append("}");
        }
    }
}
