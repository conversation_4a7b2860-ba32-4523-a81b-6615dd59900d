package com.ltgj.ivw.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ltgj.common.annotation.Excel;
import com.ltgj.common.core.domain.BaseEntity;

/**
 * 科坦和艺龙城市映射关系对象 ketan_elong_city_mapping
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class KetanElongCityMapping extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 科坦省份编码 */
    @Excel(name = "科坦省份编码")
    private String ketanProvinceCode;

    /** 省份名称 */
    @Excel(name = "省份名称")
    private String provinceName;

    /** 科坦城市编码 */
    @Excel(name = "科坦城市编码")
    private String ketanCityCode;

    /** 科坦城市名称 */
    @Excel(name = "科坦城市名称")
    private String ketanCityName;

    /** 艺龙省份编码 */
    @Excel(name = "艺龙省份编码")
    private String elongProvinceCode;

    /** 艺龙城市编码 */
    @Excel(name = "艺龙城市编码")
    private String elongCityCode;

    /** 状态（0正常 1删除） */
    private String status;

    /** 备注 */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKetanProvinceCode() {
        return ketanProvinceCode;
    }

    public void setKetanProvinceCode(String ketanProvinceCode) {
        this.ketanProvinceCode = ketanProvinceCode;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getKetanCityCode() {
        return ketanCityCode;
    }

    public void setKetanCityCode(String ketanCityCode) {
        this.ketanCityCode = ketanCityCode;
    }

    public String getKetanCityName() {
        return ketanCityName;
    }

    public void setKetanCityName(String ketanCityName) {
        this.ketanCityName = ketanCityName;
    }

    public String getElongProvinceCode() {
        return elongProvinceCode;
    }

    public void setElongProvinceCode(String elongProvinceCode) {
        this.elongProvinceCode = elongProvinceCode;
    }

    public String getElongCityCode() {
        return elongCityCode;
    }

    public void setElongCityCode(String elongCityCode) {
        this.elongCityCode = elongCityCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "KetanElongCityMapping [id=" + id + ", ketanProvinceCode=" + ketanProvinceCode + ", provinceName=" + provinceName
                + ", ketanCityCode=" + ketanCityCode + ", ketanCityName=" + ketanCityName + ", elongProvinceCode="
                + elongProvinceCode + ", elongCityCode=" + elongCityCode + ", status=" + status + "]";
    }
} 