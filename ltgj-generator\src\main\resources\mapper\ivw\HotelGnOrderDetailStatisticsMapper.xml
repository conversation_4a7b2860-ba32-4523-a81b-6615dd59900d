<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.HotelGnOrderDetailStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.HotelGnOrderDetailStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/11 16:16.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="interface_plat" jdbcType="BIGINT" property="interfacePlat" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="hotel_name" jdbcType="VARCHAR" property="hotelName" />
    <result column="subtype" jdbcType="VARCHAR" property="subtype" />
    <result column="local_id" jdbcType="VARCHAR" property="localId" />
    <result column="plat_id" jdbcType="VARCHAR" property="platId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="is_failure" jdbcType="TINYINT" property="isFailure" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="original_request" jdbcType="OTHER" property="originalRequest" />
    <result column="original_response" jdbcType="OTHER" property="originalResponse" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/11 16:16.
    -->
    id, type, interface_plat, interface_name, hotel_name, subtype, local_id, plat_id, reason, is_failure,
    create_time, original_request, original_response
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/11 16:16.
    -->
    select
    <include refid="Base_Column_List" />
    from hotel_gn_order_detail_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" resultType="com.ltgj.ivw.response.HotelGnOrderDetailStatisticsRes"
          parameterType="com.ltgj.ivw.request.hotelGnStatistics.GnOrderDetailStatisticsRequest">
    select
        <include refid="Base_Column_List" />
    from hotel_gn_order_detail_statistics
    where 1=1
    <if test="isFailure != null">
       and is_failure = #{isFailure,jdbcType=TINYINT}
    </if>
    <if test="interfacePlat != null">
        and interface_plat = #{interfacePlat,jdbcType=BIGINT}
    </if>
    order by create_time desc
  </select>
  <select id="listCount" resultType="java.lang.Integer"
          parameterType="com.ltgj.ivw.request.hotelGnStatistics.GnOrderDetailStatisticsRequest">
    select count(0)
    from hotel_gn_order_detail_statistics
    where 1=1
    <if test="isFailure != null">
      and is_failure = #{isFailure,jdbcType=TINYINT}
    </if>
    <if test="interfacePlat != null">
      and interface_plat = #{interfacePlat,jdbcType=BIGINT}
    </if>
  </select>
  <select id="selectCount" resultType="java.lang.Integer"
          parameterType="com.ltgj.ivw.request.hotelGnStatistics.GnOrderDetailStatisticsRequest">
    select count(1) from hotel_gn_order_detail_statistics
    where interface_plat = #{interfacePlat,jdbcType=BIGINT}
    <if test="isFailure != null">
      and is_failure = #{isFailure,jdbcType=TINYINT}
    </if>
    and create_time <![CDATA[>=]]> CURDATE() + INTERVAL -1 DAY
    and create_time <![CDATA[<]]> CURDATE()
  </select>
  <select id="selectCountOrderDetail" resultType="java.lang.Integer">
    select count(1) from hotel_gn_order_detail_statistics
    where create_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP} AND create_time <![CDATA[<]]> #{endTime,jdbcType=TIMESTAMP}
  </select>
  <select id="selectByPlatIdsAndInterfacePlat"
          resultType="com.ltgj.ivw.response.HotelGnOrderDetailStatisticsRes">
    select interface_plat,local_id,plat_id,jd_name from zh_jd_jdb_mapping
    where interface_plat =  #{interfacePlat}
    and plat_id in
    <foreach item="item" collection="platId" separator="," open="(" close=")" index="">
        #{item}
    </foreach>
    group by interface_plat,plat_id
  </select>
  <select id="selectByLocalIdsAndInterfacePlat"
          resultType="com.ltgj.ivw.response.HotelGnOrderDetailStatisticsRes">
    select interface_plat,local_id,plat_id,jd_name from zh_jd_jdb_mapping
    where interface_plat =  #{interfacePlat}
    and local_id in
    <foreach item="item" collection="platId" separator="," open="(" close=")" index="">
        #{item}
    </foreach>
    group by interface_plat,local_id
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/11 16:16.
    -->
    delete from hotel_gn_order_detail_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.ltgj.ivw.domain.HotelGnOrderDetailStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/11 16:16.
    -->
    insert into hotel_gn_order_detail_statistics (id, type, interface_plat,
      hotel_name, subtype, local_id,
      plat_id, reason, is_failure,
      create_time, original_request, original_response
      )
    values (#{id,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, #{interfacePlat,jdbcType=BIGINT},
      #{hotelName,jdbcType=VARCHAR}, #{subtype,jdbcType=VARCHAR}, #{localId,jdbcType=VARCHAR},
      #{platId,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{isFailure,jdbcType=TINYINT},
      #{createTime,jdbcType=TIMESTAMP}, #{originalRequest,jdbcType=OTHER}, #{originalResponse,jdbcType=OTHER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ltgj.ivw.domain.HotelGnOrderDetailStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/11 16:16.
    -->
    insert into hotel_gn_order_detail_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="interfacePlat != null">
        interface_plat,
      </if>
      <if test="hotelName != null">
        hotel_name,
      </if>
      <if test="subtype != null">
        subtype,
      </if>
      <if test="localId != null">
        local_id,
      </if>
      <if test="platId != null">
        plat_id,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="isFailure != null">
        is_failure,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="originalRequest != null">
        original_request,
      </if>
      <if test="originalResponse != null">
        original_response,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="interfacePlat != null">
        #{interfacePlat,jdbcType=BIGINT},
      </if>
      <if test="hotelName != null">
        #{hotelName,jdbcType=VARCHAR},
      </if>
      <if test="subtype != null">
        #{subtype,jdbcType=VARCHAR},
      </if>
      <if test="localId != null">
        #{localId,jdbcType=VARCHAR},
      </if>
      <if test="platId != null">
        #{platId,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="isFailure != null">
        #{isFailure,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="originalRequest != null">
        #{originalRequest,jdbcType=OTHER},
      </if>
      <if test="originalResponse != null">
        #{originalResponse,jdbcType=OTHER},
      </if>
    </trim>
  </insert>
  <insert id="insertList"
          parameterType="java.util.List">
    <foreach collection="list" item="item" index="index" separator=";">
      insert into hotel_gn_order_detail_statistics (id, type, interface_plat,interface_name,
      hotel_name, subtype, local_id,
      plat_id, reason, is_failure,
      create_time, original_request, original_response
      )
      values (#{item.id,jdbcType=BIGINT}, #{item.type,jdbcType=TINYINT}, #{item.interfacePlat,jdbcType=BIGINT},#{item.interfaceName,jdbcType=VARCHAR},
      #{item.hotelName,jdbcType=VARCHAR}, #{item.subtype,jdbcType=VARCHAR}, #{item.localId,jdbcType=VARCHAR},
      #{item.platId,jdbcType=VARCHAR}, #{item.reason,jdbcType=VARCHAR}, #{item.isFailure,jdbcType=TINYINT},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.originalRequest,jdbcType=OTHER}, #{item.originalResponse,jdbcType=OTHER}
      )
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ltgj.ivw.domain.HotelGnOrderDetailStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/11 16:16.
    -->
    update hotel_gn_order_detail_statistics
    <set>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="interfacePlat != null">
        interface_plat = #{interfacePlat,jdbcType=BIGINT},
      </if>
      <if test="hotelName != null">
        hotel_name = #{hotelName,jdbcType=VARCHAR},
      </if>
      <if test="subtype != null">
        subtype = #{subtype,jdbcType=VARCHAR},
      </if>
      <if test="localId != null">
        local_id = #{localId,jdbcType=VARCHAR},
      </if>
      <if test="platId != null">
        plat_id = #{platId,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="isFailure != null">
        is_failure = #{isFailure,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="originalRequest != null">
        original_request = #{originalRequest,jdbcType=OTHER},
      </if>
      <if test="originalResponse != null">
        original_response = #{originalResponse,jdbcType=OTHER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ltgj.ivw.domain.HotelGnOrderDetailStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/11 16:16.
    -->
    update hotel_gn_order_detail_statistics
    set type = #{type,jdbcType=TINYINT},
      interface_plat = #{interfacePlat,jdbcType=BIGINT},
      hotel_name = #{hotelName,jdbcType=VARCHAR},
      subtype = #{subtype,jdbcType=VARCHAR},
      local_id = #{localId,jdbcType=VARCHAR},
      plat_id = #{platId,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      is_failure = #{isFailure,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      original_request = #{originalRequest,jdbcType=OTHER},
      original_response = #{originalResponse,jdbcType=OTHER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
