package com.ltgj.sdk.cozyTime.model.staticdata;

import com.ltgj.sdk.cozyTime.base.CozyTimeBaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 根据国家编码查询城市信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelContentApi20FacilitiesResponse extends CozyTimeBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 城市列表
     */
    private List<Facilities> facilities;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Facilities implements Serializable {
        /**
         * 设施编码，例如 "2260"
         */
        private String facilityCode;

        /**
         * 设施名称，例如 "电竞显卡"
         */
        private String facilityNameCN;

        /**
         * 设施类型，例如 "2"
         */
        private String facilityType;

        /**
         * 分类名称，例如 "电竞设施"
         */
        private String categoryName;
    }
}


