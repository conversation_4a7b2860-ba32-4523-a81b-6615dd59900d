<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ltgj.ivw.mapper.HotelFacilityCozyTimeMapper">

    <insert id="insertBatch">
            insert into hotel_facility_cozy_time(id,facility_code,facility_name,facility_type,category_name,region) values
            <foreach collection="list" item="item" separator=",">
                (#{item.id},#{item.facilityCode},#{item.facilityName},#{item.facilityType},#{item.categoryName},#{item.region})
            </foreach>
    </insert>
    <delete id="deleteAll">
        delete
        from hotel_facility_cozy_time
    </delete>
</mapper>
