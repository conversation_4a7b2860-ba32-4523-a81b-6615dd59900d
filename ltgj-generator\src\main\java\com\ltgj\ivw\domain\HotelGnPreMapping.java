package com.ltgj.ivw.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Table: hotel_gn_pre_mapping
 */
@Data
public class HotelGnPreMapping implements Serializable {
    /** ID */
    private String id;

    /** 来源-酒店id */
    private String sourceHotelId;

    /** 来源-酒店名称 */
    private String sourceHotelName;

    /** 来源-酒店地址 */
    private String sourceHotelAddress;

    /** 来源-酒店电话 */
    private String sourceHotelPhone;

    /** 来源-酒店城市名称 */
    private String sourceHotelCityName;

    /** 来源-平台编号，20000XX */
    private String sourePlatformId;

    /** 目标-酒店id */
    private String targetHotelId;

    /** 目标-酒店名称 */
    private String targetHotelName;

    /** 目标-酒店地址 */
    private String targetHotelAddress;

    /** 目标-酒店电话 */
    private String targetHotelPhone;

    /** 目标-酒店城市名称 */
    private String targetHotelCityName;

    /** 目标-平台编号，-1，基础酒店，其他：20000XX */
    private String targetPlatformId;

    /** 预映射方式；ID，AI, mapping(直接映射)，import（导入本地酒店) */
    private String mappingType;

    /** 得分 */
    private Integer score;

    /** 得分原因 */
    private String scoreReason;

    /** 得分原因，0，未处理；1，处理成功；2，处理失败；3，映射异常; */
    private Byte status;

    /** 处理原因 */
    private String handlerReason;

    /** 备注1 */
    private String remark1;

    /** 备注2 */
    private String remark2;

    /** 创建时间 */
    private Date createTime;

    /** 最后更新时间 */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 删除标识，0：未删除；1：删除
     */
    private Boolean isDelete;
}