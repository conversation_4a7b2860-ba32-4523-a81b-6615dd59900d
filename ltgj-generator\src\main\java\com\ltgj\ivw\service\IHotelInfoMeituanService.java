package com.ltgj.ivw.service;

import com.alibaba.fastjson2.JSONObject;
import com.ltgj.ivw.domain.HotelInfoMeituan;
import com.ltgj.ivw.domain.MinPriceReq;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnMeituan;

import java.util.List;

/**
 * 美团酒店信息Service接口
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
public interface IHotelInfoMeituanService extends BaseHotelService<HotelGnBase, HotelGnMeituan>{
    /**
     * 查询美团酒店信息
     *
     * @param id 美团酒店信息主键
     * @return 美团酒店信息
     */
    public HotelInfoMeituan selectHotelInfoMeituanById(String id);

    /**
     * 查询美团酒店信息列表
     *
     * @param hotelInfoMeituan 美团酒店信息
     * @return 美团酒店信息集合
     */
    public List<HotelInfoMeituan> selectHotelInfoMeituanList(HotelInfoMeituan hotelInfoMeituan);

    public List<HotelInfoMeituan> selectHotelInfoMeituanList2(HotelInfoMeituan hotelInfoMeituan);

    /**
     * 查询酒店ID
     *
     * @param hotelInfoMeituan hotel info meituan
     * @return {@link List }<{@link String }>
     */
    List<Long> selectHotelIds(HotelInfoMeituan hotelInfoMeituan);

    /**
     * 更新低价
     *
     * @param minPriceReq 最低价格要求
     */
    void updateLowPrice(MinPriceReq minPriceReq);

    /**
     * 处理美团最低价格
     *
     * @param meituanList  帮助列表
     * @param checkInDate  入住日期
     * @param checkOutDate 退房日期
     */
    void getMeituanMinPrice(List<Long> meituanList, String checkInDate, String checkOutDate);

    /**
     * 新增美团酒店信息
     *
     * @param hotelInfoMeituan 美团酒店信息
     * @return 结果
     */
    public int insertHotelInfoMeituan(HotelInfoMeituan hotelInfoMeituan);

    public int insertHotelInfoMeituans(List<HotelInfoMeituan> hotelInfoMeituans);

    /**
     * 修改美团酒店信息
     *
     * @param hotelInfoMeituan 美团酒店信息
     * @return 结果
     */
    public int updateHotelInfoMeituan(HotelInfoMeituan hotelInfoMeituan);

    /**
     * 修改酒店国内美团信息
     *
     * @param hotelInfoMeituan 美团酒店信息
     * @return 结果
     */
    public void saveOrUpdateHotelGnMeituan(HotelInfoMeituan hotelInfoMeituan);

    /**
     * 批量删除美团酒店信息
     *
     * @param ids 需要删除的美团酒店信息主键集合
     * @return 结果
     */
    public int deleteHotelInfoMeituanByIds(String[] ids);

    /**
     * 删除美团酒店信息信息
     *
     * @param id 美团酒店信息主键
     * @return 结果
     */
    public int deleteHotelInfoMeituanById(String id);

    List<HotelInfoMeituan> selectHotelInfoNotMapping(List<String> idList, int flag);

    List<String> selectNotMappingIdList();

    List<HotelInfoMeituan> selectListByIdList(List<String> idList);

    HotelGnMeituan buildHotelGnMeituanFromJsonObject(JSONObject jsonObject);
}
