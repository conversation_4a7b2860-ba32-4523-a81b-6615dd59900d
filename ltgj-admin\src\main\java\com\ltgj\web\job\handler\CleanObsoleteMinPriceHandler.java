package com.ltgj.web.job.handler;

import com.ltgj.ivw.utils.MyTools;
import com.ltgj.quartz.task.MinPriceTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/6/6
 * @Description： 清除低价数据
 * 根据传参不同, 使用不同清楚模式
 *  beforeDate  2025-01-01 包含之前数据
 *  threshold  每次执行多少次
 */
@Slf4j
@Component
@JobHandler(value = "cleanObsoleteMinPriceHandler")
public class CleanObsoleteMinPriceHandler extends IJobHandler {

    @Resource
    private MinPriceTask minPriceTask;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        long startTime = System.currentTimeMillis();
        XxlJobLogger.log("----hotel-清除过时最低价任务----开始");

        Boolean result = true;
        try {
            String beforeDate = MyTools.getDateByCurr(-30);// 默认30天前,包含
            int threshold = 5000;// 执行5000次 一次取2000条 ，最多处理10000000(1千万)条
            if (StringUtils.isNotBlank(s)) {
                beforeDate = s.split(",")[0];
                threshold = Integer.valueOf(s.split(",")[1]);
            }
            log.info("----hotel-清除过时最低价任务 beforeDate:{} , threshold:{}----开始", beforeDate, threshold);
            this.minPriceTask.cleanObsoleteMinPrice(beforeDate, threshold);
        } catch (Exception e) {
            log.error("清除过时最低价任务异常：" + e);
            result = false;
        }

        XxlJobLogger.log("-----hotel-清除过时最低价任务-----务结束 执行结果：" + (result ? "成功" : "失败") + " 执行用时：" + (System.currentTimeMillis() - startTime) / 1000 + "秒");

        return result ? ReturnT.SUCCESS : ReturnT.FAIL;
    }
}
