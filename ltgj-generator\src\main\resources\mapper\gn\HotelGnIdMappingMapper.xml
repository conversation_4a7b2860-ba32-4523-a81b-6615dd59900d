<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.supplier.common.gn.mapper.HotelGnIdMappingMapper">

    <insert id="addOrUpdateBatch" >
        insert into hotel_gn_id_mapping
        (
        id,
        platform_id,
        platform_hotel_id,
        mapping_platform_id,
        mapping_hotel_id,
        remark,
        create_time,
        update_time,
        create_by,
        update_by,
        is_delete
        )
        values 
        <foreach collection="list" item="info" separator=",">
            (
            #{info.id},
            #{info.platformId},
            #{info.platformHotelId},
            #{info.mappingPlatformId},
            #{info.mappingHotelId},
            #{info.remark},
            #{info.createTime},
            #{info.updateTime},
            #{info.createBy},
            #{info.updateBy},
            #{info.isDelete}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            `platform_id` = VALUES(platform_id),
            `platform_hotel_id` = VALUES(platform_hotel_id),
            `mapping_platform_id` = VALUES(mapping_platform_id),
            `mapping_hotel_id` = VALUES(mapping_hotel_id),
            `remark` = VALUES(remark),
            `update_time` = VALUES(update_time),
            `update_by` = VALUES(update_by),
            `is_delete` = VALUES(is_delete)
    </insert>

    <select id="queryByPlatformIdAndHotelId" resultType="com.ltgj.supplier.common.gn.domain.HotelGnIdMapping">
        select  id,
                platform_id,
                platform_hotel_id,
                mapping_platform_id,
                mapping_hotel_id,
                remark from hotel_gn_id_mapping where platform_id = #{platformId}
                                            and platform_hotel_id = #{platformHotelId}
        order by create_time desc limit 1
    </select>
</mapper>
