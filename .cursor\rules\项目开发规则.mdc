# 项目开发规则

## 概述

本文档基于Java设计模式六大原则，制定了项目开发过程中必须遵循的编码规范和设计原则，旨在提高代码质量、可维护性和可扩展性。

## 设计原则

### 1. 单一职责原则 (Single Responsibility Principle, SRP)

#### 规则定义
- 一个类应该只有一个引起它变化的原因
- 一个类只负责一项职责

#### 实施规范

**✅ 正确示例：**
```java
// 用户数据访问职责
@Repository
public class UserRepository {
    public User findById(Long id) { }
    public void save(User user) { }
}

// 用户业务逻辑职责
@Service
public class UserService {
    public void registerUser(UserDto userDto) { }
    public void updateUserProfile(UserDto userDto) { }
}

// 用户数据转换职责
@Component
public class UserConverter {
    public UserDto toDto(User user) { }
    public User toEntity(UserDto dto) { }
}
```

**❌ 错误示例：**
```java
// 违反SRP - 一个类承担多种职责
public class UserManager {
    // 数据访问职责
    public User findById(Long id) { }
    
    // 业务逻辑职责
    public void registerUser(UserDto userDto) { }
    
    // 数据转换职责
    public UserDto toDto(User user) { }
    
    // 邮件发送职责
    public void sendEmail(String email) { }
}
```

#### 检查清单
- [ ] 类名是否清晰表达单一职责？
- [ ] 类的方法是否都围绕同一个职责？
- [ ] 修改一个功能是否只需要修改一个类？

### 2. 开闭原则 (Open/Closed Principle, OCP)

#### 规则定义
- 软件实体应该对扩展开放，对修改关闭
- 通过扩展来实现变化，而不是修改现有代码

#### 实施规范

**✅ 正确示例：**
```java
// 定义抽象接口
public interface PaymentProcessor {
    void processPayment(PaymentRequest request);
}

// 具体实现 - 可扩展
@Component
public class WechatPaymentProcessor implements PaymentProcessor {
    @Override
    public void processPayment(PaymentRequest request) {
        // 微信支付实现
    }
}

@Component
public class AlipayPaymentProcessor implements PaymentProcessor {
    @Override
    public void processPayment(PaymentRequest request) {
        // 支付宝支付实现
    }
}

// 支付管理器 - 无需修改即可支持新的支付方式
@Service
public class PaymentManager {
    private final Map<PaymentType, PaymentProcessor> processors;
    
    public void processPayment(PaymentType type, PaymentRequest request) {
        PaymentProcessor processor = processors.get(type);
        processor.processPayment(request);
    }
}
```

**❌ 错误示例：**
```java
// 违反OCP - 新增支付方式需要修改现有代码
public class PaymentManager {
    public void processPayment(String type, PaymentRequest request) {
        if ("WECHAT".equals(type)) {
            // 微信支付逻辑
        } else if ("ALIPAY".equals(type)) {
            // 支付宝支付逻辑
        } else if ("UNIONPAY".equals(type)) {
            // 新增银联支付需要修改这里 ❌
        }
    }
}
```

#### 检查清单
- [ ] 新增功能是否可以通过新增类实现？
- [ ] 是否使用了抽象接口或抽象类？
- [ ] 现有代码是否无需修改就能支持扩展？

### 3. 里氏替换原则 (Liskov Substitution Principle, LSP)

#### 规则定义
- 子类必须能够替换父类，且不影响程序正确性
- 子类可以扩展父类功能，但不能改变父类行为

#### 实施规范

**✅ 正确示例：**
```java
// 抽象基类
public abstract class Shape {
    public abstract double calculateArea();
    
    public void printInfo() {
        System.out.println("Area: " + calculateArea());
    }
}

// 子类完全符合父类契约
public class Rectangle extends Shape {
    private double width;
    private double height;
    
    @Override
    public double calculateArea() {
        return width * height; // 正确实现
    }
}

public class Circle extends Shape {
    private double radius;
    
    @Override
    public double calculateArea() {
        return Math.PI * radius * radius; // 正确实现
    }
}
```

**❌ 错误示例：**
```java
// 违反LSP - 子类改变了父类行为
public class ReadOnlyList<T> extends ArrayList<T> {
    @Override
    public boolean add(T element) {
        throw new UnsupportedOperationException(); // 改变了父类行为 ❌
    }
}
```

#### 检查清单
- [ ] 子类是否能完全替换父类使用？
- [ ] 子类是否遵循父类的契约？
- [ ] 子类是否抛出了父类未声明的异常？

### 4. 依赖倒置原则 (Dependency Inversion Principle, DIP)

#### 规则定义
- 高层模块不应依赖低层模块，都应依赖抽象
- 抽象不应依赖细节，细节应依赖抽象

#### 实施规范

**✅ 正确示例：**
```java
// 定义抽象接口
public interface NotificationService {
    void sendNotification(String message, String recipient);
}

public interface UserRepository {
    User findById(Long id);
    void save(User user);
}

// 高层模块依赖抽象
@Service
public class UserService {
    private final UserRepository userRepository;
    private final NotificationService notificationService;
    
    // 依赖注入抽象接口
    public UserService(UserRepository userRepository, 
                      NotificationService notificationService) {
        this.userRepository = userRepository;
        this.notificationService = notificationService;
    }
    
    public void registerUser(UserDto userDto) {
        User user = new User(userDto);
        userRepository.save(user);
        notificationService.sendNotification("Welcome!", user.getEmail());
    }
}

// 具体实现
@Repository
public class JpaUserRepository implements UserRepository {
    // JPA实现
}

@Service
public class EmailNotificationService implements NotificationService {
    // 邮件通知实现
}
```

**❌ 错误示例：**
```java
// 违反DIP - 直接依赖具体实现
@Service
public class UserService {
    private JpaUserRepository userRepository = new JpaUserRepository(); // 直接依赖具体类 ❌
    private EmailService emailService = new EmailService(); // 直接依赖具体类 ❌
    
    public void registerUser(UserDto userDto) {
        // 业务逻辑
    }
}
```

#### 检查清单
- [ ] 是否使用构造函数或setter注入依赖？
- [ ] 依赖的是接口还是具体类？
- [ ] 是否使用了new关键字创建依赖对象？

### 5. 接口隔离原则 (Interface Segregation Principle, ISP)

#### 规则定义
- 类不应被迫依赖它不使用的方法
- 接口应该小而专一，不要创建臃肿的接口

#### 实施规范

**✅ 正确示例：**
```java
// 细粒度接口
public interface Readable {
    String read();
}

public interface Writable {
    void write(String data);
}

public interface Closeable {
    void close();
}

// 根据需要实现相应接口
public class FileHandler implements Readable, Writable, Closeable {
    @Override
    public String read() { return "data"; }
    
    @Override
    public void write(String data) { }
    
    @Override
    public void close() { }
}

public class LogReader implements Readable, Closeable {
    @Override
    public String read() { return "log"; }
    
    @Override
    public void close() { }
    // 不需要实现write方法
}
```

**❌ 错误示例：**
```java
// 违反ISP - 臃肿的接口
public interface FileOperations {
    String read();
    void write(String data);
    void close();
    void compress();
    void encrypt();
    void backup();
}

// 强迫实现不需要的方法
public class SimpleFileReader implements FileOperations {
    @Override
    public String read() { return "data"; }
    
    @Override
    public void close() { }
    
    // 不需要但被迫实现 ❌
    @Override
    public void write(String data) { throw new UnsupportedOperationException(); }
    
    @Override
    public void compress() { throw new UnsupportedOperationException(); }
    
    @Override
    public void encrypt() { throw new UnsupportedOperationException(); }
    
    @Override
    public void backup() { throw new UnsupportedOperationException(); }
}
```

#### 检查清单
- [ ] 接口是否职责单一？
- [ ] 实现类是否需要实现所有方法？
- [ ] 是否存在空实现或抛异常的方法？

### 6. 迪米特法则 (Law of Demeter, LoD)

#### 规则定义
- 一个对象应该对其他对象有最少的了解
- 只与直接朋友交流，不与陌生人说话

#### 实施规范

**✅ 正确示例：**
```java
// 使用门面模式隐藏复杂性
@Service
public class OrderFacade {
    private final OrderService orderService;
    private final PaymentService paymentService;
    private final InventoryService inventoryService;
    private final NotificationService notificationService;
    
    public OrderResult processOrder(OrderRequest request) {
        // 门面协调各个服务
        Order order = orderService.createOrder(request);
        PaymentResult payment = paymentService.processPayment(order.getPaymentInfo());
        inventoryService.updateInventory(order.getItems());
        notificationService.sendConfirmation(order.getCustomerEmail());
        
        return new OrderResult(order, payment);
    }
}

// 控制器只与门面交互
@RestController
public class OrderController {
    private final OrderFacade orderFacade;
    
    @PostMapping("/orders")
    public ResponseEntity<OrderResult> createOrder(@RequestBody OrderRequest request) {
        OrderResult result = orderFacade.processOrder(request);
        return ResponseEntity.ok(result);
    }
}
```

**❌ 错误示例：**
```java
// 违反LoD - 过多了解其他对象的内部结构
@RestController
public class OrderController {
    @Autowired
    private OrderService orderService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private InventoryService inventoryService;
    @Autowired
    private NotificationService notificationService;
    
    @PostMapping("/orders")
    public ResponseEntity<OrderResult> createOrder(@RequestBody OrderRequest request) {
        // 控制器了解太多业务细节 ❌
        Order order = orderService.createOrder(request);
        PaymentInfo paymentInfo = order.getPaymentInfo();
        PaymentResult payment = paymentService.processPayment(paymentInfo);
        
        List<OrderItem> items = order.getItems();
        for (OrderItem item : items) {
            inventoryService.updateStock(item.getProductId(), item.getQuantity());
        }
        
        Customer customer = order.getCustomer();
        notificationService.sendEmail(customer.getEmail(), "Order Confirmation");
        
        return ResponseEntity.ok(new OrderResult(order, payment));
    }
}
```

#### 检查清单
- [ ] 类是否只与直接依赖交互？
- [ ] 是否存在链式调用（如a.getB().getC().doSomething()）？
- [ ] 是否使用了门面模式隐藏复杂性？

## 设计模式应用指南

### 常用设计模式

#### 策略模式 (Strategy Pattern)
**适用场景**: 需要在运行时选择算法的情况
```java
public interface DiscountStrategy {
    BigDecimal calculateDiscount(BigDecimal amount);
}

@Component
public class VipDiscountStrategy implements DiscountStrategy {
    @Override
    public BigDecimal calculateDiscount(BigDecimal amount) {
        return amount.multiply(new BigDecimal("0.8")); // 8折
    }
}

@Service
public class PriceCalculator {
    public BigDecimal calculatePrice(BigDecimal basePrice, DiscountStrategy strategy) {
        return strategy.calculateDiscount(basePrice);
    }
}
```

#### 工厂模式 (Factory Pattern)
**适用场景**: 创建对象的逻辑比较复杂
```java
public interface PaymentFactory {
    PaymentProcessor create(PaymentType type);
}

@Component
public class PaymentFactoryImpl implements PaymentFactory {
    @Override
    public PaymentProcessor create(PaymentType type) {
        switch (type) {
            case WECHAT: return new WechatPaymentProcessor();
            case ALIPAY: return new AlipayPaymentProcessor();
            default: throw new IllegalArgumentException("不支持的支付类型");
        }
    }
}
```

#### 观察者模式 (Observer Pattern)
**适用场景**: 对象状态改变需要通知多个依赖对象
```java
@Component
public class OrderEventPublisher {
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    public void publishOrderCreated(Order order) {
        eventPublisher.publishEvent(new OrderCreatedEvent(order));
    }
}

@EventListener
@Component
public class EmailNotificationListener {
    public void handleOrderCreated(OrderCreatedEvent event) {
        // 发送邮件通知
    }
}
```

#### 模板方法模式 (Template Method Pattern)
**适用场景**: 定义算法骨架，子类实现具体步骤
```java
public abstract class DataProcessor {
    // 模板方法
    public final void process() {
        validateData();
        processData();
        saveData();
        notifyCompletion();
    }
    
    protected abstract void validateData();
    protected abstract void processData();
    protected abstract void saveData();
    
    // 钩子方法
    protected void notifyCompletion() {
        // 默认实现
    }
}
```

#### 装饰器模式 (Decorator Pattern)
**适用场景**: 动态给对象添加功能
```java
public interface DataService {
    String getData();
}

@Component
public class CacheDecorator implements DataService {
    private final DataService dataService;
    private final CacheManager cacheManager;
    
    @Override
    public String getData() {
        String cacheKey = "data_key";
        String cachedData = cacheManager.get(cacheKey);
        if (cachedData != null) {
            return cachedData;
        }
        
        String data = dataService.getData();
        cacheManager.put(cacheKey, data);
        return data;
    }
}
```

## 代码审查清单

### 设计阶段检查
- [ ] 类的职责是否单一明确？
- [ ] 接口设计是否遵循开闭原则？
- [ ] 继承关系是否符合里氏替换原则？
- [ ] 依赖关系是否合理？
- [ ] 是否选择了合适的设计模式？

### 编码阶段检查
- [ ] 是否使用了依赖注入？
- [ ] 接口是否足够细粒度？
- [ ] 类之间的耦合度是否合理？
- [ ] 是否存在违反设计原则的代码？
- [ ] 异常处理是否合理？
- [ ] 日志记录是否完善？

### 性能检查
- [ ] 是否存在不必要的对象创建？
- [ ] 数据库查询是否优化？
- [ ] 缓存策略是否合理？
- [ ] 并发处理是否安全？

### 安全检查
- [ ] 输入验证是否充分？
- [ ] 敏感数据是否加密？
- [ ] 权限控制是否完善？
- [ ] SQL注入防护是否到位？

### 重构优先级
1. **高优先级**：违反SRP和DIP的代码、安全漏洞、性能瓶颈
2. **中优先级**：违反OCP和ISP的代码、代码重复、复杂度过高
3. **低优先级**：违反LSP和LoD的代码、命名不规范、注释不足

## 工具和自动化

### 静态代码分析
- **SonarQube**: 代码质量和安全检测
- **PMD**: 设计原则违反检测
- **SpotBugs**: 潜在Bug发现
- **Checkstyle**: 编码规范检查

### 架构决策记录 (ADR)
```markdown
# ADR-001: 使用策略模式处理多种支付方式

## 状态
已采纳

## 背景
系统需要支持多种支付方式，未来还会增加新的支付渠道

## 决策
采用策略模式，每种支付方式实现PaymentProcessor接口

## 结果
- 符合开闭原则，易于扩展
- 代码结构清晰，易于维护
- 支付逻辑与业务逻辑解耦
```

### 持续集成配置
```yaml
# .github/workflows/code-quality.yml
name: Code Quality Check
on: [push, pull_request]
jobs:
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          java-version: '11'
      - name: Run SonarQube analysis
        run: mvn sonar:sonar
      - name: Run PMD analysis
        run: mvn pmd:check
```

## 培训和最佳实践

### 团队培训计划
1. **基础培训**: 设计原则和常用模式
2. **实践培训**: 代码审查和重构技巧
3. **高级培训**: 架构设计和系统优化

### 知识分享
- 每周技术分享会
- 最佳实践案例收集
- 设计模式应用经验总结

### 代码审查规范
- 所有代码必须经过审查
- 重点关注设计原则违反
- 及时反馈和持续改进

## 持续改进

### 度量指标
- 代码质量分数
- 技术债务数量
- 缺陷密度
- 开发效率

### 定期评估
- 月度代码质量报告
- 季度架构回顾
- 年度技术债务清理

### 工具升级
- 定期更新分析工具
- 引入新的质量检测手段
- 优化CI/CD流程

---

**版本**: 1.0  
**最后更新**: 2024年  
**适用范围**: 所有Java项目开发