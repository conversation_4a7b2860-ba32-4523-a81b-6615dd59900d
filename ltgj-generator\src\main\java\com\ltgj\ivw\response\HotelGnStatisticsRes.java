package com.ltgj.ivw.response;

import com.ltgj.ivw.domain.HotelGnStatistics;
import lombok.Data;

import java.io.Serializable;

@Data
public class HotelGnStatisticsRes extends HotelGnStatistics implements Serializable {

    /**
     * 类型：0-按日期查询，1-本月数据，2-本周数据
     */
    private Integer dateType;

    /**
     * 映射率
     */
    private String  successRate;

    /**
     * 失败映射率
     */
    private String  failRate;

    /**
     * 酒店总量对比
     */
    private String allTotalNumRate;

    /**
     * 酒店成功量对比
     */
    private String allSuccessNumRate;


    /**
     * 酒店失败量对比
     */
    private String allFailNumRate;
}
