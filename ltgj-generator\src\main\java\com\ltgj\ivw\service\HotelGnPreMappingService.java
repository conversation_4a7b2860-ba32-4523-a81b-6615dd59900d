package com.ltgj.ivw.service;

import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.request.hotelGnPreMapping.ListRequest;
import com.ltgj.ivw.request.hotelGnPreMapping.ListResponse;

import java.util.List;

/**
 * 酒店-国内-预映射service
 */
public interface HotelGnPreMappingService {

    List<ListResponse> selectList(ListRequest listRequest);

    HotelGnPreMapping selectById(String id);

    int deleteByIds(String[] ids);

    void addMapping(String id);

    void addBasic(String id);

    int addHotelGnPreMapping(HotelGnPreMapping hotelGnPreMapping);

    /**
     * 定时任务处理未映射 + 100分数据
     * @param s
     */
    void processHighScoreMappingAsync(String s);

    /**
     * 定时任务处理未映射 + 0分数据
     * @param s
     */
    void processLowScoreMappingAsync(String s);

    void processHighScoreMapping(HotelGnPreMapping mapping, String userName);

    void updateMappingError(HotelGnPreMapping mapping, String reason);

    void processLowScoreMapping(HotelGnPreMapping mapping, String userName, String type);

    void filterListByStatus(List<HotelGnPreMapping> aiHotelList);
}
