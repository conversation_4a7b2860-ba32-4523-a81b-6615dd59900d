package com.ltgj.ivw.service.hotel.updater;

import com.ltgj.ivw.domain.dto.CityDTO;
import com.ltgj.ivw.domain.dto.HotelDTO;
import com.ltgj.ivw.service.impl.HotelInfoChailvgjServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 城市酒店数据处理器(模板方法模式实现)
 *
 * <AUTHOR>
 */
@Slf4j
public class CityHotelDataProcessor implements HotelDataProcessor {
    private final HotelInfoChailvgjServiceImpl service;
    private final CityDTO city;
    
    /**
     * 构造方法
     * 
     * @param service 酒店服务实现
     * @param city 城市数据
     */
    public CityHotelDataProcessor(HotelInfoChailvgjServiceImpl service, CityDTO city) {
        this.service = service;
        this.city = city;
    }
    
    @Override
    public ProcessResult process(String userName) {
        log.info("开始处理城市[{}]的酒店数据", city.getCityName());

        // 统计数据
        AtomicInteger processed = new AtomicInteger(0);
        AtomicInteger add = new AtomicInteger(0);
        AtomicInteger update = new AtomicInteger(0);
        
        // 获取城市酒店列表
        List<HotelDTO> hotels = service.getHotelListByCityId(city.getCityId());
        AtomicInteger total = new AtomicInteger(hotels.size());

        // 创建单个酒店处理器
        HotelDataProcessorFactory factory = new HotelDataProcessorFactory();

        // 获取线程池
        ThreadPoolExecutor executor = ThreadPoolManager.getInstance().getExecutor();
        
        // 任务提交与执行
        List<Future<?>> futures = Lists.newArrayList();
        for (HotelDTO hotel : hotels) {
            futures.add(executor.submit(() -> {
                try {
                    // 创建并执行单个酒店处理器
                    HotelDataProcessor processor = factory.createProcessor(
                        HotelDataProcessorType.SINGLE_HOTEL, 
                        service, 
                        hotel
                    );
                    
                    ProcessResult result = processor.process(userName);
                    processed.addAndGet(1);
                    add.addAndGet(result.getAddCount().get());
                    update.addAndGet(result.getUpdateCount().get());
                    
                } catch (Exception e) {
                    log.error("处理酒店[{}]数据异常", hotel.getHotelId(), e);
                }
            }));
        }

        // 等待所有任务完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("等待任务完成异常", e);
            }
        }
        
        log.info("城市[{}]酒店数据处理完成, 总数:{}, 成功:{}", city.getCityName(), processed, add.get() + update.get());
        return new ProcessResult(processed, add, update, total);
    }
} 