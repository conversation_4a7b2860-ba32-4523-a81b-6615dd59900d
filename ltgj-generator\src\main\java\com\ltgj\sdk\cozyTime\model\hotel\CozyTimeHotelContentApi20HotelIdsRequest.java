package com.ltgj.sdk.cozyTime.model.hotel;

import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import lombok.Data;

/**
 * 根据国家编码和城市编码查询酒店ID列表
 */
@Data
public class CozyTimeHotelContentApi20HotelIdsRequest implements CozyTimeRequestInterface<CozyTimeHotelContentApi20HotelIdsResponse> {
    @Override
    public String getRequestMethod() {
        return "GET";
    }

    @Override
    public String getEndpoint() {
        return "/hotel_content_api/2.0/hotelIds";
    }

    @Override
    public Class<CozyTimeHotelContentApi20HotelIdsResponse> getResponseClass() {
        return CozyTimeHotelContentApi20HotelIdsResponse.class;
    }

    /**
     * 国家编码
     * 必填：true
     */
    private String countryCode="CN";
    /**
     * 城市编码
     * 必填：true
     */
    private String cityCode;
    /**
     * 酒店状态
     * 0：可售
     * 1：停售
     * 2：删除
     * 必填：false
     * 默认返回可售酒店
     */
    private String hotelStatus;

}
