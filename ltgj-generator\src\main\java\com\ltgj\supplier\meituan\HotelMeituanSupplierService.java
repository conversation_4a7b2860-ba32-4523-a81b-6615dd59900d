package com.ltgj.supplier.meituan;

import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.meituan.HotelCityMeituan;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelCityService;
import com.ltgj.ivw.service.IHotelCityMeituanService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 美团供应商服务
 */
@Slf4j
@Service
public class HotelMeituanSupplierService {

    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private IHotelCityMeituanService hotelCityMeituanService;

    /**
     * 处理美团城市数据
     *
     * @param meituanCities 美团城市数据列表
     * @param idempotent    是否幂等处理
     * @return 处理的城市数量
     */
    public int processCityData(List<HotelCityMeituan> meituanCities, String idempotent) {
        int count = 0;
        if (CollectionUtils.isEmpty(meituanCities)) {
            return count;
        }

        for (HotelCityMeituan meituanCity : meituanCities) {
            try {
                HotelCity hotelCity = this.createHotelCity(meituanCity);
                // 检查是否已存在相同的城市数据（根据平台和locationId）
                HotelCity existingQuery = new HotelCity();
                existingQuery.setReserve1(PlatEnum.PLAT_MT.getValue());
                existingQuery.setCityId(hotelCity.getCityId());
                existingQuery.setLocationId(hotelCity.getLocationId());
                List<HotelCity> existingCities = this.hotelCityService.selectHotelCityList(existingQuery);

                if (StringUtils.equals(idempotent, "1") && !existingCities.isEmpty()) {
                    // 幂等模式下，如果已存在则跳过
                    log.warn("美团城市数据已存在，跳过: {}", meituanCity.getCityName());
                    continue;
                }

                if (existingCities.isEmpty()) {
                    this.hotelCityService.insertHotelCity(hotelCity);
                    count++;
                    log.warn("插入美团城市数据: {}", meituanCity.getCityName());
                } else {
                    log.warn("美团城市数据已存在: {}", meituanCity.getCityName());
                }
            } catch (Exception e) {
                log.error("处理美团城市数据异常: {}", meituanCity.getCityName(), e);
            }
        }

        return count;
    }

    /**
     * 分页处理美团城市数据
     *
     * @param idempotent 是否幂等处理
     * @return 处理的城市数量
     */
    public int processCityDataWithPagination(String idempotent) {
        int totalCount = 0;
        int pageSize = 500; // 每页500条数据
        int offset = 0;
        
        // 获取总数据量
        long totalData = hotelCityMeituanService.countHotelCityMeituan();
        log.info("开始分页处理美团城市数据，总数据量: {}", totalData);
        
        while (offset < totalData) {
            try {
                // 分页查询数据
                List<HotelCityMeituan> cityList = hotelCityMeituanService.selectHotelCityMeituanListWithPage(pageSize, offset);
                
                if (CollectionUtils.isEmpty(cityList)) {
                    log.info("第{}页无数据，结束处理", (offset / pageSize) + 1);
                    break;
                }
                
                log.info("正在处理第{}页数据，数据量: {}", (offset / pageSize) + 1, cityList.size());
                
                // 处理当前页数据
                int pageCount = processCityData(cityList, idempotent);
                totalCount += pageCount;
                
                log.info("第{}页处理完成，处理数量: {}", (offset / pageSize) + 1, pageCount);
                
                // 移动到下一页
                offset += pageSize;
                
            } catch (Exception e) {
                log.error("处理第{}页数据异常", (offset / pageSize) + 1, e);
                // 继续处理下一页
                offset += pageSize;
            }
        }
        
        log.info("美团城市数据分页处理完成，总处理数量: {}", totalCount);
        return totalCount;
    }

    /**
     * 创建城市对象
     *
     * @param meituanCity 美团城市数据
     * @return 酒店城市对象
     */
    private HotelCity createHotelCity(HotelCityMeituan meituanCity) {
        HotelCity hotelCity = new HotelCity();
        
        // 设置平台标识 - 美团
        hotelCity.setReserve1(PlatEnum.PLAT_MT.getValue());

        // 设置国家信息（默认中国）
        hotelCity.setCountryId("CN");
        hotelCity.setCountryName("中国");
        hotelCity.setCountryNameEn("CHINA");

        // 设置省份信息
        hotelCity.setProvinceId(Optional.ofNullable(meituanCity.getProvinceId()).map(String::valueOf).orElse(""));
        hotelCity.setProvinceName(Optional.ofNullable(meituanCity.getProvinceName()).orElse(""));
        hotelCity.setProvinceNameEn(Optional.ofNullable(meituanCity.getProvinceName()).orElse(""));

        // 设置城市信息
        hotelCity.setCityId(Optional.ofNullable(meituanCity.getCityId()).map(String::valueOf).orElse(""));
        hotelCity.setCityName(Optional.ofNullable(meituanCity.getCityName()).orElse(""));
        hotelCity.setCityNameEn(Optional.ofNullable(meituanCity.getCityName()).orElse(""));

        // 设置区县信息
        hotelCity.setLocationId(Optional.ofNullable(meituanCity.getLocationId()).map(String::valueOf).orElse(""));
        hotelCity.setLocationName(Optional.ofNullable(meituanCity.getLocationName()).orElse(""));
        hotelCity.setLocationNameEn(Optional.ofNullable(meituanCity.getLocationName()).orElse(""));
        
        return hotelCity;
    }
} 