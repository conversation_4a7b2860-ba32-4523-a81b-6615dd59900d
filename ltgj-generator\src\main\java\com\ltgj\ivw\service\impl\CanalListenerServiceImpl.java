package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.es.hotel.model.EsHotelInfo;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.bean.BeanCopierUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.JdJdbMapper;
import com.ltgj.ivw.mapper.ZhJdJdbMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.mapper.HotelGnBaseMapper;
import com.ltgj.supplier.klyx.gn.HotelKlyxManager;
import com.tem.search.repository.ElasticsearchTemplate;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Consumer;

/**
 * canal监听酒店相关表业务处理
 */
@Slf4j
@Service
@RefreshScope
public class CanalListenerServiceImpl implements CanalListenerService {

    @Autowired
    private JdJdbMapper jdJdbMapper;

    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;

    @Autowired
    private ZhJdJdbMapper zhJdJdbMapper;

    @Autowired
    private IJdJdbService jdJdbService;

    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;

    @Autowired
    private IHotelInfoHsjlxyService hotelInfoHsjlxyService;

    @Autowired
    private ZhJdJdbMappingBackupService zhJdJdbMappingBackupService;

    @Autowired
    private EsHotelInfoService esHotelInfoService;

    @Autowired
    private ElasticsearchTemplate<EsHotelInfo, String> hotelIntlTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private HotelKlyxManager hotelKlyxManager;

    @Autowired
    private HotelPreMappingManager hotelPreMappingManager;

    @Value("${IsDealMinPrice:0}")
    private Integer IsDealMinPrice;

    @Autowired
    private DifyApiService difyApiService;

    @Autowired
    private HotelGnBaseMapper hotelGnBaseMapper;

    @Value("${dify.isAsyncDify:false}")
    private Boolean isAsyncDify;

    @Value("${canal.zhJdJdbMapping.switch.insert:false}")
    private Boolean canalZhJdJdbMappingSwitchInsert;

    @Value("${canal.zhJdJdbMapping.switch.delete:false}")
    private Boolean canalZhJdJdbMappingSwitchDelete;

    @Value("${canal.jdJdb.switch:false}")
    private Boolean canalJdJdbSwitch;


    @Override
    public void dealJdJdbCanalMsg(String msg) {
        //新增是否执行开关
        log.info("新增开关:{}", canalJdJdbSwitch);
        if (canalJdJdbSwitch) {
            return;
        }

        //判null
        if (msg == null) {
            log.error("canal监听jd_jdb表业务处理失败，msg为null");
            return;
        }
        //处理消息
        //获取binlog中各字段属性值
        JSONObject binlogJson = JSONObject.parseObject(msg);
        //动作类型
        String type = binlogJson.getString("type");
        //语句类型
        Boolean isDdl = binlogJson.getBoolean("isDdl");
        //当前数据
        JSONArray dataList = binlogJson.getJSONArray("data");
        if (!isDdl && (("DELETE".equals(type))) || ("UPDATE".equals(type))) {
            //修改的字段
            JSONArray oldList = binlogJson.getJSONArray("old");
            dealJdJdbCanalMsg(dataList, type, oldList);
        }
    }

    @Override
    public void dealJdJdbCanalMsgForKnowledge(String msg) {
        //判null
        if (msg == null) {
            log.error("canal监听jd_jdb表业务处理失败，msg为null");
            return;
        }
        log.info("开始处理canal监听jd_jdb表业务，msg: {}", msg);

        //处理消息
        //获取binlog中各字段属性值
        JSONObject binlogJson = JSONObject.parseObject(msg);
        //动作类型
        String type = binlogJson.getString("type");
        log.info("当前操作类型: {}", type);
        //语句类型
        Boolean isDdl = binlogJson.getBoolean("isDdl");
        log.info("是否为DDL操作: {}", isDdl);
        //当前数据
        JSONArray dataList = binlogJson.getJSONArray("data");
        JSONArray old = binlogJson.getJSONArray("old");
        log.info("当前数据列表: {}", dataList.toJSONString());
        if (old != null) {
            log.info("旧数据列表: {}", old.toJSONString());
        }

        if (!isDdl && (("DELETE".equals(type))) || ("UPDATE".equals(type)) || ("INSERT".equals(type))) {
            log.info("进入非DDL操作处理逻辑，操作类型: {}", type);
            switch (type) {
                case "INSERT":
                    log.info("处理INSERT操作，数据列表: {}", dataList.toJSONString());
                    dataList.forEach(item -> {
                        JSONObject itemObj = (JSONObject) item;
                        JSONObject newItem = getJsonObject(itemObj);
                        String documentId = difyApiService.createDocumentByText(itemObj.getString("id"), newItem.toJSONString(), null);
                        JdJdb jdJdb = new JdJdb();
                        jdJdb.setId(itemObj.getString("id"));
                        jdJdb.setAiDocumentId(documentId);
                        jdJdbMapper.updateJdJdb(jdJdb);
                    });
                    break;
                case "UPDATE":
                    log.info("处理UPDATE操作，数据列表: {}", dataList.toJSONString());
                    dataList.forEach(item -> {
                        JSONObject itemObj = (JSONObject) item;
                        JSONObject newItem = getJsonObject(itemObj);
                        if (old.stream().anyMatch(o -> {
                            JSONObject oldsub = (JSONObject) o;
                            return (oldsub.containsKey("jdmc") || oldsub.containsKey("jddh") || oldsub.containsKey("jddz") || oldsub.containsKey("city_name"));
                        })) {
                            difyApiService.updateDocumentByText(itemObj.getString("ai_document_id"),
                                    itemObj.getString("id"), newItem.toJSONString());
                        }
                    });
                    break;
                case "DELETE":
                    log.info("处理DELETE操作，数据列表: {}", dataList.toJSONString());
                    dataList.forEach(item -> {
                        JSONObject itemObj = (JSONObject) item;
                        if (itemObj.containsKey("ai_document_id")) {
                            difyApiService.deleteDocument(itemObj.getString("ai_document_id"));
                        }
                    });
                    break;
                default:
                    log.warn("未知操作类型: {}", type);
                    break;
            }
            //修改的字段
            JSONArray oldList = binlogJson.getJSONArray("old");
            dealJdJdbCanalMsg(dataList, type, oldList);
        } else {
            log.info("非INSERT/UPDATE/DELETE操作，跳过处理");
        }
    }

    @Override
    public void dealJdJdbCanalMsgForKnowledgeWithChunk(String msg) {

        if (!isAsyncDify) {
            return;
        }
        //判null
        if (msg == null) {
            log.error("canal监听jd_jdb表业务处理失败，msg为null");
            return;
        }
        log.info("开始处理canal监听jd_jdb表业务，msg: {}", msg);

        //处理消息
        //获取binlog中各字段属性值
        JSONObject binlogJson = JSONObject.parseObject(msg);
        //动作类型
        String type = binlogJson.getString("type");
        log.info("当前操作类型: {}", type);
        //语句类型
        Boolean isDdl = binlogJson.getBoolean("isDdl");
        log.info("是否为DDL操作: {}", isDdl);
        //当前数据
        JSONArray dataList = binlogJson.getJSONArray("data");
        JSONArray old = binlogJson.getJSONArray("old");
        log.info("当前数据列表: {}", dataList.toJSONString());
        if (old != null) {
            log.info("旧数据列表: {}", old.toJSONString());
        }

        if (!isDdl && (("DELETE".equals(type))) || ("UPDATE".equals(type)) || ("INSERT".equals(type))) {
            log.info("进入非DDL操作处理逻辑，操作类型: {}", type);
            switch (type) {
                case "INSERT":
                    log.info("处理INSERT操作，数据列表: {}", dataList.toJSONString());
                    dataList.forEach(item -> {
                        JSONObject itemObj = (JSONObject) item;
                        JSONObject newItem = getJsonObject(itemObj);
                        retrieveCity(itemObj, segment -> {
                            String id = segment.getString("id");
                            String documentId = segment.getString("document_id");
                            difyApiService.AddChunksToDocument(documentId, null, newItem.toJSONString());
                        }, null);
                        String documentId = difyApiService.createDocumentByText(itemObj.getString("id"), newItem.toJSONString(), null);
                        JdJdb jdJdb = new JdJdb();
                        jdJdb.setId(itemObj.getString("id"));
                        jdJdb.setAiDocumentId(documentId);
                        jdJdbMapper.updateJdJdb(jdJdb);
                    });
                    break;
                case "UPDATE":
                    log.info("处理UPDATE操作，数据列表: {}", dataList.toJSONString());
                    dataList.forEach(item -> {
                        JSONObject itemObj = (JSONObject) item;
                        JSONObject newItem = getJsonObject(itemObj);
                        String aiDatasetId = itemObj.getString("ai_dataset_id");
                        if (old.stream().anyMatch(o -> {
                            JSONObject oldsub = (JSONObject) o;
                            return (oldsub.containsKey("jdmc") || oldsub.containsKey("jddh") || oldsub.containsKey("jddz") || oldsub.containsKey("city_name"));
                        })) {
                            retrieveChunks(itemObj, segment -> {
                                String id = segment.getString("id");
                                String documentId = segment.getString("document_id");
                                difyApiService.updateSegmentByText(id, documentId, newItem.toJSONString(), aiDatasetId);
                            }, aiDatasetId);
                        }
                    });
                    break;
                case "DELETE":
                    log.info("处理DELETE操作，数据列表: {}", dataList.toJSONString());
                    dataList.forEach(item -> {
                        JSONObject itemObj = (JSONObject) item;
                        String aiDatasetId = itemObj.getString("ai_dataset_id");
                        if (itemObj.containsKey("ai_document_id")) {
                            retrieveChunks(itemObj, segment -> {
                                String id = segment.getString("id");
                                String documentId = segment.getString("document_id");
                                difyApiService.deleteSegments(id, documentId, aiDatasetId);
                            }, aiDatasetId);
                        }
                    });
                    break;
                default:
                    log.warn("未知操作类型: {}", type);
                    break;
            }
            //修改的字段
            JSONArray oldList = binlogJson.getJSONArray("old");
            dealJdJdbCanalMsg(dataList, type, oldList);
        } else {
            log.info("非INSERT/UPDATE/DELETE操作，跳过处理");
        }
    }

    private void retrieveChunks(JSONObject itemObj, Consumer<JSONObject> call, String datasetId) {
        JSONArray objects = difyApiService.retrieveChunks(itemObj.getString("id"), datasetId);
        if (!CollectionUtils.isEmpty(objects)) {
            Optional<Object> first = objects.stream().findFirst();
            first.ifPresent(sem -> {
                JSONObject semObj = (JSONObject) sem;
                JSONObject segment = semObj.getJSONObject("segment");
                if (segment != null) {
                    call.accept(segment);

                }
            });
        }
    }

    private void retrieveCity(JSONObject itemObj, Consumer<JSONObject> call, String datasetId) {
        JSONArray objects = difyApiService.retrieveChunksForCity(itemObj.getString("city_name"), datasetId);
        if (!CollectionUtils.isEmpty(objects)) {
            Optional<Object> first = objects.stream().findFirst();
            first.ifPresent(sem -> {
                JSONObject semObj = (JSONObject) sem;
                JSONObject segment = semObj.getJSONObject("segment");
                if (segment != null) {
                    call.accept(segment);

                }
            });
        }
    }

    private static @NotNull JSONObject getJsonObject(JSONObject itemObj) {
        JSONObject newItem = new JSONObject();
        newItem.put("id", itemObj.getString("id"));
        newItem.put("name", itemObj.getString("jdmc"));
        newItem.put("city", itemObj.getString("city_name"));
        newItem.put("address", itemObj.getString("jddz"));
        newItem.put("phone", itemObj.getString("jddh"));
        newItem.put("platform", itemObj.getString("interfacePlat"));
        return newItem;
    }

    @Override
    public void dealZhJdJdbMappingCanalMsg(String msg) {

        //判null
        if (msg == null) {
            log.error("canal监听zh_jd_jdb_mapping表业务处理失败，msg为null");
            return;
        }
        //处理消息
        //获取binlog中各字段属性值
        JSONObject binlogJson = JSONObject.parseObject(msg);
        //动作类型
        String type = binlogJson.getString("type");
        //语句类型
        Boolean isDdl = binlogJson.getBoolean("isDdl");
        //当前数据
        JSONArray dataList = binlogJson.getJSONArray("data");
        if (!isDdl && (("INSERT".equals(type)) || ("DELETE".equals(type)))) {
            dealZhJdJdbMappingCanalMsg(dataList, type);
        }
    }

    @Override
    public void dealHotelInfoHsjlxyCanalMsg(String msg) {
        //判null
        if (msg == null) {
            log.error("canal监听hotel_info_hsjlxy表业务处理失败，msg为null");
            return;
        }

        //处理消息
        //获取binlog中各字段属性值
        JSONObject binlogJson = JSONObject.parseObject(msg);
        //动作类型
        String type = binlogJson.getString("type");
        //语句类型
        Boolean isDdl = binlogJson.getBoolean("isDdl");
        //当前数据
        JSONArray dataList = binlogJson.getJSONArray("data");
        if (!isDdl && (("INSERT".equals(type)))) {
            dealHotelInfoHsjlxyCanalMsg(dataList, type);
        }
    }

    @Override
    public void dealHotelCanalMsg(String msg, PlatEnum plat) {
        //判null
        if (msg == null) {
            log.error("canal监听" + plat.getTableName() + "表业务处理失败，msg为null");
            return;
        }
        //处理消息
        long start = System.currentTimeMillis();
        //获取binlog中各字段属性值
        JSONObject binlogJson = JSONObject.parseObject(msg);
        //动作类型
        String type = binlogJson.getString("type");
        //语句类型
        Boolean isDdl = binlogJson.getBoolean("isDdl");
        //当前数据
        JSONArray dataList = binlogJson.getJSONArray("data");
        JSONArray oldList = binlogJson.getJSONArray("old");
        if (!isDdl && (("INSERT".equals(type)) || ("UPDATE".equals(type)))) {
            log.info("canal监听" + plat.getTableName() + "表业务start");
            hotelPreMappingManager.dealHotelCanalMsg(dataList, plat, type, oldList);

            log.info("canal监听" + plat.getTableName() + "表业务end 耗时：{}毫秒", (System.currentTimeMillis() - start));
        }
    }

    private void dealHotelInfoHsjlxyCanalMsg(JSONArray dataList, String type) {
        for (int i = 0; i < dataList.size(); i++) {
            JSONObject data = dataList.getJSONObject(i);
            //加锁防止并发执行
            //主键id
            String id = data.getString("id");
            RLock rLock = redissonClient.getLock("canal:hotelInfoHsjlxy:lock:" + id);
            try {
                boolean res = rLock.tryLock(10, 20, TimeUnit.SECONDS);
                if (!res) {
                    log.error("处理canal同步hotel_info_hsjlxy表binlog日志信息处理加锁失败, id = {}", id);
                    continue;
                }
                //根据主键id查询红色加力酒店映射是否存在
                List<ZhJdJdbMapping> hsMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(id, Long.parseLong(PlatEnum.PLAT_HSJL.getValue()));
                if (!CollectionUtils.isEmpty(hsMappings)) {
                    //根据主键id查询红色加力酒店协议映射是否存在
                    List<ZhJdJdbMapping> hsxyMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(id, Long.parseLong(PlatEnum.PLAT_HSJL_XY.getValue()));
                    if (CollectionUtils.isEmpty(hsxyMappings)) {
                        //不存在，新插入红色加力协议映射
                        ZhJdJdbMapping zhJdJdbMapping = hsMappings.get(0);
                        zhJdJdbMapping.setId(null);
                        zhJdJdbMapping.setInterfacePlat(Long.parseLong(PlatEnum.PLAT_HSJL_XY.getValue()));
                        zhJdJdbMapping.setPlatId(id);
                        zhJdJdbMappingService.insert(zhJdJdbMapping);
                        //更改红色加力协议状态为已映射
                        hotelGnBaseMapper.updateStatusById(PlatEnum.PLAT_HSJL_XY.getTableNameSuffix(), id, 8, "system", new Date());
                    } else {
                        //存在，对比红色与协议的本地酒店id是否相同
                        ZhJdJdbMapping hsMapping = hsMappings.get(0);
                        ZhJdJdbMapping hsxyMapping = hsxyMappings.get(0);
                        if (!hsMapping.getLocalId().equals(hsxyMapping.getLocalId())) {
                            log.error("红色加力与红色加力协议映射本地酒店id不相同，请人工比对处理, id = {}", id);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("处理canal同步hotel_info_hsjlxy表binlog日志信息业务处理异常, id = {}, e = {}", id, e);
            } finally {
                if (rLock.isHeldByCurrentThread()) {
                    rLock.unlock();
                }
            }
        }
    }


    /**
     * 处理canal监听jd_jdb表业务处理
     *
     * @param dataList
     * @param type
     * @param oldList
     */
    private void dealJdJdbCanalMsg(JSONArray dataList, String type, JSONArray oldList) {
        //遍历数据
        for (int i = 0; i < dataList.size(); i++) {
            JSONObject data = dataList.getJSONObject(i);
            //主键id
            String id = data.getString("id");
            //城市id
            String cityId = data.getString("city_id");
            if (StringUtils.isEmpty(cityId)) {
                log.error("处理canal同步jd_jdb表binlog日志信息处理失败,数据缺失cityId, id = {}", id);
                continue;
            }
            //加锁防止并发执行
            RLock rLock = redissonClient.getLock("canal:JdJdb:lock:" + id);
            try {
                boolean res = rLock.tryLock(10, 20, TimeUnit.SECONDS);
                if (!res) {
                    log.error("处理canal同步jd_jdb表binlog日志信息处理加锁失败, id = {}", id);
                    continue;
                }

                //修改
                if ("UPDATE".equals(type)) {
                    //查询最新数据
                    JdJdb jdJdb = jdJdbMapper.getInterfacePlatById(id);
                    //查询该条数据是否存在平台映射
                    int count = zhJdJdbMappingService.findCountByLocalId(id);
                    if (count > 0) {
                        //存在
                        //查询需要同步es的数据
                        if (!ObjectUtils.isEmpty(jdJdb)) {
                            List<EsHotelInfo> esHotelInfos = esHotelInfoService.assemblyData(Lists.newArrayList(jdJdb));
                            hotelIntlTemplate.saveBatch(esHotelInfos);
                        }
                    }
                }

                //删除
                if ("DELETE".equals(type)) {
                    //查询本地酒店映射备份表数据
                    List<ZhJdJdbMapping> zhJdJdbMappingList = zhJdJdbMappingService.findByLocalId(id);
                    for (ZhJdJdbMapping zhJdJdbMapping : zhJdJdbMappingList) {
                        //备份
                        ZhJdJdbMappingBackup zhJdJdbMappingBackup = ZhJdJdbMappingBackup.builder().build();
                        BeanCopierUtils.copyProperties(zhJdJdbMapping, zhJdJdbMappingBackup, null);
                        zhJdJdbMappingBackup.setSavedate(new Date());
                        if (ObjectUtils.isEmpty(zhJdJdbMappingBackup.getPlatJdName())) {
                            zhJdJdbMappingBackup.setPlatJdName("");
                        }
                        if (ObjectUtils.isEmpty(zhJdJdbMappingBackup.getMappingScore())) {
                            zhJdJdbMappingBackup.setMappingScore("");
                        }
                        //加之前先查
                        Boolean checkRepeat = zhJdJdbMappingBackupService.checkRepeat(zhJdJdbMappingBackup);
                        if (checkRepeat) {
                            int result = zhJdJdbMappingBackupService.insertZhJdJdbMappingBackup(zhJdJdbMappingBackup);
                            log.info("ZhJdJdbMappingBackup酒店映射表备份:{},酒店id:{}", result >= 0, zhJdJdbMapping.getId());
                        } else {
                            log.info("ZhJdJdbMappingBackup酒店映射表重复添加跳过,酒店id:{}", zhJdJdbMapping.getId());
                        }
                    }

                    //删除本地酒店映射表数据
                    zhJdJdbMappingService.deleteZhJdJdbMappingByLocalId(id);

                    //同步删除es
                    EsHotelInfo esHotelInfo = new EsHotelInfo();
                    esHotelInfo.setHotelId(id);
                    hotelIntlTemplate.delete(esHotelInfo);
                }

            } catch (InterruptedException e) {
                log.error("处理canal同步jd_jdb表binlog日志信息处理加锁异常, id = {}", id);
            } catch (Exception e) {
                log.error("处理canal同步jd_jdb表binlog日志信息业务处理异常, id = {}, e = {}", id, e);
            } finally {
                if (rLock.isHeldByCurrentThread()) {
                    rLock.unlock();
                }
            }
        }
    }

    /**
     * 处理canal监听zh_jd_jdb_mapping表业务处理
     *
     * @param dataList
     * @param type
     */
    private void dealZhJdJdbMappingCanalMsg(JSONArray dataList, String type) {
        //遍历数据
        for (int i = 0; i < dataList.size(); i++) {
            JSONObject data = dataList.getJSONObject(i);
            //本地酒店id
            String localId = data.getString("local_id");
            //平台酒店id
            String platId = data.getString("plat_id");
            //平台
            String interfacePlat = data.getString("interface_plat");
            //加锁防止并发执行
            RLock rLock = redissonClient.getLock("canal:zhJdJdbMapping:lock:" + localId + platId + interfacePlat);
            try {
                boolean res = rLock.tryLock(10, 20, TimeUnit.SECONDS);
                if (!res) {
                    log.error("处理canal同步zh_jd_jdb_mapping表binlog日志信息处理加锁失败, id = {}", "canal:JdJdb:lock:" + localId + platId + interfacePlat);
                    continue;
                }

                //新增
                if ("INSERT".equals(type)) {
                    //新增是否执行开关
                    log.info("新增开关:{}", canalZhJdJdbMappingSwitchInsert);
                    if (canalZhJdJdbMappingSwitchInsert) {
                        return;
                    }

                    //查询本地酒店数据
                    JdJdb jdJdb = jdJdbMapper.selectJdJdbById(localId);
                    log.info("查询本地酒店数据:{}", JSONObject.toJSONString(jdJdb));
                    if (!ObjectUtils.isEmpty(jdJdb)) {
                        log.info("开始处理低价，id:{}", jdJdb.getId());
                        //城市id
//                        String cityId = jdJdb.getCityId();
//                        long suffix = Long.parseLong(cityId) % 10;
                        //是否存在该城市分表数据
//                        ZhJdJdb zhJdJdb = zhJdJdbMapper.selectZhJdJdbByLocalId(suffix, Long.parseLong(localId));
//                        if (ObjectUtils.isEmpty(zhJdJdb)) {
//                            //新增分表数据
//                            zhJdJdbMapper.insertZhJdJdb(suffix, jdJdb);
//                        }
                        //处理最低价
                        //千陶
                        if (PlatEnum.PLAT_QT.getValue().equals(interfacePlat)) {
                            log.info("开始处理千陶低价，id:{}", jdJdb.getId());
                            jdJdbService.dealListMinPriceQtNew(localId, platId);
                        }
                        //美团
                        else if (PlatEnum.PLAT_MT.getValue().equals(interfacePlat)) {
                            log.info("开始处理美团低价，id:{}", jdJdb.getId());
                            jdJdbService.dealListMinPriceMtNew(localId, Long.parseLong(platId));
                        }
                        // 康旅严选
                        else if (PlatEnum.PLAT_KLYX.getValue().equals(interfacePlat)) {
                            log.info("开始处理康旅严选低价，id:{}", jdJdb.getId());
                            hotelKlyxManager.updateMinPrice(platId);
                        }
                        //红色加力
                        else if (PlatEnum.PLAT_HSJL.getValue().equals(interfacePlat)) {
                            log.info("开始处理红色加力低价，id:{}", jdJdb.getId());
                            //查询红色加力协议酒店存不存在
                            HotelGnBase hotelGnBase = hotelGnBaseMapper.selectById(PlatEnum.PLAT_HSJL_XY.getTableNameSuffix(), platId);
                            if (!ObjectUtils.isEmpty(hotelGnBase)) {
                                //查询红色加力协议酒店mapping是否存在
                                List<ZhJdJdbMapping> hsjlxyMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(platId, Long.parseLong(PlatEnum.PLAT_HSJL_XY.getValue()));
                                if (CollectionUtils.isEmpty(hsjlxyMappings)) {
                                    List<ZhJdJdbMapping> hsjlMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(platId, Long.parseLong(PlatEnum.PLAT_HSJL.getValue()));
                                    ZhJdJdbMapping hsjlMapping = hsjlMappings.get(0);
                                    hsjlMapping.setId(null);
                                    hsjlMapping.setInterfacePlat(Long.parseLong(PlatEnum.PLAT_HSJL_XY.getValue()));
                                    zhJdJdbMappingService.insert(hsjlMapping);
                                }
                                //更新红色加力协议映射状态
                                hotelGnBaseMapper.updateStatusById(PlatEnum.PLAT_HSJL_XY.getTableNameSuffix(), platId, 8, "system", new Date());
                            }
                            jdJdbService.dealListMinPriceHSJLNew(localId, Long.parseLong(platId));
                        }
                        //红色加力协议
                        else if (PlatEnum.PLAT_HSJL_XY.getValue().equals(interfacePlat)) {
                            log.info("开始处理红色加力协议低价，id:{}", jdJdb.getId());
                            //查询红色加力酒店存不存在
                            HotelGnBase hotelGnBase = hotelGnBaseMapper.selectById(PlatEnum.PLAT_HSJL.getTableNameSuffix(), platId);
                            if (!ObjectUtils.isEmpty(hotelGnBase)) {
                                //查询红色加力酒店mapping是否存在
                                List<ZhJdJdbMapping> hsjlMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(platId, Long.parseLong(PlatEnum.PLAT_HSJL.getValue()));
                                if (CollectionUtils.isEmpty(hsjlMappings)) {
                                    //查询红色加力协议酒店mapping
                                    List<ZhJdJdbMapping> hsjlxyMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(platId, Long.parseLong(PlatEnum.PLAT_HSJL_XY.getValue()));
                                    ZhJdJdbMapping hsjlxyMapping = hsjlxyMappings.get(0);
                                    hsjlxyMapping.setId(null);
                                    hsjlxyMapping.setInterfacePlat(Long.parseLong(PlatEnum.PLAT_HSJL.getValue()));
                                    zhJdJdbMappingService.insert(hsjlxyMapping);
                                }
                                //更新红色加力映射状态
                                hotelGnBaseMapper.updateStatusById(PlatEnum.PLAT_HSJL.getTableNameSuffix(), platId, 8, "system", new Date());
                            }
                            jdJdbService.dealListMinPriceHSJLXYNew(localId, Long.parseLong(platId));
                        }
                        //差旅管家
                        else if (PlatEnum.PLAT_CLGJ.getValue().equals(interfacePlat)) {
                            log.info("开始处理差旅管家低价，id:{}", jdJdb.getId());
                            jdJdbService.dealListMinPriceCLGJNew(localId, Long.parseLong(platId));
                        } else if (PlatEnum.PLAT_KT.getValue().equals(interfacePlat)) {
                            log.info("是否处理低价开关{}，id:{}", IsDealMinPrice,jdJdb.getId());
                            //添加是否处理低价开关
                            if (IsDealMinPrice == 1) {
                                log.info("开始处理科特低价，id:{}", jdJdb.getId());
                                MinPriceReq minPriceReq = new MinPriceReq();
                                minPriceReq.setHotelIdList(Lists.newArrayList(Long.parseLong(platId)));
                                jdJdbService.dealListMinPriceCozyTime(minPriceReq, new LongAdder());
                            }
                        } else {
                            log.error("暂不支持该平台, id = {}", "canal:JdJdb:lock:" + localId + platId + interfacePlat);
                        }

                        //更改映射状态为已映射
//                        zhJdJdbMappingService.updatePlatStatus(PlatEnum.getplatEnum(interfacePlat).getTableName(), platId, 8);

                        //同步es
                        JdJdb jdJdbEs = jdJdbMapper.getInterfacePlatById(localId);
                        if (!ObjectUtils.isEmpty(jdJdbEs)) {
                            jdJdbEs.setRecommendLevel(jdJdb.getRecommendLevel());
                            List<EsHotelInfo> esHotelInfos = esHotelInfoService.assemblyData(Lists.newArrayList(jdJdbEs));
                            log.info("开始同步es, esHotelInfos:{}", JSONObject.toJSONString(esHotelInfos));
                            hotelIntlTemplate.saveBatch(esHotelInfos);
                        }
                    }
                }

                //删除
                if ("DELETE".equals(type)) {
                    if (canalZhJdJdbMappingSwitchDelete) {
                        return;
                    }

                    //查询此平台映射数据是否存在
                    List<ZhJdJdbMapping> zhJdJdbMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(platId, Long.parseLong(interfacePlat));
                    if (CollectionUtils.isEmpty(zhJdJdbMappings)) {
                        //更新该平台酒店状态为未映射
                        hotelGnBaseMapper.updateStatusById(PlatEnum.getplatEnum(interfacePlat).getTableNameSuffix(), platId, 1, "system", new Date());
                    } else {
                        hotelGnBaseMapper.updateStatusById(PlatEnum.getplatEnum(interfacePlat).getTableNameSuffix(), platId, 8, "system", new Date());
                    }
                    //查询本地酒店映射数据
                    int count = zhJdJdbMapper.findCountByLocalId(localId);
                    if (count > 0) {
                        //查询本地酒店数据
                        JdJdb jdJdb = jdJdbMapper.selectJdJdbById(localId);
                        if (!ObjectUtils.isEmpty(jdJdb)) {
                            //更新es
                            JdJdb jdJdbEs = jdJdbMapper.getInterfacePlatById(localId);
                            if (!ObjectUtils.isEmpty(jdJdbEs)) {
                                jdJdbEs.setRecommendLevel(jdJdb.getRecommendLevel());
                                List<EsHotelInfo> esHotelInfos = esHotelInfoService.assemblyData(Arrays.asList(jdJdbEs));
                                hotelIntlTemplate.saveBatch(esHotelInfos);
                            }
                        }
                        continue;
                    }
                    //查询es中就酒店数据是否存在
                    boolean exists = hotelIntlTemplate.exists(localId, EsHotelInfo.class);
                    if (exists) {
                        //同步删除es
                        EsHotelInfo esHotelInfo = new EsHotelInfo();
                        esHotelInfo.setHotelId(localId);
                        hotelIntlTemplate.delete(esHotelInfo);
                    }
                    saveZhJdJdbMappingBackup(JSON.parseObject(data.toJSONString(), ZhJdJdbMapping.class));
                }

            } catch (InterruptedException e) {
                log.error("处理canal同步zh_jd_jdb_mapping表binlog日志信息处理加锁异常, id = {}", "canal:JdJdb:lock:" + localId + platId + interfacePlat);
            } catch (Exception e) {
                log.error("处理canal同步zh_jd_jdb_mapping表binlog日志信息业务处理异常, id = {}, e = {}", "canal:JdJdb:lock:" + localId + platId + interfacePlat, e.getStackTrace());
            } finally {
                if (rLock.isHeldByCurrentThread()) {
                    rLock.unlock();
                }
            }
        }
    }

    private void saveZhJdJdbMappingBackup(ZhJdJdbMapping zhJdJdbMapping) {
        //备份
        ZhJdJdbMappingBackup zhJdJdbMappingBackup = ZhJdJdbMappingBackup.builder().build();
        BeanCopierUtils.copyProperties(zhJdJdbMapping, zhJdJdbMappingBackup, null);
        zhJdJdbMappingBackup.setSavedate(new Date());
        if (ObjectUtils.isEmpty(zhJdJdbMappingBackup.getPlatJdName())) {
            zhJdJdbMappingBackup.setPlatJdName("");
        }
        if (ObjectUtils.isEmpty(zhJdJdbMappingBackup.getMappingScore())) {
            zhJdJdbMappingBackup.setMappingScore("");
        }
        //加之前先查
        Boolean checkRepeat = zhJdJdbMappingBackupService.checkRepeat(zhJdJdbMappingBackup);
        if (checkRepeat) {
            int result = zhJdJdbMappingBackupService.insertZhJdJdbMappingBackup(zhJdJdbMappingBackup);
            log.info("ZhJdJdbMappingBackup酒店映射表备份:{},酒店id:{}", result >= 0, zhJdJdbMapping.getId());
        } else {
            log.info("ZhJdJdbMappingBackup酒店映射表重复添加跳过,酒店id:{}", zhJdJdbMapping.getId());
        }
    }
}
