package com.ltgj.supplier.map.request;


/**
 * 百度地图地点搜索请求
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
public class BaiduMapPlaceSearchRequest extends BaiduMapApiRequest {

    private String query;

    private String region;

    private boolean city_limit;

    private String tag;

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public boolean isCity_limit() {
        return city_limit;
    }

    public void setCity_limit(boolean city_limit) {
        this.city_limit = city_limit;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public boolean post() {
        return false;
    }

    @Override
    public String url() {
        return "/place/v2/suggestion";
    }


}
