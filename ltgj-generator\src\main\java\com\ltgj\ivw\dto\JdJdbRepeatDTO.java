package com.ltgj.ivw.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/2/12
 * @description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JdJdbRepeatDTO {
    /**
     * 酒店名称
     */
    private String jdmc;
    /**
     * 酒店地址
     */
    private String jddz;
    /**
     * 城市ID
     */
    private String cityId;
    /**
     * 酒店重复数量
     */
    private String duplicateCount;
}
