package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.bean.BeanUtils;
import com.ltgj.ivw.domain.HotelGnStatistics;
import com.ltgj.ivw.domain.HotelGnSupplier;
import com.ltgj.ivw.dto.HotelGnSupplierReq;
import com.ltgj.ivw.enums.HotelPlatEnum;
import com.ltgj.ivw.mapper.HotelGnStatisticsMapper;
import com.ltgj.ivw.mapper.HotelGnSupplierMapper;
import com.ltgj.ivw.mapper.ZhJdJdbMapper;
import com.ltgj.ivw.request.hotelGnStatistics.GnStatisticsRequest;
import com.ltgj.ivw.response.GnStatisticsResponse;
import com.ltgj.ivw.response.HotelGnStatisticsRes;
import com.ltgj.ivw.service.HotelGnStatisticsService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Service
public class HotelGnStatisticsServiceImpl implements HotelGnStatisticsService {

    @Resource
    private HotelGnStatisticsMapper hotelGnStatisticsMapper;

    @Resource
    private HotelGnSupplierMapper hotelGnSupplierMapper;

    @Resource
    private ZhJdJdbMapper zhJdJdbMapper;

    private static final List<HotelPlatEnum> TARGET_PLATS = Arrays.asList(
            HotelPlatEnum.HSJL,
            HotelPlatEnum.HSJLXY,
            HotelPlatEnum.KT,
            HotelPlatEnum.MT,
            HotelPlatEnum.QT,
            HotelPlatEnum.KLYX,
            HotelPlatEnum.CLGJ
    );

    @Override
    public GnStatisticsResponse selectList(GnStatisticsRequest request) {
        if (ObjectUtils.isEmpty(request.getDateType())) {
            log.error("类型不能为空");
            return null;
        }
        GnStatisticsResponse response =  new GnStatisticsResponse();
        //酒店统计
        request.setType(1);
        this.toPreDate(request);
        List<HotelGnStatisticsRes> hotelList = this.toListHotelGnStatisticsResponse(request);
        response.setHotelGnStatisticsRes(hotelList);
        //订单统计
        request.setType(2);
        List<HotelGnStatisticsRes> orderList = this.toListHotelGnStatisticsResponse(request);
        response.setHotelGnOrderStatisticsRes(orderList);
        return response;
    }

    private void toPreDate(GnStatisticsRequest request) {
        LocalDate now = LocalDate.now();
        switch (request.getDateType()) {
            case 0:
                break;
            case 1:
                request.setStartTime(LocalDate.now().withDayOfMonth(1));
                request.setEndTime(now);
                break;
            case 2:
                request.setStartTime(LocalDate.now().with(DayOfWeek.MONDAY));
                request.setEndTime(now);
                break;
            default:
                break;
        }
    }

    private List<HotelGnStatisticsRes> toListHotelGnStatisticsResponse(GnStatisticsRequest req) {
        GnStatisticsRequest request = new GnStatisticsRequest();
        BeanUtils.copyProperties(req, request);
        List<HotelGnStatisticsRes> list = this.toListHotelGnStatisticsRes(request);
        HotelGnStatisticsRes afterStatistics = this.toHotelStatistics(list);

        this.toStatisticsDate(request);
        if (ObjectUtils.isEmpty(request.getStartTime()) || ObjectUtils.isEmpty(request.getEndTime())) {
            return Collections.emptyList();
        }

        List<HotelGnStatisticsRes> collect = this.toListHotelGnStatisticsRes(request);
        HotelGnStatisticsRes preStatistics = this.toHotelStatistics(collect);
        this.toHotelStatistics(afterStatistics, preStatistics);
        list.add(afterStatistics);
        return list;
    }

    private void toHotelStatistics(HotelGnStatisticsRes afterStatistics, HotelGnStatisticsRes preStatistics) {
        Integer afterTotalNum = afterStatistics.getTotalNum();
        Integer afterSuccessNum = afterStatistics.getSuccessNum();
        Integer afterFailNum = afterStatistics.getFailNum();
        Integer preTotalNum = preStatistics.getTotalNum();
        Integer preSuccessNum = preStatistics.getSuccessNum();
        Integer preFailNum = preStatistics.getFailNum();
        afterStatistics.setAllTotalNumRate(Objects.equals(afterTotalNum,0)?"0%" :String.format("%.2f", ((afterTotalNum - preTotalNum) * 100.0 / afterTotalNum))+"%");
        afterStatistics.setAllSuccessNumRate(Objects.equals(afterSuccessNum,0)?"0%" :String.format("%.2f", ((afterSuccessNum - preSuccessNum) * 100.0 / afterSuccessNum))+"%");
        afterStatistics.setAllFailNumRate(Objects.equals(afterFailNum,0)?"0%" :String.format("%.2f", ((afterFailNum - preFailNum) * 100.0 / afterFailNum))+"%");
    }

    private List<HotelGnStatisticsRes> toListHotelGnStatisticsRes(GnStatisticsRequest request) {
        return hotelGnStatisticsMapper.selectList(request).stream().map(m -> {
            HotelGnStatisticsRes gnStatisticsResponse = new HotelGnStatisticsRes();
            BeanUtils.copyProperties(m, gnStatisticsResponse);
            gnStatisticsResponse.setDateType(request.getDateType());
            gnStatisticsResponse.setSuccessRate(Objects.equals(m.getTotalNum(),0)?"0%" :String.format("%.2f", (m.getSuccessNum() * 100.0 / m.getTotalNum()))+"%");
            gnStatisticsResponse.setFailRate(Objects.equals(m.getTotalNum(),0)?"0%" :String.format("%.2f", (m.getFailNum() * 100.0 / m.getTotalNum()))+"%");
            return gnStatisticsResponse;
        }).collect(Collectors.toList());
    }

    private HotelGnStatisticsRes toHotelStatistics(List<HotelGnStatisticsRes> hotelStatistics) {
        HotelGnStatisticsRes response = new HotelGnStatisticsRes();
        int totalTotalNum = hotelStatistics.stream().mapToInt(HotelGnStatistics::getTotalNum).sum();
        int totalSuccessNum = hotelStatistics.stream().mapToInt(HotelGnStatistics::getSuccessNum).sum();
        int totalFailNum = totalTotalNum - totalSuccessNum;
        response.setInterfaceName("汇总");
        response.setTotalNum(totalTotalNum);
        response.setSuccessNum(totalSuccessNum);
        response.setFailNum(totalFailNum);
        response.setSuccessRate(Objects.equals(totalTotalNum,0)?"0%" :String.format("%.2f", (totalSuccessNum * 100.0 / totalTotalNum))+"%");
        response.setFailRate(Objects.equals(totalTotalNum,0)?"0%" :String.format("%.2f", (totalFailNum * 100.0 / totalTotalNum))+"%");
        return response;
    }

    private void toStatisticsDate(GnStatisticsRequest request) {
        LocalDate[] localDates = null;
        switch (request.getDateType()) {
            case 0:
                // 基于起止日期的环比周期
                localDates = DateUtils.calculatePreviousPeriod(request.getStartTime(), request.getEndTime());
                break;
            case 1:
                // 本地时间的上个月
                localDates = DateUtils.getPreviousMonthPeriod();
                break;
            case 2:
                // 本地时间的上周
                localDates = DateUtils.getPreviousWeekPeriod();
                break;
            default:
                return;
        }
        request.setStartTime(localDates[0]);
        request.setEndTime(localDates[1]);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertHotelStatistics() {
        final LocalDate now = LocalDate.now();
        final LocalDate yesterday = now.minusDays(1);

        TARGET_PLATS.forEach(plat -> processStatisticsForPlat(plat, now, yesterday));
        return TARGET_PLATS.size();
    }

    private void processStatisticsForPlat(HotelPlatEnum plat, LocalDate now, LocalDate statDate) {
        // 条件查询是否已存在统计记录
        HotelGnStatistics condition = new HotelGnStatistics();
        condition.setStatisticsTime(statDate);
        condition.setType(1);
        condition.setInterfacePlat(Long.valueOf(plat.getValue()));

        int count = hotelGnStatisticsMapper.selectCount(condition);
        if (count>0) return;

        HotelGnSupplierReq queryReq = new HotelGnSupplierReq();
        queryReq.setCreateTime(String.valueOf(statDate));
        queryReq.setTableName(plat.getTableName());
        queryReq.setInterfacePlat(plat.getValue());
        log.info("各个酒店信息查询:{}", JSON.toJSONString(queryReq));
//        int pageSize = 100000;
//        int offset = 0;
//        boolean hasMoreData = true;
//        List<HotelGnSupplier> supplierList = new ArrayList<>();
//        while (hasMoreData) {
//            queryReq.setLimit(pageSize);
//            queryReq.setOffset(offset);
//            List<HotelGnSupplier> suppliers = hotelGnSupplierMapper.selectList(queryReq);
//            if (CollectionUtils.isEmpty(suppliers)) {
//                hasMoreData = false;
//            } else {
//                supplierList.addAll(suppliers);
//                offset += pageSize;
//            }
//        }
//
//        int successCount = 0;
//        if (!CollectionUtils.isEmpty(supplierList)) {
//            List<String> supplierIds = supplierList.stream()
//                    .map(HotelGnSupplier::getId)
//                    .distinct()
//                    .collect(Collectors.toList());
//            successCount =batchCount(supplierIds, Long.parseLong(plat.getValue()));
//        }
//        int total = CollectionUtils.isEmpty(supplierList) ? 0 : supplierList.size();
        int total = hotelGnSupplierMapper.selectSupplierCount(queryReq);
        int successCount = hotelGnSupplierMapper.selectSuccessCount(queryReq);
        HotelGnStatistics statRecord = buildStatRecord(plat, now, statDate, total, successCount);
        hotelGnStatisticsMapper.insert(statRecord);
    }

    public int batchCount(List<String> platIds, Long interfacePlat) {
        int BATCH_SIZE = 1000;
        int successTotal = 0;
        List<List<String>> idBatches = Lists.partition(platIds, BATCH_SIZE);
        for (List<String> batch : idBatches) {
            successTotal += zhJdJdbMapper.selectCountByPlatIdsAndInterfacePlat(batch,interfacePlat);
        }
        return successTotal;
    }

    private HotelGnStatistics buildStatRecord(HotelPlatEnum plat, LocalDate now, LocalDate statDate,
                                              int total, int success) {
        HotelGnStatistics record = new HotelGnStatistics();
        record.setInterfacePlat(Long.valueOf(plat.getValue()));
        record.setInterfaceName(plat.getName());
        record.setType(1);
        record.setTotalNum(total);
        record.setSuccessNum(success);
        record.setFailNum(total - success);
        record.setCreateYear(String.valueOf(now.getYear()));
        record.setCreateMonth(String.valueOf(now.getMonthValue()));
        record.setCreateDay(String.valueOf(now.getDayOfMonth()));
        record.setStatisticsTime(statDate);
        record.setCreateTime(new Date());
        return record;
    }
}
