package com.ltgj.ivw.service.impl;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnBaseCommonMapper;
import com.ltgj.ivw.service.HotelGnPreMappingService;
import com.ltgj.ivw.service.IHotelGnBaseService;
import com.ltgj.ivw.service.IHotelGnKetanService;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnKetan;
import com.ltgj.supplier.cozyTime.HotelCozyTimeSupplierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 科坦酒店信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Service("hotelGnKetanService")
@Slf4j
public class HotelGnKetanServiceImpl extends BaseHotelServiceImpl<HotelGnBase, HotelGnKetan> implements IHotelGnKetanService, IHotelGnBaseService {

    @Autowired
    private HotelGnBaseCommonMapper<HotelGnKetan> mapper;

    @Autowired
    private HotelCozyTimeSupplierService hotelCozyTimeSupplierService;

    @Override
    protected Class<HotelGnKetan> getEntityClass() {
        return HotelGnKetan.class;
    }

    @Override
    protected PlatEnum getPlatEnum() {
        return PlatEnum.PLAT_KT;
    }

    @Override
    protected List<HotelGnBase> doSelectListWithParams(HotelGnBase entity, Map<String, Object> queryParams) {
        return mapper.selectHotelGnBaseCommonList(entity, getEntityClass());
    }

    @Override
    protected HotelGnBase doSelectById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    // ==================== 实现抽象的数据访问方法 ====================



    @Override
    protected int doInsert(HotelGnBase entity) {
        log.debug("科坦酒店插入：{}", entity.getName());

        if (entity.getId() == null) {
            log.error("### HotelGnKetanServiceImpl.保存酒店信息异常：Hotel ID 不能为空");
            return 0;
        }

        try {
            List<Long> ids = new ArrayList<>();
            ids.add(Long.valueOf(entity.getId()));
            this.hotelCozyTimeSupplierService.addHotelByIds(ids);
        } catch (Exception e) {
            log.error("### HotelGnKetanServiceImpl.保存酒店信息异常，Hotel ID: {}", entity.getId(), e);
            return 0;
        }

        return 1;
    }

    @Override
    protected int doUpdate(HotelGnBase entity) {
        log.debug("科坦酒店更新：{}", entity.getName());
        return mapper.updateHotelGnBaseCommon(entity, getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        log.debug("科坦酒店删除，数量：{}", ids.length);
        return mapper.deleteHotelGnBaseCommonByIds(ids, getEntityClass());
    }

    // ==================== 业务逻辑定制 ====================

    @Override
    public Map<String, Object> buildDefaultQueryParams(HotelGnBase entity, HotelGnKetan searchParams) {
        Map<String, Object> params = super.buildDefaultQueryParams(entity, searchParams);
        
        // 添加科坦酒店特有的查询参数处理逻辑
        log.debug("科坦酒店特有参数处理：{}", params);
        
        return params;
    }

    @Override
    public String validateEntity(HotelGnBase entity, boolean isInsert) {
        // 先调用父类的基础验证
        String baseValidation = super.validateEntity(entity, isInsert);
        if (baseValidation != null && !baseValidation.isEmpty()) {
            return baseValidation;
        }

        return null;
    }

    @Override
    public HotelGnBase preprocessEntity(HotelGnBase entity, HotelGnKetan searchParams, boolean isInsert) {
        // 科坦酒店特有的预处理逻辑
        if (isInsert) {
            log.info("科坦酒店新增预处理：{}", entity.getName());
        } else {
            log.info("科坦酒店更新预处理：{}", entity.getName());
        }
        
        return super.preprocessEntity(entity, searchParams, isInsert);
    }

    @Override
    public String validateBeforeDelete(String[] ids, HotelGnKetan searchParams) {
        if (ids == null || ids.length == 0) {
            return "请选择要删除的科坦酒店数据";
        }
        
        return null; // 验证通过
    }

    @Override
    public void afterInsert(HotelGnBase entity, int result) {
        super.afterInsert(entity, result);
        if (result > 0) {
            log.info("科坦酒店新增成功：{}", entity.getName());
        }
    }

    @Override
    public void afterUpdate(HotelGnBase entity, int result) {
        super.afterUpdate(entity, result);
        if (result > 0) {
            log.info("科坦酒店更新成功：{}", entity.getName());
        }
    }

    @Override
    public void afterDelete(String[] ids, int result) {
        if (result > 0) {
            log.info("科坦酒店删除成功，数量：{}", result);
        }
    }

    // ==================== 导出相关方法实现 ====================

    @Override
    public List<?> exportData(HotelGnBase entity, HotelGnKetan searchParams) {
        log.info("开始获取科坦酒店导出数据，实体：{}，查询参数：{}", entity, searchParams);
        
        // 使用现有的查询方法获取数据，这里不进行分页
        List<HotelGnBase> result = this.selectListWithParams(entity, null);
        
        log.info("获取科坦酒店导出数据完成，数据量：{}", result.size());
        return result;
    }

    @Override
    public Class<?> getExportEntityClass() {
        return HotelGnKetan.class;
    }

    @Override
    public String getExportFileName(HotelGnKetan searchParams) {
        return "科坦酒店信息数据";
    }

    @Override
    public HotelGnBase selectHotelGnBaseById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    public void updateHotelGnBase(HotelGnBase hotelGnBase) {
        this.update(hotelGnBase);
    }
}
