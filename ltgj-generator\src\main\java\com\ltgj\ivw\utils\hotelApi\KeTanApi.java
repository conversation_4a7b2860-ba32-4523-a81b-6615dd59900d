package com.ltgj.ivw.utils.hotelApi;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.MinPriceReqV2;
import com.ltgj.ivw.domain.ketan.HotelMapping;
import com.ltgj.ivw.domain.response.QtMinPriceResponse;
import com.ltgj.ivw.utils.ConstantList;
import com.ltgj.ivw.utils.MyTools;
import com.ltgj.ivw.utils.ResumeDownload;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

//文件导入
@Slf4j
@Component
public class KeTanApi {

    @Value("${keTan.partnerCode:1064}")
    private String partnerCode ;

    @Value("${keTan.secretKey:XeQcsibSTiZPT}")
    private String secretKey;

    @Value("${keTan.baseUrl:http://openapi-test.cozy-time.com:8135}")
    private String baseUrl;

    @Value("${keTan.hotelAndRoomMappingUrl:/hotel_content_api/2.0/hotelAndRoomMapping}")
    private String hotelAndRoomMappingUrl;

    @Autowired
    private OkHttpClient okHttpClient;


    public static Map getHeaderMap(String partnerCode, Long timeStamp) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("partnerCode", partnerCode);
        headerMap.put("timeStamp", timeStamp + "");
        return headerMap;
    }

    /**
     * 根据 partnerCode、secretKey 和 timestamp 生成 x-Signature
     *
     * @param partnerCode 合作方编号
     * @param secretKey   密钥
     * @param timestamp   时间戳 (毫秒)
     * @return x-Signature 签名值
     */
    public static String generateXSignature(String partnerCode, String secretKey, long timestamp) {
        try {
            // 拼接字符串
            String input = partnerCode + secretKey + timestamp;

            // 获取 SHA-512 MessageDigest 实例
            MessageDigest md = MessageDigest.getInstance("SHA-512");

            // 执行哈希计算
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将字节数组转换为 Base64 编码字符串
            // 将字节数组转换为 Hex 字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // 补零
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成 x-Signature 签名失败", e);
        }
    }
    public Request buildRequest(String urlPath, String body) {
        long timeMillis = System.currentTimeMillis();

        Map<String, String> headerMap = getHeaderMap(partnerCode, timeMillis);
        headerMap.put("x-Signature", generateXSignature(partnerCode, secretKey, timeMillis));

        MediaType mediaType = MediaType.parse("application/json");
        RequestBody requestBody = RequestBody.create(mediaType, body);
        String url = baseUrl + urlPath;
        log.info("科坦酒店api, 请求url={}, 请求header={}, 请求参数={}", url, JSONObject.toJSONString(headerMap), body);
        return new Request.Builder().post(requestBody).url(url).headers(Headers.of(headerMap)).build();
    }

    public JSONObject sendRequest(String urlPath, String requestBody) {
        try {
            //执行同步请求，获取Response对象
            Response response = okHttpClient.newCall(buildRequest(urlPath, requestBody)).execute();
            ResponseBody body = response.body();
            if (ObjectUtils.isEmpty(body)) {
                log.info("科坦酒店返回null, urlPath = {}, requestBody = {}", urlPath, requestBody);
                return null;
            }
            String resultStr = body.string();
            log.info("科坦酒店返回 = {}, urlPath = {}, requestBody = {}",resultStr, urlPath, requestBody);
            if (StringUtils.isEmpty(resultStr)) {
                return null;
            }
            JSONObject json = JSONObject.parseObject(resultStr);
            if (json.getIntValue("resultCode") != 0) {
                log.info("科坦酒店返回异常,json = {}, urlPath = {}, requestBody = {}", json.toJSONString(), urlPath, requestBody);
                return null;
            }

            return json;
        } catch (Exception e) {
            log.error("科坦酒店返回异常 数据异常 errorMessage:{}, error:{}, urlPath = {}, requestBody = {}", e.getMessage(), e, urlPath, requestBody);
        }
        return null;
    }


    public List<HotelMapping> getHotelAndRoomMapping(List<String> hotelIds) {
        JSONObject param = new JSONObject();
        param.put("hotelIds", hotelIds);

        JSONObject result = this.sendRequest(hotelAndRoomMappingUrl, param.toJSONString());
        if (result != null){
            JSONArray jsonArray = result.getJSONArray("hotelMappingList");
            return JSONObject.parseArray(jsonArray.toJSONString(), HotelMapping.class);
        }
        return new ArrayList<>();
    }
}
