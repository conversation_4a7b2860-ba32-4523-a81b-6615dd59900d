package com.ltgj.ivw.domain;

import java.util.Date;
import lombok.Data;

/**
 * Table: hotel_info_ketan_pre_mapping
 */
@Data
public class HotelInfoKetanPreMapping {
    /**
     * Column: id
     * Type: VARCHAR(20)
     * Remark: 主键
     */
    private Long id;

    /**
     * Column: source_id
     * Type: VARCHAR(20)
     * Remark: 原酒店id
     */
    private String sourceId;

    /**
     * Column: target_id
     * Type: VARCHAR(20)
     * Remark: 目标酒店id
     */
    private String targetId;

    /**
     * Column: target_plat
     * Type: VARCHAR(20)
     * Remark: 目标酒店平台编号
     */
    private String targetPlat;

    /**
     * Column: mapping_type
     * Type: INT
     * Default value: 1
     * Remark: 映射类型 1：艺龙id映射  2：其他
     */
    private Integer mappingType;

    /**
     * Column: mapping_state
     * Type: INT
     * Default value: 0
     * Remark: 映射结果 0：未映射 1：已映射
     */
    private Integer mappingState;

    /**
     * Column: mapping_reason
     * Type: VARCHAR(255)
     * Remark: 映射原因
     */
    private String mappingReason;

    /**
     * Column: mapping_score
     * Type: VARCHAR(255)
     * Remark: 映射分数
     */
    private String mappingScore;

    /**
     * Column: create_time
     * Type: DATETIME
     * Remark: 创建时间
     */
    private Date createTime;

    /**
     * Column: update_time
     * Type: DATETIME
     * Remark: 最后更新时间
     */
    private Date updateTime;

    /**
     * Column: create_by
     * Type: VARCHAR(64)
     * Remark: 创建人
     */
    private String createBy;

    /**
     * Column: update_by
     * Type: VARCHAR(64)
     * Remark: 更新人
     */
    private String updateBy;

    /**
     * Column: remark
     * Type: VARCHAR(128)
     * Remark: 备注
     */
    private String remark;

    /**
     * Column: is_delete
     * Type: INT
     * Default value: 0
     * Remark: 删除标识
     */
    private Integer isDelete;
}