package com.ltgj.ivw.mapper;


import com.ltgj.ivw.domain.HotelGnOrderDetailStatistics;
import com.ltgj.ivw.request.hotelGnStatistics.GnOrderDetailStatisticsRequest;
import com.ltgj.ivw.request.hotelGnStatistics.GnStatisticsRequest;
import com.ltgj.ivw.request.hotelGnStatistics.HotelGnOrderDetailStatisticsRequest;
import com.ltgj.ivw.response.HotelGnOrderDetailStatisticsRes;
import org.springframework.beans.PropertyValues;

import java.util.List;

public interface HotelGnOrderDetailStatisticsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_order_detail_statistics
     *
     * @mbg.generated 2025/06/11 16:16
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_order_detail_statistics
     *
     * @mbg.generated 2025/06/11 16:16
     */
    int insert(HotelGnOrderDetailStatistics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_order_detail_statistics
     *
     * @mbg.generated 2025/06/11 16:16
     */
    int insertSelective(HotelGnOrderDetailStatistics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_order_detail_statistics
     *
     * @mbg.generated 2025/06/11 16:16
     */
    HotelGnOrderDetailStatistics selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_order_detail_statistics
     *
     * @mbg.generated 2025/06/11 16:16
     */
    int updateByPrimaryKeySelective(HotelGnOrderDetailStatistics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_order_detail_statistics
     *
     * @mbg.generated 2025/06/11 16:16
     */
    int updateByPrimaryKey(HotelGnOrderDetailStatistics record);

    List<HotelGnOrderDetailStatisticsRes> selectList(GnOrderDetailStatisticsRequest request);

    int selectCount(GnOrderDetailStatisticsRequest request);

    int insertList(List<HotelGnOrderDetailStatistics> collect);

    int selectCountOrderDetail(GnStatisticsRequest request);

    List<HotelGnOrderDetailStatisticsRes> selectByPlatIdsAndInterfacePlat(HotelGnOrderDetailStatisticsRequest requests);

    Integer listCount(GnOrderDetailStatisticsRequest request);

    List<HotelGnOrderDetailStatisticsRes> selectByLocalIdsAndInterfacePlat(HotelGnOrderDetailStatisticsRequest requests);
}
