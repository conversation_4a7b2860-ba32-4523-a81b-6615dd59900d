package com.ltgj.ivw.controller;

import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.dto.CityLevelQueryReq;
import com.ltgj.ivw.dto.ParentLookupReq;
import com.ltgj.ivw.service.IHotelInfoElongService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 酒店城市四级联动数据Controller
 * 
 * <AUTHOR>
 * @date 2024-01-19
 */
@RestController
@RequestMapping("/ivw/cityHierarchy")
@Slf4j
public class HotelCityHierarchyController extends BaseController {
    
    @Autowired
    private IHotelInfoElongService hotelInfoElongService;
    
    /**
     * 获取四级联动城市数据（国家->省份->城市->区县）
     * 
     * @param platformId 平台ID（可选参数）
     * @return 四级联动数据结构
     */
    @GetMapping("/getCityHierarchy")
    public AjaxResult getCityHierarchy(@RequestParam(value = "platformId", required = false) String platformId) {
        log.info("接收到获取四级联动城市数据请求，平台ID：{}", platformId);
        
        try {
            return hotelInfoElongService.getCityHierarchy(platformId);
        } catch (Exception e) {
            log.error("获取四级联动城市数据异常，平台ID：{}，错误信息：{}", platformId, e.getMessage(), e);
            return AjaxResult.error("获取城市数据异常：" + e.getMessage());
        }
    }
    
    /**
     * 获取指定平台的四级联动城市数据
     * 
     * @param platformId 平台ID
     * @return 四级联动数据结构
     */
    @GetMapping("/getCityHierarchy/{platformId}")
    public AjaxResult getCityHierarchyByPlatform(@PathVariable("platformId") String platformId) {
        log.info("接收到获取指定平台四级联动城市数据请求，平台ID：{}", platformId);
        
        try {
            return hotelInfoElongService.getCityHierarchy(platformId);
        } catch (Exception e) {
            log.error("获取指定平台四级联动城市数据异常，平台ID：{}，错误信息：{}", platformId, e.getMessage(), e);
            return AjaxResult.error("获取城市数据异常：" + e.getMessage());
        }
    }

    /**
     * 分级查询城市数据API
     * 根据传入的参数层级返回下一级数据
     * - 无参数：返回所有国家
     * - 传入国家：返回该国家下的省份
     * - 传入国家+省份：返回该省份下的城市
     * - 传入国家+省份+城市：返回该城市下的区县
     */
    @PostMapping("/getCityDataByLevel")
    public AjaxResult getCityDataByLevel(@RequestBody CityLevelQueryReq request) {
        log.info("接收到分级查询城市数据请求，参数：{}", request);
        
        try {
            return hotelInfoElongService.getCityDataByLevel(request);
        } catch (Exception e) {
            log.error("分级查询城市数据异常，参数：{}，错误信息：{}", request, e.getMessage(), e);
            return AjaxResult.error("查询城市数据异常：" + e.getMessage());
        }
    }

    /**
     * 分级查询城市数据API - GET方式
     * 通过查询参数方式传递条件
     */
    @GetMapping("/getCityDataByLevel")
    public AjaxResult getCityDataByLevelGet(
            @RequestParam(value = "platformId", required = false) String platformId,
            @RequestParam(value = "countryId", required = false) String countryId,
            @RequestParam(value = "provinceId", required = false) String provinceId,
            @RequestParam(value = "cityId", required = false) String cityId) {
        
        CityLevelQueryReq request = new CityLevelQueryReq();
        request.setPlatformId(platformId);
        request.setCountryId(countryId);
        request.setProvinceId(provinceId);
        request.setCityId(cityId);
        
        log.info("接收到分级查询城市数据请求(GET)，参数：{}", request);
        
        try {
            return hotelInfoElongService.getCityDataByLevel(request);
        } catch (Exception e) {
            log.error("分级查询城市数据异常(GET)，参数：{}，错误信息：{}", request, e.getMessage(), e);
            return AjaxResult.error("查询城市数据异常：" + e.getMessage());
        }
    }

    /**
     * 获取国家列表
     * 快捷方法，等同于不传任何参数的分级查询
     */
    @GetMapping("/getCountries")
    public AjaxResult getCountries(@RequestParam(value = "platformId", required = false) String platformId) {
        log.info("接收到获取国家列表请求，平台ID：{}", platformId);
        
        try {
            CityLevelQueryReq request = new CityLevelQueryReq();
            request.setPlatformId(platformId);
            return hotelInfoElongService.getCityDataByLevel(request);
        } catch (Exception e) {
            log.error("获取国家列表异常，平台ID：{}，错误信息：{}", platformId, e.getMessage(), e);
            return AjaxResult.error("获取国家列表异常：" + e.getMessage());
        }
    }

    /**
     * 根据国家ID获取省份列表
     */
    @GetMapping("/getProvinces/{countryId}")
    public AjaxResult getProvinces(
            @PathVariable("countryId") String countryId,
            @RequestParam(value = "platformId", required = false) String platformId) {
        log.info("接收到获取省份列表请求，国家ID：{}，平台ID：{}", countryId, platformId);
        
        try {
            CityLevelQueryReq request = new CityLevelQueryReq();
            request.setPlatformId(platformId);
            request.setCountryId(countryId);
            return hotelInfoElongService.getCityDataByLevel(request);
        } catch (Exception e) {
            log.error("获取省份列表异常，国家ID：{}，平台ID：{}，错误信息：{}", countryId, platformId, e.getMessage(), e);
            return AjaxResult.error("获取省份列表异常：" + e.getMessage());
        }
    }

    /**
     * 根据国家ID和省份ID获取城市列表
     */
    @GetMapping("/getCities/{countryId}/{provinceId}")
    public AjaxResult getCities(
            @PathVariable("countryId") String countryId,
            @PathVariable("provinceId") String provinceId,
            @RequestParam(value = "platformId", required = false) String platformId) {
        log.info("接收到获取城市列表请求，国家ID：{}，省份ID：{}，平台ID：{}", countryId, provinceId, platformId);
        
        try {
            CityLevelQueryReq request = new CityLevelQueryReq();
            request.setPlatformId(platformId);
            request.setCountryId(countryId);
            request.setProvinceId(provinceId);
            return hotelInfoElongService.getCityDataByLevel(request);
        } catch (Exception e) {
            log.error("获取城市列表异常，国家ID：{}，省份ID：{}，平台ID：{}，错误信息：{}", 
                     countryId, provinceId, platformId, e.getMessage(), e);
            return AjaxResult.error("获取城市列表异常：" + e.getMessage());
        }
    }

    /**
     * 根据国家ID、省份ID和城市ID获取区县列表
     */
    @GetMapping("/getDistricts/{countryId}/{provinceId}/{cityId}")
    public AjaxResult getDistricts(
            @PathVariable("countryId") String countryId,
            @PathVariable("provinceId") String provinceId,
            @PathVariable("cityId") String cityId,
            @RequestParam(value = "platformId", required = false) String platformId) {
        log.info("接收到获取区县列表请求，国家ID：{}，省份ID：{}，城市ID：{}，平台ID：{}", 
                countryId, provinceId, cityId, platformId);
        
        try {
            CityLevelQueryReq request = new CityLevelQueryReq();
            request.setPlatformId(platformId);
            request.setCountryId(countryId);
            request.setProvinceId(provinceId);
            request.setCityId(cityId);
            return hotelInfoElongService.getCityDataByLevel(request);
        } catch (Exception e) {
            log.error("获取区县列表异常，国家ID：{}，省份ID：{}，城市ID：{}，平台ID：{}，错误信息：{}", 
                     countryId, provinceId, cityId, platformId, e.getMessage(), e);
            return AjaxResult.error("获取区县列表异常：" + e.getMessage());
        }
    }

    /**
     * 清除缓存
     *
     * @return
     */
    @GetMapping("/clearCache")
    public AjaxResult clearCache() {
        hotelInfoElongService.clearCache();
        return AjaxResult.success("清除缓存成功");
    }

    /**
     * 获取上级行政区划信息的通用方法
     * 支持两种查询模式：
     * 1. 通过城市ID获取上级省份信息
     * 2. 通过区县ID获取上级城市信息
     * 
     * @return 上级行政区划信息
     */
    @PostMapping("/getParentInfo")
    public AjaxResult getParentInfo(@RequestBody ParentLookupReq request) {
        log.info("接收到获取上级行政区划请求，参数：{}", request);
        
        try {
            // 参数校验
            if (request.getCountryId() == null || request.getCountryId().trim().isEmpty()) {
                return AjaxResult.error("国家编码不能为空");
            }
            if (request.getChildId() == null || request.getChildId().trim().isEmpty()) {
                return AjaxResult.error("子级ID不能为空");
            }
            if (request.getChildType() == null || request.getChildType().trim().isEmpty()) {
                return AjaxResult.error("子级类型不能为空");
            }
            
            // 验证childType取值
            if (!"city".equals(request.getChildType()) && !"district".equals(request.getChildType())) {
                return AjaxResult.error("子级类型只能是'city'或'district'");
            }
            
            return hotelInfoElongService.getParentInfo(request);
        } catch (Exception e) {
            log.error("获取上级行政区划异常，参数：{}，错误信息：{}", request, e.getMessage(), e);
            return AjaxResult.error("获取上级行政区划异常：" + e.getMessage());
        }
    }

    /**
     * 通过城市ID获取上级省份信息 - GET方式便捷接口
     * 
     * @param countryId 国家编码
     * @param cityId 城市ID
     * @param platformId 平台ID（可选）
     * @return 省份信息
     */
    @GetMapping("/getProvinceByCity/{countryId}/{cityId}")
    public AjaxResult getProvinceByCity(
            @PathVariable("countryId") String countryId,
            @PathVariable("cityId") String cityId,
            @RequestParam(value = "platformId", required = false) String platformId) {
        
        log.info("接收到通过城市获取省份请求，国家ID：{}，城市ID：{}，平台ID：{}", countryId, cityId, platformId);
        
        try {
            ParentLookupReq request = new ParentLookupReq();
            request.setPlatformId(platformId);
            request.setCountryId(countryId);
            request.setChildId(cityId);
            request.setChildType("city");
            
            return hotelInfoElongService.getParentInfo(request);
        } catch (Exception e) {
            log.error("通过城市获取省份异常，国家ID：{}，城市ID：{}，平台ID：{}，错误信息：{}", 
                     countryId, cityId, platformId, e.getMessage(), e);
            return AjaxResult.error("获取省份信息异常：" + e.getMessage());
        }
    }

    /**
     * 通过区县ID获取上级城市信息 - GET方式便捷接口
     * 
     * @param countryId 国家编码
     * @param districtId 区县ID
     * @param platformId 平台ID（可选）
     * @return 城市信息
     */
    @GetMapping("/getCityByDistrict/{countryId}/{districtId}")
    public AjaxResult getCityByDistrict(
            @PathVariable("countryId") String countryId,
            @PathVariable("districtId") String districtId,
            @RequestParam(value = "platformId", required = false) String platformId) {
        
        log.info("接收到通过区县获取城市请求，国家ID：{}，区县ID：{}，平台ID：{}", countryId, districtId, platformId);
        
        try {
            ParentLookupReq request = new ParentLookupReq();
            request.setPlatformId(platformId);
            request.setCountryId(countryId);
            request.setChildId(districtId);
            request.setChildType("district");
            
            return hotelInfoElongService.getParentInfo(request);
        } catch (Exception e) {
            log.error("通过区县获取城市异常，国家ID：{}，区县ID：{}，平台ID：{}，错误信息：{}", 
                     countryId, districtId, platformId, e.getMessage(), e);
            return AjaxResult.error("获取城市信息异常：" + e.getMessage());
        }
    }

    /**
     * 清除上级缓存
     *
     * @return
     */
    @GetMapping("/clearCacheForParent")
    public AjaxResult clearCacheForParent() {
        hotelInfoElongService.clearCacheForParent();
        return AjaxResult.success("清除缓存成功");
    }

} 