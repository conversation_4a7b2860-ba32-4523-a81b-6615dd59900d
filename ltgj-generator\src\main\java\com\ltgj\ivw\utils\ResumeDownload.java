package com.ltgj.ivw.utils;

import com.ltgj.ivw.utils.hotelApi.DaolvInfoZipStatus;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;

import java.io.*;
import java.net.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ResumeDownload {
    private static final int BUFFER_SIZE = 1024 * 16; // 16KB

    public static void main(String[] args) {
        String remoteFilePath = "http://www.qiantaohotel.com:7001/elonghotels.zip"; // 远程文件路径
        downFile(remoteFilePath, "Qiantao");
    }

    //文件下载，断点续传
    public static void downFile(String url, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH);
            if(!file.exists()){
                file.mkdirs();
            }
            log.info("下载文件准备写入文件: {}", ConstantList.DOWN_PATH+fileName);
            File downloadFile = new File(ConstantList.DOWN_PATH+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                log.info("下载文件获取文件大小: {}", startByte);
                if(channel.indexOf("Qiantao") != -1){
                }else if(channel.indexOf("Yinglv") != -1){
                }else if(channel.indexOf("Daolv") != -1){
                    if(startByte == HotelUpdateStatus.dlFileSize){
                        HotelUpdateStatus.statusDL = 2;
                        HotelUpdateStatus.dlFileSizeGet = HotelUpdateStatus.dlFileSize;
                        return;
                    }
                }
            }
            log.info("下载文件url: {}", url);
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH+fileName);
            try {
                if (channel.indexOf("Qiantao") != -1) {
                    HotelUpdateStatus.qtFileSizeGet = Files.size(filePath);
                } else if (channel.indexOf("Yinglv") != -1) {
                    HotelUpdateStatus.ylFileSizeGet = Files.size(filePath);
                } else if (channel.indexOf("Daolv") != -1) {
                    HotelUpdateStatus.dlFileSizeGet = Files.size(filePath);
                }
            } catch (Exception e) {
                log.error("更新大小: ", e);
            }
            switch (channel){
                case "Qiantao":
                    if(HotelUpdateStatus.qtFileSize <= HotelUpdateStatus.qtFileSizeGet) {
                        HotelUpdateStatus.statusQT = 2;
                        raf.close();
                        inputStream.close();
                        return;
                    }
                    break;
                case "Yinglv":
                    if(HotelUpdateStatus.ylFileSize <= HotelUpdateStatus.ylFileSizeGet) {
                        HotelUpdateStatus.statusYL = 2;
                        raf.close();
                        inputStream.close();
                        return;
                    }
                    break;
                case "Daolv":
                    if(HotelUpdateStatus.dlFileSize <= HotelUpdateStatus.dlFileSizeGet) {
                        HotelUpdateStatus.statusDL = 2;
                        raf.close();
                        inputStream.close();
                        return;
                    }
                    break;
            }
            log.info("准备写入数据: {}", url);
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("Qiantao") != -1){
                    HotelUpdateStatus.qtFileSizeGet += BUFFER_SIZE;
                }else if(channel.indexOf("Yinglv") != -1){
                    HotelUpdateStatus.ylFileSizeGet += BUFFER_SIZE;
                }else if(channel.indexOf("Daolv") != -1){
                    HotelUpdateStatus.dlFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            log.info("写入数据完成");
            switch (channel){
                case "Qiantao":
                    HotelUpdateStatus.statusQT = 2;
                    break;
                case "Yinglv":
                    HotelUpdateStatus.statusYL = 2;
                    break;
                case "Daolv":
                    HotelUpdateStatus.statusDL = 2;
                    break;
            }
        } catch (Exception e) {
            log.error("下载异常中断: ", e);
//            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
//                    "log.txt");
            switch (channel){
                case "Qiantao":
                    HotelUpdateStatus.statusQT = 0;
                    break;
                case "Yinglv":
                    HotelUpdateStatus.statusYL = 0;
                    break;
                case "Daolv":
                    HotelUpdateStatus.statusDL = 0;
                    break;
            }
        }
    }

    private static long getFileSize(File file) {
        long size = 0;
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            size = raf.length();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return size;
    }

    public static long getRemoteFileSize(String fileUrl) {
        try {
            log.info("获取远程文件大小: {}", fileUrl);
            URL url = new URL(fileUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.connect();
            int responseCode = connection.getResponseCode();
            log.info("获取远程文件响应code: {}", responseCode);
            if (responseCode == HttpURLConnection.HTTP_OK) {
                String contentLength = connection.getHeaderField("Content-Length");
                if (contentLength != null) {
                    log.info("获取远程文件响应文件长度: {}", contentLength);
                    return Long.parseLong(contentLength);
                }
            }
        }catch (Exception e){
            log.error("获取远程文件大小失败: ", e);
        }
        return -1l; // 返回-1表示获取文件大小失败
    }



    /**
     * 道旅酒店列表静态资源下载
     * @param listDownUrl
     * @param channel
     */
    public static void listDownFile(String listDownUrl, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH_DAOLV_INFO);
            if(!file.exists()){
                file.mkdirs();
            }
            File downloadFile = new File(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                if(channel.indexOf("ListDaolv") != -1){
                    if(startByte == DaolvInfoZipStatus.dlListFileSize){
                        DaolvInfoZipStatus.listStage = 2;
                        DaolvInfoZipStatus.dlListFileSizeGet = DaolvInfoZipStatus.dlListFileSize;
                        return;
                    }
                }
            }
            HttpURLConnection connection = (HttpURLConnection) new URL(listDownUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            try {
                if(channel.indexOf("ListDaolv") != -1){
                    DaolvInfoZipStatus.dlListFileSizeGet = Files.size(filePath);
                }
            }catch (Exception e){}
            if(DaolvInfoZipStatus.dlListFileSize <= DaolvInfoZipStatus.dlListFileSizeGet) {
                DaolvInfoZipStatus.listStage = 2;
                raf.close();
                inputStream.close();
                return;
            }
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("ListDaolv") != -1){
                    DaolvInfoZipStatus.dlListFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            DaolvInfoZipStatus.listStage = 2;
        } catch (Exception e) {
            log.error("道旅执行异常:", e);
            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
                "log.txt");
            DaolvInfoZipStatus.listStage = 0;
        }
    }

    /**
     * 道旅酒店简介静态资源下载
     * @param descriptionDownUrl
     * @param channel
     */
    public static void descriptionDownFile(String descriptionDownUrl, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH_DAOLV_INFO);
            if(!file.exists()){
                file.mkdirs();
            }
            File downloadFile = new File(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                if(channel.indexOf("DescriptionDaolv") != -1){
                    if(startByte == DaolvInfoZipStatus.dlDescriptionFileSize){
                        DaolvInfoZipStatus.descriptionStage = 2;
                        DaolvInfoZipStatus.dlDescriptionFileSizeGet = DaolvInfoZipStatus.dlDescriptionFileSize;
                        return;
                    }
                }
            }
            HttpURLConnection connection = (HttpURLConnection) new URL(descriptionDownUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            try {
                if(channel.indexOf("DescriptionDaolv") != -1){
                    DaolvInfoZipStatus.dlDescriptionFileSizeGet = Files.size(filePath);
                }
            }catch (Exception e){}
            if(DaolvInfoZipStatus.dlDescriptionFileSize <= DaolvInfoZipStatus.dlDescriptionFileSizeGet) {
                DaolvInfoZipStatus.descriptionStage = 2;
                raf.close();
                inputStream.close();
                return;
            }
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("DescriptionDaolv") != -1){
                    DaolvInfoZipStatus.dlDescriptionFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            DaolvInfoZipStatus.descriptionStage = 2;
        } catch (Exception e) {
            e.printStackTrace();
            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
                "log.txt");
            DaolvInfoZipStatus.descriptionStage = 0;
            log.error("下载异常中断 errorMessage:{}", e.getMessage(), e);
        }
    }

    /**
     * 道旅酒店设施静态资源下载
     * @param facilitiesDownUrl
     * @param channel
     */
    public static void facilitiesDownFile(String facilitiesDownUrl, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH_DAOLV_INFO);
            if(!file.exists()){
                file.mkdirs();
            }
            File downloadFile = new File(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                if(channel.indexOf("FacilitiesDaolv") != -1){
                    if(startByte == DaolvInfoZipStatus.dlFacilitiesFileSize){
                        DaolvInfoZipStatus.facilitiesStage = 2;
                        DaolvInfoZipStatus.dlFacilitiesFileSizeGet = DaolvInfoZipStatus.dlFacilitiesFileSize;
                        return;
                    }
                }
            }
            HttpURLConnection connection = (HttpURLConnection) new URL(facilitiesDownUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            try {
                if(channel.indexOf("FacilitiesDaolv") != -1){
                    DaolvInfoZipStatus.dlFacilitiesFileSizeGet = Files.size(filePath);
                }
            }catch (Exception e){}
            if(DaolvInfoZipStatus.dlFacilitiesFileSize <= DaolvInfoZipStatus.dlFacilitiesFileSizeGet) {
                DaolvInfoZipStatus.facilitiesStage = 2;
                raf.close();
                inputStream.close();
                return;
            }
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("FacilitiesDaolv") != -1){
                    DaolvInfoZipStatus.dlFacilitiesFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            DaolvInfoZipStatus.facilitiesStage = 2;
        } catch (Exception e) {
            e.printStackTrace();
            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
                "log.txt");
            DaolvInfoZipStatus.facilitiesStage = 0;
        }
    }

    /**
     * 道旅酒店政策静态资源下载
     * @param policyDownUrl
     * @param channel
     */
    public static void policyDownFile(String policyDownUrl, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH_DAOLV_INFO);
            if(!file.exists()){
                file.mkdirs();
            }
            File downloadFile = new File(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                if(channel.indexOf("PolicyDaolv") != -1){
                    if(startByte == DaolvInfoZipStatus.dlPolicyFileSize){
                        DaolvInfoZipStatus.policyStage = 2;
                        DaolvInfoZipStatus.dlPolicyFileSizeGet = DaolvInfoZipStatus.dlPolicyFileSize;
                        return;
                    }
                }
            }
            HttpURLConnection connection = (HttpURLConnection) new URL(policyDownUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            try {
                if(channel.indexOf("PolicyDaolv") != -1){
                    DaolvInfoZipStatus.dlPolicyFileSizeGet = Files.size(filePath);
                }
            }catch (Exception e){}
            if(DaolvInfoZipStatus.dlPolicyFileSize <= DaolvInfoZipStatus.dlPolicyFileSizeGet) {
                DaolvInfoZipStatus.policyStage = 2;
                raf.close();
                inputStream.close();
                return;
            }
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("PolicyDaolv") != -1){
                    DaolvInfoZipStatus.dlPolicyFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            DaolvInfoZipStatus.policyStage = 2;
        } catch (Exception e) {
            log.error("道旅酒店政策静态资源下载异常");
            e.printStackTrace();
            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
                "log.txt");
            DaolvInfoZipStatus.policyStage = 0;
        }
    }

    /**
     * 道旅酒店图片静态资源下载
     * @param hotelImageDownUrl
     * @param channel
     */
    public static void hotelImageDownFile(String hotelImageDownUrl, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH_DAOLV_INFO);
            if(!file.exists()){
                file.mkdirs();
            }
            File downloadFile = new File(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                if(channel.indexOf("HotelImageDaolv") != -1){
                    if(startByte == DaolvInfoZipStatus.dlHotelImageFileSize){
                        DaolvInfoZipStatus.hotelImageStage = 2;
                        DaolvInfoZipStatus.dlHotelImageFileSizeGet = DaolvInfoZipStatus.dlHotelImageFileSize;
                        return;
                    }
                }
            }
            HttpURLConnection connection = (HttpURLConnection) new URL(hotelImageDownUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            try {
                if(channel.indexOf("HotelImageDaolv") != -1){
                    DaolvInfoZipStatus.dlHotelImageFileSizeGet = Files.size(filePath);
                }
            }catch (Exception e){}
            if(DaolvInfoZipStatus.dlHotelImageFileSize <= DaolvInfoZipStatus.dlHotelImageFileSizeGet) {
                DaolvInfoZipStatus.hotelImageStage = 2;
                raf.close();
                inputStream.close();
                return;
            }
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("HotelImageDaolv") != -1){
                    DaolvInfoZipStatus.dlHotelImageFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            DaolvInfoZipStatus.hotelImageStage = 2;
        } catch (Exception e) {
            e.printStackTrace();
            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
                "log.txt");
            DaolvInfoZipStatus.hotelImageStage = 0;
        }
    }

    /**
     * 道旅酒店房型信息静态资源下载
     * @param roomInfoDownUrl
     * @param channel
     */
    public static void roomInfoDownFile(String roomInfoDownUrl, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH_DAOLV_INFO);
            if(!file.exists()){
                file.mkdirs();
            }
            File downloadFile = new File(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                if(channel.indexOf("RoomInfoDaolv") != -1){
                    if(startByte == DaolvInfoZipStatus.dlRoomInfoFileSize){
                        DaolvInfoZipStatus.roomInfoStage = 2;
                        DaolvInfoZipStatus.dlRoomInfoFileSizeGet = DaolvInfoZipStatus.dlRoomInfoFileSize;
                        return;
                    }
                }
            }
            HttpURLConnection connection = (HttpURLConnection) new URL(roomInfoDownUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            try {
                if(channel.indexOf("RoomInfoDaolv") != -1){
                    DaolvInfoZipStatus.dlRoomInfoFileSizeGet = Files.size(filePath);
                }
            }catch (Exception e){}
            if(DaolvInfoZipStatus.dlRoomInfoFileSize <= DaolvInfoZipStatus.dlRoomInfoFileSizeGet) {
                DaolvInfoZipStatus.roomInfoStage = 2;
                raf.close();
                inputStream.close();
                return;
            }
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("RoomInfoDaolv") != -1){
                    DaolvInfoZipStatus.dlRoomInfoFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            DaolvInfoZipStatus.roomInfoStage = 2;
        } catch (Exception e) {
            e.printStackTrace();
            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
                "log.txt");
            DaolvInfoZipStatus.roomInfoStage = 0;
        }
    }

    /**
     * 道旅酒店房型图片信息静态资源下载
     * @param roomImageDownUrl
     * @param channel
     */
    public static void roomImageDownFile(String roomImageDownUrl, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH_DAOLV_INFO);
            if(!file.exists()){
                file.mkdirs();
            }
            File downloadFile = new File(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                if(channel.indexOf("RoomImageDaolv") != -1){
                    if(startByte == DaolvInfoZipStatus.dlRoomImageFileSize){
                        DaolvInfoZipStatus.roomImageStage = 2;
                        DaolvInfoZipStatus.dlRoomImageFileSizeGet = DaolvInfoZipStatus.dlRoomImageFileSize;
                        return;
                    }
                }
            }
            HttpURLConnection connection = (HttpURLConnection) new URL(roomImageDownUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            try {
                if(channel.indexOf("RoomImageDaolv") != -1){
                    DaolvInfoZipStatus.dlRoomImageFileSizeGet = Files.size(filePath);
                }
            }catch (Exception e){}
            if(DaolvInfoZipStatus.dlRoomImageFileSize <= DaolvInfoZipStatus.dlRoomImageFileSizeGet) {
                DaolvInfoZipStatus.roomImageStage = 2;
                raf.close();
                inputStream.close();
                return;
            }
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("RoomImageDaolv") != -1){
                    DaolvInfoZipStatus.dlRoomImageFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            DaolvInfoZipStatus.roomImageStage = 2;
        } catch (Exception e) {
            e.printStackTrace();
            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
                "log.txt");
            DaolvInfoZipStatus.roomImageStage = 0;
        }
    }

    /**
     * 道旅酒店房型窗户信息静态资源下载
     * @param roomWindowDownUrl
     * @param channel
     */
    public static void roomWindowDownFile(String roomWindowDownUrl, String channel) {
        RandomAccessFile raf = null;
        try {
            String fileName = channel+".zip";
            File file = new File(ConstantList.DOWN_PATH_DAOLV_INFO);
            if(!file.exists()){
                file.mkdirs();
            }
            File downloadFile = new File(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            long startByte = 0;
            if (downloadFile.exists()) {
                startByte = getFileSize(downloadFile);
                if(channel.indexOf("RoomWindowDaolv") != -1){
                    if(startByte == DaolvInfoZipStatus.dlRoomWindowFileSize){
                        DaolvInfoZipStatus.roomWindowStage = 2;
                        DaolvInfoZipStatus.dlRoomWindowFileSizeGet = DaolvInfoZipStatus.dlRoomWindowFileSize;
                        return;
                    }
                }
            }
            HttpURLConnection connection = (HttpURLConnection) new URL(roomWindowDownUrl).openConnection();
            connection.setRequestProperty("Range", "bytes=" + startByte + "-"); // 设置范围请求，从上次断点开始
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            raf = new RandomAccessFile(downloadFile, "rw");
            raf.seek(startByte); // 将文件指针移到断点处
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            Path filePath = Paths.get(ConstantList.DOWN_PATH_DAOLV_INFO+fileName);
            try {
                if(channel.indexOf("RoomWindowDaolv") != -1){
                    DaolvInfoZipStatus.dlRoomWindowFileSizeGet = Files.size(filePath);
                }
            }catch (Exception e){}
            if(DaolvInfoZipStatus.dlRoomWindowFileSize <= DaolvInfoZipStatus.dlRoomWindowFileSizeGet) {
                DaolvInfoZipStatus.roomWindowStage = 2;
                raf.close();
                inputStream.close();
                return;
            }
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                raf.write(buffer, 0, bytesRead); // 将数据写入文件
                if(channel.indexOf("RoomWindowDaolv") != -1){
                    DaolvInfoZipStatus.dlRoomWindowFileSizeGet += BUFFER_SIZE;
                }
            }
            raf.close();
            inputStream.close();
            DaolvInfoZipStatus.roomWindowStage = 2;
        } catch (Exception e) {
            e.printStackTrace();
            TXTUtil.writeTXT(new Date() + " *** 下载异常中断", ConstantList.LOG_PATH,
                "log.txt");
            DaolvInfoZipStatus.roomWindowStage = 0;
        }
    }

}
