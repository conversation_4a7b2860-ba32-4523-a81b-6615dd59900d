<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.HotelGnStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.HotelGnStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 15:30.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="interface_name" jdbcType="VARCHAR" property="interfaceName" />
    <result column="interface_plat" jdbcType="BIGINT" property="interfacePlat" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="total_num" jdbcType="INTEGER" property="totalNum" />
    <result column="success_num" jdbcType="INTEGER" property="successNum" />
    <result column="fail_num" jdbcType="INTEGER" property="failNum" />
    <result column="create_year" jdbcType="VARCHAR" property="createYear" />
    <result column="create_month" jdbcType="VARCHAR" property="createMonth" />
    <result column="create_day" jdbcType="VARCHAR" property="createDay" />
    <result column="statistics_time" jdbcType="DATE" property="statisticsTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 15:30.
    -->
    id, interface_name, interface_plat, type, total_num, success_num, fail_num, create_year,
    create_month, create_day, statistics_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 15:30.
    -->
    select
    <include refid="Base_Column_List" />
    from hotel_gn_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectCount" resultType="java.lang.Integer"
          parameterType="com.ltgj.ivw.domain.HotelGnStatistics">
    select count(1) from hotel_gn_statistics where interface_plat = #{interfacePlat,jdbcType=BIGINT}
    and type=#{type,jdbcType=TINYINT} and statistics_time=#{statisticsTime,jdbcType=DATE}
  </select>
  <select id="selectList" resultType="com.ltgj.ivw.response.HotelGnStatisticsRes"
          parameterType="com.ltgj.ivw.request.hotelGnStatistics.GnStatisticsRequest">
    select
    <include refid="Base_Column_List" />,
        sum(total_num) as totalNum,
        sum(success_num) as successNum,
        sum(fail_num) as failNum
    from hotel_gn_statistics
    <where>
      1=1
      <if test="interfaceName != null">
        AND interface_name = #{interfaceName,jdbcType=VARCHAR}
      </if>
      <if test="interfacePlat != null">
        AND  interface_plat = #{interfacePlat,jdbcType=BIGINT}
      </if>
      <if test="type != null">
        AND  type = #{type,jdbcType=TINYINT}
      </if>
      <if test="totalNum != null">
        AND  total_num = #{totalNum,jdbcType=INTEGER}
      </if>
      <if test="successNum != null">
        AND success_num = #{successNum,jdbcType=INTEGER}
      </if>
      <if test="failNum != null">
        AND fail_num = #{failNum,jdbcType=INTEGER}
      </if>
      <if test="createYear != null">
        AND create_year = #{createYear,jdbcType=VARCHAR}
      </if>
      <if test="createMonth != null">
        AND create_month = #{createMonth,jdbcType=VARCHAR}
      </if>
      <if test="createDay != null">
        AND create_day = #{createDay,jdbcType=VARCHAR}
      </if>
      <!--<if test="statisticsTime != null">
        statistics_time = #{statisticsTime,jdbcType=DATE},
      </if>-->
      <if test="createTime != null">
        AND create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="startTime != null">
        AND statistics_time <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        AND statistics_time <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    group by interface_plat
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 15:30.
    -->
    delete from hotel_gn_statistics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.ltgj.ivw.domain.HotelGnStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 15:30.
    -->
    insert into hotel_gn_statistics (id, interface_name, interface_plat,
      type, total_num, success_num,
      fail_num, create_year, create_month,
      create_day, statistics_time, create_time
      )
    values (#{id,jdbcType=BIGINT}, #{interfaceName,jdbcType=VARCHAR}, #{interfacePlat,jdbcType=BIGINT},
      #{type,jdbcType=TINYINT}, #{totalNum,jdbcType=INTEGER}, #{successNum,jdbcType=INTEGER},
      #{failNum,jdbcType=INTEGER}, #{createYear,jdbcType=VARCHAR}, #{createMonth,jdbcType=VARCHAR},
      #{createDay,jdbcType=VARCHAR}, #{statisticsTime,jdbcType=DATE}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ltgj.ivw.domain.HotelGnStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 15:30.
    -->
    insert into hotel_gn_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="interfaceName != null">
        interface_name,
      </if>
      <if test="interfacePlat != null">
        interface_plat,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="totalNum != null">
        total_num,
      </if>
      <if test="successNum != null">
        success_num,
      </if>
      <if test="failNum != null">
        fail_num,
      </if>
      <if test="createYear != null">
        create_year,
      </if>
      <if test="createMonth != null">
        create_month,
      </if>
      <if test="createDay != null">
        create_day,
      </if>
      <if test="statisticsTime != null">
        statistics_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="interfaceName != null">
        #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="interfacePlat != null">
        #{interfacePlat,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="totalNum != null">
        #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="successNum != null">
        #{successNum,jdbcType=INTEGER},
      </if>
      <if test="failNum != null">
        #{failNum,jdbcType=INTEGER},
      </if>
      <if test="createYear != null">
        #{createYear,jdbcType=VARCHAR},
      </if>
      <if test="createMonth != null">
        #{createMonth,jdbcType=VARCHAR},
      </if>
      <if test="createDay != null">
        #{createDay,jdbcType=VARCHAR},
      </if>
      <if test="statisticsTime != null">
        #{statisticsTime,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ltgj.ivw.domain.HotelGnStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 15:30.
    -->
    update hotel_gn_statistics
    <set>
      <if test="interfaceName != null">
        interface_name = #{interfaceName,jdbcType=VARCHAR},
      </if>
      <if test="interfacePlat != null">
        interface_plat = #{interfacePlat,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="totalNum != null">
        total_num = #{totalNum,jdbcType=INTEGER},
      </if>
      <if test="successNum != null">
        success_num = #{successNum,jdbcType=INTEGER},
      </if>
      <if test="failNum != null">
        fail_num = #{failNum,jdbcType=INTEGER},
      </if>
      <if test="createYear != null">
        create_year = #{createYear,jdbcType=VARCHAR},
      </if>
      <if test="createMonth != null">
        create_month = #{createMonth,jdbcType=VARCHAR},
      </if>
      <if test="createDay != null">
        create_day = #{createDay,jdbcType=VARCHAR},
      </if>
      <if test="statisticsTime != null">
        statistics_time = #{statisticsTime,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ltgj.ivw.domain.HotelGnStatistics">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on 2025/06/06 15:30.
    -->
    update hotel_gn_statistics
    set interface_name = #{interfaceName,jdbcType=VARCHAR},
      interface_plat = #{interfacePlat,jdbcType=BIGINT},
      type = #{type,jdbcType=TINYINT},
      total_num = #{totalNum,jdbcType=INTEGER},
      success_num = #{successNum,jdbcType=INTEGER},
      fail_num = #{failNum,jdbcType=INTEGER},
      create_year = #{createYear,jdbcType=VARCHAR},
      create_month = #{createMonth,jdbcType=VARCHAR},
      create_day = #{createDay,jdbcType=VARCHAR},
      statistics_time = #{statisticsTime,jdbcType=DATE},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteStatistics">
    TRUNCATE TABLE hotel_gn_statistics
  </delete>
</mapper>
