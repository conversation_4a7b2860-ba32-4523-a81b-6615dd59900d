package com.ltgj.supplier.klyx.gn.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 严选酒店
 */
@Data
@Builder
@ApiModel("严选酒店")
@AllArgsConstructor
@NoArgsConstructor
public class KlyxGetHotelInfoResp {
    /**
     * 主键
     * Long
     */
    @ApiModelProperty("酒店id")
    private Long jdid;

    /**
     * 酒店名称
     * String
     */
    @ApiModelProperty("酒店中文名称")
    private String jdmc;
    /**
     * 酒店名称
     * String
     */
    @ApiModelProperty("酒店英文名称")
    private String jden;
    /**
     * 酒店类型
     * String
     */
    @ApiModelProperty("酒店类型")
    private String jdlx;// 酒店类型

    /**
     * 地址
     * String
     */
    @ApiModelProperty("地址")
    private String address;

    /**
     * 品牌编号
     */
    @ApiModelProperty("品牌编号")
    private String brandId;

    /**
     * 品牌中文名称
     */
    @ApiModelProperty("品牌中文名称")
    private String brandName;

    /**
     * 前台电话1
     * String
     */
    @ApiModelProperty("前台电话,多个电话用','分割")
    private String phone;

    /**
     * 酒店首图
     * String
     */
    @ApiModelProperty(value = "酒店首图")
    private String img;

    /**
     * 百度经度
     * BigDecimal
     */
    @ApiModelProperty("百度经度")
    private BigDecimal baiduLon;

    /**
     * 百度纬度
     * BigDecimal
     */
    @ApiModelProperty("百度纬度")
    private BigDecimal baiduLat;

    /**
     * 谷歌经度
     * BigDecimal
     */
    @ApiModelProperty("谷歌经度")
    private BigDecimal googleLon;
    /**
     * 谷歌纬度
     * BigDecimal
     */
    @ApiModelProperty("谷歌纬度")
    private BigDecimal googleLat;
    /**
     * 市名称
     * String
     */
    @ApiModelProperty("所在城市名称")
    private String cityName;

    /**
     * 市编码
     * String
     */
    @ApiModelProperty("所在城市ID")
    private String cityId;
    /**
     * 商圈编码
     * String
     */
    @ApiModelProperty("商圈编码")
    private String businessZone;
    /**
     * 商圈名称
     * String
     */
    @ApiModelProperty("商圈名称")
    private String businessZonename;
    /**
     * 集团id
     * String
     */
    @ApiModelProperty("集团id")
    private String groupId;
    /**
     * 集团名称
     * String
     */
    @ApiModelProperty("集团名称")
    private String groupName;
    /**
     * 政策信息
     * String
     */
    @ApiModelProperty("政策信息")
    private String policyInfo;

    /**
     * 区名称
     * String
     */
    @ApiModelProperty("行政区名称")
    private String districtname;

    /**
     * 区编码
     * String
     */
    @ApiModelProperty("行政区编码")
    private String district;

    /**
     * 酒店星级 1.民宿酒店 2.携程2星 3.携程3星 4.携程4星 5.携程5星 6.携程6星
     * String
     */
    @ApiModelProperty("酒店星级 1.民宿酒店 2.携程2星 3.携程3星 4.携程4星 5.携程5星 6.携程6星")
    private Integer startRate;

    /**
     * 酒店评分 String
     * String
     */
    @ApiModelProperty("酒店评分")
    private String score;// 酒店评分 String

    /**
     * 酒店开业时间
     * String
     */
    @ApiModelProperty("酒店开业时间")
    private String establishmentDate;

    /**
     * 装修时间
     * LocalDateTime
     */
    @ApiModelProperty("装修时间")
    private String renovationDate;

    /**
     * 酒店介绍
     * String
     */
    @ApiModelProperty("酒店介绍")
    private String introEditor;

    /**
     * 酒店设施及服务json
     * String
     */
    @ApiModelProperty("酒店设施及服务json")
    private String facilities;

    /**
     * 酒店政策
     */
    @ApiModelProperty("value = 入离时间描述")
    private String checkInOutDesc;

    /**
     * 酒店id
     * String
     * */
    @ApiModelProperty("抓取酒店id")
    private String collectHotelId;
    /**
     * 供应商类型
     * String
     */
    @ApiModelProperty("供应商类型")
    private String supplierType;

    /**
     * 状态
     * Integer
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 发票类型
     */
    @ApiModelProperty("发票类型 空:无发票 4:平台专票 5:平台普票")
    private String invoiceModel;

}