package com.ltgj.web.canal;

import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 消费端分组与主题对应关系配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rocketmq")
public class ConsumerClient {

    private String accessKey;

    private String secretKey;

    private String nameSrvAddr;

    @Autowired
    private JdJdbCanalListener jdJdbCanalListener;

    @Autowired
    private ZhJdJdbMappingCanalListener zhJdJdbMappingCanalListener;

    @Autowired
    private HotelInfoHsjlxyCanalListener hotelInfoHsjlxyCanalListener;

    @Autowired
    KnowledgeSyncCanalListener knowledgeSyncCanalListener;

    @Autowired
    private HotelGnHsjlCanalListener hotelGnHsjlCanalListener;

    @Autowired
    private HotelGnHsjlxyCanalListener hotelGnHsjlxyCanalListener;

    @Autowired
    private HotelGnKTCanalListener hotelGnKTCanalListener;

//    @Autowired
//    private HotelGnKLYXCanalListener hotelGnKLYXCanalListener;

    @Autowired
    private HotelGnCLGJCanalListener hotelGnCLGJCanalListener;

    @Autowired
    private HotelGnQTCanalListener hotelGnQTCanalListener;

    @Autowired
    private HotelGnMTCanalListener hotelGnMTCanalListener;

    private ConsumerBean createConsumerBean(String topic, String groupId, MessageListener listener) {
        // 配置消费者属性
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.AccessKey, accessKey);
        properties.put(PropertyKeyConst.SecretKey, secretKey);
        properties.put(PropertyKeyConst.NAMESRV_ADDR, nameSrvAddr);
        properties.put(PropertyKeyConst.GROUP_ID, groupId);

        // 创建ConsumerBean实例
        ConsumerBean consumerBean = new ConsumerBean();
        consumerBean.setProperties(properties);

        // 配置订阅信息
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(topic);
        // 订阅该 Topic 下的所有消息
        subscription.setExpression("*");
        subscriptionTable.put(subscription, listener);
        consumerBean.setSubscriptionTable(subscriptionTable);

        return consumerBean;
    }

    @Bean(name = "jdJdbConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean jdJdbConsumer() {
        return createConsumerBean("ivw_hotel_jd_jdb", "GID_canal_ivw_hotel_jd_jdb", jdJdbCanalListener);
    }

    @Bean(name = "zhJdJdbMappingConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean zhJdJdbMappingConsumer() {
        return createConsumerBean("ivw_hotel_zh_jd_jdb_mapping", "GID_canal_ivw_zh_jd_jdb_mapping", zhJdJdbMappingCanalListener);
    }

    @Bean(name = "hotelInfoHsjlxyConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean hotelInfoHsjlxyConsumer() {
        return createConsumerBean("ivw_hotel_info_hsjlxy", "GID_canal_ivw_hotel_info_hsjlx", hotelInfoHsjlxyCanalListener);
    }

    @Bean(name = "KnowledgeSyncConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean KnowledgeSyncConsumer() {
        return createConsumerBean("knowledge_sync_consumer", "GID_canal_knowledge_sync_consumer", knowledgeSyncCanalListener);
    }

    @Bean(name = "hotelGnHsjlConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean hotelGnHsjlConsumer() {
        return createConsumerBean("ivw_hotel_hotel_gn_hsjl", "GID_canal_ivw_hotel_hotel_gn_hsjl", hotelGnHsjlCanalListener);
    }

    @Bean(name = "hotelGnHsjlxyConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean hotelGnHsjlxyConsumer() {
        return createConsumerBean("ivw_hotel_hotel_gn_hsjlxy", "GID_canal_ivw_hotel_hotel_gn_hsjlxy", hotelGnHsjlxyCanalListener);
    }

    @Bean(name = "hotelGnKTConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean hotelGnKTConsumer() {
        return createConsumerBean("ivw_hotel_hotel_gn_ketan", "GID_canal_ivw_hotel_hotel_gn_ketan", hotelGnKTCanalListener);
    }

    @Bean(name = "hotelGnMTConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean hotelGnMTConsumer() {
        return createConsumerBean("ivw_hotel_hotel_gn_meituan", "GID_canal_ivw_hotel_hotel_gn_meituan", hotelGnMTCanalListener);
    }

    @Bean(name = "hotelGnQTConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean hotelGnQTConsumer() {
        return createConsumerBean("ivw_hotel_hotel_gn_qiantao", "GID_canal_ivw_hotel_hotel_gn_qiantao", hotelGnQTCanalListener);
    }

//    @Bean(name = "hotelGnKLYXConsumer", initMethod = "start", destroyMethod = "shutdown")
//    public ConsumerBean hotelGnKLYXConsumer() {
//        return createConsumerBean("ivw_hotel_hotel_gn_klyx", "GID_canal_ivw_hotel_hotel_gn_klyx", hotelGnKLYXCanalListener);
//    }

    @Bean(name = "hotelGnCLGJConsumer", initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean hotelGnCLGJConsumer() {
        return createConsumerBean("ivw_hotel_hotel_gn_clgj", "GID_canal_ivw_hotel_hotel_gn_clgj", hotelGnCLGJCanalListener);
    }
}
