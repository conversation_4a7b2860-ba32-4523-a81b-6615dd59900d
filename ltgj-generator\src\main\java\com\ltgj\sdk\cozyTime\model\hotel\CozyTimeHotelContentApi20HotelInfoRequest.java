package com.ltgj.sdk.cozyTime.model.hotel;

import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import lombok.Data;

import java.util.List;

/**
 * 根据国家编码和城市编码查询酒店ID列表
 */
@Data
public class CozyTimeHotelContentApi20HotelInfoRequest implements CozyTimeRequestInterface<CozyTimeHotelContentApi20HotelInfoResponse> {
    @Override
    public String getRequestMethod() {
        return "POST";
    }

    @Override
    public String getEndpoint() {
        return "/hotel_content_api/2.0/hotelInfo";
    }

    @Override
    public Class<CozyTimeHotelContentApi20HotelInfoResponse> getResponseClass() {
        return CozyTimeHotelContentApi20HotelInfoResponse.class;
    }

    /**
     * 酒店编码，最多查10个酒店
     * 必填：true
     */
    private List<Long> hotelIds;
    /**
     * 其他条件
     * 必填：false
     */
    private String options;


}
