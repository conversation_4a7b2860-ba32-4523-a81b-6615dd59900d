package com.ltgj.ivw.service;

import com.ltgj.web.job.handler.HotelCitySyncHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Date 2025/6/10
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HotelCitySyncHandlerTest {

    @Autowired
    private HotelCitySyncHandler hotelCitySyncHandler;

    @Test
    public void test() {
        try {
            hotelCitySyncHandler.execute("");
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }
}
