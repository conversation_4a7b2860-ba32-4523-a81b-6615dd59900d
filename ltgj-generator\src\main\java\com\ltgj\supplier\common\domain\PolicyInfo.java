package com.ltgj.supplier.common.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/5
 * @description: 政策信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PolicyInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 通知政策
     */
    private NoticePolicy noticePolicy;
    /**
     * 入住政策
     */
    private CheckInOutPolicy checkInOutPolicy;
    /**
     * 客户政策
     */
    private CustomerPolicy customerPolicy;
    /**
     * 儿童政策
     */
    private ChildPolicy childPolicy;
    /**
     * 宠物政策
     */
    private PetPolicy petPolicy;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NoticePolicy implements Serializable {
        /**
         * 通知信息
         */
        private String noticeInfo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CheckInOutPolicy implements Serializable {
        /**
         * 最早入住时间
         */
        private String checkIn;
        /**
         * 最晚退房时间
         */
        private String checkOut;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CustomerPolicy implements Serializable {
        /**
         * 客户类型
         */
        private String customerType;
        /**
         * 描述
         */
        private String customerDesc;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ChildPolicy implements Serializable {
        /**
         * 允许儿童入住
         */
        private String content;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PetPolicy implements Serializable {
        /**
         * 允许宠物入住
         */
        private Boolean isAllows;
    }


}
