package com.ltgj.web.job.handler;

import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.HotelPreMappingManager;
import com.ltgj.quartz.task.HotelIncrementTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@JobHandler(value = "hotelInfoNotMappingHandler")
public class HotelInfoNotMappingHandler extends IJobHandler {

    @Resource
    private HotelPreMappingManager hotelPreMappingManager;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try{
            hotelPreMappingManager.handlePreMapping();
        }catch (Exception e) {
            log.error("平台酒店预映射处理任务异常：{}", e);
        }
        return ReturnT.SUCCESS;
    }
}
