package com.ltgj.ivw.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    /**
     * 获取 Spring 管理的 Bean
     *
     * @param beanClass Bean 的 Class 类型
     * @return 实例化的 Bean
     */
    public static <T> T getBean(Class<T> beanClass) {
        return context.getBean(beanClass);
    }

    /**
     * 获取 Spring 管理的 Bean
     *
     * @param name Bean 名称
     * @return 实例化的 Bean
     */
    public static Object getBean(String name) {
        return context.getBean(name);
    }
}
