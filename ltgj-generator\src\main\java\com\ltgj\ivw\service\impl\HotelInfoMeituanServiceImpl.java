package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.response.HotelLowestPrices;
import com.ltgj.ivw.domain.response.PriceItems;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnBaseCommonMapper;
import com.ltgj.ivw.mapper.HotelInfoMeituanMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.MyTools;
import com.ltgj.ivw.utils.hotelApi.MeituanApi;
import com.ltgj.supplier.common.domain.FacilitiesInfo;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnMeituan;
import com.ltgj.supplier.common.gn.enums.HotelGnCustomerTypeEnum;
import com.ltgj.supplier.common.gn.mapper.HotelGnBaseMapper;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.common.utils.IdUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 美团酒店信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-19
 */
@Service("hotelInfoMeituanService")
public class HotelInfoMeituanServiceImpl extends BaseHotelServiceImpl<HotelGnBase, HotelGnMeituan> implements IHotelInfoMeituanService, IHotelInfoService, IHotelGnBaseService {

    @Autowired
    private HotelInfoMeituanMapper hotelInfoMeituanMapper;

    @Autowired
    private IZhJdJdbMinPriceService zhJdJdbMinPriceService;

    @Autowired
    private IJdJdbMappingService jdJdbMappingService;

    @Autowired
    private HotelGnBaseMapper hotelGnBaseMapper;

    @Autowired
    private HotelGnBaseCommonMapper<HotelGnMeituan> mapper;

    @Autowired
    private HotelGnBaseService hotelGnBaseService;

    private static final int threadNumPriceMeituan = 38;
    private static final int maxCountPriceMeituan = 10;

    @Override
    public HotelInfo selectHotelInfoById(String id) {
        return this.hotelInfoMeituanMapper.selectHotelInfoMeituanById(id);
    }

    @Override
    public int updateHotelInfo(HotelInfo hotelInfo) {
        return this.hotelInfoMeituanMapper.updateHotelInfoMeituan((HotelInfoMeituan) hotelInfo);
    }

    /**
     * 查询美团酒店信息
     *
     * @param id 美团酒店信息主键
     * @return 美团酒店信息
     */
    @Override
    public HotelInfoMeituan selectHotelInfoMeituanById(String id) {
        return this.hotelInfoMeituanMapper.selectHotelInfoMeituanById(id);
    }

    /**
     * 查询美团酒店信息列表
     *
     * @param hotelInfoMeituan 美团酒店信息
     * @return 美团酒店信息
     */
    @Override
    public List<HotelInfoMeituan> selectHotelInfoMeituanList(HotelInfoMeituan hotelInfoMeituan) {
        return this.hotelInfoMeituanMapper.selectHotelInfoMeituanList(hotelInfoMeituan);
    }

    @Override
    public List<HotelInfoMeituan> selectHotelInfoMeituanList2(HotelInfoMeituan hotelInfoMeituan) {
        return this.hotelInfoMeituanMapper.selectHotelInfoMeituanList2(hotelInfoMeituan);
    }

    @Override
    public List<Long> selectHotelIds(HotelInfoMeituan hotelInfoMeituan) {
        return this.hotelInfoMeituanMapper.selectHotelIds(hotelInfoMeituan);
    }

    @Override
    public void updateLowPrice(MinPriceReq minPriceReq) {
        if (CollectionUtils.isEmpty(minPriceReq.getHotelIdList())) {
            HotelInfoMeituan hotelInfoMeituan = new HotelInfoMeituan();
            hotelInfoMeituan.setStatus(8);//映射数据
            List<Long> list = this.selectHotelIds(hotelInfoMeituan);
            this.logger.info("更新美团低价映射数据条数：{}", list.size());
            this.getMeituanMinPrice(list, minPriceReq.getCheckInDate(), minPriceReq.getCheckOutDate());
        }
    }

    @Override
    public void getMeituanMinPrice(List<Long> meituanList, String checkInDate, String checkOutDate) {
        try {
            List<List<Long>> hotelIdList = MyTools.splitList(meituanList, 10);
            int count = 0;
            int totalCount = 0;
            this.logger.info("美团低价查询日期：{}，酒店分组数量：{}", checkInDate, hotelIdList.size());
            for (List<Long> hotelIdList1 : hotelIdList) {
                count++;
                this.logger.info("美团低价查询批次：{}", count);
                List<HotelLowestPrices> hotelLowestPrices = MeituanApi.hotelProductMinPrice(hotelIdList1, checkInDate, checkOutDate);
                this.logger.info("美团API低价查询数据返回：{}", JSON.toJSONString(hotelLowestPrices));
                this.logger.info("美团API低价查询数据返回数据包大小：{}", hotelLowestPrices.size());
                for (HotelLowestPrices hotelLowestPrice : hotelLowestPrices) {
                    // 获取映射关系
                    JdJdbMapping jdJdbMapping = this.jdJdbMappingService.selectJdJdbMappingByPlatIdAndInterfacePlat(hotelLowestPrice.getHotelId(), PlatEnum.PLAT_MT.getValue());
                    if (jdJdbMapping == null) {
                        this.logger.info("美团API低价查询未找到映射关系，美团酒店ID：{}", hotelLowestPrice.getHotelId());
                        continue;
                    }
                    // 映射不为空，比较API查询的数据和本地库数据的价格，取最低价
                    String localId = jdJdbMapping.getLocalId();
                    List<PriceItems> priceItems = hotelLowestPrice.getPriceItems();
                    for (PriceItems priceItem : priceItems) {
                        Date saleDate = priceItem.getSaleDate();
                        BigDecimal salePrice = MyTools.convertToYuan(priceItem.getSalePrice());
                        ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                        zhJdJdbMinPrice.setJdid(localId);
                        zhJdJdbMinPrice.setSxsj(saleDate);
                        List<ZhJdJdbMinPrice> prices = this.zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                        if (prices.isEmpty()) {
                            totalCount++;
                            zhJdJdbMinPrice.setSxsj(saleDate);
                            zhJdJdbMinPrice.setMinPrice(salePrice);
                            this.zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
                            this.logger.info("美团API最低价插入成功 localId：{} sxsj：{}", localId, salePrice);
                        } else {
                            zhJdJdbMinPrice = prices.get(0);
                            if (zhJdJdbMinPrice.getMinPrice().compareTo(salePrice) < 0) {
                                //不需要更新最低价
                                this.logger.info("美团API酒店不需要更新最低价 sxsj：{},酒店ID：{},最低价：{}", zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getMinPrice());
                            } else {
                                totalCount++;
                                zhJdJdbMinPrice.setSxsj(saleDate);
                                zhJdJdbMinPrice.setMinPrice(salePrice);
                                this.zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                                this.logger.info("美团API最低价更新成功 localId：{} sxsj：{}", localId, saleDate);
                            }
                        }
                    }
                }
                try {
                    //暂停3秒 防止三方错误
                    Thread.sleep(200);
                    this.logger.info("美团低价更新休眠完成 count:{}", count);
                } catch (Exception e) {
                    this.logger.error("美团API最低价暂停异常 errorMessage:{}", e.getMessage(), e);
                }
            }
            this.logger.info("美团低价更新完成，插入和更新总条数为：{}", totalCount);
        } catch (Exception e) {
            this.logger.error("美团低价更新异常", e);
        }
    }

    /**
     * 新增美团酒店信息
     *
     * @param hotelInfoMeituan 美团酒店信息
     * @return 结果
     */
    @Override
    public int insertHotelInfoMeituan(HotelInfoMeituan hotelInfoMeituan) {
        hotelInfoMeituan.setCreateTime(DateUtils.getNowDate());
        return this.hotelInfoMeituanMapper.insertHotelInfoMeituan(hotelInfoMeituan);
    }

    @Override
    public int insertHotelInfoMeituans(List<HotelInfoMeituan> hotelInfoMeituans) {
        return this.hotelInfoMeituanMapper.insertHotelInfoMeituans(hotelInfoMeituans);
    }

    @Override
    public int updateHotelInfoMeituan(HotelInfoMeituan hotelInfoMeituan) {
        hotelInfoMeituan.setUpdateTime(DateUtils.getNowDate());
        return this.hotelInfoMeituanMapper.updateHotelInfoMeituan(hotelInfoMeituan);
    }

    /**
     * 修改酒店国内美团信息
     *
     * @param hotelInfo 美团酒店信息
     * @return 结果
     * <p>
     * 看这里封装的美团api接口信息
     * com.ltgj.ivw.controller.HotelInfoMeituanController#getHotelInfoMeituan(com.alibaba.fastjson2.JSONObject)
     */
    @Override
    public void saveOrUpdateHotelGnMeituan(HotelInfoMeituan hotelInfo) {
        if (hotelInfo == null) {
            throw new RuntimeException("酒店国内美团不能为空");
        }
        this.logger.error("更新酒店国内美团信息开始, hotelInfoMeituan:{}", JSON.toJSONString(hotelInfo));
        HotelGnMeituan hotelGnMeituan = new HotelGnMeituan();
        hotelGnMeituan.setReserve0(hotelInfo.getJsonStr());
        hotelGnMeituan.setId(hotelInfo.getId());
        hotelGnMeituan.setName(hotelInfo.getName());
        hotelGnMeituan.setNameEn(hotelInfo.getNameEn());

        // 如果为空默认酒店
        if(StringUtils.isBlank(hotelInfo.getTypeCode())){
            hotelGnMeituan.setTypeId(1);
        }
        try {
            hotelGnMeituan.setTypeId(Integer.valueOf(hotelInfo.getTypeCode()));
        } catch (Exception e) {

        }

        hotelGnMeituan.setOpenDate(hotelInfo.getOpenDate());
        hotelGnMeituan.setDecorationDate(hotelInfo.getDecorationDate());
        hotelGnMeituan.setPhone(hotelInfo.getPhone());
        hotelGnMeituan.setCountryId("CN");
        hotelGnMeituan.setCountryName("中国");
        hotelGnMeituan.setProvinceId(hotelInfo.getProvinceId());
        hotelGnMeituan.setProvinceName(hotelInfo.getProvinceName());
        hotelGnMeituan.setCityId(hotelInfo.getCityId());
        hotelGnMeituan.setCityName(hotelInfo.getCityName());
        hotelGnMeituan.setAreaId(hotelInfo.getAreaId());
        hotelGnMeituan.setAreaName(hotelInfo.getAreaName());
        hotelGnMeituan.setBusinessDistrictId(hotelInfo.getBusinessDistrictId());
        hotelGnMeituan.setBusinessDistrictName(hotelInfo.getBusinessDistricts());
        hotelGnMeituan.setAddress(hotelInfo.getAddressLine());
        hotelGnMeituan.setAddressEn(hotelInfo.getAddressLineEn());
        hotelGnMeituan.setLonGoogle(hotelInfo.getLonGg());
        hotelGnMeituan.setLatGoogle(hotelInfo.getLatGg());
        hotelGnMeituan.setLonBaidu(hotelInfo.getLonBd());
        hotelGnMeituan.setLatBaidu(hotelInfo.getLatBd());
        hotelGnMeituan.setLonGaode(hotelInfo.getLonGd());
        hotelGnMeituan.setLatGaode(hotelInfo.getLatGd());
        hotelGnMeituan.setGroupId(hotelInfo.getGroupName());// 没反 看映射
        hotelGnMeituan.setGroupName(hotelInfo.getGroupId());// 没反 看映射
        hotelGnMeituan.setBrandId(hotelInfo.getBrandName());// 没反 看映射
        hotelGnMeituan.setBrandName(hotelInfo.getBrandId());// 没反 看映射
        if (hotelInfo.getStar() != null) {
            try {
                hotelGnMeituan.setStar(String.valueOf(hotelInfo.getStar()));
            } catch (Exception e) {
            }
        }
        hotelGnMeituan.setImage(hotelInfo.getImage());
        hotelGnMeituan.setScore(hotelInfo.getReserve2());
        hotelGnMeituan.setSynopsis(hotelInfo.getDescription());

        String policyStr = hotelInfo.getPolicyInfo();
        if (StringUtils.isNotBlank(policyStr)) {
            // 政策信息ho
            PolicyInfo policyInfo = new PolicyInfo();
            JSONObject policyObject = JSON.parseObject(policyStr);

            PolicyInfo.CheckInOutPolicy checkInOutPolicy = new PolicyInfo.CheckInOutPolicy();
            policyInfo.setCheckInOutPolicy(checkInOutPolicy);
            if (policyObject.containsKey("checkinPolicy")) {
                checkInOutPolicy.setCheckIn(policyObject.getJSONObject("checkinPolicy").getString("start"));
            }
            if (policyObject.containsKey("checkOutPolicy")) {
                checkInOutPolicy.setCheckOut(policyObject.getJSONObject("checkOutPolicy").getString("end"));
            }
            if (policyObject.containsKey("guestPolicy")) {
                if (policyObject.getJSONObject("guestPolicy").containsKey("guestType")) {
                    Integer guestType = policyObject.getJSONObject("guestPolicy").getJSONObject("guestType").getInteger("code");
                    if (guestType != null) {
                        PolicyInfo.CustomerPolicy customerPolicy = new PolicyInfo.CustomerPolicy();
                        policyInfo.setCustomerPolicy(customerPolicy);

//                            美团0=5
//                            美团1=1
//                            美团2=2
//                            美团3=3
                        if (guestType == 0) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.NONE.getCode()));
                        }
                        if (guestType == 1) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.GLOBAL_GUEST.getCode()));
                            customerPolicy.setCustomerDesc((HotelGnCustomerTypeEnum.GLOBAL_GUEST.getDescription()));
                        }
                        if (guestType == 2) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_GUEST.getCode()));
                            customerPolicy.setCustomerDesc((HotelGnCustomerTypeEnum.MAINLAND_GUEST.getDescription()));
                        }
                        if (guestType == 3) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getCode()));
                            customerPolicy.setCustomerDesc((HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getDescription()));
                        }

                    }
                }
            }

            if (policyObject.containsKey("childPolicy")) {
                String allowedChild = policyObject.getJSONObject("childPolicy").getString("allowedChild");
                if (allowedChild != null) {
                    PolicyInfo.ChildPolicy childPolicy = new PolicyInfo.ChildPolicy();
                    policyInfo.setChildPolicy(childPolicy);
                    childPolicy.setContent(allowedChild);
                }
            }
            if (policyObject.containsKey("petPolicy")) {
                Integer allowed = policyObject.getJSONObject("petPolicy").getInteger("allowed");
                if (allowed != null) {
                    PolicyInfo.PetPolicy petPolicy = new PolicyInfo.PetPolicy();
                    policyInfo.setPetPolicy(petPolicy);
                    petPolicy.setIsAllows(true);
                    if (allowed == 0) {
                        petPolicy.setIsAllows(false);
                    }
                }
            }

            hotelGnMeituan.setPolicyInfo(JSON.toJSONString(policyInfo));
        }

        String facilitiesInfoStr = hotelInfo.getFacilitiesInfo();
        // fixme 精确定位策略
        if (StringUtils.isNotBlank(facilitiesInfoStr)) {
            JSONArray facilitiesArray = JSON.parseArray(facilitiesInfoStr);
            FacilitiesInfo facilitiesInfo = new FacilitiesInfo();

            // 停车
            FacilitiesInfo.Parking parking = new FacilitiesInfo.Parking();
            parking.setIsHave(Boolean.FALSE);
            parking.setIsCharge(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("停车")) {
                            if ("1".equals(code)) {
                                parking.setIsHave(Boolean.TRUE);
                            }

                            if (name.contains("收费")) {
                                if ("1".equals(code)) {
                                    parking.setIsCharge(Boolean.TRUE);
                                }

                            }

                            break outer;

                        }
                    }

                }
            facilitiesInfo.setParking(parking);


            //充电
            FacilitiesInfo.Charging charging = new FacilitiesInfo.Charging();
            charging.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("充电桩")) {
                            if ("1".equals(code)) {
                                charging.setIsHave(Boolean.TRUE);
                            }

                            break outer;

                        }
                    }

                }
            facilitiesInfo.setCharging(charging);

            //电梯
            FacilitiesInfo.Lift lift = new FacilitiesInfo.Lift();
            lift.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("电梯")) {
                            if ("1".equals(code)) {
                                lift.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }

                }
            facilitiesInfo.setLift(lift);

            //行李寄存
            FacilitiesInfo.Baggage baggage = new FacilitiesInfo.Baggage();
            baggage.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("行李寄存")) {
                            if ("1".equals(code)) {
                                baggage.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }

                }
            facilitiesInfo.setBaggage(baggage);
            //餐厅
            FacilitiesInfo.Restaurant restaurant = new FacilitiesInfo.Restaurant();
            restaurant.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("餐厅")) {
                            if ("1".equals(code)) {
                                restaurant.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }

                }
            facilitiesInfo.setRestaurant(restaurant);

            // 会议室
            FacilitiesInfo.MeetingRoom meetingRoom = new FacilitiesInfo.MeetingRoom();
            meetingRoom.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("会议")) {
                            if ("1".equals(code)) {
                                meetingRoom.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }

                }
            facilitiesInfo.setMeetingRoom(meetingRoom);

            // 公共区域WIFI
            FacilitiesInfo.PublicWifi publicWifi = new FacilitiesInfo.PublicWifi();
            publicWifi.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("公用区wifi")) {
                            if ("1".equals(code)) {
                                publicWifi.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }
                }
            facilitiesInfo.setPublicWifi(publicWifi);
            //健身房
            FacilitiesInfo.Gym gym = new FacilitiesInfo.Gym();
            gym.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("健身")) {
                            if ("1".equals(code)) {
                                gym.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }
                }
            facilitiesInfo.setGym(gym);

            //洗衣房
            FacilitiesInfo.Laundry laundry = new FacilitiesInfo.Laundry();
            laundry.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("洗衣")) {
                            if ("1".equals(code)) {
                                laundry.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }
                }
            facilitiesInfo.setLaundry(laundry);


            hotelGnMeituan.setFacilitiesInfo(JSON.toJSONString(facilitiesInfo));
        }

        hotelGnMeituan.setStatus(1);
        hotelGnMeituan.setIsDelete(0);
        hotelGnMeituan.setIncrementStatus(0);
        hotelGnMeituan.setIncrementTime(new Date());
        hotelGnMeituan.setCreateTime(new Date());
        hotelGnMeituan.setUpdateTime(new Date());
        // 如果是定时任务会报错
        String userName = "admin";
        try {
            userName = SecurityUtils.getUsername();
        } catch (Exception e) {

        }
        hotelGnMeituan.setCreateBy(userName);
        hotelGnMeituan.setUpdateBy(userName);


        this.hotelGnBaseMapper.addOrUpdateBatch(PlatEnum.PLAT_MT.getTableNameSuffix(), Lists.newArrayList(hotelGnMeituan));
        this.logger.error("更新酒店国内美团信息结束, hotelId:{}, hotelName:{}", hotelInfo.getId(), hotelInfo.getName());


    }

    /**
     * 批量删除美团酒店信息
     *
     * @param ids 需要删除的美团酒店信息主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoMeituanByIds(String[] ids) {
        return this.hotelInfoMeituanMapper.deleteHotelInfoMeituanByIds(ids);
    }

    /**
     * 删除美团酒店信息信息
     *
     * @param id 美团酒店信息主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoMeituanById(String id) {
        return this.hotelInfoMeituanMapper.deleteHotelInfoMeituanById(id);
    }

    @Override
    public List<HotelInfoMeituan> selectHotelInfoNotMapping(List<String> idList, int flag) {
        return this.hotelInfoMeituanMapper.selectHotelInfoNotMapping(idList, flag);
    }

    @Override
    public List<String> selectNotMappingIdList() {
        return this.hotelInfoMeituanMapper.selectNotMappingIdList();
    }

    @Override
    public List<HotelInfoMeituan> selectListByIdList(List<String> idList) {
        return this.hotelInfoMeituanMapper.selectListByIdList(idList);
    }

    @Override
    protected Class<HotelGnMeituan> getEntityClass() {
        return HotelGnMeituan.class;
    }

    @Override
    protected PlatEnum getPlatEnum() {
        return PlatEnum.PLAT_MT;
    }

    @Override
    protected List<HotelGnBase> doSelectListWithParams(HotelGnBase entity, Map<String, Object> queryParams) {
        return mapper.selectHotelGnBaseCommonList(entity,getEntityClass());
    }

    @Override
    protected HotelGnBase doSelectById(String id) {
        return mapper.selectHotelGnBaseCommonById(id, getEntityClass());
    }

    @Override
    protected int doInsert(HotelGnBase entity) {
        List<Long> hotelIds = new ArrayList<>();
        hotelIds.add(Long.valueOf(entity.getId()));
        String res = MeituanApi.hotelDetail(hotelIds);
        JSONObject resJson = JSONObject.parseObject(res);
        try {
            logger.info("添加美团酒店,获取美团报文信息,jsonObject:{}", res);
            HotelGnMeituan hotelGnMeituan =this.buildHotelGnMeituanFromJsonObject(resJson.getJSONObject("result").getJSONArray("hotelContents").getJSONObject(0));
            this.hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_MT, Lists.newArrayList(hotelGnMeituan));
        } catch (Exception e) {
            logger.error("插入美团酒店信息失败,id:{}, resjson:{}", entity.getId(), res, e);
            return 0;
        }
        return 1;
    }

    @Override
    public HotelGnMeituan buildHotelGnMeituanFromJsonObject(JSONObject jsonObject) {

        JSONObject mtBase = jsonObject.getJSONObject("baseInfo");
        JSONObject mtDetail = jsonObject.getJSONObject("detailInfo");
        JSONArray mtNotices = jsonObject.getJSONArray("notices");
        JSONArray mtImages = jsonObject.getJSONArray("images");


        HotelGnMeituan hotelGnMeituan = new HotelGnMeituan();
        hotelGnMeituan.setReserve0(jsonObject.toString());

        hotelGnMeituan.setId(jsonObject.getString("hotelId"));
        hotelGnMeituan.setName(mtBase.getString("name"));
        hotelGnMeituan.setNameEn(mtBase.getString("nameEn"));
        JSONArray types = mtDetail.getJSONArray("types");
        hotelGnMeituan.setTypeId(1); // 默认酒店
        if (types != null) {
            try {
                hotelGnMeituan.setTypeId(types.getJSONObject(0).getInteger("code"));
            } catch (Exception e) {
            }
        }
        hotelGnMeituan.setOpenDate(mtDetail.getString("openDate"));
        hotelGnMeituan.setDecorationDate(mtDetail.getString("decorationDate"));
        hotelGnMeituan.setPhone(mtBase.getString("phone"));
        hotelGnMeituan.setCountryId("CN");
        hotelGnMeituan.setCountryName("中国");
        JSONObject mtAddr = mtBase.getJSONObject("address");

        if (mtAddr.getJSONObject("province") != null) {
            hotelGnMeituan.setProvinceId(mtAddr.getJSONObject("province").getString("code"));
            hotelGnMeituan.setProvinceName(mtAddr.getJSONObject("province").getString("name"));
        }

        hotelGnMeituan.setCityId(mtAddr.getJSONObject("city").getString("code"));
        hotelGnMeituan.setCityName(mtAddr.getJSONObject("city").getString("name"));
        if (mtAddr.getJSONObject("area") != null) {
            hotelGnMeituan.setAreaId(mtAddr.getJSONObject("area").getString("code"));
            hotelGnMeituan.setAreaName(mtAddr.getJSONObject("area").getString("name"));
        }

        hotelGnMeituan.setBusinessDistrictId(mtAddr.getJSONArray("businessDistricts")
                .getJSONObject(0).getString("code"));
        hotelGnMeituan.setBusinessDistrictName(mtAddr.getJSONArray("businessDistricts")
                .getJSONObject(0).getString("name"));
        hotelGnMeituan.setAddress(mtAddr.getJSONObject("addressLine").getString("name"));
        hotelGnMeituan.setAddressEn(mtAddr.getJSONObject("addressLine").getString("nameEn"));


        JSONArray lonlats = mtAddr.getJSONArray("coordinates");
        for (int i = 0; i < lonlats.size(); i++) {
            JSONObject lonlat = lonlats.getJSONObject(i);
            String name = lonlat.getString("provider");
            BigDecimal lon = BigDecimal.valueOf(lonlat.getLong("longitude")).divide(BigDecimal.valueOf(1000000));
            BigDecimal lat = BigDecimal.valueOf(lonlat.getLong("latitude")).divide(BigDecimal.valueOf(1000000));
            lat = lat.setScale(11, BigDecimal.ROUND_HALF_UP);
            lon = lon.setScale(11, BigDecimal.ROUND_HALF_UP);
            switch (name) {
                case "GAODE":
                    hotelGnMeituan.setLonGaode(lon);
                    hotelGnMeituan.setLatGaode(lat);
                    break;
                case "BAIDU":
                    hotelGnMeituan.setLonBaidu(lon);
                    hotelGnMeituan.setLatBaidu(lat);
                    break;
                case "GOOGLE":
                    hotelGnMeituan.setLonGoogle(lon);
                    hotelGnMeituan.setLatGoogle(lat);

            }
        }

        JSONObject groupInfo = mtDetail.getJSONObject("group");
        if (groupInfo != null) {
            hotelGnMeituan.setGroupId(groupInfo.getString("code"));
            hotelGnMeituan.setGroupName(groupInfo.getString("name"));
        }
        JSONObject brandInfo = mtDetail.getJSONObject("brand");
        if (brandInfo != null) {
            hotelGnMeituan.setBrandId(brandInfo.getString("code"));
            hotelGnMeituan.setBrandName(brandInfo.getString("name"));

        }
        JSONObject starInfo = mtDetail.getJSONObject("star");
        if (starInfo != null) {
            hotelGnMeituan.setStar(starInfo.getString("code"));

        }
        for (int n = 0; n < mtImages.size(); n++) {
            JSONObject image = mtImages.getJSONObject(n);
            if (image.getString("title").equals("酒店首图")) {
                hotelGnMeituan.setImage(image.getJSONArray("links").getJSONObject(0).getString("url"));
            }
        }
        JSONArray scores = mtDetail.getJSONArray("ratings");
        for (int k = 0; k < scores.size(); k++) {
            JSONObject score = scores.getJSONObject(k);
            if (score.getString("type").equals("AVG_SCORE")) {
                hotelGnMeituan.setScore(score.getString("value"));

            }
        }
        hotelGnMeituan.setSynopsis(mtDetail.getString("description"));



        String policyStr = jsonObject.getJSONObject("policy").toString();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(policyStr)) {
            // 政策信息ho
            PolicyInfo policyInfo = new PolicyInfo();
            JSONObject policyObject = JSON.parseObject(policyStr);

            PolicyInfo.CheckInOutPolicy checkInOutPolicy = new PolicyInfo.CheckInOutPolicy();
            policyInfo.setCheckInOutPolicy(checkInOutPolicy);
            if (policyObject.containsKey("checkinPolicy")) {
                checkInOutPolicy.setCheckIn(policyObject.getJSONObject("checkinPolicy").getString("start"));
            }
            if (policyObject.containsKey("checkOutPolicy")) {
                checkInOutPolicy.setCheckOut(policyObject.getJSONObject("checkOutPolicy").getString("end"));
            }
            if (policyObject.containsKey("guestPolicy")) {
                if (policyObject.getJSONObject("guestPolicy").containsKey("guestType")) {
                    Integer guestType = policyObject.getJSONObject("guestPolicy").getJSONObject("guestType").getInteger("code");
                    if (guestType != null) {
                        PolicyInfo.CustomerPolicy customerPolicy = new PolicyInfo.CustomerPolicy();
                        policyInfo.setCustomerPolicy(customerPolicy);

//                            美团0=5
//                            美团1=1
//                            美团2=2
//                            美团3=3
                        if (guestType == 0) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.NONE.getCode()));
                        }
                        if (guestType == 1) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.GLOBAL_GUEST.getCode()));
                            customerPolicy.setCustomerDesc((HotelGnCustomerTypeEnum.GLOBAL_GUEST.getDescription()));
                        }
                        if (guestType == 2) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_GUEST.getCode()));
                            customerPolicy.setCustomerDesc((HotelGnCustomerTypeEnum.MAINLAND_GUEST.getDescription()));
                        }
                        if (guestType == 3) {
                            customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getCode()));
                            customerPolicy.setCustomerDesc((HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getDescription()));
                        }

                    }
                }
            }

            if (policyObject.containsKey("childPolicy")) {
                String allowedChild = policyObject.getJSONObject("childPolicy").getString("allowedChild");
                if (allowedChild != null) {
                    PolicyInfo.ChildPolicy childPolicy = new PolicyInfo.ChildPolicy();
                    policyInfo.setChildPolicy(childPolicy);
                    childPolicy.setContent(allowedChild);
                }
            }
            if (policyObject.containsKey("petPolicy")) {
                Integer allowed = policyObject.getJSONObject("petPolicy").getInteger("allowed");
                if (allowed != null) {
                    PolicyInfo.PetPolicy petPolicy = new PolicyInfo.PetPolicy();
                    policyInfo.setPetPolicy(petPolicy);
                    petPolicy.setIsAllows(true);
                    if (allowed == 0) {
                        petPolicy.setIsAllows(false);
                    }
                }
            }

            hotelGnMeituan.setPolicyInfo(JSON.toJSONString(policyInfo));
        }else {
            hotelGnMeituan.setPolicyInfo(JSON.toJSONString( new PolicyInfo()));
        }

        String facilitiesInfoStr = jsonObject.getJSONArray("facilities").toString();

        // fixme 精确定位策略
        if (org.apache.commons.lang3.StringUtils.isNotBlank(facilitiesInfoStr)) {
            JSONArray facilitiesArray = JSON.parseArray(facilitiesInfoStr);
            FacilitiesInfo facilitiesInfo = new FacilitiesInfo();

            // 停车
            FacilitiesInfo.Parking parking = new FacilitiesInfo.Parking();
            parking.setIsHave(Boolean.FALSE);
            parking.setIsCharge(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("停车")) {
                            if ("1".equals(code)) {
                                parking.setIsHave(Boolean.TRUE);
                            }

                            if (name.contains("收费")) {
                                if ("1".equals(code)) {
                                    parking.setIsCharge(Boolean.TRUE);
                                }

                            }

                            break outer;

                        }
                    }

                }
            facilitiesInfo.setParking(parking);


            //充电
            FacilitiesInfo.Charging charging = new FacilitiesInfo.Charging();
            charging.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("充电桩")) {
                            if ("1".equals(code)) {
                                charging.setIsHave(Boolean.TRUE);
                            }

                            break outer;

                        }
                    }

                }
            facilitiesInfo.setCharging(charging);

            //电梯
            FacilitiesInfo.Lift lift = new FacilitiesInfo.Lift();
            lift.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("电梯")) {
                            if ("1".equals(code)) {
                                lift.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }

                }
            facilitiesInfo.setLift(lift);

            //行李寄存
            FacilitiesInfo.Baggage baggage = new FacilitiesInfo.Baggage();
            baggage.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("行李寄存")) {
                            if ("1".equals(code)) {
                                baggage.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }

                }
            facilitiesInfo.setBaggage(baggage);
            //餐厅
            FacilitiesInfo.Restaurant restaurant = new FacilitiesInfo.Restaurant();
            restaurant.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("餐厅")) {
                            if ("1".equals(code)) {
                                restaurant.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }

                }
            facilitiesInfo.setRestaurant(restaurant);

            // 会议室
            FacilitiesInfo.MeetingRoom meetingRoom = new FacilitiesInfo.MeetingRoom();
            meetingRoom.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("会议")) {
                            if ("1".equals(code)) {
                                meetingRoom.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }

                }
            facilitiesInfo.setMeetingRoom(meetingRoom);

            // 公共区域WIFI
            FacilitiesInfo.PublicWifi publicWifi = new FacilitiesInfo.PublicWifi();
            publicWifi.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("公用区wifi")) {
                            if ("1".equals(code)) {
                                publicWifi.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }
                }
            facilitiesInfo.setPublicWifi(publicWifi);
            //健身房
            FacilitiesInfo.Gym gym = new FacilitiesInfo.Gym();
            gym.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("健身")) {
                            if ("1".equals(code)) {
                                gym.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }
                }
            facilitiesInfo.setGym(gym);

            //洗衣房
            FacilitiesInfo.Laundry laundry = new FacilitiesInfo.Laundry();
            laundry.setIsHave(Boolean.FALSE);
            for (Object o : facilitiesArray)
                outer:{
                    JSONObject facility = (JSONObject) o;
                    JSONArray facilityItems = facility.getJSONArray("facilityItems");
                    for (Object facilityItem : facilityItems) {
                        JSONObject itemObject = (JSONObject) facilityItem;
                        // 0 无 1 有
                        String name = itemObject.getString("name");
                        String code = itemObject.getString("value");
                        if (name.contains("洗衣")) {
                            if ("1".equals(code)) {
                                laundry.setIsHave(Boolean.TRUE);
                            }
                            break outer;

                        }
                    }
                }
            facilitiesInfo.setLaundry(laundry);


            hotelGnMeituan.setFacilitiesInfo(JSON.toJSONString(facilitiesInfo));
        }else {
            // 默认为空
            hotelGnMeituan.setFacilitiesInfo(JSON.toJSONString(new FacilitiesInfo()));
        }

        hotelGnMeituan.setStatus(1);
        hotelGnMeituan.setIsDelete(0);
        hotelGnMeituan.setIncrementStatus(0);
        hotelGnMeituan.setIncrementTime(new Date());
        hotelGnMeituan.setCreateTime(new Date());
        hotelGnMeituan.setUpdateTime(new Date());
        // 如果是定时任务会报错
        String userName = "admin";
        try {
            userName = SecurityUtils.getUsername();
        } catch (Exception e) {

        }
        hotelGnMeituan.setCreateBy(userName);
        hotelGnMeituan.setUpdateBy(userName);

        return hotelGnMeituan;
    }

    @Override
    protected int doUpdate(HotelGnBase entity) {
        return mapper.updateHotelGnBaseCommon(entity, getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        return mapper.deleteHotelGnBaseCommonByIds(ids, getEntityClass());
    }

    // ==================== 导出相关方法实现 ====================

    @Override
    public List<?> exportData(HotelGnBase entity, HotelGnMeituan searchParams) {
        logger.info("开始获取美团酒店导出数据，实体：{}，查询参数：{}", entity, searchParams);
        
        // 使用通用的查询方法获取数据，不进行分页
        Map<String, Object> queryParams = buildDefaultQueryParams(entity, searchParams);
        List<HotelGnBase> result = doSelectListWithParams(entity, queryParams);
        
        logger.info("获取美团酒店导出数据完成，数据量：{}", result.size());
        return result;
    }

    @Override
    public Class<?> getExportEntityClass() {
        // 使用HotelGnMeituan作为导出实体类型，与Controller泛型保持一致
        return HotelGnMeituan.class;
    }

    @Override
    public String getExportFileName(HotelGnMeituan searchParams) {
        return "美团酒店信息数据";
    }

    @Override
    public HotelGnBase selectHotelGnBaseById(String id) {
        return mapper.selectHotelGnBaseCommonById(id, getEntityClass());
    }

    @Override
    public void updateHotelGnBase(HotelGnBase hotelGnBase) {
        this.update(hotelGnBase);
    }

}
