package com.ltgj.ivw.service.impl;

import java.util.Date;
import java.util.List;

import com.ltgj.common.exception.ServiceException;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.poi.ExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ltgj.ivw.mapper.SupplierElongCityMappingMapper;
import com.ltgj.ivw.domain.SupplierElongCityMapping;
import com.ltgj.ivw.service.ISupplierElongCityMappingService;
import org.springframework.web.multipart.MultipartFile;

/**
 * 供应商与艺龙城市映射关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
public class SupplierElongCityMappingServiceImpl implements ISupplierElongCityMappingService 
{
    private static final Logger log = LoggerFactory.getLogger(SupplierElongCityMappingServiceImpl.class);
    
    @Autowired
    private SupplierElongCityMappingMapper supplierElongCityMappingMapper;

    /**
     * 查询供应商与艺龙城市映射关系
     * 
     * @param id 供应商与艺龙城市映射关系主键
     * @return 供应商与艺龙城市映射关系
     */
    @Override
    public SupplierElongCityMapping selectSupplierElongCityMappingById(Long id)
    {
        return supplierElongCityMappingMapper.selectSupplierElongCityMappingById(id);
    }

    /**
     * 查询供应商与艺龙城市映射关系列表
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 供应商与艺龙城市映射关系
     */
    @Override
    public List<SupplierElongCityMapping> selectSupplierElongCityMappingList(SupplierElongCityMapping supplierElongCityMapping)
    {
        return supplierElongCityMappingMapper.selectSupplierElongCityMappingList(supplierElongCityMapping);
    }

    /**
     * 根据供应商编码和供应商城市编码查询映射关系
     * 
     * @param supplierCode 供应商编码
     * @param supplierCityCode 供应商城市编码
     * @return 供应商与艺龙城市映射关系
     */
    @Override
    public SupplierElongCityMapping selectBySupplierAndCityCode(String supplierCode, String supplierCityCode)
    {
        return supplierElongCityMappingMapper.selectBySupplierAndCityCode(supplierCode, supplierCityCode);
    }

    /**
     * 根据供应商编码和艺龙城市编码查询映射关系
     * 
     * @param supplierCode 供应商编码
     * @param elongCityCode 艺龙城市编码
     * @return 供应商与艺龙城市映射关系列表
     */
    @Override
    public List<SupplierElongCityMapping> selectBySupplierAndElongCityCode(String supplierCode, String elongCityCode)
    {
        return supplierElongCityMappingMapper.selectBySupplierAndElongCityCode(supplierCode, elongCityCode);
    }

    /**
     * 新增供应商与艺龙城市映射关系
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 结果
     */
    @Override
    public int insertSupplierElongCityMapping(SupplierElongCityMapping supplierElongCityMapping)
    {
        if (StringUtils.isEmpty(supplierElongCityMapping.getDelFlag())) {
            supplierElongCityMapping.setDelFlag("0");
        }
        if (StringUtils.isEmpty(supplierElongCityMapping.getStatus())) {
            supplierElongCityMapping.setStatus("0");
        }
        supplierElongCityMapping.setCreateTime(DateUtils.getNowDate());
        return supplierElongCityMappingMapper.insertSupplierElongCityMapping(supplierElongCityMapping);
    }

    /**
     * 修改供应商与艺龙城市映射关系
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 结果
     */
    @Override
    public int updateSupplierElongCityMapping(SupplierElongCityMapping supplierElongCityMapping)
    {
        supplierElongCityMapping.setUpdateTime(DateUtils.getNowDate());
        return supplierElongCityMappingMapper.updateSupplierElongCityMapping(supplierElongCityMapping);
    }

    /**
     * 批量删除供应商与艺龙城市映射关系
     * 
     * @param ids 需要删除的供应商与艺龙城市映射关系主键
     * @return 结果
     */
    @Override
    public int deleteSupplierElongCityMappingByIds(Long[] ids)
    {
        return supplierElongCityMappingMapper.deleteSupplierElongCityMappingByIds(ids);
    }

    /**
     * 删除供应商与艺龙城市映射关系信息
     * 
     * @param id 供应商与艺龙城市映射关系主键
     * @return 结果
     */
    @Override
    public int deleteSupplierElongCityMappingById(Long id)
    {
        return supplierElongCityMappingMapper.deleteSupplierElongCityMappingById(id);
    }
    
    /**
     * 导入Excel数据
     * 
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，是否更新
     * @param operName 操作人
     * @return 导入结果
     * @throws Exception 异常
     */
    @Override
    public String importExcel(MultipartFile file, boolean updateSupport, String operName) throws Exception {
        try {
            // 读取Excel文件
            ExcelUtil<SupplierElongCityMapping> util = new ExcelUtil<>(SupplierElongCityMapping.class);
            List<SupplierElongCityMapping> mappingList = util.importExcel(file.getInputStream());
            if (StringUtils.isNull(mappingList) || mappingList.isEmpty()) {
                throw new ServiceException("导入数据为空");
            }
            
            // 统计信息
            int successCount = 0;
            int failureCount = 0;
            int updateCount = 0;
            StringBuilder successMsg = new StringBuilder();
            StringBuilder failureMsg = new StringBuilder();
            
            // 处理数据
            for (SupplierElongCityMapping mapping : mappingList) {
                try {
                    // 校验必填字段
                    if (StringUtils.isEmpty(mapping.getSupplierCode()) || 
                        StringUtils.isEmpty(mapping.getSupplierCityCode()) || 
                        StringUtils.isEmpty(mapping.getElongCityCode())) {
                        failureCount++;
                        failureMsg.append("<br/>供应商编码、供应商城市编码或艺龙城市编码为空，第" + (failureCount + successCount + 1) + "条数据导入失败");
                        continue;
                    }
                    
                    // 检查是否存在
                    SupplierElongCityMapping existMapping = new SupplierElongCityMapping();
                    existMapping.setSupplierCode(mapping.getSupplierCode());
                    existMapping.setSupplierCityCode(mapping.getSupplierCityCode());
                    existMapping.setElongCityCode(mapping.getElongCityCode());
                    SupplierElongCityMapping exist = supplierElongCityMappingMapper.checkExistMapping(existMapping);
                    
                    // 设置操作人和操作时间
                    mapping.setDelFlag("0");
                    mapping.setStatus("0");
                    if (StringUtils.isNotEmpty(operName)) {
                        mapping.setCreateBy(operName);
                        mapping.setUpdateBy(operName);
                    }
                    mapping.setCreateTime(DateUtils.getNowDate());
                    mapping.setUpdateTime(DateUtils.getNowDate());
                    
                    if (exist != null) {
                        // 已存在，判断是否更新
                        if (updateSupport) {
                            mapping.setId(exist.getId());
                            supplierElongCityMappingMapper.updateSupplierElongCityMapping(mapping);
                            updateCount++;
                        } else {
                            failureCount++;
                            failureMsg.append("<br/>供应商编码 ").append(mapping.getSupplierCode())
                                    .append(" 供应商城市编码 ").append(mapping.getSupplierCityCode())
                                    .append(" 艺龙城市编码 ").append(mapping.getElongCityCode())
                                    .append(" 已存在，第").append(failureCount + successCount + 1).append("条数据导入失败");
                        }
                    } else {
                        // 不存在，新增
                        supplierElongCityMappingMapper.insertSupplierElongCityMapping(mapping);
                        successCount++;
                    }
                } catch (Exception e) {
                    failureCount++;
                    String msg = "<br/>导入第" + (failureCount + successCount + 1) + "条数据失败：" + e.getMessage();
                    log.error(msg, e);
                    failureMsg.append(msg);
                }
            }
            
            // 组装返回信息
            if (successCount > 0) {
                successMsg.append("成功导入 ").append(successCount).append(" 条数据");
            }
            if (updateCount > 0) {
                successMsg.append("，更新 ").append(updateCount).append(" 条数据");
            }
            if (failureCount > 0) {
                failureMsg.insert(0, "，失败 " + failureCount + " 条数据，错误如下：");
                successMsg.append(failureMsg);
            }
            
            return successMsg.toString();
        } catch (Exception e) {
            log.error("导入Excel异常", e);
            throw new ServiceException("导入Excel失败：" + e.getMessage());
        }
    }
} 