package com.ltgj.ivw.service.impl;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.BCity;
import com.ltgj.ivw.domain.GeoCityGeo;
import com.ltgj.ivw.domain.GeoProvince;
import com.ltgj.ivw.mapper.BCityMapper;
import com.ltgj.ivw.mapper.GeoCityGeoMapper;
import com.ltgj.ivw.mapper.GeoProvinceMapper;
import com.ltgj.ivw.service.IGeoBaseDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 地理基础数据服务实现类
 * 专门用于处理需要切换到DEVYIXBASE数据源的地理数据查询
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@Service
public class GeoBaseDataServiceImpl implements IGeoBaseDataService {
    
    @Autowired
    private GeoCityGeoMapper geoCityGeoMapper;
    
    @Autowired
    private BCityMapper bCityMapper;
    
    @Autowired
    private GeoProvinceMapper geoProvinceMapper;
    
    @Override
    @DataSource(DataSourceType.DEVYIXBASE)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public GeoCityGeo selectGeoCityGeoById(String id) {
        log.debug("从DEVYIXBASE数据源查询区县信息，ID：{}", id);
        return geoCityGeoMapper.selectGeoCityGeoById(id);
    }
    
    @Override
    @DataSource(DataSourceType.DEVYIXBASE)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<GeoCityGeo> selectDistrictsByCity(String cityId, Long geoType, Boolean validOnly) {
        log.debug("从DEVYIXBASE数据源查询区县列表，城市ID：{}，类型：{}，仅有效：{}", cityId, geoType, validOnly);
        return geoCityGeoMapper.selectDistrictsByCity(cityId, geoType, validOnly);
    }
    
    @Override
    @DataSource(DataSourceType.DEVYIXBASE)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public BCity selectBCityById(String id) {
        log.debug("从DEVYIXBASE数据源查询城市信息，ID：{}", id);
        return bCityMapper.selectBCityById(id);
    }
    
    @Override
    @DataSource(DataSourceType.DEVYIXBASE)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<BCity> selectCitiesByProvince(String provinceId, Boolean validOnly) {
        log.debug("从DEVYIXBASE数据源查询城市列表，省份ID：{}，仅有效：{}", provinceId, validOnly);
        return bCityMapper.selectCitiesByProvince(provinceId, validOnly);
    }
    
    @Override
    @DataSource(DataSourceType.DEVYIXBASE)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public GeoProvince selectGeoProvinceById(String id) {
        log.debug("从DEVYIXBASE数据源查询省份信息，ID：{}", id);
        return geoProvinceMapper.selectGeoProvinceById(id);
    }
    
    @Override
    @DataSource(DataSourceType.DEVYIXBASE)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<Map<String, Object>> selectDistinctCountries(Boolean validOnly) {
        log.debug("从DEVYIXBASE数据源查询国家列表，仅有效：{}", validOnly);
        return geoProvinceMapper.selectDistinctCountries(validOnly);
    }
    
    @Override
    @DataSource(DataSourceType.DEVYIXBASE)
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<GeoProvince> selectProvincesByCountry(String countryId, Boolean validOnly) {
        log.debug("从DEVYIXBASE数据源查询省份列表，国家ID：{}，仅有效：{}", countryId, validOnly);
        return geoProvinceMapper.selectProvincesByCountry(countryId, validOnly);
    }
} 