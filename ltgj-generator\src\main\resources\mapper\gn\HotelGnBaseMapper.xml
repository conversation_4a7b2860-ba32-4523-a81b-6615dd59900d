<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.supplier.common.gn.mapper.HotelGnBaseMapper">

    <!-- 查询通用查询结果列 无reserve0 -->
    <sql id="selectAllColumn">
        id,
        name,
        name_en,
        type_id,
        open_date,
        decoration_date,
        phone,
        country_id,
        country_name,
        province_id,
        province_name,
        city_id,
        city_name,
        area_id,
        area_name,
        business_district_id,
        business_district_name,
        address,
        address_en,
        lon_google,
        lat_google,
        lon_baidu,
        lat_baidu,
        lon_gaode,
        lat_gaode,
        group_id,
        group_name,
        brand_id,
        brand_name,
        star,
        image,
        score,
        synopsis,
        sparkle,
        policy_info,
        facilities_info,
        round_info,
        status,
        remark,
        increment_status,
        increment_time,
        increment_type,
        create_time,
        update_time,
        create_by,
        update_by,
        reserve1,
        reserve2,
        reserve3,
        reserve4,
        reserve5,
        reserve6,
        reserve7,
        reserve8,
        reserve9
    </sql>

    <!-- 批量插入字段列表 -->
    <sql id="insertColumns">
        id,
        name,
        name_en,
        type_id,
        open_date,
        decoration_date,
        phone,
        country_id,
        country_name,
        province_id,
        province_name,
        city_id,
        city_name,
        area_id,
        area_name,
        business_district_id,
        business_district_name,
        address,
        address_en,
        lon_google,
        lat_google,
        lon_baidu,
        lat_baidu,
        lon_gaode,
        lat_gaode,
        group_id,
        group_name,
        brand_id,
        brand_name,
        star,
        image,
        score,
        synopsis,
        sparkle,
        policy_info,
        facilities_info,
        round_info,
        status,
        remark,
        increment_status,
        increment_time,
        increment_type,
        create_time,
        update_time,
        create_by,
        update_by,
        reserve0,
        reserve1,
        reserve2,
        reserve3,
        reserve4,
        reserve5,
        reserve6,
        reserve7,
        reserve8,
        reserve9
    </sql>

    <!-- 批量插入参数绑定 -->
    <sql id="insertValues">
        #{item.id},
        #{item.name},
        #{item.nameEn},
        #{item.typeId},
        #{item.openDate},
        #{item.decorationDate},
        #{item.phone},
        #{item.countryId},
        #{item.countryName},
        #{item.provinceId},
        #{item.provinceName},
        #{item.cityId},
        #{item.cityName},
        #{item.areaId},
        #{item.areaName},
        #{item.businessDistrictId},
        #{item.businessDistrictName},
        #{item.address},
        #{item.addressEn},
        #{item.lonGoogle},
        #{item.latGoogle},
        #{item.lonBaidu},
        #{item.latBaidu},
        #{item.lonGaode},
        #{item.latGaode},
        #{item.groupId},
        #{item.groupName},
        #{item.brandId},
        #{item.brandName},
        #{item.star},
        #{item.image},
        #{item.score},
        #{item.synopsis},
        #{item.sparkle},
        #{item.policyInfo},
        #{item.facilitiesInfo},
        #{item.roundInfo},
        #{item.status},
        #{item.remark},
        #{item.incrementStatus},
        #{item.incrementTime},
        #{item.incrementType},
        #{item.createTime},
        #{item.updateTime},
        #{item.createBy},
        #{item.updateBy},
        #{item.reserve0},
        #{item.reserve1},
        #{item.reserve2},
        #{item.reserve3},
        #{item.reserve4},
        #{item.reserve5},
        #{item.reserve6},
        #{item.reserve7},
        #{item.reserve8},
        #{item.reserve9}
    </sql>

    <!-- ON DUPLICATE KEY UPDATE 更新条件 -->
    <sql id="onDuplicateKeyUpdate">
        name = VALUES(name),
        name_en = VALUES(name_en),
        type_id = VALUES(type_id),
        open_date = VALUES(open_date),
        decoration_date = VALUES(decoration_date),
        phone = VALUES(phone),
        country_id = VALUES(country_id),
        country_name = VALUES(country_name),
        province_id = VALUES(province_id),
        province_name = VALUES(province_name),
        city_id = VALUES(city_id),
        city_name = VALUES(city_name),
        area_id = VALUES(area_id),
        area_name = VALUES(area_name),
        business_district_id = VALUES(business_district_id),
        business_district_name = VALUES(business_district_name),
        address = VALUES(address),
        address_en = VALUES(address_en),
        lon_google = VALUES(lon_google),
        lat_google = VALUES(lat_google),
        lon_baidu = VALUES(lon_baidu),
        lat_baidu = VALUES(lat_baidu),
        lon_gaode = VALUES(lon_gaode),
        lat_gaode = VALUES(lat_gaode),
        group_id = VALUES(group_id),
        group_name = VALUES(group_name),
        brand_id = VALUES(brand_id),
        brand_name = VALUES(brand_name),
        star = VALUES(star),
        image = VALUES(image),
        score = VALUES(score),
        synopsis = VALUES(synopsis),
        sparkle = VALUES(sparkle),
        policy_info = VALUES(policy_info),
        facilities_info = VALUES(facilities_info),
        round_info = VALUES(round_info),
        `status` = VALUES(`status`),
        is_delete = VALUES(is_delete),
        remark = VALUES(remark),
        update_time = VALUES(update_time),
        update_by = VALUES(update_by),
        reserve0 = VALUES(reserve0),
        reserve1 = VALUES(reserve1),
        reserve2 = VALUES(reserve2),
        reserve3 = VALUES(reserve3),
        reserve4 = VALUES(reserve4),
        reserve5 = VALUES(reserve5),
        reserve6 = VALUES(reserve6),
        reserve7 = VALUES(reserve7),
        reserve8 = VALUES(reserve8),
        reserve9 = VALUES(reserve9)
    </sql>

    <insert id="addOrUpdateBatch">
        INSERT INTO hotel_gn_${tableSuffix} (
        id,
        name,
        name_en,
        type_id,
        open_date,
        decoration_date,
        phone,
        country_id,
        country_name,
        province_id,
        province_name,
        city_id,
        city_name,
        area_id,
        area_name,
        business_district_id,
        business_district_name,
        address,
        address_en,
        lon_google,
        lat_google,
        lon_baidu,
        lat_baidu,
        lon_gaode,
        lat_gaode,
        group_id,
        group_name,
        brand_id,
        brand_name,
        star,
        image,
        score,
        synopsis,
        sparkle,
        policy_info,
        facilities_info,
        round_info,
        status,
        remark,
        increment_status,
        increment_time,
        increment_type,
        create_time,
        update_time,
        create_by,
        update_by,
        reserve0,
        reserve1,
        reserve2,
        reserve3,
        reserve4,
        reserve5,
        reserve6,
        reserve7,
        reserve8,
        reserve9
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.name},
            #{item.nameEn},
            #{item.typeId},
            #{item.openDate},
            #{item.decorationDate},
            #{item.phone},
            #{item.countryId},
            #{item.countryName},
            #{item.provinceId},
            #{item.provinceName},
            #{item.cityId},
            #{item.cityName},
            #{item.areaId},
            #{item.areaName},
            #{item.businessDistrictId},
            #{item.businessDistrictName},
            #{item.address},
            #{item.addressEn},
            #{item.lonGoogle},
            #{item.latGoogle},
            #{item.lonBaidu},
            #{item.latBaidu},
            #{item.lonGaode},
            #{item.latGaode},
            #{item.groupId},
            #{item.groupName},
            #{item.brandId},
            #{item.brandName},
            #{item.star},
            #{item.image},
            #{item.score},
            #{item.synopsis},
            #{item.sparkle},
            #{item.policyInfo},
            #{item.facilitiesInfo},
            #{item.roundInfo},
            #{item.status},
            #{item.remark},
            #{item.incrementStatus},
            #{item.incrementTime},
            #{item.incrementType},
            #{item.createTime},
            #{item.updateTime},
            #{item.createBy},
            #{item.updateBy},
            #{item.reserve0},
            #{item.reserve1},
            #{item.reserve2},
            #{item.reserve3},
            #{item.reserve4},
            #{item.reserve5},
            #{item.reserve6},
            #{item.reserve7},
            #{item.reserve8},
            #{item.reserve9}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        name_en = VALUES(name_en),
        type_id = VALUES(type_id),
        open_date = VALUES(open_date),
        decoration_date = VALUES(decoration_date),
        phone = VALUES(phone),
        country_id = VALUES(country_id),
        country_name = VALUES(country_name),
        province_id = VALUES(province_id),
        province_name = VALUES(province_name),
        city_id = VALUES(city_id),
        city_name = VALUES(city_name),
        area_id = VALUES(area_id),
        area_name = VALUES(area_name),
        business_district_id = VALUES(business_district_id),
        business_district_name = VALUES(business_district_name),
        address = VALUES(address),
        address_en = VALUES(address_en),
        lon_google = VALUES(lon_google),
        lat_google = VALUES(lat_google),
        lon_baidu = VALUES(lon_baidu),
        lat_baidu = VALUES(lat_baidu),
        lon_gaode = VALUES(lon_gaode),
        lat_gaode = VALUES(lat_gaode),
        group_id = VALUES(group_id),
        group_name = VALUES(group_name),
        brand_id = VALUES(brand_id),
        brand_name = VALUES(brand_name),
        star = VALUES(star),
        image = VALUES(image),
        score = VALUES(score),
        synopsis = VALUES(synopsis),
        sparkle = VALUES(sparkle),
        policy_info = VALUES(policy_info),
        facilities_info = VALUES(facilities_info),
        round_info = VALUES(round_info),
        `status` = IF (`status` = 0, VALUES(`status`), `status`),
        is_delete = VALUES(is_delete),
        remark = VALUES(remark),
        update_time = VALUES(update_time),
        update_by = VALUES(update_by),
        reserve0 = VALUES(reserve0),
        reserve1 = VALUES(reserve1),
        reserve2 = VALUES(reserve2),
        reserve3 = VALUES(reserve3),
        reserve4 = VALUES(reserve4),
        reserve5 = VALUES(reserve5),
        reserve6 = VALUES(reserve6),
        reserve7 = VALUES(reserve7),
        reserve8 = VALUES(reserve8),
        reserve9 = VALUES(reserve9)
    </insert>

    <insert id="addOrUpdateBatchNoFunction">
        INSERT INTO hotel_gn_${tableSuffix} (
        <include refid="insertColumns" />
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            <include refid="insertValues" />
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        name_en = VALUES(name_en),
        type_id = VALUES(type_id),
        open_date = VALUES(open_date),
        decoration_date = VALUES(decoration_date),
        phone = VALUES(phone),
        country_id = VALUES(country_id),
        country_name = VALUES(country_name),
        province_id = VALUES(province_id),
        province_name = VALUES(province_name),
        city_id = VALUES(city_id),
        city_name = VALUES(city_name),
        area_id = VALUES(area_id),
        area_name = VALUES(area_name),
        business_district_id = VALUES(business_district_id),
        business_district_name = VALUES(business_district_name),
        address = VALUES(address),
        address_en = VALUES(address_en),
        lon_google = VALUES(lon_google),
        lat_google = VALUES(lat_google),
        lon_baidu = VALUES(lon_baidu),
        lat_baidu = VALUES(lat_baidu),
        lon_gaode = VALUES(lon_gaode),
        lat_gaode = VALUES(lat_gaode),
        group_id = VALUES(group_id),
        group_name = VALUES(group_name),
        brand_id = VALUES(brand_id),
        brand_name = VALUES(brand_name),
        star = VALUES(star),
        image = VALUES(image),
        score = VALUES(score),
        synopsis = VALUES(synopsis),
        sparkle = VALUES(sparkle),
        policy_info = VALUES(policy_info),
        facilities_info = VALUES(facilities_info),
        round_info = VALUES(round_info),
        `status` = VALUES(status),
        is_delete = VALUES(is_delete),
        remark = VALUES(remark),
        update_time = VALUES(update_time),
        update_by = VALUES(update_by),
        reserve0 = VALUES(reserve0),
        reserve1 = VALUES(reserve1),
        reserve2 = VALUES(reserve2),
        reserve3 = VALUES(reserve3),
        reserve4 = VALUES(reserve4),
        reserve5 = VALUES(reserve5),
        reserve6 = VALUES(reserve6),
        reserve7 = VALUES(reserve7),
        reserve8 = VALUES(reserve8),
        reserve9 = VALUES(reserve9)
    </insert>

    <select id="selectById" resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
     SELECT <include refid="selectAllColumn" />
        FROM hotel_gn_${tableSuffix}
        WHERE id = #{id}
    </select>

    <select id="selectByIdList" resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
     SELECT <include refid="selectAllColumn" />
        FROM hotel_gn_${tableSuffix}
        <where>
            id IN
            <foreach item="item" collection="idList" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
    </select>


    <select id="selectHotelGnBaseList" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnBase"
            resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        select <include refid="selectAllColumn" />
        from hotel_gn_${tableSuffix}
        <where>
            is_delete = 0
            <if test="hotelGnBase.status != null ">and status = #{hotelGnBase.status}</if>
            <if test="hotelGnBase.cityId != null  and hotelGnBase.cityId != ''">and city_id = #{hotelGnBase.cityId}</if>
            <if test="hotelGnBase.cityName != null  and hotelGnBase.vcityName != ''">and city_name =
                #{hotelGnBase.cityName}
            </if>
            <if test="hotelGnBase.id != null  and hotelGnBase.id != ''">and id = #{hotelGnBase.id}</if>
            <if test="hotelGnBase.name != null  and hotelGnBase.name != ''">and name like concat('%',
                #{hotelGnBase.name}, '%')
            </if>
            <if test="hotelGnBase.reserve9 != null  and hotelGnBase.reserve9 != ''">and reserve9 =
                #{hotelGnBase.reserve9}
            </if>
            <if test="hotelGnBase.reserve9 == null or hotelGnBase.reserve9 == ''">and is_delete = 0</if>
            <if test="hotelGnBase.incrementStatus != null ">and increment_status = #{hotelGnBase.incrementStatus}</if>
        </where>
    </select>

    <insert id="insertSelective" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        INSERT INTO hotel_gn_${tableSuffix}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hotelGnBase.id != null">id,</if>
            <if test="hotelGnBase.name != null">name,</if>
            <if test="hotelGnBase.nameEn != null">name_en,</if>
            <if test="hotelGnBase.typeId != null">type_id,</if>
            <if test="hotelGnBase.openDate != null">open_date,</if>
            <if test="hotelGnBase.decorationDate != null">decoration_date,</if>
            <if test="hotelGnBase.phone != null">phone,</if>
            <if test="hotelGnBase.countryId != null">country_id,</if>
            <if test="hotelGnBase.countryName != null">country_name,</if>
            <if test="hotelGnBase.provinceId != null">province_id,</if>
            <if test="hotelGnBase.provinceName != null">province_name,</if>
            <if test="hotelGnBase.cityId != null">city_id,</if>
            <if test="hotelGnBase.cityName != null">city_name,</if>
            <if test="hotelGnBase.areaId != null">area_id,</if>
            <if test="hotelGnBase.areaName != null">area_name,</if>
            <if test="hotelGnBase.businessDistrictId != null">business_district_id,</if>
            <if test="hotelGnBase.businessDistrictName != null">business_district_name,</if>
            <if test="hotelGnBase.address != null">address,</if>
            <if test="hotelGnBase.addressEn != null">address_en,</if>
            <if test="hotelGnBase.lonGoogle != null">lon_google,</if>
            <if test="hotelGnBase.latGoogle != null">lat_google,</if>
            <if test="hotelGnBase.lonBaidu != null">lon_baidu,</if>
            <if test="hotelGnBase.latBaidu != null">lat_baidu,</if>
            <if test="hotelGnBase.lonGaode != null">lon_gaode,</if>
            <if test="hotelGnBase.latGaode != null">lat_gaode,</if>
            <if test="hotelGnBase.groupId != null">group_id,</if>
            <if test="hotelGnBase.groupName != null">group_name,</if>
            <if test="hotelGnBase.brandId != null">brand_id,</if>
            <if test="hotelGnBase.brandName != null">brand_name,</if>
            <if test="hotelGnBase.star != null">star,</if>
            <if test="hotelGnBase.image != null">image,</if>
            <if test="hotelGnBase.score != null">score,</if>
            <if test="hotelGnBase.synopsis != null">synopsis,</if>
            <if test="hotelGnBase.sparkle != null">sparkle,</if>
            <if test="hotelGnBase.policyInfo != null">policy_info,</if>
            <if test="hotelGnBase.facilitiesInfo != null">facilities_info,</if>
            <if test="hotelGnBase.roundInfo != null">round_info,</if>
            <if test="hotelGnBase.status != null">status,</if>
            <if test="hotelGnBase.isDelete != null">is_delete,</if>
            <if test="hotelGnBase.remark != null">remark,</if>
            <if test="hotelGnBase.incrementStatus != null">increment_status,</if>
            <if test="hotelGnBase.incrementTime != null">increment_time,</if>
            <if test="hotelGnBase.incrementType != null">increment_type,</if>
            <if test="hotelGnBase.createTime != null">create_time,</if>
            <if test="hotelGnBase.updateTime != null">update_time,</if>
            <if test="hotelGnBase.createBy != null">create_by,</if>
            <if test="hotelGnBase.updateBy != null">update_by,</if>
            <if test="hotelGnBase.reserve0 != null">reserve0,</if>
            <if test="hotelGnBase.reserve1 != null">reserve1,</if>
            <if test="hotelGnBase.reserve2 != null">reserve2,</if>
            <if test="hotelGnBase.reserve3 != null">reserve3,</if>
            <if test="hotelGnBase.reserve4 != null">reserve4,</if>
            <if test="hotelGnBase.reserve5 != null">reserve5,</if>
            <if test="hotelGnBase.reserve6 != null">reserve6,</if>
            <if test="hotelGnBase.reserve7 != null">reserve7,</if>
            <if test="hotelGnBase.reserve8 != null">reserve8,</if>
            <if test="hotelGnBase.reserve9 != null">reserve9,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="hotelGnBase.id != null">#{hotelGnBase.id},</if>
            <if test="hotelGnBase.name != null">#{hotelGnBase.name},</if>
            <if test="hotelGnBase.nameEn != null">#{hotelGnBase.nameEn},</if>
            <if test="hotelGnBase.typeId != null">#{hotelGnBase.typeId},</if>
            <if test="hotelGnBase.openDate != null">#{hotelGnBase.openDate},</if>
            <if test="hotelGnBase.decorationDate != null">#{hotelGnBase.decorationDate},</if>
            <if test="hotelGnBase.phone != null">#{hotelGnBase.phone},</if>
            <if test="hotelGnBase.countryId != null">#{hotelGnBase.countryId},</if>
            <if test="hotelGnBase.countryName != null">#{hotelGnBase.countryName},</if>
            <if test="hotelGnBase.provinceId != null">#{hotelGnBase.provinceId},</if>
            <if test="hotelGnBase.provinceName != null">#{hotelGnBase.provinceName},</if>
            <if test="hotelGnBase.cityId != null">#{hotelGnBase.cityId},</if>
            <if test="hotelGnBase.cityName != null">#{hotelGnBase.cityName},</if>
            <if test="hotelGnBase.areaId != null">#{hotelGnBase.areaId},</if>
            <if test="hotelGnBase.areaName != null">#{hotelGnBase.areaName},</if>
            <if test="hotelGnBase.businessDistrictId != null">#{hotelGnBase.businessDistrictId},</if>
            <if test="hotelGnBase.businessDistrictName != null">#{hotelGnBase.businessDistrictName},</if>
            <if test="hotelGnBase.address != null">#{hotelGnBase.address},</if>
            <if test="hotelGnBase.addressEn != null">#{hotelGnBase.addressEn},</if>
            <if test="hotelGnBase.lonGoogle != null">#{hotelGnBase.lonGoogle},</if>
            <if test="hotelGnBase.latGoogle != null">#{hotelGnBase.latGoogle},</if>
            <if test="hotelGnBase.lonBaidu != null">#{hotelGnBase.lonBaidu},</if>
            <if test="hotelGnBase.latBaidu != null">#{hotelGnBase.latBaidu},</if>
            <if test="hotelGnBase.lonGaode != null">#{hotelGnBase.lonGaode},</if>
            <if test="hotelGnBase.latGaode != null">#{hotelGnBase.latGaode},</if>
            <if test="hotelGnBase.groupId != null">#{hotelGnBase.groupId},</if>
            <if test="hotelGnBase.groupName != null">#{hotelGnBase.groupName},</if>
            <if test="hotelGnBase.brandId != null">#{hotelGnBase.brandId},</if>
            <if test="hotelGnBase.brandName != null">#{hotelGnBase.brandName},</if>
            <if test="hotelGnBase.star != null">#{hotelGnBase.star},</if>
            <if test="hotelGnBase.image != null">#{hotelGnBase.image},</if>
            <if test="hotelGnBase.score != null">#{hotelGnBase.score},</if>
            <if test="hotelGnBase.synopsis != null">#{hotelGnBase.synopsis},</if>
            <if test="hotelGnBase.sparkle != null">#{hotelGnBase.sparkle},</if>
            <if test="hotelGnBase.policyInfo != null">#{hotelGnBase.policyInfo},</if>
            <if test="hotelGnBase.facilitiesInfo != null">#{hotelGnBase.facilitiesInfo},</if>
            <if test="hotelGnBase.roundInfo != null">#{hotelGnBase.roundInfo},</if>
            <if test="hotelGnBase.status != null">#{hotelGnBase.status},</if>
            <if test="hotelGnBase.isDelete != null">#{hotelGnBase.isDelete},</if>
            <if test="hotelGnBase.remark != null">#{hotelGnBase.remark},</if>
            <if test="hotelGnBase.incrementStatus != null">#{hotelGnBase.incrementStatus},</if>
            <if test="hotelGnBase.incrementTime != null">#{hotelGnBase.incrementTime},</if>
            <if test="hotelGnBase.incrementType != null">#{hotelGnBase.incrementType},</if>
            <if test="hotelGnBase.createTime != null">#{hotelGnBase.createTime},</if>
            <if test="hotelGnBase.updateTime != null">#{hotelGnBase.updateTime},</if>
            <if test="hotelGnBase.createBy != null">#{hotelGnBase.createBy},</if>
            <if test="hotelGnBase.updateBy != null">#{hotelGnBase.updateBy},</if>
            <if test="hotelGnBase.reserve0 != null">#{hotelGnBase.reserve0},</if>
            <if test="hotelGnBase.reserve1 != null">#{hotelGnBase.reserve1},</if>
            <if test="hotelGnBase.reserve2 != null">#{hotelGnBase.reserve2},</if>
            <if test="hotelGnBase.reserve3 != null">#{hotelGnBase.reserve3},</if>
            <if test="hotelGnBase.reserve4 != null">#{hotelGnBase.reserve4},</if>
            <if test="hotelGnBase.reserve5 != null">#{hotelGnBase.reserve5},</if>
            <if test="hotelGnBase.reserve6 != null">#{hotelGnBase.reserve6},</if>
            <if test="hotelGnBase.reserve7 != null">#{hotelGnBase.reserve7},</if>
            <if test="hotelGnBase.reserve8 != null">#{hotelGnBase.reserve8},</if>
            <if test="hotelGnBase.reserve9 != null">#{hotelGnBase.reserve9},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        UPDATE hotel_gn_${tableSuffix}
        <set>
            <if test="hotelGnBase.name != null">name = #{hotelGnBase.name},</if>
            <if test="hotelGnBase.nameEn != null">name_en = #{hotelGnBase.nameEn},</if>
            <if test="hotelGnBase.typeId != null">type_id = #{hotelGnBase.typeId},</if>
            <if test="hotelGnBase.openDate != null">open_date = #{hotelGnBase.openDate},</if>
            <if test="hotelGnBase.decorationDate != null">decoration_date = #{hotelGnBase.decorationDate},</if>
            <if test="hotelGnBase.phone != null">phone = #{hotelGnBase.phone},</if>
            <if test="hotelGnBase.countryId != null">country_id = #{hotelGnBase.countryId},</if>
            <if test="hotelGnBase.countryName != null">country_name = #{hotelGnBase.countryName},</if>
            <if test="hotelGnBase.provinceId != null">province_id = #{hotelGnBase.provinceId},</if>
            <if test="hotelGnBase.provinceName != null">province_name = #{hotelGnBase.provinceName},</if>
            <if test="hotelGnBase.cityId != null">city_id = #{hotelGnBase.cityId},</if>
            <if test="hotelGnBase.cityName != null">city_name = #{hotelGnBase.cityName},</if>
            <if test="hotelGnBase.areaId != null">area_id = #{hotelGnBase.areaId},</if>
            <if test="hotelGnBase.areaName != null">area_name = #{hotelGnBase.areaName},</if>
            <if test="hotelGnBase.businessDistrictId != null">business_district_id =
                #{hotelGnBase.businessDistrictId},
            </if>
            <if test="hotelGnBase.businessDistrictName != null">business_district_name =
                #{hotelGnBase.businessDistrictName},
            </if>
            <if test="hotelGnBase.address != null">address = #{hotelGnBase.address},</if>
            <if test="hotelGnBase.addressEn != null">address_en = #{hotelGnBase.addressEn},</if>
            <if test="hotelGnBase.lonGoogle != null">lon_google = #{hotelGnBase.lonGoogle},</if>
            <if test="hotelGnBase.latGoogle != null">lat_google = #{hotelGnBase.latGoogle},</if>
            <if test="hotelGnBase.lonBaidu != null">lon_baidu = #{hotelGnBase.lonBaidu},</if>
            <if test="hotelGnBase.latBaidu != null">lat_baidu = #{hotelGnBase.latBaidu},</if>
            <if test="hotelGnBase.lonGaode != null">lon_gaode = #{hotelGnBase.lonGaode},</if>
            <if test="hotelGnBase.latGaode != null">lat_gaode = #{hotelGnBase.latGaode},</if>
            <if test="hotelGnBase.groupId != null">group_id = #{hotelGnBase.groupId},</if>
            <if test="hotelGnBase.groupName != null">group_name = #{hotelGnBase.groupName},</if>
            <if test="hotelGnBase.brandId != null">brand_id = #{hotelGnBase.brandId},</if>
            <if test="hotelGnBase.brandName != null">brand_name = #{hotelGnBase.brandName},</if>
            <if test="hotelGnBase.star != null">star = #{hotelGnBase.star},</if>
            <if test="hotelGnBase.image != null">image = #{hotelGnBase.image},</if>
            <if test="hotelGnBase.score != null">score = #{hotelGnBase.score},</if>
            <if test="hotelGnBase.synopsis != null">synopsis = #{hotelGnBase.synopsis},</if>
            <if test="hotelGnBase.sparkle != null">sparkle = #{hotelGnBase.sparkle},</if>
            <if test="hotelGnBase.policyInfo != null">policy_info = #{hotelGnBase.policyInfo},</if>
            <if test="hotelGnBase.facilitiesInfo != null">facilities_info = #{hotelGnBase.facilitiesInfo},</if>
            <if test="hotelGnBase.roundInfo != null">round_info = #{hotelGnBase.roundInfo},</if>
            <if test="hotelGnBase.status != null">status = #{hotelGnBase.status},</if>
            <if test="hotelGnBase.isDelete != null">is_delete = #{hotelGnBase.isDelete},</if>
            <if test="hotelGnBase.remark != null">remark = #{hotelGnBase.remark},</if>
            <if test="hotelGnBase.incrementStatus != null">increment_status = #{hotelGnBase.incrementStatus},</if>
            <if test="hotelGnBase.incrementTime != null">increment_time = #{hotelGnBase.incrementTime},</if>
            <if test="hotelGnBase.incrementType != null">increment_type = #{hotelGnBase.incrementType},</if>
            <if test="hotelGnBase.updateTime != null">update_time = #{hotelGnBase.updateTime},</if>
            <if test="hotelGnBase.updateBy != null">update_by = #{hotelGnBase.updateBy},</if>
            <if test="hotelGnBase.reserve0 != null">reserve0 = #{hotelGnBase.reserve0},</if>
            <if test="hotelGnBase.reserve1 != null">reserve1 = #{hotelGnBase.reserve1},</if>
            <if test="hotelGnBase.reserve2 != null">reserve2 = #{hotelGnBase.reserve2},</if>
            <if test="hotelGnBase.reserve3 != null">reserve3 = #{hotelGnBase.reserve3},</if>
            <if test="hotelGnBase.reserve4 != null">reserve4 = #{hotelGnBase.reserve4},</if>
            <if test="hotelGnBase.reserve5 != null">reserve5 = #{hotelGnBase.reserve5},</if>
            <if test="hotelGnBase.reserve6 != null">reserve6 = #{hotelGnBase.reserve6},</if>
            <if test="hotelGnBase.reserve7 != null">reserve7 = #{hotelGnBase.reserve7},</if>
            <if test="hotelGnBase.reserve8 != null">reserve8 = #{hotelGnBase.reserve8},</if>
            <if test="hotelGnBase.reserve9 != null">reserve9 = #{hotelGnBase.reserve9},</if>
        </set>
        WHERE id = #{hotelGnBase.id}
    </update>

    <select id="selectAll" resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        SELECT <include refid="selectAllColumn" />
        FROM hotel_gn_${tableSuffix}
    </select>

    <select id="selectAllId" resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        SELECT id FROM hotel_gn_${tableSuffix}
    </select>

    <select id="selectByIncrementStatus" resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        SELECT <include refid="selectAllColumn" />
        FROM hotel_gn_${tableSuffix}
        where increment_status = #{incrementStatus}
    </select>

    <select id="selectByStatus" resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        SELECT <include refid="selectAllColumn" />
        FROM hotel_gn_${tableSuffix}
        where `status` = #{status}
    </select>

    <update id="updateStatusById">
        UPDATE hotel_gn_${tableSuffix} SET status = #{status}, update_by = #{updateBy}, update_time = #{updateTime} where id= #{id}
    </update>

    <select id="selectByLimit" resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        SELECT id,`name`,city_name,address,status,phone FROM hotel_gn_${tableSuffix} where 1=1
        <if test="notStatus != null">  and status != ${notStatus}</if>
        <if test="minId != null and minId != '' "> and id >= #{minId} </if>
        <if test="maxId != null and maxId != '' "> and #{maxId} > id </if>
        order by id limit ${offset}, ${limit}
    </select>

    <select id="selectByCityIds" resultType="com.ltgj.supplier.common.gn.domain.HotelGnBase">
        select id, city_id from hotel_gn_${tableSuffix}
        <where>
            is_delete = 0
            <if test="cityIds != null and cityIds.size() > 0">
                and city_id in
                <foreach collection="cityIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
