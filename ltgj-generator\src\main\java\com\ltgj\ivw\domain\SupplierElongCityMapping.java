package com.ltgj.ivw.domain;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ltgj.common.annotation.Excel;
import com.ltgj.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 供应商与艺龙城市映射关系对象 supplier_elong_city_mapping
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierElongCityMapping extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 供应商编码 */
    @Excel(name = "供应商编码")
    private String supplierCode;

    /** 供应商省份编码 */
    @Excel(name = "供应商省份编码")
    private String supplierProvinceCode;

    /** 省份名称 */
    @Excel(name = "省份名称")
    private String provinceName;

    /** 供应商城市编码 */
    @Excel(name = "供应商城市编码")
    private String supplierCityCode;

    /** 供应商城市名称 */
    @Excel(name = "供应商城市名称")
    private String supplierCityName;

    /** 艺龙省份编码 */
    @Excel(name = "艺龙省份编码")
    private String elongProvinceCode;

    /** 艺龙城市编码 */
    @Excel(name = "艺龙城市编码")
    private String elongCityCode;

    /** 映射类型（1-ID映射；2-名称映射） */
    @Excel(name = "映射类型", readConverterExp = "1=ID映射,2=名称映射")
    private String mappingType;

    /** 映射分数（用于评估映射质量） */
    @Excel(name = "映射分数")
    private BigDecimal mappingScore;

    /** 删除标记（0代表存在 1代表删除） */
    private String delFlag;

    /** 状态（0正常 1停用） */
    private String status;


    @Override
    public String toString() {
        return "SupplierElongCityMapping [id=" + id + ", supplierCode=" + supplierCode + ", supplierProvinceCode="
                + supplierProvinceCode
                + ", provinceName=" + provinceName + ", supplierCityCode=" + supplierCityCode + ", supplierCityName="
                + supplierCityName + ", elongProvinceCode=" + elongProvinceCode + ", elongCityCode=" + elongCityCode
                + ", mappingType=" + mappingType + ", mappingScore=" + mappingScore + ", delFlag=" + delFlag
                + ", status=" + status + "]";
    }
}