package com.ltgj.ivw.service;

import com.ltgj.ivw.enums.PlatEnum;

/**
 * canal监听酒店相关表业务处理
 */
public interface CanalListenerService {

    /**
     * 处理jd_jdb表消息
     * @param msg
     */
    void dealJdJdbCanalMsg(String msg);

    void dealJdJdbCanalMsgForKnowledge(String msg);

    void dealJdJdbCanalMsgForKnowledgeWithChunk(String msg);

    /**
     * 处理zh_jd_jdb_mapping表消息
     * @param msg
     */
    void dealZhJdJdbMappingCanalMsg(String msg);

    /**
     * 处理hotel_info_hsjlxy表消息
     * @param msg
     */
    void dealHotelInfoHsjlxyCanalMsg(String msg);

    void dealHotelCanalMsg(String msg, PlatEnum platHsjl);
}
