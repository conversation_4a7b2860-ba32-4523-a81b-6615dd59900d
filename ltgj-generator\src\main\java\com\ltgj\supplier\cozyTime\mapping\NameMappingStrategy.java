package com.ltgj.supplier.cozyTime.mapping;

import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.domain.SupplierElongCityMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 名称映射策略实现类
 * 通过城市名称进行映射
 * 
 * <AUTHOR>
 * @date 2024-12-21
 */
@Component
public class NameMappingStrategy implements CityMappingStrategy {
    
    private static final Logger log = LoggerFactory.getLogger(NameMappingStrategy.class);
    
    /**
     * 映射类型：名称映射
     */
    private static final String MAPPING_TYPE = "2";
    
    @Override
    public HotelCityMapping createMapping(SupplierElongCityMapping mapping, 
                                        Map<String, HotelCity> ketanCityMap,
                                        List<HotelCityMapping> elongCityMappings) {
        String ketanCityCode = mapping.getSupplierCityCode();
        String ketanCityName = mapping.getSupplierCityName();
        String elongCityCode = mapping.getElongCityCode();
        
        if (StringUtils.isEmpty(ketanCityCode) || StringUtils.isEmpty(elongCityCode)) {
            log.warn("科坦城市编码或艺龙城市编码为空，无法创建映射");
            return null;
        }
        
        // 通过名称查找科坦城市
        HotelCity ketanCity = null;
        if (StringUtils.isNotEmpty(ketanCityName)) {
            // 先尝试通过编码查找
            ketanCity = ketanCityMap.get(ketanCityCode);
            
            // 如果找不到或者名称不匹配，则通过名称查找
            if (ketanCity == null || !StringUtils.equals(getMathStr(ketanCity.getCityName()), getMathStr(ketanCityName))) {
                // 遍历所有城市，查找名称匹配的城市
                for (HotelCity city : ketanCityMap.values()) {
                    if (StringUtils.equals(getMathStr(city.getCityName()), getMathStr(ketanCityName))) {
                        ketanCity = city;
                        break;
                    }
                }
            }
        } else {
            // 如果没有名称，则通过编码查找
            ketanCity = ketanCityMap.get(ketanCityCode);
        }
        
        if (ketanCity == null) {
            log.warn("未找到科坦城市名称{}对应的城市数据，无法创建映射", ketanCityName);
            return null;
        }
        
        if (elongCityMappings == null || elongCityMappings.isEmpty()) {
            log.warn("未找到艺龙城市编码{}对应的映射数据，无法创建映射", elongCityCode);
            return null;
        }
        
        // 创建科坦到本地的映射
        HotelCityMapping ketanMapping = new HotelCityMapping();
        ketanMapping.setInterfacePlat(Long.valueOf(ketanCity.getReserve1()));
        ketanMapping.setLocalId(elongCityMappings.get(0).getLocalId());
        ketanMapping.setPlatNum(ketanCity.getCityId());
        ketanMapping.setCityName(ketanCity.getCityName());
        
        return ketanMapping;
    }
    
    @Override
    public String getMappingType() {
        return MAPPING_TYPE;
    }
    
    /**
     * 获取标准化的城市名称（用于匹配）
     * 
     * @param cityName 城市名称
     * @return 标准化后的城市名称
     */
    private String getMathStr(String cityName) {
        if (StringUtils.isEmpty(cityName)) {
            return "";
        }
        // 去除空格和特殊字符
        return cityName.replaceAll("\\s+", "").replaceAll("[\\(\\)（）]", "");
    }
} 