package com.ltgj.supplier.map.response;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by heyuanjing on 18/9/11.
 */
public class BMapPlaceLocation implements Serializable {

	private static final long serialVersionUID = 1L;

	private BigDecimal lat;

    private BigDecimal lng;

    public BigDecimal getLat() {
        return lat;
    }

    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }

    public BigDecimal getLng() {
        return lng;
    }

    public void setLng(BigDecimal lng) {
        this.lng = lng;
    }
}
