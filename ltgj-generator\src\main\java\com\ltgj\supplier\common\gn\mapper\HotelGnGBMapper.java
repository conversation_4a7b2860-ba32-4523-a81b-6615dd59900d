package com.ltgj.supplier.common.gn.mapper;

import com.ltgj.supplier.common.gn.domain.HotelGnGB;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HotelGnGBMapper {

    int addBatch(@Param("list") List<HotelGnGB> list);

    List<HotelGnGB> selectByPlatformIdAndType(@Param("platformId") String platformId, @Param("type") String type);

    List<HotelGnGB> selectByPlatformIdAndTypeAndParentId(@Param("platformId") String platformId, @Param("type") String type, @Param("parentGbId") String parentGbId);

    HotelGnGB selectByPlatformIdAndTypeAndGBName(@Param("platformId") String platformId, @Param("type") String type, @Param("gbName") String gbName);

    HotelGnGB selectByPlatformIdAndTypeAndGBId(@Param("platformId") String platformId, @Param("type") String type, @Param("gbId") String gbId);

    int updateMappingStatus(HotelGnGB hotelGnGB);

    String selectMaxGbId(@Param("platformId") String platformId, @Param("type") String type);

    List<HotelGnGB> selectAllByPlatformId(@Param("platformId") String platformId);

    int countByPlatformIdAndTypeAndParam(@Param("platformId") String platformId, @Param("type") String type
            , @Param("gbId") String gbId
            , @Param("gbName") String gbName);

    List<HotelGnGB> selectByPlatformIdAndTypeAndParam(@Param("platformId") String platformId, @Param("type") String type
            , @Param("gbId") String gbId
            , @Param("gbName") String gbName
            , @Param("start") int start
            , @Param("pageSize") int pageSize);
}
