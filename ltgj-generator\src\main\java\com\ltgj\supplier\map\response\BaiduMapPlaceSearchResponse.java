package com.ltgj.supplier.map.response;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * 百度地图位置搜索响应
 *
 * <AUTHOR> SYH
 * @date 2025-06-06
 */
public class BaiduMapPlaceSearchResponse {

    private String address;

    private String uid;

    private String city;

    private String cityid;

    private String area;

    private Integer adcode;

    private String tag;

    private String name;

    private String province;

    private BMapPlaceLocation location;

    private List<BmaPlaceSearchChildren> children;

    @JSONField(name = "detail_info")
    private BmapPlaceDetailInfo detailInfo;

    public List<BmaPlaceSearchChildren> getChildren() {
        return children;
    }

    public void setChildren(List<BmaPlaceSearchChildren> children) {
        this.children = children;
    }

    public BmapPlaceDetailInfo getDetailInfo() {
        return detailInfo;
    }

    public void setDetailInfo(BmapPlaceDetailInfo detailInfo) {
        this.detailInfo = detailInfo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public BMapPlaceLocation getLocation() {
        return location;
    }

    public void setLocation(BMapPlaceLocation location) {
        this.location = location;
    }

    public String getCityid() {
        return cityid;
    }

    public void setCityid(String cityid) {
        this.cityid = cityid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getAdcode() {
        return adcode;
    }

    public void setAdcode(Integer adcode) {
        this.adcode = adcode;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }
}
