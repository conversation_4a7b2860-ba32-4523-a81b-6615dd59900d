package com.ltgj.common.core.domain;

import java.util.HashMap;
import com.ltgj.common.constant.HttpStatus;
import com.ltgj.common.utils.StringUtils;

/**
 * 操作消息提醒
 *
 * <AUTHOR>
 */
public class AjaxResult<T> extends HashMap<String, Object>
{
    private static final long serialVersionUID = 1L;

    /** 状态码 */
    public static final String CODE_TAG = "code";

    /** 返回内容 */
    public static final String MSG_TAG = "msg";

    /** 数据对象 */
    public static final String DATA_TAG = "data";

    /**
     * 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。
     */
    public AjaxResult()
    {
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态码
     * @param msg 返回内容
     */
    public AjaxResult(int code, String msg)
    {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态码
     * @param msg 返回内容
     * @param data 数据对象
     */
    public AjaxResult(int code, String msg, Object data)
    {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
        if (StringUtils.isNotNull(data))
        {
            super.put(DATA_TAG, data);
        }
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static AjaxResult<?> success()
    {
        return AjaxResult.success("操作成功");
    }

    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static AjaxResult<?> success(Object data)
    {
        return AjaxResult.success("操作成功", data);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @return 成功消息
     */
    public static AjaxResult<?> success(String msg)
    {
        return AjaxResult.success(msg, null);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static AjaxResult<?> success(String msg, Object data)
    {
        return new AjaxResult<>(HttpStatus.SUCCESS, msg, data);
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static AjaxResult<?> warn(String msg)
    {
        return AjaxResult.warn(msg, null);
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static AjaxResult<?> warn(String msg, Object data)
    {
        return new AjaxResult<>(HttpStatus.WARN, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @return 错误消息
     */
    public static AjaxResult<?> error()
    {
        return AjaxResult.error("操作失败");
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @return 错误消息
     */
    public static AjaxResult<?> error(String msg)
    {
        return AjaxResult.error(msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static AjaxResult<?> error(String msg, Object data)
    {
        return new AjaxResult<>(HttpStatus.ERROR, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param msg 返回内容
     * @return 错误消息
     */
    public static AjaxResult<?> error(int code, String msg)
    {
        return new AjaxResult<>(code, msg, null);
    }

    public static AjaxResult<?> fail(String msg) {
        return new AjaxResult<>(500, msg, null);
    }

    // ============= 新增的泛型方法 =============

    /**
     * 返回成功数据（泛型版本）
     *
     * @param data 数据对象
     * @param <U> 数据类型
     * @return 成功消息
     */
    public static <U> AjaxResult<U> successWith(U data)
    {
        return AjaxResult.successWith("操作成功", data);
    }

    /**
     * 返回成功消息（泛型版本）
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @param <U> 数据类型
     * @return 成功消息
     */
    public static <U> AjaxResult<U> successWith(String msg, U data)
    {
        return new AjaxResult<U>(HttpStatus.SUCCESS, msg, data);
    }

    /**
     * 返回警告消息（泛型版本）
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @param <U> 数据类型
     * @return 警告消息
     */
    public static <U> AjaxResult<U> warnWith(String msg, U data)
    {
        return new AjaxResult<U>(HttpStatus.WARN, msg, data);
    }

    /**
     * 返回错误消息（泛型版本）
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @param <U> 数据类型
     * @return 错误消息
     */
    public static <U> AjaxResult<U> errorWith(String msg, U data)
    {
        return new AjaxResult<U>(HttpStatus.ERROR, msg, data);
    }

    /**
     * 返回指定类型的错误消息（用于类型匹配）
     *
     * @param msg 返回内容
     * @param <U> 数据类型
     * @return 错误消息
     */
    public static <U> AjaxResult<U> errorFor(String msg)
    {
        return new AjaxResult<U>(HttpStatus.ERROR, msg, null);
    }

    /**
     * 返回指定类型的警告消息（用于类型匹配）
     *
     * @param msg 返回内容
     * @param <U> 数据类型
     * @return 警告消息
     */
    public static <U> AjaxResult<U> warnFor(String msg)
    {
        return new AjaxResult<U>(HttpStatus.WARN, msg, null);
    }

    /**
     * 返回指定类型的成功消息（用于类型匹配）
     *
     * @param msg 返回内容
     * @param <U> 数据类型
     * @return 成功消息
     */
    public static <U> AjaxResult<U> successFor(String msg)
    {
        return new AjaxResult<U>(HttpStatus.SUCCESS, msg, null);
    }

    /**
     * 返回指定类型的成功消息（用于类型匹配）
     *
     * @param <U> 数据类型
     * @return 成功消息
     */
    public static <U> AjaxResult<U> successFor()
    {
        return new AjaxResult<U>(HttpStatus.SUCCESS, "操作成功", null);
    }

    /**
     * 获取类型安全的数据对象
     *
     * @return 数据对象
     */
    @SuppressWarnings("unchecked")
    public T getData()
    {
        return (T) super.get(DATA_TAG);
    }

    /**
     * 设置数据对象（类型安全版本）
     *
     * @param data 数据对象
     * @return 当前对象，支持链式调用
     */
    public AjaxResult<T> setData(T data)
    {
        super.put(DATA_TAG, data);
        return this;
    }

    /**
     * 方便链式调用
     *
     * @param key 键
     * @param value 值
     * @return 数据对象
     */
    @Override
    public AjaxResult<T> put(String key, Object value)
    {
        super.put(key, value);
        return this;
    }
}
