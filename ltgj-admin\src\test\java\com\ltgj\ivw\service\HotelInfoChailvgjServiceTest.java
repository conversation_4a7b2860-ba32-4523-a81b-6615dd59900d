package com.ltgj.ivw.service;

import com.ltgj.ivw.domain.dto.HotelDetailDTO;
import com.ltgj.ivw.service.impl.HotelInfoChailvgjServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.mockito.ArgumentMatchers.any;

/**
 * HotelInfoChailvgjServiceImpl的updateAll方法单元测试
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HotelInfoChailvgjServiceTest {
    @Autowired
    private IHotelInfoChailvgjService service;

    @Autowired
    private HotelInfoChailvgjServiceImpl hotelInfoChailvgjService;
    @Test
    public void setup() {
        try {
            service.updateAll("111");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void getOneHotel() {
        HotelDetailDTO hotelDetailDTO = hotelInfoChailvgjService.getHotelDetail("3840144");
        hotelInfoChailvgjService.saveHotelInfo(hotelDetailDTO);
    }

} 