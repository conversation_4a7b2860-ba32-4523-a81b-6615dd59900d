package com.ltgj.web.job.handler;

import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.IHotelInfoChailvgjService;
import com.ltgj.ivw.service.IHotelInfoHsjlService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler(value = "hsjlHotelInitDataHandler")
public class HsjlHotelInitDataHandler extends IJobHandler {
    @Autowired
    private IHotelInfoHsjlService hotelInfoHsjlService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try{
            hotelInfoHsjlService.resetStatus();
            hotelInfoHsjlService.updateHsjlAll("xxl-job", "xxl-job");
        }catch (Exception e) {
            e.printStackTrace();
            log.error("红色加力-酒店增量同步任务异常：{}", e);
        }
        log.info("红色加力-全量数据执行完成");
        return ReturnT.SUCCESS;
    }
}
