package com.ltgj.supplier.common.gn.vo.req;

import com.ltgj.common.core.page.PageLimit;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryBrandListReq extends PageLimit {

    @ApiModelProperty(value = "集团id")
    private String gbId;

    @ApiModelProperty(value = "集团名称")
    private String gbName;
}
