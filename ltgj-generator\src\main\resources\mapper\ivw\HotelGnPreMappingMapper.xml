<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.HotelGnPreMappingMapper">
  <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.HotelGnPreMapping">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="source_hotel_id" jdbcType="VARCHAR" property="sourceHotelId" />
    <result column="source_hotel_name" jdbcType="VARCHAR" property="sourceHotelName" />
    <result column="source_hotel_address" jdbcType="VARCHAR" property="sourceHotelAddress" />
    <result column="source_hotel_phone" jdbcType="VARCHAR" property="sourceHotelPhone" />
    <result column="source_hotel_city_name" jdbcType="VARCHAR" property="sourceHotelCityName" />
    <result column="soure_platform_id" jdbcType="VARCHAR" property="sourePlatformId" />
    <result column="target_hotel_id" jdbcType="VARCHAR" property="targetHotelId" />
    <result column="target_hotel_name" jdbcType="VARCHAR" property="targetHotelName" />
    <result column="target_hotel_address" jdbcType="VARCHAR" property="targetHotelAddress" />
    <result column="target_hotel_phone" jdbcType="VARCHAR" property="targetHotelPhone" />
    <result column="target_hotel_city_name" jdbcType="VARCHAR" property="targetHotelCityName" />
    <result column="target_platform_id" jdbcType="VARCHAR" property="targetPlatformId" />
    <result column="mapping_type" jdbcType="VARCHAR" property="mappingType" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="score_reason" jdbcType="VARCHAR" property="scoreReason" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="handler_reason" jdbcType="VARCHAR" property="handlerReason" />
    <result column="remark1" jdbcType="VARCHAR" property="remark1" />
    <result column="remark2" jdbcType="VARCHAR" property="remark2" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, source_hotel_id, source_hotel_name, source_hotel_address, source_hotel_phone,
    source_hotel_city_name, soure_platform_id, target_hotel_id, target_hotel_name, target_hotel_address,
    target_hotel_phone, target_hotel_city_name, target_platform_id, mapping_type, score,
    score_reason, `status`, handler_reason, remark1, remark2, create_time, update_time,
    create_by, update_by, is_delete
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select
    <include refid="Base_Column_List" />
    from hotel_gn_pre_mapping
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from hotel_gn_pre_mapping
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.ltgj.ivw.domain.HotelGnPreMapping">
    insert into hotel_gn_pre_mapping (id, source_hotel_id, source_hotel_name, source_hotel_address,
      source_hotel_phone, source_hotel_city_name,
      soure_platform_id, target_hotel_id, target_hotel_name,
      target_hotel_address, target_hotel_phone, target_hotel_city_name,
      target_platform_id, mapping_type, score,
      score_reason, `status`, handler_reason,
      remark1, remark2, create_time,
      update_time, create_by, update_by,
      is_delete)
    values (#{id,jdbcType=VARCHAR},#{sourceHotelId,jdbcType=VARCHAR}, #{sourceHotelName,jdbcType=VARCHAR}, #{sourceHotelAddress,jdbcType=VARCHAR},
      #{sourceHotelPhone,jdbcType=VARCHAR}, #{sourceHotelCityName,jdbcType=VARCHAR},
      #{sourePlatformId,jdbcType=VARCHAR}, #{targetHotelId,jdbcType=VARCHAR}, #{targetHotelName,jdbcType=VARCHAR},
      #{targetHotelAddress,jdbcType=VARCHAR}, #{targetHotelPhone,jdbcType=VARCHAR}, #{targetHotelCityName,jdbcType=VARCHAR},
      #{targetPlatformId,jdbcType=VARCHAR}, #{mappingType,jdbcType=VARCHAR}, #{score,jdbcType=INTEGER},
      #{scoreReason,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{handlerReason,jdbcType=VARCHAR},
      #{remark1,jdbcType=VARCHAR}, #{remark2,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{isDelete,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.ltgj.ivw.domain.HotelGnPreMapping">
    insert into hotel_gn_pre_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceHotelId != null">
        source_hotel_id,
      </if>
      <if test="sourceHotelName != null">
        source_hotel_name,
      </if>
      <if test="sourceHotelAddress != null">
        source_hotel_address,
      </if>
      <if test="sourceHotelPhone != null">
        source_hotel_phone,
      </if>
      <if test="sourceHotelCityName != null">
        source_hotel_city_name,
      </if>
      <if test="sourePlatformId != null">
        soure_platform_id,
      </if>
      <if test="targetHotelId != null">
        target_hotel_id,
      </if>
      <if test="targetHotelName != null">
        target_hotel_name,
      </if>
      <if test="targetHotelAddress != null">
        target_hotel_address,
      </if>
      <if test="targetHotelPhone != null">
        target_hotel_phone,
      </if>
      <if test="targetHotelCityName != null">
        target_hotel_city_name,
      </if>
      <if test="targetPlatformId != null">
        target_platform_id,
      </if>
      <if test="mappingType != null">
        mapping_type,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="scoreReason != null">
        score_reason,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="handlerReason != null">
        handler_reason,
      </if>
      <if test="remark1 != null">
        remark1,
      </if>
      <if test="remark2 != null">
        remark2,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelId != null">
        #{sourceHotelId,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelName != null">
        #{sourceHotelName,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelAddress != null">
        #{sourceHotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelPhone != null">
        #{sourceHotelPhone,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelCityName != null">
        #{sourceHotelCityName,jdbcType=VARCHAR},
      </if>
      <if test="sourePlatformId != null">
        #{sourePlatformId,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelId != null">
        #{targetHotelId,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelName != null">
        #{targetHotelName,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelAddress != null">
        #{targetHotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelPhone != null">
        #{targetHotelPhone,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelCityName != null">
        #{targetHotelCityName,jdbcType=VARCHAR},
      </if>
      <if test="targetPlatformId != null">
        #{targetPlatformId,jdbcType=VARCHAR},
      </if>
      <if test="mappingType != null">
        #{mappingType,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=INTEGER},
      </if>
      <if test="scoreReason != null">
        #{scoreReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="handlerReason != null">
        #{handlerReason,jdbcType=VARCHAR},
      </if>
      <if test="remark1 != null">
        #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null">
        #{remark2,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ltgj.ivw.domain.HotelGnPreMapping">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update hotel_gn_pre_mapping
    <set>
      <if test="sourceHotelId != null">
        source_hotel_id = #{sourceHotelId,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelName != null">
        source_hotel_name = #{sourceHotelName,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelAddress != null">
        source_hotel_address = #{sourceHotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelPhone != null">
        source_hotel_phone = #{sourceHotelPhone,jdbcType=VARCHAR},
      </if>
      <if test="sourceHotelCityName != null">
        source_hotel_city_name = #{sourceHotelCityName,jdbcType=VARCHAR},
      </if>
      <if test="sourePlatformId != null">
        soure_platform_id = #{sourePlatformId,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelId != null">
        target_hotel_id = #{targetHotelId,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelName != null">
        target_hotel_name = #{targetHotelName,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelAddress != null">
        target_hotel_address = #{targetHotelAddress,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelPhone != null">
        target_hotel_phone = #{targetHotelPhone,jdbcType=VARCHAR},
      </if>
      <if test="targetHotelCityName != null">
        target_hotel_city_name = #{targetHotelCityName,jdbcType=VARCHAR},
      </if>
      <if test="targetPlatformId != null">
        target_platform_id = #{targetPlatformId,jdbcType=VARCHAR},
      </if>
      <if test="mappingType != null">
        mapping_type = #{mappingType,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=INTEGER},
      </if>
      <if test="scoreReason != null">
        score_reason = #{scoreReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="handlerReason != null">
        handler_reason = #{handlerReason,jdbcType=VARCHAR},
      </if>
      <if test="remark1 != null">
        remark1 = #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null">
        remark2 = #{remark2,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ltgj.ivw.domain.HotelGnPreMapping">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update hotel_gn_pre_mapping
    set source_hotel_id = #{sourceHotelId,jdbcType=VARCHAR},
      source_hotel_name = #{sourceHotelName,jdbcType=VARCHAR},
      source_hotel_address = #{sourceHotelAddress,jdbcType=VARCHAR},
      source_hotel_phone = #{sourceHotelPhone,jdbcType=VARCHAR},
      source_hotel_city_name = #{sourceHotelCityName,jdbcType=VARCHAR},
      soure_platform_id = #{sourePlatformId,jdbcType=VARCHAR},
      target_hotel_id = #{targetHotelId,jdbcType=VARCHAR},
      target_hotel_name = #{targetHotelName,jdbcType=VARCHAR},
      target_hotel_address = #{targetHotelAddress,jdbcType=VARCHAR},
      target_hotel_phone = #{targetHotelPhone,jdbcType=VARCHAR},
      target_hotel_city_name = #{targetHotelCityName,jdbcType=VARCHAR},
      target_platform_id = #{targetPlatformId,jdbcType=VARCHAR},
      mapping_type = #{mappingType,jdbcType=VARCHAR},
      score = #{score,jdbcType=INTEGER},
      score_reason = #{scoreReason,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      handler_reason = #{handlerReason,jdbcType=VARCHAR},
      remark1 = #{remark1,jdbcType=VARCHAR},
      remark2 = #{remark2,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by = #{updateBy,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectList" resultType="com.ltgj.ivw.request.hotelGnPreMapping.ListResponse">
    select
    <include refid="Base_Column_List"/>
    from hotel_gn_pre_mapping
    <where>
      <if test="sourceHotelId != null">
        and source_hotel_id = #{sourceHotelId,jdbcType=VARCHAR}
      </if>
      <if test="sourceHotelName != null">
        and source_hotel_name like concat('%', #{sourceHotelName}, '%')
      </if>
      <if test="sourePlatformId != null">
        and soure_platform_id = #{sourePlatformId}
      </if>
      <if test="sourceHotelCityName != null">
        and source_hotel_city_name like concat('%', #{sourceHotelCityName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="targetHotelId != null">
        and target_hotel_id = #{targetHotelId,jdbcType=VARCHAR}
      </if>
      <if test="targetHotelName != null">
        and target_hotel_name like concat('%', #{targetHotelName}, '%')
      </if>
      <if test="mappingType != null">
        and mapping_type = #{mappingType,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        and status = #{status,jdbcType=TINYINT}
      </if>
      <if test="lowerScore != null">
        and score &gt;= #{lowerScore,jdbcType=INTEGER}
      </if>
      <if test="lowerScore != null">
        and score &lt;= #{highScore,jdbcType=INTEGER}
      </if>
    and is_delete = 0
    </where>
    order by create_time desc
  </select>
    <delete id="deleteByIds">
    update hotel_gn_pre_mapping
    set is_delete = 1
    where id in
    <foreach item="id" collection="ids" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
  <select id="selectNeedProcess" resultType="com.ltgj.ivw.domain.HotelGnPreMapping">
    select
    <include refid="Base_Column_List"/>
    from hotel_gn_pre_mapping
    <where>
      <if test="ids != null and ids.size() > 0">
        and source_hotel_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="interfacePlats != null and interfacePlats.size() > 0">
        and soure_platform_id in
        <foreach item="interfacePlat" collection="interfacePlats" open="(" separator="," close=")">
          #{interfacePlat}
        </foreach>
      </if>
      <if test="highScore != null">
        and score &gt;= #{highScore,jdbcType=INTEGER}
      </if>
      <if test="lowScore != null">
        and score &lt;= #{lowScore,jdbcType=INTEGER}
      </if>
      and status = 0
      and is_delete = 0
    </where>
    order by create_time,FIELD( soure_platform_id, '2000082', '2000023', '2000054', '2000077', '2000079', '2000010', '2000080' )
    limit #{batchSize}
  </select>

  <select id="selectListByStatus" resultType="com.ltgj.ivw.domain.HotelGnPreMapping">
    select source_hotel_id from hotel_gn_pre_mapping where source_hotel_id in
    <foreach item="id" collection="aiHotelList" open="(" separator="," close=")">
      #{id.sourceHotelId}
    </foreach>
    and status = 0
    and soure_platform_id = #{plat}
    and is_delete = 0
  </select>
</mapper>