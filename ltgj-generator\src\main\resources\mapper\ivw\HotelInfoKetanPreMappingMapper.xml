<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ltgj.ivw.mapper.HotelInfoKetanPreMappingMapper">
  <resultMap id="BaseResultMap" type="com.ltgj.ivw.domain.HotelInfoKetanPreMapping">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="source_id" jdbcType="VARCHAR" property="sourceId" />
    <result column="target_id" jdbcType="VARCHAR" property="targetId" />
    <result column="target_plat" jdbcType="VARCHAR" property="targetPlat" />
    <result column="mapping_type" jdbcType="INTEGER" property="mappingType" />
    <result column="mapping_state" jdbcType="INTEGER" property="mappingState" />
    <result column="mapping_reason" jdbcType="VARCHAR" property="mappingReason" />
    <result column="mapping_score" jdbcType="VARCHAR" property="mappingScore" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    id, source_id, target_id, target_plat, mapping_type, mapping_state, mapping_reason, 
    mapping_score, create_time, update_time, create_by, update_by, remark, is_delete
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select 
    <include refid="Base_Column_List" />
    from hotel_info_ketan_pre_mapping
    where id = #{id,jdbcType=VARCHAR}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from hotel_info_ketan_pre_mapping
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.ltgj.ivw.domain.HotelInfoKetanPreMapping">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into hotel_info_ketan_pre_mapping (source_id, target_id, target_plat, 
      mapping_type, mapping_state, mapping_reason, 
      mapping_score, create_time, update_time, 
      create_by, update_by, remark, 
      is_delete)
    values (#{sourceId,jdbcType=VARCHAR}, #{targetId,jdbcType=VARCHAR}, #{targetPlat,jdbcType=VARCHAR}, 
      #{mappingType,jdbcType=INTEGER}, #{mappingState,jdbcType=INTEGER}, #{mappingReason,jdbcType=VARCHAR}, 
      #{mappingScore,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.ltgj.ivw.domain.HotelInfoKetanPreMapping">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.String">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into hotel_info_ketan_pre_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="targetId != null">
        target_id,
      </if>
      <if test="targetPlat != null">
        target_plat,
      </if>
      <if test="mappingType != null">
        mapping_type,
      </if>
      <if test="mappingState != null">
        mapping_state,
      </if>
      <if test="mappingReason != null">
        mapping_reason,
      </if>
      <if test="mappingScore != null">
        mapping_score,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sourceId != null">
        #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="targetPlat != null">
        #{targetPlat,jdbcType=VARCHAR},
      </if>
      <if test="mappingType != null">
        #{mappingType,jdbcType=INTEGER},
      </if>
      <if test="mappingState != null">
        #{mappingState,jdbcType=INTEGER},
      </if>
      <if test="mappingReason != null">
        #{mappingReason,jdbcType=VARCHAR},
      </if>
      <if test="mappingScore != null">
        #{mappingScore,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ltgj.ivw.domain.HotelInfoKetanPreMapping">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update hotel_info_ketan_pre_mapping
    <set>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="targetId != null">
        target_id = #{targetId,jdbcType=VARCHAR},
      </if>
      <if test="targetPlat != null">
        target_plat = #{targetPlat,jdbcType=VARCHAR},
      </if>
      <if test="mappingType != null">
        mapping_type = #{mappingType,jdbcType=INTEGER},
      </if>
      <if test="mappingState != null">
        mapping_state = #{mappingState,jdbcType=INTEGER},
      </if>
      <if test="mappingReason != null">
        mapping_reason = #{mappingReason,jdbcType=VARCHAR},
      </if>
      <if test="mappingScore != null">
        mapping_score = #{mappingScore,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ltgj.ivw.domain.HotelInfoKetanPreMapping">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update hotel_info_ketan_pre_mapping
    set source_id = #{sourceId,jdbcType=VARCHAR},
      target_id = #{targetId,jdbcType=VARCHAR},
      target_plat = #{targetPlat,jdbcType=VARCHAR},
      mapping_type = #{mappingType,jdbcType=INTEGER},
      mapping_state = #{mappingState,jdbcType=INTEGER},
      mapping_reason = #{mappingReason,jdbcType=VARCHAR},
      mapping_score = #{mappingScore,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_by = #{updateBy,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <update id="batchUpdate">
    <foreach collection="list" item="item" separator=";">
      update hotel_info_ketan_pre_mapping set mapping_state = #{item.mappingState}, mapping_reason = #{item.mappingReason}, mapping_score = #{item.mappingScore}, update_time = #{item.updateTime} where id = #{item.id}
    </foreach>
  </update>

  <select id="selectIdsByPage" resultType="java.lang.Long">
    select id from hotel_info_ketan_pre_mapping where is_delete = 0 limit #{start}, #{size}
  </select>

  <insert id="insertBatch">
    insert into hotel_info_ketan_pre_mapping (
    source_id,
    target_id,
    target_plat,
    mapping_type,
    mapping_state,
    mapping_reason,
    mapping_score,
    create_time,
    update_time,
    create_by,
    update_by,
    remark,
    is_delete
    ) values
    <foreach item="item" collection="list" separator=",">
      (
      #{item.sourceId},
      #{item.targetId},
      #{item.targetPlat},
      #{item.mappingType},
      #{item.mappingState},
      #{item.mappingReason},
      #{item.mappingScore},
      #{item.createTime},
      #{item.updateTime},
      #{item.createBy},
      #{item.updateBy},
      #{item.remark},
      #{item.isDelete}
      )
    </foreach>
  </insert>

  <select id="selectNeedProcessByPage" resultType="com.ltgj.ivw.domain.HotelInfoKetanPreMapping">
    SELECT
    <include refid="Base_Column_List" />
    FROM
        hotel_info_ketan_pre_mapping
    WHERE
       is_delete = 0
    order by
      id
    LIMIT #{start}, #{size}

  </select>

  <select id="selectCount" resultType="java.lang.Integer">
    SELECT
      count(1)
    FROM
      hotel_info_ketan_pre_mapping
    WHERE
      is_delete = 0
  </select>

  <select id="selectBySourceIds" resultType="com.ltgj.ivw.domain.HotelInfoKetanPreMapping">
    SELECT
    <include refid="Base_Column_List" />
    FROM
      hotel_info_ketan_pre_mapping
    WHERE
      source_id IN
      <foreach collection="ids" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
      AND is_delete = 0
  </select>
</mapper>