package com.ltgj.supplier.common.gn.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HotelGnGBResp {

    @ApiModelProperty("集团或品牌id")
    private String gbId;
    @ApiModelProperty("集团或品牌名称")
    private String gbName;
    @ApiModelProperty("集团或品牌英文名称")
    private String gbNameEn;
}
