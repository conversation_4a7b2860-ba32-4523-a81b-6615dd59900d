package com.ltgj.ivw.service;

import com.alibaba.fastjson.JSON;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.supplier.common.dto.KetanCityDTO;
import com.ltgj.supplier.cozyTime.KetanApi;
import com.ltgj.supplier.cozyTime.KetanApi.HttpMethod;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * KetanApi测试类
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CozyTimeApiTest {

    @Before
    public void setup() {
        // 在测试前打印关键配置信息，帮助诊断问题
        System.out.println("===== 科坦API配置信息 =====");
        System.out.println("API地址: " + PropertiesUtil.getProp("ketan.url"));
        System.out.println("合作商编码: " + PropertiesUtil.getProp("ketan.partnerCode"));
        System.out.println("连接超时(ms): " + PropertiesUtil.getProp("ketan.connectTimeout", "60000"));
        System.out.println("读取超时(ms): " + PropertiesUtil.getProp("ketan.readTimeout", "60000"));
        // 不打印敏感信息如密钥
        System.out.println("===========================\n");
    }

    /**
     * 测试获取科坦城市列表（自动选择GET或POST方法）
     */
    @Test
    public void testGetCityList() {
        try {
            // 调用城市列表接口，自动选择合适的HTTP方法
            KetanCityDTO cityDTO = KetanApi.getCityList();
            printCityListResult(cityDTO, "自动选择方法");
        } catch (Exception e) {
            System.err.println("测试获取城市列表时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试使用GET方法获取科坦城市列表
     */
    @Test
    public void testGetCityListWithGet() {
        try {
            // 使用GET方法调用城市列表接口
            KetanCityDTO cityDTO = KetanApi.getCityList(HttpMethod.GET);
            printCityListResult(cityDTO, "GET方法");
        } catch (Exception e) {
            System.err.println("测试使用GET方法获取城市列表时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试使用POST方法获取科坦城市列表
     */
    @Test
    public void testGetCityListWithPost() {
        try {
            // 使用POST方法调用城市列表接口
            KetanCityDTO cityDTO = KetanApi.getCityList(HttpMethod.POST);
            printCityListResult(cityDTO, "POST方法");
        } catch (Exception e) {
            System.err.println("测试使用POST方法获取城市列表时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 打印城市列表结果
     * 
     * @param cityDTO 城市DTO
     * @param methodDesc 方法描述
     */
    private void printCityListResult(KetanCityDTO cityDTO, String methodDesc) {
        if (cityDTO != null) {
            System.out.println("===== 科坦城市列表获取成功 (" + methodDesc + ") =====");
            System.out.println("国家编码: " + cityDTO.getCountryCode());
            System.out.println("城市数量: " + (cityDTO.getCities() != null ? cityDTO.getCities().size() : 0));
            
            // 打印城市详情
            if (cityDTO.getCities() != null && !cityDTO.getCities().isEmpty()) {
                System.out.println("\n===== 城市详情示例（前5个） =====");
                int count = 0;
                for (KetanCityDTO.City city : cityDTO.getCities()) {
                    System.out.println("城市编码: " + city.getCityCode());
                    System.out.println("城市名称: " + city.getName());
                    System.out.println("城市中文名: " + city.getNameCN());
                    System.out.println("省份编码: " + city.getProvinceCode());
                    System.out.println("省份名称: " + city.getProvinceNameCN());
                    System.out.println("区县数量: " + (city.getDistricts() != null ? city.getDistricts().size() : 0));
                    System.out.println("------------------------");
                    
                    if (++count >= 5) {
                        break;
                    }
                }
            }
            
            // 输出完整JSON
            System.out.println("\n===== 完整JSON数据 =====");
            System.out.println(JSON.toJSONString(cityDTO, true));
        } else {
            System.out.println("获取科坦城市列表失败 (" + methodDesc + ")，返回为空");
        }
    }
    
    /**
     * 测试签名生成
     */
    @Test
    public void testGenerateSignature() {
        try {
            String partnerCode = PropertiesUtil.getProp("ketan.partnerCode", "test_partner");
            long timestamp = System.currentTimeMillis() / 1000;
            
            String signature = KetanApi.generateSignature(partnerCode, timestamp);
            System.out.println("===== 测试签名生成 =====");
            System.out.println("合作商编码: " + partnerCode);
            System.out.println("时间戳: " + timestamp);
            System.out.println("生成的签名: " + signature);
        } catch (Exception e) {
            System.err.println("测试签名生成时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 