package com.ltgj.ivw.strategy;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 地理位置查询策略接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface GeoQueryStrategy {
    
    /**
     * 执行查询
     * 
     * @param request 查询请求
     * @return 查询结果
     */
    List<GeoHierarchyNode> execute(GeoLevelQueryReq request);
    
    /**
     * 获取策略支持的查询级别
     * 
     * @return 查询级别
     */
    String getSupportedLevel();
    
} 