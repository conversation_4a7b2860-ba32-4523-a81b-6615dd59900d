package com.ltgj.ivw.mapper;


import com.ltgj.ivw.domain.HotelInfoCozytime;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface HotelInfoCozytimeMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(String id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(HotelInfoCozytime row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(HotelInfoCozytime row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    HotelInfoCozytime selectByPrimaryKey(String id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(HotelInfoCozytime row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeyWithBLOBs(HotelInfoCozytime row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(HotelInfoCozytime row);

    /**
     * 查询总数
     *
     * @return
     */
    int selectCount();

    /**
     * 分页查询id
     *
     * @param start
     * @param size
     * @return
     */
    List<String> selectIdsByPage(@Param("start") int start, @Param("size") int size);

    /**
     * 根据id批量查询
     *
     * @param sourceIds
     * @return
     */
    List<HotelInfoCozytime> selectBatchByIds(@Param("sourceIds") List<String> sourceIds);

    void batchUpdate(@Param("list") List<HotelInfoCozytime> list);

    void insertBatch(@Param("list") List<HotelInfoCozytime> hotelInfoCozytimeList);

    void batchInsertOrUpdate(@Param("list") List<HotelInfoCozytime> hotelInfoCozytimeList);
}