package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ltgj.common.core.redis.RedisCache;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.bean.BeanUtils;
import com.ltgj.ivw.domain.HotelGnOrderDetailStatistics;
import com.ltgj.ivw.domain.HotelGnStatistics;
import com.ltgj.ivw.domain.HotelGnSupplier;
import com.ltgj.ivw.dto.HotelStatisticsDetailDto;
import com.ltgj.ivw.enums.HotelPlatEnum;
import com.ltgj.ivw.mapper.HotelGnOrderDetailStatisticsMapper;
import com.ltgj.ivw.mapper.HotelGnStatisticsMapper;
import com.ltgj.ivw.mapper.ZhJdJdbMapper;
import com.ltgj.ivw.request.hotelGnStatistics.GnOrderDetailStatisticsRequest;
import com.ltgj.ivw.request.hotelGnStatistics.GnStatisticsRequest;
import com.ltgj.ivw.request.hotelGnStatistics.HotelGnOrderDetailStatisticsRequest;
import com.ltgj.ivw.response.HotelGnOrderDetailStatisticsRes;
import com.ltgj.ivw.service.HotelGnOrderDetailStatisticsService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Log4j2
@Service
public class HotelGnOrderDetailStatisticsServiceImpl implements HotelGnOrderDetailStatisticsService {

    @Resource
    private HotelGnStatisticsMapper hotelGnStatisticsMapper;

    @Resource
    private HotelGnOrderDetailStatisticsMapper hotelGnOrderDetailStatisticsMapper;

    @Autowired
    private RedisCache redisCache;

    private static final List<HotelPlatEnum> TARGET_PLATS = Arrays.asList(
            HotelPlatEnum.HSJL,
            HotelPlatEnum.HSJLXY,
            HotelPlatEnum.KT,
            HotelPlatEnum.MT,
            HotelPlatEnum.QT,
            HotelPlatEnum.KLYX,
            HotelPlatEnum.CLGJ
    );

    @Override
    public int insertHotelGnOrderDetailStatistics() {
        final LocalDate now = LocalDate.now();
        final LocalDate yesterday = now.minusDays(1);

        List<HotelStatisticsDetailDto> list = Stream.of(
                        getDataFromRedis("HOTEL_GN_ORDER", yesterday),
                        getDataFromRedis("HOTEL_GN_PAY", yesterday),
                        getDataFromRedis("HOTEL_GN_VALIDATE", yesterday)
                )
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        log.info("处理订单详情统计数据开始，共处理数据: {}", JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        List<HotelGnOrderDetailStatisticsRequest> requests = TARGET_PLATS.stream()
                .map(targetPlat -> {
                    List<String> platIds = list.stream()
                            .filter(n -> targetPlat.getValue().equals(String.valueOf(n.getInterfacePlat())))
                            .map(HotelStatisticsDetailDto::getPlatId)
                            .distinct()
                            .collect(Collectors.toList());
                    return new HotelGnOrderDetailStatisticsRequest(targetPlat.getValue(), platIds);
                })
                .collect(Collectors.toList());
        Map<String, HotelGnOrderDetailStatisticsRes> mapPlatId = requests.stream()
                .filter(m -> !CollectionUtils.isEmpty(m.getPlatId()))
                .flatMap(m ->
                        hotelGnOrderDetailStatisticsMapper.selectByPlatIdsAndInterfacePlat(m).stream()
                ).collect(Collectors.toMap(m -> m.getInterfacePlat() + "_" + m.getPlatId(), n -> n));
        Map<String, HotelGnOrderDetailStatisticsRes> mapLocalId = requests.stream()
                .filter(m -> !CollectionUtils.isEmpty(m.getPlatId()))
                .flatMap(m ->
                        hotelGnOrderDetailStatisticsMapper.selectByLocalIdsAndInterfacePlat(m).stream()
                ).collect(Collectors.toMap(m -> m.getInterfacePlat() + "_" + m.getLocalId(), n -> n));
        List<HotelGnOrderDetailStatistics> collect = list.stream().map(m -> {
            HotelGnOrderDetailStatistics hotelGnOrderDetailStatistics = new HotelGnOrderDetailStatistics();
            BeanUtils.copyProperties(m, hotelGnOrderDetailStatistics);
            hotelGnOrderDetailStatistics.setCreateTime(new Date(Long.parseLong(m.getCreateTime())));
            //差旅管家platId是供应商酒店id
            if (ObjectUtils.isNotEmpty(mapPlatId) && ObjectUtils.isNotEmpty(mapPlatId.get(m.getInterfacePlat() + "_" + m.getPlatId()))) {
                hotelGnOrderDetailStatistics.setLocalId(mapPlatId.get(m.getInterfacePlat() + "_" + m.getPlatId()).getLocalId());
                hotelGnOrderDetailStatistics.setHotelName(mapPlatId.get(m.getInterfacePlat() + "_" + m.getPlatId()).getJdName());
            }
            //非差旅管家platId是本地酒店id
            if(ObjectUtils.isNotEmpty(mapLocalId) && ObjectUtils.isNotEmpty(mapLocalId.get(m.getInterfacePlat() + "_" + m.getPlatId()))){
                hotelGnOrderDetailStatistics.setLocalId(mapLocalId.get(m.getInterfacePlat() + "_" + m.getPlatId()).getLocalId());
                hotelGnOrderDetailStatistics.setPlatId(null);
                hotelGnOrderDetailStatistics.setHotelName(mapLocalId.get(m.getInterfacePlat() + "_" + m.getPlatId()).getJdName());
            }
            return hotelGnOrderDetailStatistics;
        }).filter(ObjectUtils::isNotEmpty).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(collect)) {
            return 0;
        }
        hotelGnOrderDetailStatisticsMapper.insertList(collect);

        TARGET_PLATS.forEach(plat -> processStatisticsForPlat(plat, now, yesterday));
        return TARGET_PLATS.size();
    }

    private List<HotelStatisticsDetailDto> getDataFromRedis(String prefix, LocalDate date) {
        String key = prefix + "_" + date;
        Map<String, Object> cacheMap = redisCache.getCacheMap(key);

        if (MapUtils.isEmpty(cacheMap)) {
            return Collections.emptyList();
        }

        List<HotelStatisticsDetailDto> list = new ArrayList<>();
        cacheMap.forEach((k, v) -> {
            HotelStatisticsDetailDto dto = JSON.parseObject(JSON.toJSONString(v), HotelStatisticsDetailDto.class);
            list.add(dto);
        });

        return list.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void processStatisticsForPlat(HotelPlatEnum plat, LocalDate now, LocalDate statDate) {
        // 条件查询是否已存在统计记录
        HotelGnStatistics condition = new HotelGnStatistics();
        condition.setStatisticsTime(statDate);
        condition.setType(2);
        condition.setInterfacePlat(Long.valueOf(plat.getValue()));
        boolean exists = hotelGnStatisticsMapper.selectCount(condition) > 0;
        if (exists) return;
        GnOrderDetailStatisticsRequest request = new GnOrderDetailStatisticsRequest();
        request.setInterfacePlat(Long.valueOf(plat.getValue()));
        int total = hotelGnOrderDetailStatisticsMapper.selectCount(request);
        request.setIsFailure(1);
        int failCount = hotelGnOrderDetailStatisticsMapper.selectCount(request);
        HotelGnStatistics statRecord = buildStatRecord(plat, now, statDate, total, failCount);
        hotelGnStatisticsMapper.insert(statRecord);
    }

    private HotelGnStatistics buildStatRecord(HotelPlatEnum plat, LocalDate now, LocalDate statDate, int total, int failCount) {
        HotelGnStatistics record = new HotelGnStatistics();
        record.setInterfacePlat(Long.valueOf(plat.getValue()));
        record.setInterfaceName(plat.getName());
        record.setType(2);
        record.setTotalNum(total);
        record.setSuccessNum(total - failCount);
        record.setFailNum(failCount);
        record.setCreateYear(String.valueOf(now.getYear()));
        record.setCreateMonth(String.valueOf(now.getMonthValue()));
        record.setCreateDay(String.valueOf(now.getDayOfMonth()));
        record.setStatisticsTime(statDate);
        record.setCreateTime(new Date());
        return record;
    }

    @Override
    public List<HotelGnOrderDetailStatisticsRes> selectList(GnOrderDetailStatisticsRequest request) {
        return hotelGnOrderDetailStatisticsMapper.selectList(request).stream().peek(m -> m.setSubtypeName(this.toTypeName(m.getSubtype()))).collect(Collectors.toList());
    }

    @Override
    public int selectCount(GnOrderDetailStatisticsRequest request) {
        Integer count = hotelGnOrderDetailStatisticsMapper.listCount(request);
        return count==null?0:count;
    }

    private String toTypeName(String m) {
        return Objects.equals(m, "1") ? "验单" : Objects.equals(m, "2") ? "下单" : "支付";
    }
}
