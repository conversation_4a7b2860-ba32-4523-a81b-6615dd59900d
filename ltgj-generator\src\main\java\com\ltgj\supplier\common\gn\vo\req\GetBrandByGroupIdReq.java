package com.ltgj.supplier.common.gn.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetBrandByGroupIdReq {

    @NotBlank
    @ApiModelProperty(value = "集团id", required = true)
    private String groupId;
}
