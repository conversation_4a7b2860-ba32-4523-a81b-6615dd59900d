package com.ltgj.ivw.dto;

import lombok.Data;
import lombok.ToString;
import java.util.List;

/**
 * 缓存清除请求DTO
 * 用于选择性清除地理位置层级缓存
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@ToString
public class CacheClearRequest {
    
    /**
     * 要清除的缓存类型列表
     * 支持的值：
     * - "hierarchy": 完整四级联动数据缓存
     * - "level": 分级查询数据缓存
     * - "parent": 上级查询缓存
     * - "structure": 层次结构查询缓存
     * - "all": 清除所有缓存
     */
    private List<String> cacheTypes;
    
    public CacheClearRequest() {
    }
    
    public CacheClearRequest(List<String> cacheTypes) {
        this.cacheTypes = cacheTypes;
    }
} 