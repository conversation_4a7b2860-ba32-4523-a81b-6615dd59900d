package com.ltgj.ivw.service;


import com.ltgj.ivw.controller.HotelInfoMeituanController;
import com.ltgj.ivw.controller.HotelMeiTuanCallbackController;
import com.ltgj.ivw.domain.HotelInfoMeituan;
import com.ltgj.ivw.domain.HotelUpdateRecode;
import com.ltgj.ivw.utils.MyTools;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class HotelInfoMeituanTest {

    @Autowired
    private HotelInfoMeituanController hotelInfoMeituanController;
    @Autowired
    private HotelMeiTuanCallbackController hotelMeiTuanCallbackController;


    @Test
    public void getMeituanDetailTest() {
        HotelUpdateRecode hotelUpdateRecode = new HotelUpdateRecode();
        hotelUpdateRecode.setYl2("2000023");
        hotelUpdateRecode.setId("MT" + MyTools.getTimesNo());
        hotelUpdateRecode.setUpdateBy("admin");
        hotelUpdateRecode.setTimeStart(new Date());
        hotelInfoMeituanController.getMeituanDetail(hotelUpdateRecode, 3, 4);
    }

    @Test
    public void hotelInfoChangeCallbackTest() {
        try {
            String s = "{\"data\":{\"changeInfos\":[{\"hotelId\":110,\"changeTypes\":[\"1\"]},{\"hotelId\":110,\"changeTypes\":[\"2\"]}]}}";
            hotelMeiTuanCallbackController.hotelInfoChangeCallback(s);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
