package com.ltgj.sdk.cozyTime.model.price;

import com.ltgj.sdk.cozyTime.base.CozyTimeBaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelApi20HotelLowestPriceIncrementResponse extends CozyTimeBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 起价增量酒店ID列表
     */
    private List<Long> hotelIdList;
}
