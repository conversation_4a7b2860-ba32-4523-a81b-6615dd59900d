package com.ltgj.ivw.service.impl;

import com.ltgj.ivw.domain.meituan.HotelCityMeituan;
import com.ltgj.ivw.domain.meituan.HotelCityMeituanExample;
import com.ltgj.ivw.mapper.HotelCityMeituanMapper;
import com.ltgj.ivw.service.IHotelCityMeituanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 美团城市数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
@Slf4j
public class HotelCityMeituanServiceImpl implements IHotelCityMeituanService {

    @Autowired
    private HotelCityMeituanMapper hotelCityMeituanMapper;

    /**
     * 查询美团城市数据列表
     *
     * @return 美团城市数据集合
     */
    @Override
    public List<HotelCityMeituan> selectHotelCityMeituanList() {
        HotelCityMeituanExample example = new HotelCityMeituanExample();
        return hotelCityMeituanMapper.selectByExample(example);
    }

    /**
     * 分页查询美团城市数据列表
     *
     * @param pageSize 每页大小
     * @param offset 偏移量
     * @return 美团城市数据集合
     */
    @Override
    public List<HotelCityMeituan> selectHotelCityMeituanListWithPage(int pageSize, int offset) {
        HotelCityMeituanExample example = new HotelCityMeituanExample();
        example.setOrderByClause("id ASC");
        
        // 使用分页功能，需要在Example中设置limit和offset
        example.setLimit(pageSize);
        example.setOffset((long) offset);
        
        return hotelCityMeituanMapper.selectByExample(example);
    }

    /**
     * 查询美团城市数据总数
     *
     * @return 总数
     */
    @Override
    public long countHotelCityMeituan() {
        HotelCityMeituanExample example = new HotelCityMeituanExample();
        return hotelCityMeituanMapper.countByExample(example);
    }

    /**
     * 根据城市ID查询美团城市数据
     *
     * @param cityId 城市ID
     * @return 美团城市数据
     */
    @Override
    public HotelCityMeituan selectHotelCityMeituanByCityId(Integer cityId) {
        if (cityId == null) {
            return null;
        }
        
        HotelCityMeituanExample example = new HotelCityMeituanExample();
        example.createCriteria().andCityIdEqualTo(cityId);
        
        List<HotelCityMeituan> list = hotelCityMeituanMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 批量查询美团城市数据
     *
     * @param cityIds 城市ID列表
     * @return 美团城市数据集合
     */
    @Override
    public List<HotelCityMeituan> selectHotelCityMeituanByCityIds(List<Integer> cityIds) {
        if (cityIds == null || cityIds.isEmpty()) {
            return null;
        }
        
        HotelCityMeituanExample example = new HotelCityMeituanExample();
        example.createCriteria().andCityIdIn(cityIds);
        
        return hotelCityMeituanMapper.selectByExample(example);
    }
} 