package com.ltgj.ivw.service.impl;

import com.ltgj.ivw.domain.CityMapping;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelCityMapping;
import com.ltgj.ivw.service.CozyTimeDataSyncService;
import com.ltgj.ivw.service.idempotent.IdempotentResult;
import com.ltgj.ivw.service.idempotent.impl.CityMappingIdempotentProcessor;
import com.ltgj.ivw.service.idempotent.impl.HotelCityIdempotentProcessor;
import com.ltgj.ivw.service.idempotent.impl.HotelCityMappingIdempotentProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 科坦数据同步服务实现类
 */
@Slf4j
@Service
public class CozyTimeDataSyncServiceImpl implements CozyTimeDataSyncService {
    
    @Autowired
    private HotelCityIdempotentProcessor hotelCityProcessor;
    
    @Autowired
    private CityMappingIdempotentProcessor cityMappingProcessor;
    
    @Autowired
    private HotelCityMappingIdempotentProcessor hotelCityMappingProcessor;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IdempotentResult<HotelCity> syncHotelCityData(List<HotelCity> hotelCityList) {
        log.info("开始同步HotelCity数据，数量: {}", 
                CollectionUtils.isEmpty(hotelCityList) ? 0 : hotelCityList.size());
        
        if (CollectionUtils.isEmpty(hotelCityList)) {
            log.warn("HotelCity数据为空，跳过同步");
            return IdempotentResult.<HotelCity>builder()
                    .totalCount(0)
                    .processingTimeMs(0)
                    .build();
        }
        
        try {
            IdempotentResult<HotelCity> result = hotelCityProcessor.processIdempotent(hotelCityList);
            log.info("HotelCity数据同步完成: {}", result.getSummary());
            return result;
        } catch (Exception e) {
            log.error("同步HotelCity数据时发生异常", e);
            throw e;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IdempotentResult<CityMapping> syncCityMappingData(List<CityMapping> cityMappingList) {
        log.info("开始同步CityMapping数据，数量: {}", 
                CollectionUtils.isEmpty(cityMappingList) ? 0 : cityMappingList.size());
        
        if (CollectionUtils.isEmpty(cityMappingList)) {
            log.warn("CityMapping数据为空，跳过同步");
            return IdempotentResult.<CityMapping>builder()
                    .totalCount(0)
                    .processingTimeMs(0)
                    .build();
        }
        
        try {
            IdempotentResult<CityMapping> result = cityMappingProcessor.processIdempotent(cityMappingList);
            log.info("CityMapping数据同步完成: {}", result.getSummary());
            return result;
        } catch (Exception e) {
            log.error("同步CityMapping数据时发生异常", e);
            throw e;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public IdempotentResult<HotelCityMapping> syncHotelCityMappingData(List<HotelCityMapping> hotelCityMappingList) {
        log.info("开始同步HotelCityMapping数据，数量: {}", 
                CollectionUtils.isEmpty(hotelCityMappingList) ? 0 : hotelCityMappingList.size());
        
        if (CollectionUtils.isEmpty(hotelCityMappingList)) {
            log.warn("HotelCityMapping数据为空，跳过同步");
            return IdempotentResult.<HotelCityMapping>builder()
                    .totalCount(0)
                    .processingTimeMs(0)
                    .build();
        }
        
        try {
            IdempotentResult<HotelCityMapping> result = hotelCityMappingProcessor.processIdempotent(hotelCityMappingList);
            log.info("HotelCityMapping数据同步完成: {}", result.getSummary());
            return result;
        } catch (Exception e) {
            log.error("同步HotelCityMapping数据时发生异常", e);
            throw e;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CozyTimeSyncResult syncAllData(List<HotelCity> hotelCityList,
                                          List<CityMapping> cityMappingList,
                                          List<HotelCityMapping> hotelCityMappingList) {
        long startTime = System.currentTimeMillis();
        
        log.info("开始批量同步科坦相关数据 - HotelCity:{}, CityMapping:{}, HotelCityMapping:{}", 
                CollectionUtils.isEmpty(hotelCityList) ? 0 : hotelCityList.size(),
                CollectionUtils.isEmpty(cityMappingList) ? 0 : cityMappingList.size(),
                CollectionUtils.isEmpty(hotelCityMappingList) ? 0 : hotelCityMappingList.size());
        
        CozyTimeSyncResult.CozyTimeSyncResultBuilder resultBuilder = CozyTimeSyncResult.builder();
        boolean allSuccess = true;
        
        try {
            // 1. 同步HotelCity数据
            IdempotentResult<HotelCity> hotelCityResult = null;
            if (!CollectionUtils.isEmpty(hotelCityList)) {
                hotelCityResult = syncHotelCityData(hotelCityList);
                if (hotelCityResult.getErrorCount() > 0) {
                    allSuccess = false;
                }
            }
            
            // 2. 同步CityMapping数据
            IdempotentResult<CityMapping> cityMappingResult = null;
            if (!CollectionUtils.isEmpty(cityMappingList)) {
                cityMappingResult = syncCityMappingData(cityMappingList);
                if (cityMappingResult.getErrorCount() > 0) {
                    allSuccess = false;
                }
            }
            
            // 3. 同步HotelCityMapping数据
            IdempotentResult<HotelCityMapping> hotelCityMappingResult = null;
            if (!CollectionUtils.isEmpty(hotelCityMappingList)) {
                hotelCityMappingResult = syncHotelCityMappingData(hotelCityMappingList);
                if (hotelCityMappingResult.getErrorCount() > 0) {
                    allSuccess = false;
                }
            }
            
            // 4. 构建综合结果
            long totalTime = System.currentTimeMillis() - startTime;
            CozyTimeSyncResult result = resultBuilder
                    .hotelCityResult(hotelCityResult)
                    .cityMappingResult(cityMappingResult)
                    .hotelCityMappingResult(hotelCityMappingResult)
                    .totalProcessingTimeMs(totalTime)
                    .allSuccess(allSuccess)
                    .build();
            
            log.info("科坦数据批量同步完成: {}", result.getOverallSummary());
            log.debug("科坦数据同步详细报告:\n{}", result.getDetailedReport());
            
            return result;
            
        } catch (Exception e) {
            log.error("批量同步科坦数据时发生异常", e);
            
            // 构建错误结果
            long totalTime = System.currentTimeMillis() - startTime;
            return resultBuilder
                    .totalProcessingTimeMs(totalTime)
                    .allSuccess(false)
                    .build();
        }
    }
} 