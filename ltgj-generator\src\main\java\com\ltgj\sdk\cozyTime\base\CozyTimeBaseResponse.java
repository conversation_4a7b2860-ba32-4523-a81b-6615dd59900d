package com.ltgj.sdk.cozyTime.base;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class CozyTimeBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 0 成功
     * <p>
     * 101 invalid request (错误的请求)
     * <p>
     * 102 internal service error（系统错误）
     * <p>
     * 103 no room（无房）
     * <p>
     * 104 order not found (订单不存在)
     * <p>
     * 105 can not cancel this order（该订单已过取消时间，无法取消）
     * <p>
     * 106 insufficient credit amount（额度不足）
     * <p>
     * 107 uplicated order（重复订单）
     * <p>
     * 111 can not cancel this order（该订单已确认，无法取消）
     * <p>
     * 400 bad request（请求错误）
     * <p>
     * 401 unauthorized（无权限）
     * <p>
     * 429 too many requests（请求次数超过阀值）
     */
    private Integer resultCode;

    private String resultMessage;

    private Long traceId;


    public Boolean success() {
        return Objects.equals(0, this.resultCode);
    }
}
