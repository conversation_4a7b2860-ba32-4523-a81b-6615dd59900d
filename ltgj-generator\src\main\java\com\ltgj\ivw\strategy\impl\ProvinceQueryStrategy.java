package com.ltgj.ivw.strategy.impl;

import com.ltgj.ivw.domain.GeoProvince;
import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.mapper.GeoProvinceMapper;
import com.ltgj.ivw.strategy.GeoQueryStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 省份查询策略实现
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@Slf4j
public class ProvinceQueryStrategy implements GeoQueryStrategy {
    
    @Autowired
    private GeoProvinceMapper geoProvinceMapper;
    
    @Override
    public List<GeoHierarchyNode> execute(GeoLevelQueryReq request) {
        log.info("执行省份查询策略，参数：{}", request);
        
        // 查询指定国家下的省份
        List<GeoProvince> provinces = geoProvinceMapper.selectProvincesByCountry(
                request.getParentId(), true);
        
        return provinces.stream()
                .map(this::convertToNode)
                .collect(Collectors.toList());
    }
    
    @Override
    public String getSupportedLevel() {
        return "province";
    }
    
    /**
     * 转换为层级节点
     */
    private GeoHierarchyNode convertToNode(GeoProvince province) {
        GeoHierarchyNode node = new GeoHierarchyNode();
        node.setId(province.getId());
        node.setName(province.getProvinceName());
        node.setEnName(province.getProvinceEname());
        node.setFullName(province.getProvinceFullName());
        node.setType("province");
        node.setLevel(2);
        node.setParentId(province.getNation());
        node.setParentName(province.getNationName());
        return node;
    }
    
} 