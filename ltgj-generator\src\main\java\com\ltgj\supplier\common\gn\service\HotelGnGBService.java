package com.ltgj.supplier.common.gn.service;

import com.ltgj.common.utils.StringUtils;
import com.ltgj.common.utils.uuid.IdUtils;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.supplier.common.gn.domain.HotelGnGB;
import com.ltgj.supplier.common.gn.enums.HotelGnGBTypeEnum;
import com.ltgj.supplier.common.gn.mapper.HotelGnGBMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HotelGnGBService {

    @Autowired
    private HotelGnGBMapper hotelGnGBMapper;

    private static final int startId = 80000000;

    public int addBatch(List<HotelGnGB> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        list.stream().forEach(gb ->{
            gb.setId(IdUtils.fastSimpleUUID());
            gb.setCreateTime(new Date());
        });
        return hotelGnGBMapper.addBatch(list);
    }

    public HotelGnGB getByPlatformIdAndTypeAndGBName(String platformId, String type, String gbName) {
        return hotelGnGBMapper.selectByPlatformIdAndTypeAndGBName(platformId, type, gbName);
    }

    public List<HotelGnGB> getByPlatformIdAndType(String platformId, String type) {
        return hotelGnGBMapper.selectByPlatformIdAndType(platformId, type);
    }

    public List<HotelGnGB> getByPlatformIdAndTypeAndParentId(String platformId, String type, String parentGbId) {
        return hotelGnGBMapper.selectByPlatformIdAndTypeAndParentId(platformId, type, parentGbId);
    }

    public HotelGnGB getByPlatformIdAndTypeAndGBId(String platformId, String type, String gbId) {
        return hotelGnGBMapper.selectByPlatformIdAndTypeAndGBId(platformId, type, gbId);
    }

    public int updateMappingStatus(HotelGnGB hotelGnGB) {
        hotelGnGB.setUpdateTime(new Date());
        return hotelGnGBMapper.updateMappingStatus(hotelGnGB);
    }

    public String getMaxGbId(String platformId, String type) {
        String maxGbId = hotelGnGBMapper.selectMaxGbId(platformId, type);
        int currentId = startId;
        if (!StringUtils.isBlank(maxGbId)) {
            currentId = Integer.parseInt(maxGbId.substring(1)) + 1;
        }
        return HotelGnGBTypeEnum.GROUP.getType().equals(type) ? "G" + currentId : "B" + currentId;
    }

    public List<HotelGnGB> getAllByPlatformId(String platformId) {
        return hotelGnGBMapper.selectAllByPlatformId(platformId);
    }

    public int countByPlatformIdAndTypeAndParam(PlatEnum platEnum, HotelGnGBTypeEnum gnGBTypeEnum, String gbId, String gbName) {
        return hotelGnGBMapper.countByPlatformIdAndTypeAndParam(platEnum.getValue(), gnGBTypeEnum.getType(), gbId, gbName);
    }

    public List<HotelGnGB> getGroupByPlatformIdAndTypeAndParam(PlatEnum platEnum, HotelGnGBTypeEnum gnGBTypeEnum
            , String gbId, String gbName
            , int start, int pageSize) {
        return hotelGnGBMapper.selectByPlatformIdAndTypeAndParam(platEnum.getValue(), gnGBTypeEnum.getType(), gbId, gbName, start, pageSize);
    }
}
