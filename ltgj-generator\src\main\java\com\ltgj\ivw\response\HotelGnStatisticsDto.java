package com.ltgj.ivw.response;

import com.ltgj.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

@Data
public class HotelGnStatisticsDto implements Serializable {

    @Excel(name = "供应商")
    private String interfaceName;
    /**
     * 总数
     */
    @Excel(name = "酒店总数")
    private Integer totalNum;

    /**
     * 成功数
     */
    @Excel(name = "映射成功")
    private Integer successNum;

    /**
     * 失败数
     */
    @Excel(name = "未映射")
    private Integer notNum;

    /**
     * 失败数
     */
    @Excel(name = "映射失败")
    private Integer failNum;

    /**
     * 映射率
     */
    @Excel(name = "映射率")
    private String successRate;
}
