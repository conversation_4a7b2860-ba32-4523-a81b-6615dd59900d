package com.ltgj.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 缓存配置属性
 * <p>
 * 用于从 application.yml 文件中读取自定义的缓存配置
 * </p>
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "mall.cache")
@Data
public class CacheProperties {

    /**
     * 全局默认过期时间，单位秒。
     * 默认 7200 秒 (2小时)。
     */
    private Long timeout = 7200L;

    /**
     * 为指定名称的缓存设置独立的过期时间，单位秒。
     * Key: 缓存名称 (即 @Cacheable 注解的 value 值)
     * Value: 过期时间 (单位秒)
     */
    private Map<String, Long> ttl;
} 