package com.ltgj.ivw.service;

import com.alibaba.fastjson2.JSONArray;
import com.ltgj.ivw.utils.dto.AIMappingResultDto;
import com.ltgj.ivw.utils.dto.HotelInfoDTO;

import java.util.List;
import java.util.Map;

/**
 * @description: ai映射服务
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2024年11月11日 20:36
 */
public interface DifyApiService {


    String createDocumentByText(String documentName, String content, String knowledgeId);

    void updateDocumentByText(String id, String documentName, String content);

    void updateSegmentByText(String id, String documentId, String content, String datasetId);

    JSONArray retrieveChunks(String Query, String datasetId);

    JSONArray retrieveChunksForCity(String Query, String datasetId);


    void deleteDocument(String id);

    void AddChunksToDocument(String documentId, String aiDatasetId, String content);

    void deleteSegments(String id, String documentId, String aiDatasetId);

    JSONArray getDocuments(String name);

    AIMappingResultDto runWorkflow(String name, String cityName, String address, String phone);

    List<AIMappingResultDto> runBatchWorkflow(JSONArray list);

    List<AIMappingResultDto> runBatchWorkflowV2(List<HotelInfoDTO> hotelList, boolean isStep);

    List<AIMappingResultDto> runBatchWorkflowByStep(JSONArray list, String difyURL, String step1Token, String step2Token);

    void updateMetadata(String metaDataId, String metadataName);

    void createMetadata(String metadataName);

    void updateKnowledgeMetadata(Map<String, String> metadataMap, String documentId,String metaDataId);
}
