package com.ltgj.ivw.mapper;


import com.ltgj.ivw.domain.HotelGnStatistics;
import com.ltgj.ivw.request.hotelGnStatistics.GnStatisticsRequest;

import java.util.List;

public interface HotelGnStatisticsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_statistics
     *
     * @mbg.generated 2025/06/06 15:30
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_statistics
     *
     * @mbg.generated 2025/06/06 15:30
     */
    int insert(HotelGnStatistics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_statistics
     *
     * @mbg.generated 2025/06/06 15:30
     */
    int insertSelective(HotelGnStatistics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_statistics
     *
     * @mbg.generated 2025/06/06 15:30
     */
    HotelGnStatistics selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_statistics
     *
     * @mbg.generated 2025/06/06 15:30
     */
    int updateByPrimaryKeySelective(HotelGnStatistics record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table hotel_gn_statistics
     *
     * @mbg.generated 2025/06/06 15:30
     */
    int updateByPrimaryKey(HotelGnStatistics record);

    void deleteStatistics();

    int selectCount(HotelGnStatistics hotelGnStatistics);

    List<HotelGnStatistics> selectList(GnStatisticsRequest request);
}
