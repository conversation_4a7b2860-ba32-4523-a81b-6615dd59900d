package com.ltgj.ivw.service;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.GeoStructureQueryReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * HotelCityGeoService 测试类
 * 演示如何使用新的缓存功能
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HotelCityGeoServiceImplTest {
    
    @Autowired
    private IHotelCityGeoService hotelCityGeoService;
    
    /**
     * 测试从缓存获取地理位置层次结构
     */
    @Test
    public void testGetGeoStructureByIdFromCache() {
        // 构建查询请求
        GeoStructureQueryReq request = GeoStructureQueryReq.builder()
                .id("110100")  // 北京市ID
                .idType("city")
                .includeFullChildren(true)  // 包含所有子级
                .includeParents(true)       // 包含上级
                .build();  // 不再需要 geoType，缓存只包含三级
        
        // 第一次调用，会触发缓存加载
        long startTime = System.currentTimeMillis();
        AjaxResult<GeoHierarchyNode> result = hotelCityGeoService.getGeoStructureByIdFromCache(request);
        long firstCallTime = System.currentTimeMillis() - startTime;
        
        System.out.println("第一次调用耗时：" + firstCallTime + "ms");
        System.out.println("查询结果：" + result.getData());
        if (result != null && result.getData() != null) {
            printGeoNode(result.getData(), 0);
        }
        
        // 第二次调用，直接从缓存获取
        startTime = System.currentTimeMillis();
        result = hotelCityGeoService.getGeoStructureByIdFromCache(request);
        long secondCallTime = System.currentTimeMillis() - startTime;
        
        System.out.println("\n第二次调用耗时：" + secondCallTime + "ms");
        System.out.println("性能提升：" + (firstCallTime / secondCallTime) + "倍");
    }
    
    /**
     * 测试查询区县级别数据
     */
    @Test
    public void testGetDistrictFromCache() {
        GeoStructureQueryReq request = new GeoStructureQueryReq();
        request.setId("110105");  // 朝阳区ID
        request.setIdType("district");
        request.setIncludeFullChildren(false);
        request.setIncludeParents(true);
        
        AjaxResult<GeoHierarchyNode> result = hotelCityGeoService.getGeoStructureByIdFromCache(request);
        
        if (result != null && result.getData() != null) {
            System.out.println("查询区县成功：");
            printGeoNode(result.getData(), 0);
        }
    }
    
    /**
     * 测试手动预加载缓存
     */
    @Test
    public void testPreloadCache() {
        // 手动触发缓存预加载
        hotelCityGeoService.preloadGeoHierarchyCache();
        
        // 验证缓存已加载
        GeoStructureQueryReq request = new GeoStructureQueryReq();
        request.setId("CN");
        request.setIdType("country");
        request.setIncludeFullChildren(false);
        
        AjaxResult<GeoHierarchyNode> result = hotelCityGeoService.getGeoStructureByIdFromCache(request);
        System.out.println("缓存预加载后查询：" + result.getData());
    }
    
    /**
     * 打印地理节点信息
     */
    private void printGeoNode(GeoHierarchyNode node, int level) {
        if (node == null) {
            return;
        }
        
        // 打印缩进
        for (int i = 0; i < level; i++) {
            System.out.print("  ");
        }
        
        // 打印节点信息
        System.out.printf("%s [%s] - %s (Level: %d)%n", 
            node.getType(), node.getId(), node.getName(), node.getLevel());
        
        // 递归打印子节点
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (GeoHierarchyNode child : node.getChildren()) {
                printGeoNode(child, level + 1);
            }
        }
    }
    
    /**
     * 测试查询上海市
     */
    @Test
    public void testGetShanghaiFromCache() {
        GeoStructureQueryReq request = new GeoStructureQueryReq();
        request.setId("10621");  // 上海市ID
        request.setIncludeFullChildren(true);
        request.setIncludeParents(true);
        
        AjaxResult<GeoHierarchyNode> result = hotelCityGeoService.getGeoStructureByIdFromCache(request);
        
        System.out.println("查询上海市结果：");
        System.out.println("Response Code: " + result.get("code"));
        System.out.println("Response Msg: " + result.get("msg"));
        
        if (result != null && result.getData() != null) {
            System.out.println("\n完整层级结构：");
            printGeoNode(result.getData(), 0);
            
            // 验证层级关系
            GeoHierarchyNode rootNode = result.getData();
            System.out.println("\n验证层级关系：");
            System.out.println("根节点应该是国家：" + rootNode.getType() + " - " + rootNode.getName());
            
            if (rootNode.getChildren() != null && !rootNode.getChildren().isEmpty()) {
                GeoHierarchyNode provinceNode = rootNode.getChildren().get(0);
                System.out.println("第二级应该是省份：" + provinceNode.getType() + " - " + provinceNode.getName());
                
                if (provinceNode.getChildren() != null && !provinceNode.getChildren().isEmpty()) {
                    GeoHierarchyNode cityNode = provinceNode.getChildren().get(0);
                    System.out.println("第三级应该是城市：" + cityNode.getType() + " - " + cityNode.getName());
                    System.out.println("注意：缓存只包含三级数据，不包含区县");
                }
            }
        }
    }
    
    /**
     * 测试查询上海市（不包含上级）
     */
    @Test
    public void testGetShanghaiWithoutParents() {
        GeoStructureQueryReq request = new GeoStructureQueryReq();
        request.setId("10621");  // 上海市ID
        request.setIncludeFullChildren(true);
        request.setIncludeParents(false);  // 不包含上级
        
        AjaxResult<GeoHierarchyNode> result = hotelCityGeoService.getGeoStructureByIdFromCache(request);
        
        System.out.println("查询上海市结果（不含上级）：");
        System.out.println("Response Code: " + result.get("code"));
        System.out.println("Response Msg: " + result.get("msg"));
        
        if (result != null && result.getData() != null) {
            GeoHierarchyNode cityNode = result.getData();
            System.out.println("\n返回的节点信息：");
            System.out.println("节点类型: " + cityNode.getType());
            System.out.println("节点名称: " + cityNode.getName());
            System.out.println("节点ID: " + cityNode.getId());
            System.out.println("子节点数量: " + (cityNode.getChildren() != null ? cityNode.getChildren().size() : 0));
            
            // 缓存只包含三级，城市下没有区县数据
            if (cityNode.getChildren() != null && !cityNode.getChildren().isEmpty()) {
                System.out.println("\n注意：由于缓存只包含三级数据，城市节点下应该没有子节点");
                System.out.println("如果有子节点，说明数据有误");
            } else {
                System.out.println("\n正确：城市节点下没有子节点（缓存不包含区县）");
            }
        }
    }
} 