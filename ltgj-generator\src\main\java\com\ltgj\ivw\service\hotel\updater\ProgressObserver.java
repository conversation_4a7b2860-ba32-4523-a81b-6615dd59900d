package com.ltgj.ivw.service.hotel.updater;

/**
 * 酒店数据更新进度观察者(观察者模式)
 * 
 * <AUTHOR>
 */
public interface ProgressObserver {
    /**
     * 更新进度回调方法
     * @param processed 已处理的数据数量
     * @param total 总数据数量
     * @param add 新增的数据数量
     * @param update 更新的数据数量
     */
    void onProgressUpdated(int processed, int total, int add, int update);
    
    /**
     * 更新完成回调方法
     * @param processed 总处理的数据数量
     * @param add 新增的数据数量
     * @param update 更新的数据数量
     */
    void onCompleted(int processed, int add, int update);
    
    /**
     * 更新过程中发生错误回调方法
     * @param e 发生的异常
     */
    void onError(Exception e);
} 