package com.ltgj.ivw.dto;

import lombok.Data;

/**
 * 上级行政区划查询请求DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class GeoParentLookupReq {
    
    /**
     * 子级ID
     */
    private String childId;
    
    /**
     * 子级类型：province（省份）、city（城市）、district（区县）
     */
    private String childType;
    
    @Override
    public String toString() {
        return "GeoParentLookupReq{" +
                "childId='" + childId + '\'' +
                ", childType='" + childType + '\'' +
                '}';
    }
} 