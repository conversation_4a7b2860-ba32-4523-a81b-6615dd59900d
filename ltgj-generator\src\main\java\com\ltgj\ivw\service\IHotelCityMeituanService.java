package com.ltgj.ivw.service;

import com.ltgj.ivw.domain.meituan.HotelCityMeituan;
import java.util.List;

/**
 * 美团城市数据Service接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IHotelCityMeituanService {
    
    /**
     * 查询美团城市数据列表
     *
     * @return 美团城市数据集合
     */
    List<HotelCityMeituan> selectHotelCityMeituanList();
    
    /**
     * 分页查询美团城市数据列表
     *
     * @param pageSize 每页大小
     * @param offset 偏移量
     * @return 美团城市数据集合
     */
    List<HotelCityMeituan> selectHotelCityMeituanListWithPage(int pageSize, int offset);
    
    /**
     * 查询美团城市数据总数
     *
     * @return 总数
     */
    long countHotelCityMeituan();
    
    /**
     * 根据城市ID查询美团城市数据
     *
     * @param cityId 城市ID
     * @return 美团城市数据
     */
    HotelCityMeituan selectHotelCityMeituanByCityId(Integer cityId);
    
    /**
     * 批量查询美团城市数据
     *
     * @param cityIds 城市ID列表
     * @return 美团城市数据集合
     */
    List<HotelCityMeituan> selectHotelCityMeituanByCityIds(List<Integer> cityIds);
} 