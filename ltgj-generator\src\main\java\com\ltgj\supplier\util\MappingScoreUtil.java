package com.ltgj.supplier.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 映射分数计算工具类
 * 
 * <AUTHOR>
 * @date 2024-06-28
 */
public class MappingScoreUtil {

    /**
     * ID映射基础分数
     */
    private static final BigDecimal ID_MAPPING_BASE_SCORE = new BigDecimal("80.00");

    /**
     * 名称映射基础分数
     */
    private static final BigDecimal NAME_MAPPING_BASE_SCORE = new BigDecimal("60.00");

    /**
     * 完全匹配加分
     */
    private static final BigDecimal EXACT_MATCH_BONUS = new BigDecimal("20.00");

    /**
     * 部分匹配加分
     */
    private static final BigDecimal PARTIAL_MATCH_BONUS = new BigDecimal("10.00");

    /**
     * 最高分数
     */
    private static final BigDecimal MAX_SCORE = new BigDecimal("100.00");

    /**
     * 最低分数
     */
    private static final BigDecimal MIN_SCORE = new BigDecimal("0.00");

    /**
     * 计算ID映射分数
     * 
     * @param exactMatch 是否完全匹配
     * @return 映射分数
     */
    public static BigDecimal calculateIdMappingScore(boolean exactMatch) {
        BigDecimal score = ID_MAPPING_BASE_SCORE;
        if (exactMatch) {
            score = score.add(EXACT_MATCH_BONUS);
        }
        return limitScore(score);
    }

    /**
     * 计算名称映射分数
     * 
     * @param exactMatch   是否完全匹配
     * @param partialMatch 是否部分匹配
     * @return 映射分数
     */
    public static BigDecimal calculateNameMappingScore(boolean exactMatch, boolean partialMatch) {
        BigDecimal score = NAME_MAPPING_BASE_SCORE;
        if (exactMatch) {
            score = score.add(EXACT_MATCH_BONUS);
        } else if (partialMatch) {
            score = score.add(PARTIAL_MATCH_BONUS);
        }
        return limitScore(score);
    }

    /**
     * 根据映射类型计算分数
     * 
     * @param mappingType  映射类型（1-ID映射；2-名称映射）
     * @param exactMatch   是否完全匹配
     * @param partialMatch 是否部分匹配
     * @return 映射分数
     */
    public static BigDecimal calculateMappingScore(Integer mappingType, boolean exactMatch, boolean partialMatch) {
        if (mappingType == null) {
            return MIN_SCORE;
        }

        if (mappingType == 1) { // ID映射
            return calculateIdMappingScore(exactMatch);
        } else if (mappingType == 2) { // 名称映射
            return calculateNameMappingScore(exactMatch, partialMatch);
        } else {
            return MIN_SCORE;
        }
    }

    /**
     * 限制分数在有效范围内
     * 
     * @param score 原始分数
     * @return 限制后的分数
     */
    private static BigDecimal limitScore(BigDecimal score) {
        if (score.compareTo(MAX_SCORE) > 0) {
            return MAX_SCORE;
        } else if (score.compareTo(MIN_SCORE) < 0) {
            return MIN_SCORE;
        } else {
            return score.setScale(2, RoundingMode.HALF_UP);
        }
    }
}