package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.ltgj.common.properties.PropertiesUtil;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.utils.HttpUtil;
import com.ltgj.ivw.utils.dto.AIMappingResultDto;
import com.ltgj.ivw.utils.dto.HotelInfoDTO;
import com.ltgj.limiter.LtgjRateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.runtime.directive.Foreach;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RRateLimiter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: ai映射服务
 * @author: GuangFu Wei
 * @date: 2024年11月12日 9:03
 */
@Slf4j
@Service
@Primary
public class DifyApiServiceImpl implements DifyApiService {

    @Value("${dify.mainUrl:http://test-dify.aitojoy.com/v1}")
    private String DifyURL;
    private static final String workflowsKey = "app-jVrcBYbzhEyY03SlJL3fwmOF";

    @Value("${dify.token:app-YjKTkYwHNtCRxEOvFf3K1dCl}")
    private String workflowBatchKey;
    @Value("${dify.knowledge.hotelId:c15e873d-f264-4e73-abf9-09d0d8876285}")
    private String knowledgeId;// "ea5e289f-d3ca-40fc-a839-97e81d4a1572";
    @Value("${dify.knowledge.token}")
    private String knowledgeKey;//"dataset-1e02uVYmcwKpPwQl5SoRxaru";
    @Value("dify.knowledge.cityMetaId:")
    private String knowledgeMateDataId;

    @Value("${dify.step1.token:app-pyavfwohbBFVvvB5X0ZCKksM}")
    private String step1Token;

    @Value("${dify.step2.token:app-44PiOW5TLFvchJBQsCC8Fe9g}")
    private String step2Token;

//    private String cityMetaDataId = "dcc6ea28-946e-4b58-8cf5-f059e6cf98a2";
//    private String DifyURL = "http://172.150.100.137/v1";
//    private static final String workflowsKey = "app-HVdrMGuzeTZPb1unhvAm6iyX";
//
//    //    @Value("${dify.token:app-YjKTkYwHNtCRxEOvFf3K1dCl}")
//    private String workflowBatchKey="app-sZr5mRWU6Jis4Q8KKFyCcfEy";
//    private String knowledgeId = "12da0332-5e68-49d0-9a32-392b85e41b05";
//    private String knowledgeKey = "dataset-KXdy8lsisSl4JorB45qFLbJ7";

//    private String DifyURL = "https://dify-web.aitojoy.com/v1";
//    private static final String workflowsKey = "app-HVdrMGuzeTZPb1unhvAm6iyX";
//
//    //    @Value("${dify.token:app-YjKTkYwHNtCRxEOvFf3K1dCl}")
//    private String workflowBatchKey="app-sZr5mRWU6Jis4Q8KKFyCcfEy";
//    private String knowledgeId = "7e750983-b270-4aff-9e6c-3ca4f2a2f61d";
//    private String knowledgeKey = "dataset-KL4BA3zUFjbQkiilXnPRsHn3";

//        private String DifyURL = "https://dify.lvtuguanjia.com/v1";
//    private static final String workflowsKey = "app-HVdrMGuzeTZPb1unhvAm6iyX";
//
//    //    @Value("${dify.token:app-YjKTkYwHNtCRxEOvFf3K1dCl}")
//    private String workflowBatchKey="app-sZr5mRWU6Jis4Q8KKFyCcfEy";
//    private String knowledgeId = "f2ba8d5f-4576-4a27-b9ca-3a4e3bbcfb48";
//    private String knowledgeKey = "dataset-wXufcNmBVQZDupg7ZVMfnFZk";

    @Autowired
    private LtgjRateLimiter limiter;
    @Autowired
    private IJdJdbService jdJdbService;

    @Autowired
    @Qualifier("difyApiService2Impl")
    private DifyApiService difyApiService2;

    @Override
    public String createDocumentByText(String documentName, String content, String knowledgeId) {
        AtomicInteger count = new AtomicInteger(0);
        int maxCount = 20;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", documentName);
        jsonObject.put("text", content);
        jsonObject.put("indexing_technique", "high_quality");
        JSONObject processRule = new JSONObject();
        processRule.put("mode", "custom");
        JSONObject rules = new JSONObject();
        rules.put("pre_processing_rules", new JSONArray());
        JSONObject segmentation = new JSONObject();
        segmentation.put("separator", "\\n");
        segmentation.put("max_tokens", 1000);
        rules.put("segmentation", segmentation);
        processRule.put("rules", rules);
        jsonObject.put("process_rule", processRule);
//        return limiterData("difyApi", 1, 5, (i) -> {
        JSONObject responseObj = HttpUtil.postJson(DifyURL + "/datasets/" + (StringUtils.isEmpty(knowledgeId) ? this.knowledgeId : knowledgeId) + "/document/create_by_text", "Bearer " + knowledgeKey, jsonObject);

        if (count.addAndGet(1) > maxCount || (Objects.nonNull(responseObj) && StringUtils.isNotBlank(responseObj.getString("batch")))) {
            return responseObj.getJSONObject("document").getString("id");
        } else {
            log.error("知识库创建文件失败{}", responseObj);
            return responseObj.toJSONString();
        }
//        });

    }

    public <T> T limiterData(String key, long second, long num, Function<Integer, T> consumer) {
        try {
            int tryNum = 1;
            RRateLimiter rRateLimiter = this.limiter.getLimiter(key, second, num);
            boolean tryAcquire = rRateLimiter.tryAcquire(1);
            while (!tryAcquire) {
                log.info("当前请求【{}】限流.......睡眠200ms", key);
                try {
                    Thread.sleep(200);
                } catch (InterruptedException ex) {
                    log.info("睡眠异常。。。。。。。。。。");
                }
                tryNum++;
                tryAcquire = rRateLimiter.tryAcquire(1);
            }
            return consumer.apply(tryNum);
        } catch (Exception ex) {
            log.info("限流异常。。。。。。。。。。{}", ex.getMessage(), ex);
            throw new RuntimeException("限流处理失败", ex);
        }
    }


    @Override
    public void updateDocumentByText(String id, String documentName, String content) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", documentName);
        jsonObject.put("text", content);
        JSONObject responseObj = HttpUtil.postJson(DifyURL + "/datasets/" + knowledgeId + "/documents/" + id + "/update-by-text", "Bearer " + knowledgeKey, jsonObject);
        if ((Objects.nonNull(responseObj) && StringUtils.isNotBlank(responseObj.getString("batch")))) {
            log.info("知识库更新文件成功");

        } else {
            log.error("知识库更新文件失败id：{}", id);
        }
    }

    @Override
    public void updateSegmentByText(String id, String documentId, String content, String datasetId) {
        JSONObject jsonObject = new JSONObject();
        JSONObject segment = new JSONObject();
        jsonObject.put("segment", segment);
        segment.put("content", content);
        JSONObject responseObj = HttpUtil.postJson(DifyURL + "/datasets/" + (StringUtils.isEmpty(datasetId) ? knowledgeId : datasetId) + "/documents/" + documentId + "/segments/" + id, "Bearer " + knowledgeKey, jsonObject);
        if ((Objects.nonNull(responseObj) && StringUtils.isNotBlank(responseObj.getString("doc_form")))) {
            log.info("知识库更新chunk成功docFrom{}", responseObj.getString("doc_form"));
        } else {
            log.error("知识库更新chunk失败id：{}", id);
        }
    }

    @Override
    public JSONArray retrieveChunks(String query, String datasetId) {
        return innerRetrieveChunks(query, "keyword_search", datasetId);
    }

    private @Nullable JSONArray innerRetrieveChunks(String query, String keyword_search, String datasetId) {
        JSONObject jsonObject = new JSONObject();
        JSONObject retrievalModel = new JSONObject();
        jsonObject.put("query", query);
        jsonObject.put("retrieval_model", retrievalModel);
        retrievalModel.put("search_method", keyword_search);
        retrievalModel.put("reranking_enable", false);
        retrievalModel.put("score_threshold_enabled", false);
        retrievalModel.put("top_k", 1);
        log.info("retrieveChunks入参：{}", jsonObject.toJSONString());
        String url = DifyURL + "/datasets/" + (StringUtils.isEmpty(datasetId) ? knowledgeId : datasetId) + "/retrieve";
        JSONObject responseObj = HttpUtil.postJson(url, "Bearer " + knowledgeKey, jsonObject);
        if ((Objects.nonNull(responseObj) && StringUtils.isNotBlank(responseObj.getString("records")))) {
            log.info("知识库检索成功{}", responseObj.getString("records"));
            return responseObj.getJSONArray("records");
        } else {
            log.error("知识库更新文件失败id：{}", query);
            return null;
        }
    }

    @Override
    public JSONArray retrieveChunksForCity(String query, String datasetId) {
        return innerRetrieveChunks("city:" + query, "semantic_search", datasetId);
    }


    @Override
    public void deleteDocument(String id) {
        JSONObject responseObj = HttpUtil.delete(DifyURL + "/datasets/" + knowledgeId + "/documents/" + id, "Bearer " + knowledgeKey);
        if ((Objects.nonNull(responseObj))) {
            log.info("知识库更新文件成功");

        } else {
            log.error("知识库更新文件失败id：{}", id);
        }
    }

    @Override
    public void AddChunksToDocument(String documentId, String aiDatasetId, String content) {
        JSONObject jsonObject = new JSONObject();
        JSONArray segments = new JSONArray();
        JSONObject segment = new JSONObject();
        segments.add(segment);
        jsonObject.put("segments", segments);
        segment.put("content", content);
        JSONObject responseObj = HttpUtil.postJson(DifyURL + "/datasets/" + (StringUtils.isEmpty(aiDatasetId) ? knowledgeId : aiDatasetId) + "/documents/" + documentId + "/segments", "Bearer " + knowledgeKey, jsonObject);
        if ((Objects.nonNull(responseObj) && StringUtils.isNotBlank(responseObj.getString("doc_form")))) {
            log.info("知识库更新chunk成功docFrom{}", responseObj.getString("doc_form"));
        } else {
            log.error("知识库更新chunk失败id：{}", content);
        }
    }

    @Override
    public void deleteSegments(String id, String documentId, String aiDatasetId) {
        JSONObject responseObj = HttpUtil.delete(DifyURL + "/datasets/" + (StringUtils.isEmpty(aiDatasetId) ? knowledgeId : aiDatasetId) + "/documents/" + documentId + "/segments/" + id, "Bearer " + knowledgeKey);
        if ((Objects.nonNull(responseObj))) {
            log.info("知识库更新文件成功");

        } else {
            log.error("知识库更新文件失败id：{}", id);
        }
    }

    @Override
    public JSONArray getDocuments(String name) {
        Map param = new HashMap();
        if (name != null) {
            param.put("name", name);
        } else {
            param = null;
        }
        String url = DifyURL + "/datasets/" + knowledgeId + "/documents";
        JSONObject responseObj = HttpUtil.get(url, "Bearer " + knowledgeKey, param);
        if ((Objects.nonNull(responseObj) && StringUtils.isNotBlank(responseObj.getString("data")))) {
            log.info("知识库更新文件成功");
            return responseObj.getJSONArray("data");
        } else {
            log.error("知识库更新文件失败id：{}", name);
            return null;
        }
    }

    @Override
    public AIMappingResultDto runWorkflow(String name, String cityName, String address, String phone) {
        JSONObject params = new JSONObject();

        JSONObject inputs = new JSONObject();
        inputs.put("name", name);
        inputs.put("city", cityName);
        inputs.put("address", address);
        inputs.put("phone", phone);
        params.put("inputs", inputs);

        params.put("response_mode", "blocking");
        params.put("user", "hotel-mapping-system");
        JSONObject jsonObject = HttpUtil.postJson(PropertiesUtil.getProp("dify.url"), "Bearer " + PropertiesUtil.getProp("dify.token"), params);
        log.info("ai响应结果: {}", jsonObject);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject outputs = data.getJSONObject("outputs");
        if (outputs.size() == 0) {
            throw new RuntimeException(data.getString("error"));
        }
        String text = outputs.getString("text");
        JSONObject textObj = JSONObject.parseObject(text);
        String id = textObj.getString("id");
        Integer score = textObj.getInteger("score");
        String reason = textObj.getString("reason");
        log.info("酒店匹配结果: id={}, score={}", id, score);
        AIMappingResultDto aiMappingResultDto = new AIMappingResultDto();
        aiMappingResultDto.setId(id);
        aiMappingResultDto.setScore(score);
        aiMappingResultDto.setReason(reason);
        return aiMappingResultDto;
    }

    @Override
    public List<AIMappingResultDto> runBatchWorkflow(JSONArray list) {
        JSONObject params = new JSONObject();

        JSONObject inputs = new JSONObject();
        inputs.put("hotelList", list.toJSONString());

        params.put("inputs", inputs);

        params.put("response_mode", "blocking");
        params.put("user", "hotel-mapping-system");
        JSONObject jsonObject = null;
        try {
            jsonObject = HttpUtil.postJson(DifyURL + "/workflows/run", "Bearer " + workflowBatchKey, params);
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        log.info("ai响应结果: {}", jsonObject);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject outputs = data.getJSONObject("outputs");
        if (outputs.size() == 0) {
            throw new RuntimeException(data.getString("error"));
        }
        String text = outputs.getString("output");
        List<String> textObj = null;
        try {
            textObj = JSONArray.parseArray(text, String.class);
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        log.info("酒店匹配结果: output={}", text);
        List<AIMappingResultDto> resultDtos = new ArrayList<>();
        for (String s : textObj) {
            AIMappingResultDto aiMappingResultDto = JSONObject.parseObject(s, AIMappingResultDto.class);
            resultDtos.add(aiMappingResultDto);
        }
        return resultDtos;
    }

    @Override
    public List<AIMappingResultDto> runBatchWorkflowV2(List<HotelInfoDTO> hotelList, boolean isStep) {
        List<AIMappingResultDto> resultDtos = new ArrayList<>();
        try {
            log.info("runBatchWorkflowV2 接收到的酒店列表数量: {}", hotelList.size());
            if (isStep) {
                resultDtos = runBatchWorkflowByStep(JSONArray.from(hotelList), DifyURL, step1Token, step2Token);
            } else {
                resultDtos = runBatchWorkflow(JSONArray.from(hotelList));
            }
            if (CollectionUtils.isEmpty(resultDtos)) {
                log.warn("runBatchWorkflow 返回空结果");
                return null;
            }
            log.info("runBatchWorkflow 返回结果: {}", resultDtos);
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        List<String> idList = resultDtos.stream().filter(d -> com.alibaba.nacos.common.utils.StringUtils.isNotEmpty(d.getTargetId())).map(AIMappingResultDto::getTargetId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(idList)) {
            List<JdJdb> jdJdbs = jdJdbService.listByIds(idList);
            log.info("根据 targetIds 查询到的 JdJdb 数量: {}", jdJdbs.size());
            Map<String, JdJdb> stringJdJdbMap = jdJdbs.stream().collect(Collectors.toMap(JdJdb::getId, j -> j));
            resultDtos.stream().forEach(r -> {
                if (com.alibaba.nacos.common.utils.StringUtils.isNotEmpty(r.getTargetId())) {
                    if (stringJdJdbMap.containsKey(r.getTargetId())) {
                        JdJdb jdJdb = stringJdJdbMap.get(r.getTargetId());
                        r.setName(jdJdb.getJdmc());
                        r.setAddress(jdJdb.getJddz());
                        r.setPhone(jdJdb.getJddh());
                        r.setCity(jdJdb.getCityName());
                    }

                }
            });
        }
        List<AIMappingResultDto> resultDtosNew = new ArrayList<>();
        Map<String, AIMappingResultDto> mappingResultDtoMap = resultDtos.stream().collect(Collectors.toMap(AIMappingResultDto::getId, d -> d));
        hotelList.forEach(h -> {
            AIMappingResultDto aiMappingResultDto = mappingResultDtoMap.get(h.getId());
            if (aiMappingResultDto != null) {
                resultDtosNew.add(aiMappingResultDto);
            } else {
                AIMappingResultDto aiMappingResultDto1 = new AIMappingResultDto();
                aiMappingResultDto1.setId(h.getId());
                aiMappingResultDto1.setScore(0);
                aiMappingResultDto1.setReason("未匹配到");
                resultDtosNew.add(aiMappingResultDto1);
            }
            aiMappingResultDto.setId(aiMappingResultDto.getTargetId());
        });
        log.info("最终返回的 AIMappingResultDto 数量: {}", resultDtosNew.size());
        return resultDtosNew;

    }

    @Override
    public List<AIMappingResultDto> runBatchWorkflowByStep(JSONArray list, String difyURL, String step1Token, String step2Token) {
        return difyApiService2.runBatchWorkflowByStep(list, difyURL, step1Token, step2Token);
    }

    @Override
    public void updateMetadata(String metaDataId, String metadataName) {
        JSONObject params = new JSONObject();
        params.put("name", metadataName);
        JSONObject jsonObject = null;
        try {
            jsonObject = HttpUtil.postJson(DifyURL + "/datasets/" + knowledgeId + "/metadata/" + metaDataId, "Bearer " + knowledgeKey, params);
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        log.info("ai响应结果: {}", jsonObject);
        String data = jsonObject.getString("id");
        log.info("metaDataId: {}", data);

    }

    @Override
    public void createMetadata(String metadataName) {
        JSONObject params = new JSONObject();
        params.put("type", "string");
        params.put("name", metadataName);
        JSONObject jsonObject = null;
        try {
            jsonObject = HttpUtil.postJson(DifyURL + "/datasets/" + knowledgeId + "/metadata/", "Bearer " + knowledgeKey, params);
        } catch (Exception e) {
            log.error(ExceptionUtil.getStackTrace(e));
        }
        log.info("ai响应结果: {}", jsonObject);
        String data = jsonObject.getString("id");
        log.info("metaDataId: {}", data);
    }

    @Override
    public void updateKnowledgeMetadata(Map<String, String> metadataMap, String documentId,String metaDataId) {
        JSONObject params = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        params.put("operation_data", jsonArray);
        JSONObject metadata = new JSONObject();
        metadata.put("document_id", documentId);
        JSONArray metadataList = new JSONArray();
        JSONObject metadataItem = new JSONObject();
        metadataList.add(metadataItem);
        metadata.put("metadata_list", metadataList);
        jsonArray.add(metadata);
        metadataMap.forEach((k, v) -> {
            metadataItem.put("id",metaDataId );
            metadataItem.put("name", k);
            metadataItem.put("value", v);
        });
        JSONObject jsonObject = null;
        try {
            jsonObject = HttpUtil.postJson(DifyURL + "/datasets/" + knowledgeId + "/documents/metadata", "Bearer " + knowledgeKey, params);
        } catch (Exception e) {
            log.error("更新元数据失败",ExceptionUtil.getStackTrace(e));
        }
        log.info("更新元数据响应结果: {}", jsonObject);
    }
}
