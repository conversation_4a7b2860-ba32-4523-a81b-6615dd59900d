package com.ltgj.supplier.common.gn.mapper;

import com.ltgj.supplier.common.gn.domain.HotelGnGB;
import com.ltgj.supplier.common.gn.domain.HotelGnGBMapping;
import org.apache.ibatis.annotations.Param;


import java.util.List;

public interface HotelGnGBMappingMapper {

    int add(HotelGnGBMapping mapping);

    HotelGnGBMapping selectByPlatformIdAndTypeAndGBId(@Param("platformId") String platformId, @Param("type") String type, @Param("gbId") String gbId);

    List<HotelGnGBMapping> selectByPlatformIdAndType(@Param("platformId") String platformId, @Param("type") String type);
}
