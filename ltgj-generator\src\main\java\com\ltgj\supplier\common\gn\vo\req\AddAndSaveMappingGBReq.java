package com.ltgj.supplier.common.gn.vo.req;

import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.supplier.common.gn.enums.HotelGnGBTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddAndSaveMappingGBReq {

    /**
     * 平台id
     */
    private PlatEnum platform;

    /**
     * 平台-集团id
     */
    private String groupId;

    /**
     * 平台-集团名称
     */
    private String groupName;

    /**
     * 平台-集团名称
     */
    private String groupNameEn;

    /**
     * 平台-品牌id
     */
    private String brandId;

    /**
     * 平台-品牌名称
     */
    private String brandName;

    /**
     * 平台-品牌名称
     */
    private String brandNameEn;
}
