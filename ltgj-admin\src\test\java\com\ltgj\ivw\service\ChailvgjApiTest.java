package com.ltgj.ivw.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.HotelInfoChailvgj;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.mapper.JdJdbMapper;
import com.ltgj.ivw.utils.hotelApi.ChailvgjApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class ChailvgjApiTest {

    @Autowired
    private ChailvgjApi chailvgjApi;

    @Autowired
    private JdJdbMapper jdJdbMapper;

    @Autowired
    private IHotelInfoChailvgjService hotelInfoChailvgjService;

    @Test
    public void testRefreshTokenSuccess() {
        chailvgjApi.refreshTokenNew();
    }
    @Test
    public void refreshToken() {
        chailvgjApi.refreshToken();
    }
    @Test
    public void refreshTokenStatic() {
        ChailvgjApi.refreshToken();
    }

    @Test
    public void hotelDetailNew() {
//        Long param = 466296L;
        Long param = 3003640L;
        String s = chailvgjApi.hotelDetailNew(param);
        System.out.println("s = " + s);
    }

    @Test
    public void hotelDetail() {
        Long param = 2351478L;
        String s = ChailvgjApi.hotelDetail(param);
        System.out.println("s = " + s);
    }

    @Test
    public void updatejdjb(){
        //更新数据
        JdJdb jdJdb = new JdJdb();
        try {
            log.info("V3_batch补全更新酒店数据 suffix:{} hotelInfo:{}", 0, JSON.toJSONString(jdJdb));
            jdJdb.setId("20000502915002");
            jdJdb.setJddz(null);
            jdJdb.setCityId(null);
            jdJdb.setCityName(null);
            jdJdb.setDistrict(null);
            jdJdb.setDistrictName(null);
            jdJdb.setBrandId(null);
            jdJdb.setJddz(null);
            jdJdb.setJddh(null);
            jdJdb.setImgUrl(null);
            if (StringUtils.isNotEmpty(jdJdb.getImgUrl()) && jdJdb.getImgUrl().indexOf("http://139.9.5.84") != -1) {
                jdJdb.setImgUrl(jdJdb.getImgUrl().replace("http://139.9.5.84", "https://image.qiantaohotel.com"));
            }
            jdJdb.setBrandName(null);
            jdJdb.setScore(new BigDecimal(3));
            jdJdb.setJdxj("null");
            jdJdb.setLonBaidu(null);
            jdJdb.setLatBaidu(null);
            jdJdb.setLonGoogle(null);
            jdJdb.setLatGoogle(null);
            jdJdb.setLonGaode(null);
            jdJdb.setLatGaode(null);
            jdJdb.setBusinessZone(null);
            //jdJdbMapper.updateZhJdJdb(jdJdb, 0);

        } catch (Exception e) {
            log.error("e:{}",e);
        }
    }


    @Test
    public void hotelSearch() {
        HotelInfoChailvgj hotelInfoChailvgjQuery = new HotelInfoChailvgj();
        hotelInfoChailvgjQuery.setCityId("91");
        List<HotelInfoChailvgj> hotelInfoChailvgjList = hotelInfoChailvgjService.selectHotelInfoChailvgjList(hotelInfoChailvgjQuery);
        log.info("差旅管家酒店数量：{}", hotelInfoChailvgjList.size());
        if (CollectionUtils.isEmpty(hotelInfoChailvgjList)) {
            return;
        }
        List<List<HotelInfoChailvgj>> hotelInfoGroup = ListUtils.partition(hotelInfoChailvgjList, 20);

        for (List<HotelInfoChailvgj> hotelInfoChailvgjs : hotelInfoGroup) {
            JSONArray filters = new JSONArray();
            List<String> hotelIds = new ArrayList<>();
            for (HotelInfoChailvgj hotelInfoChailvgj : hotelInfoChailvgjs) {
                JSONObject filter = new JSONObject();
                filter.put("Type", 15);
                filter.put("Value", hotelInfoChailvgj.getId());
                filters.add(filter);
                hotelIds.add(hotelInfoChailvgj.getId());
            }
            chailvgjApi.hotelSearch(91, "2020-12-04", filters, 1, 10);

        }
    }
}
