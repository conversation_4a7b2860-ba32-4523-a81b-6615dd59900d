package com.ltgj.ivw.service;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.controller.HotelGnKetanController;
import com.ltgj.sdk.cozyTime.CozyTimeSdkApi;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20CitiesResponse;
import com.ltgj.supplier.cozyTime.HotelCozyTimeSupplierService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 科坦酒店信息控制器测试类
 * 
 * <AUTHOR>
 * @date 2024-07-15
 */
class HotelGnKetanControllerTest {

    @Mock
    private HotelCozyTimeSupplierService hotelCozyTimeSupplierService;

    @Mock
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Mock
    private CozyTimeSdkApi cozyTimeSdkApi;

    @InjectMocks
    private HotelGnKetanController controller;

    protected static final String idempotent = "1";

    protected static final String mappingStrategy = "1";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @SneakyThrows
    void testTestConnection() {
        // 准备模拟数据
        CozyTimeHotelContentApi20CitiesResponse mockResponse = new CozyTimeHotelContentApi20CitiesResponse();
        when(cozyTimeSdkApi.getCityList(anyString())).thenReturn(mockResponse);

        // 执行测试
        AjaxResult result = controller.test();

        // 验证结果
        assertEquals(200, result.get("code"));
        assertEquals("科坦接口连接成功", result.get("msg"));
        assertNotNull(result.get("data"));
        
        // 验证方法调用
        verify(cozyTimeSdkApi, times(1)).getCityList("CN");
    }

    @Test
    @SneakyThrows
    void testTestConnectionFailure() {
        // 模拟异常
        when(cozyTimeSdkApi.getCityList(anyString())).thenThrow(new RuntimeException("连接失败"));

        // 执行测试
        AjaxResult result = controller.test();

        // 验证结果
        assertEquals(500, result.get("code"));
        assertTrue(result.get("msg").toString().contains("科坦接口连接失败"));
        
        // 验证方法调用
        verify(cozyTimeSdkApi, times(1)).getCityList("CN");
    }

    @Test
    void testSyncCityData() throws Exception {
        // 创建模拟数据
        CozyTimeHotelContentApi20CitiesResponse mockCitiesResponse = new CozyTimeHotelContentApi20CitiesResponse();
        mockCitiesResponse.setCountryCode("CN");
        
        // 添加一些测试城市数据
        CozyTimeHotelContentApi20CitiesResponse.City mockCity = new CozyTimeHotelContentApi20CitiesResponse.City();
        mockCity.setNameCN("北京");
        mockCity.setName("Beijing");
        mockCity.setCityCode("BJS");
        mockCity.setProvinceCode("11");
        mockCity.setProvinceNameCN("北京");
        mockCity.setProvinceName("Beijing");
        mockCity.setTimeZone("GMT+8");
        
        // 添加城市到响应
        mockCitiesResponse.setCities(Collections.singletonList(mockCity));
        
        // 模拟SDK接口调用
        when(cozyTimeSdkApi.getCityList("CN")).thenReturn(mockCitiesResponse);
        
        // 在模拟的方法内部调用cozyTimeSdkApi.getCityList
        doAnswer(invocation -> {
            cozyTimeSdkApi.getCityList("CN");
            // 返回一个结果
            return "同步成功：处理城市数据1条，本地映射1条，艺龙映射1条";
        }).when(hotelCozyTimeSupplierService).syncCityData("1", mappingStrategy);
        
        // 模拟线程池执行，立即执行传入的Runnable
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            runnable.run();
            return null;
        }).when(threadPoolTaskExecutor).execute(any(Runnable.class));
        
        // 执行测试
        AjaxResult result = controller.syncCityData(idempotent,mappingStrategy);

        // 验证结果
        assertEquals(200, result.get("code"));
        assertTrue(result.get("msg").toString().contains("科坦城市数据同步任务已提交"));
        
        // 验证方法调用顺序和次数
        verify(threadPoolTaskExecutor, times(1)).execute(any(Runnable.class));
        verify(hotelCozyTimeSupplierService, times(1)).syncCityData(idempotent,mappingStrategy);
        verify(cozyTimeSdkApi, times(1)).getCityList("CN");
    }

    @Test
    void testSyncCityDataWithException() {
        // 模拟SDK接口调用抛出异常
        try {
            when(cozyTimeSdkApi.getCityList(anyString())).thenThrow(new RuntimeException("API调用失败"));
        } catch (Exception e) {
            fail("模拟SDK调用时发生异常", e);
        }
        
        // 模拟线程池执行，立即执行传入的Runnable并捕获异常
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            try {
                runnable.run();
            } catch (Exception e) {
                // 异常应该被捕获，不应该传播到这里
                fail("线程池执行时异常未被正确处理: " + e.getMessage());
            }
            return null;
        }).when(threadPoolTaskExecutor).execute(any(Runnable.class));
        
        // 执行测试
        AjaxResult result = controller.syncCityData(idempotent,mappingStrategy);
        
        // 验证结果 - 即使内部发生异常，控制器也应该返回成功，因为是异步执行
        assertEquals(200, result.get("code"));
        assertTrue(result.get("msg").toString().contains("科坦城市数据同步任务已提交"));
        
        // 验证方法调用
        verify(threadPoolTaskExecutor, times(1)).execute(any(Runnable.class));
        verify(hotelCozyTimeSupplierService, times(1)).syncCityData("1", mappingStrategy);
        
        // 验证异常处理
        // 由于异常在syncCityData内部被捕获，所以这里不需要验证异常处理
        // 但我们可以验证内部方法的调用
        try {
            verify(cozyTimeSdkApi, times(1)).getCityList(anyString());
            // 由于发生异常，后续方法应该不会被调用
            verify(hotelCozyTimeSupplierService, never()).processCityData(any(CozyTimeHotelContentApi20CitiesResponse.class), idempotent);
            verify(hotelCozyTimeSupplierService, never()).processCityMapping(anyString(),"1");
            verify(hotelCozyTimeSupplierService, never()).processElongCityMapping(idempotent);
        } catch (Exception e) {
            fail("验证方法调用时发生异常", e);
        }
    }
    
    @Test
    void testSyncCityDataWithPartialFailure() {
        // 创建模拟数据
        CozyTimeHotelContentApi20CitiesResponse mockCitiesResponse = new CozyTimeHotelContentApi20CitiesResponse();
        mockCitiesResponse.setCountryCode("CN");
        
        // 添加一些测试城市数据
        CozyTimeHotelContentApi20CitiesResponse.City mockCity = new CozyTimeHotelContentApi20CitiesResponse.City();
        mockCity.setNameCN("北京");
        mockCity.setName("Beijing");
        mockCity.setCityCode("BJS");
        mockCity.setProvinceCode("11");
        mockCity.setProvinceNameCN("北京");
        mockCity.setProvinceName("Beijing");
        mockCity.setTimeZone("GMT+8");
        
        // 添加城市到响应
        mockCitiesResponse.getCities().add(mockCity);
        
        // 模拟SDK接口调用成功
        try {
            when(cozyTimeSdkApi.getCityList(anyString())).thenReturn(mockCitiesResponse);
        } catch (Exception e) {
            fail("模拟SDK调用时发生异常", e);
        }
        
        // 模拟processCityData方法执行成功
        when(hotelCozyTimeSupplierService.processCityData(any(CozyTimeHotelContentApi20CitiesResponse.class), idempotent)).thenReturn(1);
        
        // 模拟processCityMapping方法执行失败
        when(hotelCozyTimeSupplierService.processCityMapping(anyString(),idempotent)).thenThrow(new RuntimeException("映射处理失败"));
        
        // 由于前面的方法失败，processElongCityMapping方法应该不会被调用，但我们还是模拟它
        when(hotelCozyTimeSupplierService.processElongCityMapping(idempotent)).thenReturn(1);
        
        // 模拟线程池执行，立即执行传入的Runnable并捕获异常
        doAnswer(invocation -> {
            Runnable runnable = invocation.getArgument(0);
            try {
                runnable.run();
            } catch (Exception e) {
                // 异常应该被捕获，不应该传播到这里
                fail("线程池执行时异常未被正确处理: " + e.getMessage());
            }
            return null;
        }).when(threadPoolTaskExecutor).execute(any(Runnable.class));
        
        // 执行测试
        AjaxResult result = controller.syncCityData(idempotent,mappingStrategy);
        
        // 验证结果 - 即使内部发生异常，控制器也应该返回成功，因为是异步执行
        assertEquals(200, result.get("code"));
        assertTrue(result.get("msg").toString().contains("科坦城市数据同步任务已提交"));
        
        // 验证方法调用
        verify(threadPoolTaskExecutor, times(1)).execute(any(Runnable.class));
        verify(hotelCozyTimeSupplierService, times(1)).syncCityData("1", mappingStrategy);
        
        // 验证部分成功部分失败的情况
        try {
            // 前两个方法应该被调用
            verify(cozyTimeSdkApi, times(1)).getCityList(anyString());
            verify(hotelCozyTimeSupplierService, times(1)).processCityData(any(CozyTimeHotelContentApi20CitiesResponse.class), idempotent);
            verify(hotelCozyTimeSupplierService, times(1)).processCityMapping(anyString(),idempotent);
            
            // 由于processCityMapping失败，后续方法不应该被调用
            verify(hotelCozyTimeSupplierService, never()).processElongCityMapping(idempotent);
        } catch (Exception e) {
            fail("验证方法调用时发生异常", e);
        }
    }

    @Test
    void testSyncCityDataSync() {
        // 准备模拟数据
        when(hotelCozyTimeSupplierService.syncCityData("1", "1")).thenReturn("同步成功");

        // 执行测试
        AjaxResult result = controller.syncCityDataSync("1","1");

        // 验证结果
        assertEquals(200, result.get("code"));
        assertEquals("同步成功", result.get("data"));
        
        // 验证方法调用
        verify(hotelCozyTimeSupplierService, times(1)).syncCityData("1", "1");
    }

    @Test
    void testProcessCityData() {
        // 准备模拟数据
        CozyTimeHotelContentApi20CitiesResponse citiesResponse = new CozyTimeHotelContentApi20CitiesResponse();
        when(hotelCozyTimeSupplierService.processCityData(any(CozyTimeHotelContentApi20CitiesResponse.class), idempotent)).thenReturn(10);

        // 执行测试
        AjaxResult result = controller.processCityData(citiesResponse,idempotent);

        // 验证结果
        assertEquals(200, result.get("code"));
        assertTrue(result.get("msg").toString().contains("处理成功，共处理城市数据: 10条"));
        
        // 验证方法调用
        verify(hotelCozyTimeSupplierService, times(1)).processCityData(citiesResponse, idempotent);
    }

    @Test
    void testGetCityData() {
        // 准备模拟数据
        CozyTimeHotelContentApi20CitiesResponse mockResponse = new CozyTimeHotelContentApi20CitiesResponse();
        try {
            when(cozyTimeSdkApi.getCityList(anyString())).thenReturn(mockResponse);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 执行测试
        AjaxResult result = controller.getCityData("CN");

        // 验证结果
        assertEquals(200, result.get("code"));
        assertEquals("获取科坦城市数据成功", result.get("msg"));
        assertNotNull(result.get("data"));
        
        // 验证方法调用
        try {
            verify(cozyTimeSdkApi, times(1)).getCityList("CN");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testProcessCityMapping() {
        // 准备模拟数据
        when(hotelCozyTimeSupplierService.processCityMapping(idempotent)).thenReturn(15);

        // 执行测试
        AjaxResult result = controller.processCityMapping(idempotent);

        // 验证结果
        assertEquals(200, result.get("code"));
        assertTrue(result.get("msg").toString().contains("处理成功，共创建本地映射: 15条"));
        
        // 验证方法调用
        verify(hotelCozyTimeSupplierService, times(1)).processCityMapping(idempotent);
    }

    @Test
    void testProcessCityMappingWithStrategy() {
        // 准备模拟数据
        when(hotelCozyTimeSupplierService.processCityMapping(anyString(),idempotent)).thenReturn(15);

        // 执行测试
        AjaxResult result = controller.processCityMappingWithStrategy(idempotent,mappingStrategy);

        // 验证结果
        assertEquals(200, result.get("code"));
        assertTrue(result.get("msg").toString().contains("处理成功，映射策略：艺龙映射"));
        
        // 验证方法调用
        verify(hotelCozyTimeSupplierService, times(1)).processCityMapping(idempotent);
    }

    @Test
    void testProcessElongMapping() {
        // 准备模拟数据
        when(hotelCozyTimeSupplierService.processElongCityMapping("1")).thenReturn(20);

        // 执行测试
        AjaxResult result = controller.processElongMapping("1");

        // 验证结果
        assertEquals(200, result.get("code"));
        assertTrue(result.get("msg").toString().contains("处理成功，共创建艺龙映射: 20条"));
        
        // 验证方法调用
        verify(hotelCozyTimeSupplierService, times(1)).processElongCityMapping("1");
    }
} 