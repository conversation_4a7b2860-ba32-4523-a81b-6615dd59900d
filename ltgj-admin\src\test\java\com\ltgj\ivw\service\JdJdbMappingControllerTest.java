package com.ltgj.ivw.service;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.controller.JdJdbMappingController;
import com.ltgj.ivw.domain.HotelInfo;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.domain.JdJdbMapping;
import com.ltgj.ivw.domain.ZhJdJdbMapping;
import com.ltgj.ivw.enums.PlatEnum;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @Date 2024/12/3
 * @description:
 */
@RunWith(MockitoJUnitRunner.class)
@SpringBootTest
public class JdJdbMappingControllerTest {

    @InjectMocks
    private JdJdbMappingController jdJdbMappingController;

    @Mock
    private IJdJdbService jdJdbService;

    @Mock
    private IJdJdbMappingService jdJdbMappingService;

    @Mock
    private ZhJdJdbMappingService zhJdJdbMappingService;


    @Mock
    private IZhJdJdbMinPriceService zhJdJdbMinPriceService;


    private JdJdbMapping jdJdbMapping;
    private ZhJdJdbService zhJdJdbService;
    private JdJdb jdJdb;
    private HotelInfo hotelInfo;
    private ZhJdJdbMapping zhJdJdbMapping;

    @SneakyThrows
    @Before
    public void setUp() {
        jdJdbMapping = new JdJdbMapping();
        jdJdbMapping.setLocalId(String.valueOf(1L));
        jdJdbMapping.setInterfacePlat(1L);
        jdJdbMapping.setPlatId("123");
        jdJdbMapping.setReserve0("10");

        jdJdb = new JdJdb();
        jdJdb.setId(String.valueOf(1L));
        jdJdb.setJdmc("Test Hotel");
        jdJdb.setCityId(String.valueOf(1));

        hotelInfo = new HotelInfo();
        hotelInfo.setId("123");
        hotelInfo.setName("Test Hotel");

        zhJdJdbMapping = new ZhJdJdbMapping();
        zhJdJdbMapping.setMappingScore("10");

        when(jdJdbService.selectJdJdbById(String.valueOf(anyLong()))).thenReturn(jdJdb);
        when(jdJdbMappingService.selectJdJdbMappingByLocalIdAndInterfacePlat(String.valueOf(anyLong()), anyLong())).thenReturn(null);
        when(zhJdJdbMappingService.findByLocalIdAndInterfacePlat(String.valueOf(anyLong()), anyLong())).thenReturn(null);
        when(zhJdJdbMappingService.insert(any(ZhJdJdbMapping.class))).thenReturn(1);
        when(jdJdbMappingService.insertJdJdbMapping(any(JdJdbMapping.class))).thenReturn(1);
        when(zhJdJdbMinPriceService.getHotelMinPriceList(String.valueOf(anyLong()), anyInt())).thenReturn(new ArrayList<>());
    }

    @Test
    public void testAdd_LocalHotelNotExist() {
        when(jdJdbService.selectJdJdbById(String.valueOf(anyLong()))).thenReturn(null);
        AjaxResult result = jdJdbMappingController.add(null);
        assertEquals(AjaxResult.error("本地酒店不存在"), result);
    }

    @Test
    public void testAdd_NewMapping() {
//        AjaxResult result = jdJdbMappingController.add(jdJdbMapping);
//        assertEquals(AjaxResult.success(), result);
//        verify(jdJdbMappingService, times(1)).insertJdJdbMapping(any(JdJdbMapping.class));
//        verify(zhJdJdbMappingService, times(1)).insert(any(ZhJdJdbMapping.class));
    }

    @Test
    public void testAdd_ExistingMapping() {
        when(jdJdbMappingService.selectJdJdbMappingByLocalIdAndInterfacePlat(String.valueOf(anyLong()), anyLong())).thenReturn(new JdJdbMapping());
        when(zhJdJdbMappingService.findByLocalIdAndInterfacePlat(String.valueOf(anyLong()), anyLong())).thenReturn(new ZhJdJdbMapping());
        AjaxResult result = jdJdbMappingController.add(null);
        assertEquals(AjaxResult.success(), result);
        verify(jdJdbMappingService, never()).insertJdJdbMapping(any(JdJdbMapping.class));
        verify(zhJdJdbMappingService, never()).insert(any(ZhJdJdbMapping.class));
    }

    @Test
    public void testAdd_RedJiaLiXYProtocol() {
        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL_XY.getValue()));
        AjaxResult result = jdJdbMappingController.add(null);
        assertEquals(AjaxResult.success(), result);
        verify(zhJdJdbService, times(1)).insertZhJdJdb(any(JdJdb.class), String.valueOf(anyInt()));
        verify(jdJdbService, times(1)).dealListMinPriceHSJLXYNew(String.valueOf(anyLong()), anyLong());
    }

    @Test
    public void testAdd_RedJiaLiProtocol() {
        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_HSJL.getValue()));
        AjaxResult result = jdJdbMappingController.add(null);
        assertEquals(AjaxResult.success(), result);
        verify(zhJdJdbService, times(1)).insertZhJdJdb(any(JdJdb.class), String.valueOf(anyInt()));
        verify(jdJdbService, times(1)).dealListMinPriceHSJLNew(String.valueOf(anyLong()), anyLong());
    }

    @Test
    public void testAdd_MeiTuanProtocol() {
        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_MT.getValue()));
        AjaxResult result = jdJdbMappingController.add(null);
        assertEquals(AjaxResult.success(), result);
        verify(zhJdJdbService, times(1)).insertZhJdJdb(any(JdJdb.class), String.valueOf(anyInt()));
        verify(jdJdbService, times(1)).dealListMinPriceMtNew(String.valueOf(anyLong()), anyLong());
    }

    @Test
    public void testAdd_QianTaoProtocol() {
        zhJdJdbMapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_QT.getValue()));
        AjaxResult result = jdJdbMappingController.add(null);
        assertEquals(AjaxResult.success(), result);
        verify(zhJdJdbService, times(1)).insertZhJdJdb(any(JdJdb.class), String.valueOf(anyInt()));
        verify(jdJdbService, times(1)).dealListMinPriceQtNew(String.valueOf(anyLong()), anyString());
    }
}
