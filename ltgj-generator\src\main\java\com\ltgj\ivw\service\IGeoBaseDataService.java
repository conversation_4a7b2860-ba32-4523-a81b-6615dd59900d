package com.ltgj.ivw.service;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.BCity;
import com.ltgj.ivw.domain.GeoCityGeo;
import com.ltgj.ivw.domain.GeoProvince;

import java.util.List;
import java.util.Map;

/**
 * 地理基础数据服务接口
 * 专门用于处理需要切换到DEVYIXBASE数据源的地理数据查询
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface IGeoBaseDataService {
    
    /**
     * 根据ID查询区县信息
     * @param id 区县ID
     * @return 区县信息
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    GeoCityGeo selectGeoCityGeoById(String id);
    
    /**
     * 根据城市ID查询区县列表
     * @param cityId 城市ID
     * @param geoType 地区类型
     * @param validOnly 是否只查询有效数据
     * @return 区县列表
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    List<GeoCityGeo> selectDistrictsByCity(String cityId, Long geoType, Boolean validOnly);
    
    /**
     * 根据ID查询城市信息
     * @param id 城市ID
     * @return 城市信息
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    BCity selectBCityById(String id);
    
    /**
     * 根据省份ID查询城市列表
     * @param provinceId 省份ID
     * @param validOnly 是否只查询有效数据
     * @return 城市列表
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    List<BCity> selectCitiesByProvince(String provinceId, Boolean validOnly);
    
    /**
     * 根据ID查询省份信息
     * @param id 省份ID
     * @return 省份信息
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    GeoProvince selectGeoProvinceById(String id);
    
    /**
     * 查询去重的国家信息
     * @param validOnly 是否只查询有效数据
     * @return 国家信息集合
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    List<Map<String, Object>> selectDistinctCountries(Boolean validOnly);
    
    /**
     * 根据国家ID查询省份列表
     * @param countryId 国家ID
     * @param validOnly 是否只查询有效数据
     * @return 省份列表
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    List<GeoProvince> selectProvincesByCountry(String countryId, Boolean validOnly);
} 