package com.ltgj.ivw.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 国家节点DTO - 三级联动数据结构
 * 
 * <AUTHOR>
 * @date 2024-01-19
 */
@Data
public class CountryNode {
    
    /** 国家ID */
    private String id;
    
    /** 国家名称 */
    private String name;
    
    /** 国家英文名称 */
    private String nameEn;
    
    /** 省份集合（用于内部构建，不返回给前端） */
    private Map<String, ProvinceNode> provinces;
    
    /** 省份列表（返回给前端的数据） */
    private List<ProvinceNode> provinceList;
} 