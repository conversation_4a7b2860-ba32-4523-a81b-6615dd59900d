package com.ltgj.ivw.request.hotelGnStatistics;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class HotelGnOrderDetailStatisticsRequest implements Serializable {
    private String interfacePlat;
    private List<String> platId;

    public HotelGnOrderDetailStatisticsRequest(String value, List<String> platIds) {
        this.interfacePlat = value;
        this.platId = platIds;
    }
}
