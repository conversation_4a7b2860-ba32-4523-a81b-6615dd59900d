package com.ltgj.ivw.utils;

import java.math.BigDecimal;
import java.util.regex.Pattern;

/**
 * 经纬度校验工具类
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
public class CoordinateValidator {

    /**
     * 经度的正则表达式
     * 经度范围：-180 ~ 180
     */
    private static final Pattern LONGITUDE_PATTERN = Pattern.compile("^-?(180(\\.0+)?|1[0-7]\\d(\\.\\d+)?|[1-9]?\\d(\\.\\d+)?)$");

    /**
     * 纬度的正则表达式
     * 纬度范围：-90 ~ 90
     */
    private static final Pattern LATITUDE_PATTERN = Pattern.compile("^-?(90(\\.0+)?|[1-8]?\\d(\\.\\d+)?)$");

    /**
     * 校验经度是否合法
     *
     * @param longitude 经度
     * @return true-合法，false-不合法
     */
    public static boolean isValidLongitude(BigDecimal longitude) {
        if (longitude == null) {
            return false;
        }
        return LONGITUDE_PATTERN.matcher(longitude.toString()).matches();
    }

    /**
     * 校验经度是否合法
     *
     * @param longitude 经度字符串
     * @return true-合法，false-不合法
     */
    public static boolean isValidLongitude(String longitude) {
        if (longitude == null || longitude.trim().isEmpty()) {
            return false;
        }
        try {
            BigDecimal lon = new BigDecimal(longitude);
            return isValidLongitude(lon);
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 校验纬度是否合法
     *
     * @param latitude 纬度
     * @return true-合法，false-不合法
     */
    public static boolean isValidLatitude(BigDecimal latitude) {
        if (latitude == null) {
            return false;
        }
        return LATITUDE_PATTERN.matcher(latitude.toString()).matches();
    }

    /**
     * 校验纬度是否合法
     *
     * @param latitude 纬度字符串
     * @return true-合法，false-不合法
     */
    public static boolean isValidLatitude(String latitude) {
        if (latitude == null || latitude.trim().isEmpty()) {
            return false;
        }
        try {
            BigDecimal lat = new BigDecimal(latitude);
            return isValidLatitude(lat);
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 校验经纬度对是否合法
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return true-合法，false-不合法
     */
    public static boolean isValidCoordinate(BigDecimal longitude, BigDecimal latitude) {
        return isValidLongitude(longitude) && isValidLatitude(latitude);
    }

    /**
     * 校验经纬度对是否合法（字符串形式）
     *
     * @param longitude 经度字符串
     * @param latitude  纬度字符串
     * @return true-合法，false-不合法
     */
    public static boolean isValidCoordinate(String longitude, String latitude) {
        return isValidLongitude(longitude) && isValidLatitude(latitude);
    }

    /**
     * 清理经纬度字符串中的特殊字符
     *
     * @param coordinate 经纬度字符串
     * @return 清理后的字符串
     */
    public static String cleanCoordinate(String coordinate) {
        if (coordinate == null) {
            return null;
        }
        // 只保留数字、小数点和负号
        return coordinate.replaceAll("[^0-9.-]", "");
    }

    /**
     * 尝试解析经纬度值
     *
     * @param coordinate 经纬度字符串
     * @return BigDecimal 值，如果无法解析则返回 null
     */
    public static BigDecimal parseCoordinate(String coordinate) {
        if (coordinate == null || coordinate.trim().isEmpty()) {
            return null;
        }
        try {
            String cleaned = cleanCoordinate(coordinate);
            return new BigDecimal(cleaned);
        } catch (NumberFormatException e) {
            return null;
        }
    }
} 