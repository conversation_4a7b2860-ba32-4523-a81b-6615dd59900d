package com.ltgj.supplier.common.gn.mapper;

import com.ltgj.supplier.common.gn.domain.HotelGnGeoMapping;
import java.util.List;

import com.ltgj.supplier.common.gn.domain.HotelGnGeoMappingExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface HotelGnGeoMappingMapper {
    long countByExample(HotelGnGeoMappingExample example);

    int deleteByExample(HotelGnGeoMappingExample example);

    int deleteByPrimaryKey(String id);

    int insert(HotelGnGeoMapping record);

    int insertSelective(HotelGnGeoMapping record);

    List<HotelGnGeoMapping> selectByExample(HotelGnGeoMappingExample example);

    HotelGnGeoMapping selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") HotelGnGeoMapping record, @Param("example") HotelGnGeoMappingExample example);

    int updateByExample(@Param("record") HotelGnGeoMapping record, @Param("example") HotelGnGeoMappingExample example);

    int updateByPrimaryKeySelective(HotelGnGeoMapping record);

    int updateByPrimaryKey(HotelGnGeoMapping record);

    int batchInsertOrUpdate(@Param("list") List<HotelGnGeoMapping> list);
}