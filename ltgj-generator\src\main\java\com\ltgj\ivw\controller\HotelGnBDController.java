package com.ltgj.ivw.controller;

import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.supplier.common.gn.manager.HotelGnGBManager;
import com.ltgj.supplier.common.gn.vo.req.GetBrandByGroupIdReq;
import com.ltgj.supplier.common.gn.vo.req.QueryBrandListReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 国内酒店品牌管理
 */
@RestController
@RequestMapping("/ivw/bd")
public class HotelGnBDController extends BaseController {

    @Autowired
    private HotelGnGBManager  hotelGnGBManager;

    /**
     * 获取所有国内酒店品牌信息
     *
     * @return
     */
    @RequestMapping("/getAllGBInfo")
    public AjaxResult getAllGBInfo() {
        return success(hotelGnGBManager.getAllGBInfo());
    }

    @RequestMapping("/getAllGroup")
    public AjaxResult getAllGroup(@RequestBody @Valid QueryBrandListReq req) {
        return success(hotelGnGBManager.getAllGroup(req));
    }

    @RequestMapping("/getAllBrandByGroupId")
    public AjaxResult getAllBrandByGroupId(@RequestBody @Valid GetBrandByGroupIdReq req) {
        return success(hotelGnGBManager.getAllBrandByGroupId(req));
    }

}
