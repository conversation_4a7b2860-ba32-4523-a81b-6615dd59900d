package com.ltgj.ivw.builder;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.strategy.GeoQueryStrategyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 地理位置层级数据构建器
 * 使用建造者模式构建完整的四级联动数据结构
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Component
@Slf4j
@DataSource(DataSourceType.DEVYIXBASE)
@Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
public class GeoHierarchyBuilder {
    
    @Autowired
    private GeoQueryStrategyManager strategyManager;
    
    /**
     * 构建完整的四级联动数据结构
     * 
     * @return 四级联动数据
     */
    public List<GeoHierarchyNode> buildFullHierarchy() {
        log.info("开始构建完整四级联动数据");
        
        try {
            // 1. 查询所有国家
            List<GeoHierarchyNode> countries = getCountries();
            
            // 2. 为每个国家构建省份数据
            for (GeoHierarchyNode country : countries) {
                List<GeoHierarchyNode> provinces = getProvinces(country.getId());
                country.setChildren(provinces);
                
                // 3. 为每个省份构建城市数据
                for (GeoHierarchyNode province : provinces) {
                    List<GeoHierarchyNode> cities = getCities(province.getId());
                    province.setChildren(cities);
                    
                    // 4. 为每个城市构建区县数据
                    for (GeoHierarchyNode city : cities) {
                        List<GeoHierarchyNode> districts = getDistricts(city.getId(), null);
                        city.setChildren(districts);
                    }
                }
            }
            
            log.info("完整四级联动数据构建完成，国家数量：{}", countries.size());
            return countries;
            
        } catch (Exception e) {
            log.error("构建完整四级联动数据异常", e);
            throw new RuntimeException("构建四级联动数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 构建指定国家的四级联动数据结构
     * 
     * @param countryCode 国家代码
     * @return 指定国家的四级联动数据
     */
    public List<GeoHierarchyNode> buildFullHierarchy(String countryCode) {
        log.info("开始构建指定国家的四级联动数据，国家代码：{}", countryCode);
        
        try {
            // 1. 查询指定国家
            List<GeoHierarchyNode> countries = getCountries();
            GeoHierarchyNode targetCountry = countries.stream()
                .filter(country -> countryCode.equals(country.getId()))
                .findFirst()
                .orElse(null);
            
            if (targetCountry == null) {
                log.warn("未找到指定的国家，国家代码：{}", countryCode);
                return new ArrayList<>();
            }
            
            // 2. 构建该国家的省份数据
            List<GeoHierarchyNode> provinces = getProvinces(targetCountry.getId());
            targetCountry.setChildren(provinces);
            
            // 3. 为每个省份构建城市数据
            for (GeoHierarchyNode province : provinces) {
                List<GeoHierarchyNode> cities = getCities(province.getId());
                province.setChildren(cities);
                
                // 4. 为每个城市构建区县数据
                for (GeoHierarchyNode city : cities) {
                    List<GeoHierarchyNode> districts = getDistricts(city.getId(), null);
                    city.setChildren(districts);
                }
            }
            
            log.info("指定国家的四级联动数据构建完成，国家：{} - {}", 
                targetCountry.getId(), targetCountry.getName());
            
            // 返回包含单个国家的列表
            List<GeoHierarchyNode> result = new ArrayList<>();
            result.add(targetCountry);
            return result;
            
        } catch (Exception e) {
            log.error("构建指定国家的四级联动数据异常，国家代码：{}", countryCode, e);
            throw new RuntimeException("构建四级联动数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 构建指定层级的数据
     */
    public List<GeoHierarchyNode> buildByLevel(GeoLevelQueryReq request) {
        log.info("构建指定层级数据，请求：{}", request);
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        List<GeoHierarchyNode> result = strategyManager.executeQuery(request);
        
        // 如果需要包含下级数据
        if (Boolean.TRUE.equals(request.getIncludeChildren())) {
            // 检查是否可能产生大量数据
            if (result.size() > 50 && "country".equals(request.getLevel())) {
                log.warn("国家级查询包含子级可能产生大量数据，当前国家数量：{}，请求：{}", result.size(), request);
            }
            
            loadChildrenWithDepth(result, request, 4);
        }
        
        long elapsedTime = System.currentTimeMillis() - startTime;
        
        // 计算总节点数（包括子节点）
        int totalNodes = countTotalNodes(result);
        
        if (totalNodes > 5000) {
            log.warn("构建数据包含大量节点，总数：{}，耗时：{}ms，请求：{}", totalNodes, elapsedTime, request);
        } else {
            log.info("构建数据完成，总节点数：{}，耗时：{}ms", totalNodes, elapsedTime);
        }
        
        return result;
    }
    
    /**
     * 递归计算总节点数
     */
    private int countTotalNodes(List<GeoHierarchyNode> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return 0;
        }
        
        int count = nodes.size();
        for (GeoHierarchyNode node : nodes) {
            if (node.getChildren() != null) {
                count += countTotalNodes(node.getChildren());
            }
        }
        return count;
    }
    
    /**
     * 加载子级数据
     */
    private void loadChildren(List<GeoHierarchyNode> nodes, GeoLevelQueryReq request) {
        loadChildrenWithDepth(nodes, request, 1);
    }
    
    /**
     * 加载子级数据（带深度控制）
     */
    private void loadChildrenWithDepth(List<GeoHierarchyNode> nodes, GeoLevelQueryReq request, int currentDepth) {
        // 检查是否超过最大深度
        if (request.getMaxDepth() != null && currentDepth > request.getMaxDepth()) {
            log.info("已达到最大递归深度：{}，停止加载子级", request.getMaxDepth());
            return;
        }
        
        for (GeoHierarchyNode node : nodes) {
            List<GeoHierarchyNode> children = loadChildrenForNode(node, request);
            node.setChildren(children);
            
            // 递归加载下一级
            if (!children.isEmpty() && currentDepth < 3) { // 最多加载到区县级别
                loadChildrenWithDepth(children, request, currentDepth + 1);
            }
        }
    }
    
    /**
     * 为指定节点加载子级数据
     */
    private List<GeoHierarchyNode> loadChildrenForNode(GeoHierarchyNode node, GeoLevelQueryReq request) {
        GeoLevelQueryReq childRequest = new GeoLevelQueryReq();
        childRequest.setGeoType(request.getGeoType());
        
        switch (node.getType()) {
            case "country":
                childRequest.setLevel("province");
                childRequest.setParentId(node.getId());
                break;
            case "province":
                childRequest.setLevel("city");
                childRequest.setParentId(node.getId());
                break;
            case "city":
                childRequest.setLevel("district");
                childRequest.setParentId(node.getId());
                break;
            default:
                return new ArrayList<>();
        }
        
        return strategyManager.executeQuery(childRequest);
    }
    
    /**
     * 获取国家列表
     */
    private List<GeoHierarchyNode> getCountries() {
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("country");
        return strategyManager.getStrategy("country").execute(request);
    }
    
    /**
     * 获取省份列表
     */
    private List<GeoHierarchyNode> getProvinces(String countryId) {
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("province");
        request.setParentId(countryId);
        return strategyManager.getStrategy("province").execute(request);
    }
    
    /**
     * 获取城市列表
     */
    private List<GeoHierarchyNode> getCities(String provinceId) {
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("city");
        request.setParentId(provinceId);
        return strategyManager.getStrategy("city").execute(request);
    }
    
    /**
     * 获取区县列表
     */
    private List<GeoHierarchyNode> getDistricts(String cityId, String geoType) {
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("district");
        request.setParentId(cityId);
        request.setGeoType(geoType);
        return strategyManager.getStrategy("district").execute(request);
    }
    
    /**
     * 构建三级联动数据结构（国家-省-市，不包含区县）
     * 专门用于缓存场景，减少内存占用
     * 
     * @param countryCode 国家代码
     * @return 三级联动数据
     */
    public List<GeoHierarchyNode> buildThreeLevelHierarchy(String countryCode) {
        log.info("开始构建三级联动数据（不含区县），国家代码：{}", countryCode);
        
        try {
            // 1. 查询指定国家
            List<GeoHierarchyNode> countries = getCountries();
            GeoHierarchyNode targetCountry = countries.stream()
                .filter(country -> countryCode.equals(country.getId()))
                .findFirst()
                .orElse(null);
            
            if (targetCountry == null) {
                log.warn("未找到指定的国家，国家代码：{}", countryCode);
                return new ArrayList<>();
            }
            
            // 2. 构建该国家的省份数据
            List<GeoHierarchyNode> provinces = getProvinces(targetCountry.getId());
            targetCountry.setChildren(provinces);
            
            // 3. 为每个省份构建城市数据（不加载区县）
            int totalCities = 0;
            for (GeoHierarchyNode province : provinces) {
                List<GeoHierarchyNode> cities = getCities(province.getId());
                province.setChildren(cities);
                totalCities += cities.size();
                
                // 确保城市节点的children为空列表
                for (GeoHierarchyNode city : cities) {
                    city.setChildren(new ArrayList<>());
                }
            }
            
            log.info("三级联动数据构建完成，国家：{} - {}，省份数量：{}，城市总数：{}", 
                targetCountry.getId(), targetCountry.getName(), provinces.size(), totalCities);
            
            // 返回包含单个国家的列表
            List<GeoHierarchyNode> result = new ArrayList<>();
            result.add(targetCountry);
            return result;
            
        } catch (Exception e) {
            log.error("构建三级联动数据异常，国家代码：{}", countryCode, e);
            throw new RuntimeException("构建三级联动数据失败：" + e.getMessage());
        }
    }
    
}