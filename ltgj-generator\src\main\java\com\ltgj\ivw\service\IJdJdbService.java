package com.ltgj.ivw.service;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.domain.MinPriceReq;
import com.ltgj.ivw.dto.HotelCheckImageDto;
import com.ltgj.ivw.dto.JdJdbRepeatDTO;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.LongAdder;

/**
 * 酒店基础信息Service接口
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
public interface IJdJdbService {
    /**
     * 查询酒店基础信息
     *
     * @param id 酒店基础信息主键
     * @return 酒店基础信息
     */
    public JdJdb selectJdJdbById(String id);

    List<JdJdb> selectJdJdbByInterfacePlat(String interfacePlat);

    void updateAllForeElements(List<JdJdb> list);

    /**
     * 查询酒店基础信息列表
     *
     * @param jdJdb 酒店基础信息
     * @return 酒店基础信息集合
     */
    public List<JdJdb> selectJdJdbList(JdJdb jdJdb);

    public List<JdJdb> selectJdJdbListNew(JdJdb jdJdb);

    /**
     * 新增酒店基础信息
     *
     * @param jdJdb 酒店基础信息
     * @return 结果
     */
    public int insertJdJdb(JdJdb jdJdb);

    /**
     * 修改酒店基础信息
     *
     * @param jdJdb 酒店基础信息
     * @return 结果
     */
    public int updateJdJdb(JdJdb jdJdb);

    /**
     * 批量删除酒店基础信息
     *
     * @param ids 需要删除的酒店基础信息主键集合
     * @return 结果
     */
    public int deleteJdJdbByIds(String[] ids);

    /**
     * 删除酒店基础信息信息
     *
     * @param id 酒店基础信息主键
     * @return 结果
     */
    public int deleteJdJdbById(String id);

    public int deleteAllByCityIdYs(String cityIdYs);

    void deleteJdJdbByIdZh(String id);

    AjaxResult listMinPriceHSJL(MinPriceReq minPriceReq);

    AjaxResult listMinPriceHSJLXY(MinPriceReq minPriceReq);

    AjaxResult listMinPriceHSJLXYNew(MinPriceReq minPriceReq);

    void dealListMinPriceHSJLNew(String localId, Long hotelId);

    void dealListMinPriceQtNew(String localId, String hotelCode);

    void dealListMinPriceMtNew(String localId, Long hotelId);

    void dealListMinPriceHSJLXYNew(String localId, Long hotelId);

    AjaxResult listMinPriceMT(MinPriceReq minPriceReq);

    AjaxResult listMinPriceTravelButler(MinPriceReq minPriceReq);

    AjaxResult listMinPriceQT(String randomKey);

    void updateAllImageData(List<Optional<HotelCheckImageDto>> list);

    String downloadFile(MinPriceReq minPriceReq);

    void deleteFile();

    List<String> selectInIds(List<String> idList);

    List<String> selectAllJson(int i, int pageSize);

    List<String> selectAllJsonForScore(int i, int pageSize);

    List<String> selectIdList(String minId, int pageNumber);

    List<JdJdb> selectJdJdbByPlaId(String plat, String platId);

    /**
     * 差旅管家处理单条酒店最低价
     *
     * @param localId
     * @param hotelCode
     */
    void dealListMinPriceCLGJNew(String localId, Long hotelCode);

    List<JdJdbRepeatDTO> selectJdJdbRepeatByJdmcAndJddz(JdJdbRepeatDTO jdJdbRepeatDTO);

    /**
     * 批量查询酒店保存时间
     *
     * @param ids 酒店ID集合
     * @return 包含保存时间的酒店列表
     */
    List<JdJdb> selectJdJdbByIds(String[] ids);

    /**
     * 更新科坦最低价格
     *
     * @param minPriceReq
     * @return
     */
    AjaxResult updateLowPriceOfCozyTime(MinPriceReq minPriceReq);

    /**
     * 更新科坦最低价格
     * @param minPriceReq
     * @param count
     */
    void dealListMinPriceCozyTime(MinPriceReq minPriceReq, LongAdder count);

    List<JdJdb> selectAll(int i, int pageSize);

    List<String> selectCityId();

    List<JdJdb> selectByCityId(String cityId, Date date);

    int count();

    List<JdJdb> listByIds(List<String> ids);

    void updateKnowledgeIdByCityIdAndDate(String cityId, String knowledgeId, Date date);
}
