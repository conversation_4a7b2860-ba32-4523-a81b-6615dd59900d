package com.ltgj.ivw.dto;

import lombok.Data;

/**
 * 分级查询地理位置请求DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class GeoLevelQueryReq {
    
    /**
     * 查询级别：country, province, city, district
     */
    private String level;
    
    /**
     * 上级ID（根据级别确定上级类型）
     */
    private String parentId;
    
    /**
     * 是否包含子级数据
     */
    private Boolean includeChildren = false;
    
    /**
     * 地区类型（针对区县级别）
     * 1-商圈 2-行政区 3-标志物
     */
    private String geoType;
    
    /**
     * 分页参数 - 页码
     */
    private Integer pageNum;
    
    /**
     * 分页参数 - 每页大小
     */
    private Integer pageSize;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 最大递归深度（防止内存溢出）
     * 默认为4，可以加载完整的四级数据
     */
    private Integer maxDepth = 4;
    
    @Override
    public String toString() {
        return "GeoLevelQueryReq{" +
                "level='" + level + '\'' +
                ", parentId='" + parentId + '\'' +
                ", includeChildren=" + includeChildren +
                ", geoType='" + geoType + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", orderBy='" + orderBy + '\'' +
                ", maxDepth=" + maxDepth +
                '}';
    }
} 