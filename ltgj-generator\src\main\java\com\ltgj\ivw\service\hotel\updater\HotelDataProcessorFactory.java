package com.ltgj.ivw.service.hotel.updater;

import com.alibaba.fastjson.JSON;
import com.ltgj.ivw.domain.dto.CityDTO;
import com.ltgj.ivw.domain.dto.HotelDTO;
import com.ltgj.ivw.service.impl.HotelInfoChailvgjServiceImpl;

/**
 * 酒店数据处理器工厂(工厂模式)
 *
 * <AUTHOR>
 */
public class HotelDataProcessorFactory {
    /**
     * 创建数据处理器
     * @param type 处理器类型
     * @param service 酒店服务实现
     * @param data 数据对象
     * @return 酒店数据处理器
     */
    public HotelDataProcessor createProcessor(HotelDataProcessorType type, HotelInfoChailvgjServiceImpl service, Object data) {
        System.out.println("创建数据处理器: " + type+", data: " + data+", service: " + service+", data: " + JSON.toJSONString(data));
        switch (type) {
            case CITY_HOTEL:
                return new CityHotelDataProcessor(service, (CityDTO) data);
            case SINGLE_HOTEL:
                return new SingleHotelDataProcessor(service, (HotelDTO) data);
            default:
                throw new IllegalArgumentException("不支持的处理器类型: " + type);
        }
    }
} 