package com.ltgj.ivw.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * @ClassName: HotelGnSupplier
 * @Description: 酒店-国内
 * @Author: nxy
 * @CreateDate: 2025/6/6
 * @UpdateUser: nxy
 * @UpdateDate: 2025/6/6
 */
@Data
public class HotelGnSupplierReq implements Serializable {

    private String tableName;
    private String createTime;
    private Integer offset;
    private Integer limit;
    private String interfacePlat;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_gn_ketan
     *
     * @mbg.generated 2025/06/06 16:16
     */
    private static final long serialVersionUID = 1L;

}
