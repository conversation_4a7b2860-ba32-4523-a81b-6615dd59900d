package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.domain.chalvgj.HotelInfoChailvgj1;
import com.ltgj.ivw.domain.dto.*;
import com.ltgj.ivw.domain.response.ClgjPriceItems;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.HotelGnBaseCommonMapper;
import com.ltgj.ivw.mapper.HotelInfoChailvgjMapper;
import com.ltgj.ivw.service.*;
import com.ltgj.ivw.service.hotel.updater.DirectChailvgjUpdateStrategy;
import com.ltgj.ivw.service.hotel.updater.HotelDataUpdateContext;
import com.ltgj.ivw.service.hotel.updater.ProcessResult;
import com.ltgj.ivw.service.hotel.updater.ThreadPoolManager;
import com.ltgj.ivw.utils.RateLimitUtil;
import com.ltgj.ivw.utils.hotelApi.ChailvgjApi;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import com.ltgj.supplier.common.domain.FacilitiesInfo;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnClgj;
import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import com.ltgj.supplier.common.gn.enums.HotelGnCustomerTypeEnum;
import com.ltgj.supplier.common.gn.enums.HotelGnTypeEnum;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.common.gn.service.HotelGnIdMappingService;
import com.ltgj.supplier.common.utils.CheckInOutUtil;
import com.ltgj.supplier.common.utils.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 差旅管家数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Service("hotelInfoChailvService")
@Slf4j
public class HotelInfoChailvgjServiceImpl extends BaseHotelServiceImpl<HotelGnBase, HotelGnClgj> implements IHotelInfoChailvgjService, IHotelGnBaseService
{
    @Autowired
    private HotelInfoChailvgjMapper hotelInfoChailvgjMapper;

    @Autowired
    private IZhJdJdbMinPriceService zhJdJdbMinPriceService;

    @Autowired
    private IHotelUpdateRecodeService hotelUpdateRecodeService;

    @Autowired
    private IHotelCityService hotelCityService;

    @Autowired
    private RateLimitUtil rateLimitUtil;

    @Autowired
    private HotelGnBaseService hotelGnBaseService;

    @Autowired
    private HotelGnIdMappingService hotelGnIdMappingService;

    @Autowired
    private HotelGnBaseCommonMapper<HotelGnClgj> mapper;

    @Value("${rateLimiter.clgj.GetHotelInfo:20}")
    private Integer getHotelInfoNum;

    private static final int MAX_REQUESTS_PER_SECOND = 4;
    private static final long INTERVAL_MS = 1000 / MAX_REQUESTS_PER_SECOND;


    @Override
    public Class<HotelGnClgj> getEntityClass() {
        return HotelGnClgj.class;
    }

    @Override
    protected PlatEnum getPlatEnum() {
        return PlatEnum.PLAT_CLGJ;
    }

    @Override
    protected List<HotelGnBase> doSelectListWithParams(HotelGnBase entity, Map<String, Object> queryParams) {
        log.debug("差旅管家酒店查询列表，参数：{}", queryParams);
        return mapper.selectHotelGnBaseCommonList(entity,getEntityClass());
    }

    @Override
    protected HotelGnBase doSelectById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,getEntityClass());
    }

    @Override
    protected int doInsert(HotelGnBase entity) {
        try {
            String res = ChailvgjApi.hotelDetail(Long.valueOf(entity.getId()));
            JSONObject resJson = JSONObject.parseObject(res);
            if(resJson.getInteger("Code") == 997) {
                return 0;
            }
            HotelDetailDTO detailDTO = this.buildHotelDetailDTO(resJson.getJSONObject("Data"));
            this.saveHotelInfo(detailDTO);
        } catch (NumberFormatException e) {
            log.error("查询酒店详情异常", e);
            return 0;
        }
        return 1;
    }


    @Override
    protected int doUpdate(HotelGnBase entity) {
        return mapper.updateHotelGnBaseCommon(entity,getEntityClass());
    }

    @Override
    protected int doDeleteByIds(String[] ids) {
        return mapper.deleteHotelGnBaseCommonByIds(ids,getEntityClass());
    }



    /**
     * 查询差旅管家数据列表
     *
     * @param hotelInfoChailvgj 差旅管家数据
     * @return 差旅管家数据
     */
    @Override
    public List<HotelInfoChailvgj> selectHotelInfoChailvgjList(HotelInfoChailvgj hotelInfoChailvgj) {
        return hotelInfoChailvgjMapper.selectHotelInfoChailvgjList(hotelInfoChailvgj);
    }

    @Override
    public List<Long> selectHotelIds(HotelInfoChailvgj hotelInfoChailvgj) {
        return hotelInfoChailvgjMapper.selectHotelIds(hotelInfoChailvgj);
    }

    /**
     * 新增差旅管家数据
     *
     * @param hotelInfoChailvgj 差旅管家数据
     * @return 结果
     */
    @Override
    public int insertHotelInfoChailvgj(HotelInfoChailvgj hotelInfoChailvgj)
    {
        hotelInfoChailvgj.setIncrementTime(DateUtils.getNowDate());
        hotelInfoChailvgj.setCreateTime(DateUtils.getNowDate());
        return hotelInfoChailvgjMapper.insertHotelInfoChailvgj(hotelInfoChailvgj);
    }

    /**
     * 修改差旅管家数据
     *
     * @param hotelInfoChailvgj 差旅管家数据
     * @return 结果
     */
    @Override
    public int updateHotelInfoChailvgj(HotelInfoChailvgj hotelInfoChailvgj)
    {
        hotelInfoChailvgj.setUpdateTime(DateUtils.getNowDate());
        return hotelInfoChailvgjMapper.updateHotelInfoChailvgj(hotelInfoChailvgj);
    }

    /**
     * 批量删除差旅管家数据
     *
     * @param ids 需要删除的差旅管家数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoChailvgjByIds(String[] ids)
    {
        return hotelInfoChailvgjMapper.deleteHotelInfoChailvgjByIds(ids);
    }

    /**
     * 删除差旅管家数据信息
     *
     * @param id 差旅管家数据主键
     * @return 结果
     */
    @Override
    public int deleteHotelInfoChailvgjById(String id)
    {
        return hotelInfoChailvgjMapper.deleteHotelInfoChailvgjById(id);
    }

    @Override
    public int deleteHotelInfoChailvgjByPlatIds(String[] ids) {
        return hotelInfoChailvgjMapper.deleteHotelInfoChailvgjByPlatIds(ids);
    }


    @Override
    public void updateLowPrice(MinPriceReq minPriceReq) {
        /*// 控制每秒4次请求, 每次20条, 每次抓7天的数据,
        if(StringUtils.isBlank(minPriceReq.getCheckInDate())){
            //默认明天
            String tomorrow  = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            minPriceReq.setCheckInDate(tomorrow);
        }

        if(CollectionUtils.isEmpty(minPriceReq.getHotelIdList())){
            HotelInfoChailvgj search = new HotelInfoChailvgj();
            search.setStatus(8);//未映射数据
            List<Long> hotelIdList = selectHotelIds(search);
            minPriceReq.setHotelIdList(hotelIdList);
        }
        HotelUpdateStatus.status = 1;
        // 从开始时间列出七天日期
        List<String> dateList = DateUtils.getDateList(minPriceReq.getCheckInDate(), 1);
        int count = 0;
        int dateCount = 0;
        for (String checkInDate : dateList) {
            dateCount ++;
            logger.info("差旅管家低价查询，第{}天", dateCount);
            //hotelIdList按照每20个值进行分组
            List<List<Long>> hotelIdList = MyTools.splitList(minPriceReq.getHotelIdList(), 20);
            logger.info("差旅管家低价查询日期：{}，酒店分组数量：{}", checkInDate, hotelIdList.size());
            for(List<Long> idList : hotelIdList){
                count++;
                logger.info("差旅管家低价查询日期：{}，批次：{}", checkInDate, count);
                String res = ChailvgjApi.hotelSearch(null, checkInDate, 1, 500, null);
                JSONObject resJson = JSONObject.parseObject(res);
                System.out.println("差旅管家搜索查询API结果：" + resJson);
                if(res == null || !resJson.get("Code").equals("200")) {
                    logger.error("差旅管家低价查询API返回异常 {}", resJson);
                }else {
                    List<ClgjPriceItems> minPriceList = ChailvgjApi.getMinPrice(resJson, checkInDate);
                    //获取localId
                    for (ClgjPriceItems priceItem : minPriceList) {
                        String hotelId = priceItem.getHotelId();
                        //1.根据酒店id和PlatEnum查询jd_jdb_mapping
                        JdJdbMapping jdJdbMapping = jdJdbMappingService.selectJdJdbMappingByPlatIdAndInterfacePlat(hotelId, PlatEnum.PLAT_CLGJ.getValue());
                        if (null != jdJdbMapping) {
                            handleMappingData(priceItem, jdJdbMapping);
                        }else {
                            handleNoMappingData(priceItem, hotelId);
                        }
                    }
                }
                // 加入延迟
                try {
                    Thread.sleep(INTERVAL_MS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.error("Thread was interrupted while sleeping");
                }
            }
        }
        HotelUpdateStatus.status = 0;*/
    }

    @Override
    public int deleteHotelInfoChailvgjByIds(List<String> idsDelete) {
        return hotelInfoChailvgjMapper.deleteHotelInfoChailvgjByIdList(idsDelete);
    }

    @Override
    public List<HotelInfoChailvgj> batchSelectIdList(String minId, int pageNumber) {
        return hotelInfoChailvgjMapper.batchSelectIdList(minId, pageNumber);
    }

    @Override
    public List<HotelInfoChailvgj1> selectHotelInfoChailvgj1ListByCityIds(List<String> cityIds) {
        return hotelInfoChailvgjMapper.selectHotelInfoChailvgj1ListByCityIds(cityIds);
    }

    @Override
    public int insertOrUpdateHotelInfoChailvgj(HotelInfoChailvgj hotelInfoChailvgj) {
        hotelInfoChailvgj.setCreateTime(DateUtils.getNowDate());
        return hotelInfoChailvgjMapper.insertOrUpdateHotelInfoChailvgj(hotelInfoChailvgj);
    }

    /**
     * 处理未映射数据
     *
     * @param priceItem 价格项目
     * @param hotelId   酒店id
     */
    private void handleNoMappingData(ClgjPriceItems priceItem, String hotelId) {
        String localId = PlatEnum.PLAT_CLGJ.getValue() + hotelId;
        logger.info("差旅管家酒店未映射酒店数据最低价插入成功2 localId:{} hotelId:{}",localId, hotelId);
        ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
        zhJdJdbMinPrice.setJdid(localId);
        zhJdJdbMinPrice.setMinPrice(priceItem.getSalePrice());
        zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
        zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
        logger.info("差旅管家酒店最低价插入成功2 local_id:{}", zhJdJdbMinPrice.getJdid());
    }

    /**
     * 处理映射数据
     *
     * @param priceItem    价格项目
     * @param jdJdbMapping jd-jdb映射
     */
    private void handleMappingData(ClgjPriceItems priceItem, JdJdbMapping jdJdbMapping) {
        String localId = jdJdbMapping.getLocalId();
        logger.info("差旅管家酒店映射成功最低价插入成功1 local_id:{}", jdJdbMapping.getLocalId());
        // 差旅管家的金额单位是元，因此不用转换
        BigDecimal salePrice = priceItem.getSalePrice();
        Date date = priceItem.getSaleDate();
        ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
        zhJdJdbMinPrice.setJdid(localId);
        zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
        List<ZhJdJdbMinPrice> prices = zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
        if (prices.size() == 0) {
            zhJdJdbMinPrice.setSxsj(priceItem.getSaleDate());
            zhJdJdbMinPrice.setMinPrice(salePrice);
            zhJdJdbMinPriceService.insertZhJdJdbMinPrice(zhJdJdbMinPrice);
            logger.info("差旅管家酒店最低价插入成功 localId：{} sxsj：{}", localId, date);
        } else {
            zhJdJdbMinPrice = prices.get(0);
            if (zhJdJdbMinPrice.getMinPrice().compareTo(salePrice) < 0) {
                //不需要更新最低价
                logger.info("当前酒店不需要更新最低价 clgj：{},酒店ID：{},最低价：{}", zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), zhJdJdbMinPrice.getSxsj());
            } else {
                zhJdJdbMinPrice.setSxsj(date);
                zhJdJdbMinPrice.setMinPrice(salePrice);
                zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                logger.info("差旅管家酒店最低价更新成功 localId：{} sxsj：{}", localId, date);
            }
        }
    }

    /**
     * 使用多线程获取所有城市数据
     * @return 城市列表
     */
    public List<CityDTO> getAllCities() {
        log.info("开始获取差旅管家城市数据");
        // 使用线程安全的集合存储结果
        List<CityDTO> cityList = Collections.synchronizedList(new ArrayList<>());

        try {
            // 获取城市数据
            HotelCity hotelCitySearch = new HotelCity();
            hotelCitySearch.setReserve1(PlatEnum.PLAT_CLGJ.getValue());
            List<HotelCity> hotelCities = hotelCityService.selectHotelCityList(hotelCitySearch);

            if (CollectionUtils.isEmpty(hotelCities)) {
                log.error("获取城市数据失败: {}", hotelCities);
                return cityList;
            }

            // 获取ThreadPoolManager单例实例
            ThreadPoolExecutor executor = ThreadPoolManager.getInstance().getExecutor();

            // 使用CountDownLatch等待所有线程完成
            final CountDownLatch latch = new CountDownLatch(hotelCities.size());

            // 处理并发任务
            for (HotelCity hotelCity : hotelCities) {
                executor.submit(() -> {
                    try {
                        CityDTO city = new CityDTO();
                        city.setCityId(hotelCity.getCityId());
                        city.setCityName(hotelCity.getCityName());
                        city.setProvinceId(hotelCity.getProvinceId());
                        city.setProvinceName(hotelCity.getProvinceName());
                        city.setCountryId(hotelCity.getCountryId());
                        city.setCountryName(hotelCity.getCountryName());
                        city.setParentCityId(hotelCity.getReserve3());

                        // 线程安全地添加到结果集合中
                        cityList.add(city);
                    } catch (Exception e) {
                        logger.error("处理城市数据异常: {}", hotelCity.getCityId(), e);
                    } finally {
                        // 完成一个任务，计数器减一
                        latch.countDown();
                    }
                });
            }

            // 等待所有线程完成
            try {
                latch.await(30, TimeUnit.SECONDS); // 设置30秒超时，防止无限等待
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("等待城市数据处理线程被中断", e);
            }

            logger.info("获取城市数据成功，共{}条数据", cityList.size());
        } catch (Exception e) {
            logger.error("获取城市数据异常", e);
        }

        return cityList;
    }

    /**
     * 根据城市ID获取酒店列表
     * @param cityId 城市ID
     * @return 酒店列表
     */
    public List<HotelDTO> getHotelListByCityId(String cityId) {
        logger.info("开始获取城市[{}]的酒店列表", cityId);
        List<HotelDTO> hotelList = new ArrayList<>();

        int pageNo = 1;
        int pageSize = 100;
        int totalPage = 1;

        try {
            do {
                // 调用酒店列表接口
                String hotelListResult = ChailvgjApi.getHotelList(Integer.valueOf(cityId), pageNo, pageSize);
                JSONObject jsonObject = JSONObject.parseObject(hotelListResult);

                if (!"200".equals(jsonObject.getString("Code"))) {
                    logger.error("获取城市[{}]酒店列表失败: {}", cityId, jsonObject.getString("Msg"));
                    break;
                }

                JSONObject data = jsonObject.getJSONObject("Data");
                JSONObject hotelListJson = data.getJSONObject("HotelList");
                totalPage = hotelListJson.getInteger("Page");

                JSONArray hotelArray = hotelListJson.getJSONArray("HotelList");
                for (int i = 0; i < hotelArray.size(); i++) {
                    JSONObject hotelJson = hotelArray.getJSONObject(i);

                    // 只处理可预订的酒店
                    boolean isBookable = hotelJson.getBooleanValue("IsBookable");
                    if (!isBookable) {
                        continue;
                    }

                    HotelDTO hotel = new HotelDTO();
                    hotel.setHotelId(hotelJson.getString("HotelId"));
                    hotel.setHotelName(hotelJson.getString("HotelName"));
                    hotel.setCityId(hotelJson.getString("CityId"));
                    hotel.setCityName(hotelJson.getString("CityName"));
                    hotel.setUpdateTime(hotelJson.getString("UpdatedTime"));
                    hotel.setIsBookable(isBookable);

                    // 处理酒店映射关系
//                    JSONArray mappingArray = hotelJson.getJSONArray("MappingHotels");
//                    if (mappingArray != null && !mappingArray.isEmpty()) {
//                        List<MappingDTO> mappings = new ArrayList<>();
//
//                        for (int j = 0; j < mappingArray.size(); j++) {
//                            JSONObject mappingJson = mappingArray.getJSONObject(j);
//
//                            MappingDTO mapping = new MappingDTO();
//                            mapping.setMappingCode(mappingJson.getString("MappingHotelCode"));
//                            mapping.setMappingType(mappingJson.getInteger("MappingType"));
//
//                            mappings.add(mapping);
//                        }
//
//                        hotel.setMappings(mappings);
//                    }

                    hotelList.add(hotel);
                }

                pageNo++;
            } while (pageNo <= totalPage);

            logger.info("获取城市[{}]酒店列表成功，共{}条数据", cityId, hotelList.size());
        } catch (Exception e) {
            logger.error("获取城市[{}]酒店列表异常", cityId, e);
        }

        return hotelList;
    }

    /**
     * 获取酒店详细信息
     * @param hotelId 酒店ID
     * @return 酒店详情
     */
    public HotelDetailDTO getHotelDetail(String hotelId) {
        logger.info("开始获取酒店[{}]详情", hotelId);

        try {
            // 调用酒店详情接口
            String hotelDetailResult = (String) rateLimitUtil.fetchDataFunction(PlatEnum.PLAT_CLGJ.getValue() + ":GetHotelInfo", 1, getHotelInfoNum, (result) -> ChailvgjApi.getHotelInfo(Long.valueOf(hotelId), 3));
            JSONObject jsonObject = JSONObject.parseObject(hotelDetailResult);

            if (!"200".equals(jsonObject.getString("Code"))) {
                logger.error("获取酒店[{}]详情失败: {}", hotelId, jsonObject.getString("Msg"));
                return null;
            }
            JSONObject data = jsonObject.getJSONObject("Data");
            HotelDetailDTO hotel = buildHotelDetailDTO(data);
            logger.info("获取酒店[{}]详情成功", hotelId);
            return hotel;
        } catch (Exception e) {
            logger.error("获取酒店[{}]详情异常", hotelId, e);
            return null;
        }
    }


    public HotelDetailDTO buildHotelDetailDTO(JSONObject data) {
        HotelDetailDTO hotel = new HotelDetailDTO();
        hotel.setOriginalText(data.toJSONString());
        hotel.setHotelId(data.getString("HotelId"));
        hotel.setHotelName(data.getString("HotelName"));
        hotel.setHotelNameEn(data.getString("HotelNameEn"));
        hotel.setLogUrl(data.getString("LogUrl"));
        hotel.setCityId(data.getString("PrefectureLevelCityId"));
        hotel.setCityName(data.getString("PrefectureLevelCityName"));
        hotel.setAreaId(data.getString("CityId"));
        hotel.setAreaName(data.getString("CityName"));
        hotel.setAddress(data.getString("Address"));
        hotel.setStarRating(data.getInteger("StarRating"));
        hotel.setGuestType(data.getInteger("GuestType"));
        hotel.setStarDesc(data.getString("StarRateDesc"));
        hotel.setDistrict(data.getString("District"));
        hotel.setBusinessArea(data.getString("Business"));
        hotel.setTelephone(data.getString("TelephoneNum"));
        hotel.setBrandCode(data.getString("BrandCode"));
        hotel.setBrandName(data.getString("BrandName"));
        hotel.setOpeningYear(data.getString("OpenYear"));
        hotel.setInOutDesc(data.getString("InOutDesc"));
        hotel.setRenovationYear(data.getString("RenovationYear"));
        hotel.setReviewScore(data.getBigDecimal("ReviewScore"));
        hotel.setIsBookable(data.getBooleanValue("IsBookable"));
        hotel.setIsScarce(data.getBooleanValue("IsScarce"));
        hotel.setIsAdvantage(data.getBooleanValue("IsAdvantage"));
        hotel.setIsHighDemand(data.getBooleanValue("IsHighDemand"));
        hotel.setFacilityList(data.getList("FacilityList", Integer.class));

        // 处理地理位置信息
        JSONObject geo = data.getJSONObject("Geo");
        if (geo != null) {
            hotel.setLongitude(geo.getBigDecimal("Lon"));
            hotel.setLatitude(geo.getBigDecimal("Lat"));
        }

        // 处理酒店图片
        JSONObject hotelImages = data.getJSONObject("HotelImages");
        if (hotelImages != null) {
            JSONArray imageList = hotelImages.getJSONArray("ImageList");
            if (imageList != null && !imageList.isEmpty()) {
                List<ImageDTO> images = new ArrayList<>();

                for (int i = 0; i < imageList.size(); i++) {
                    JSONObject imageJson = imageList.getJSONObject(i);

                    ImageDTO image = new ImageDTO();
                    image.setName(imageJson.getString("Name"));
                    image.setType(imageJson.getString("Type"));
                    image.setUrl(imageJson.getString("Src"));

                    images.add(image);
                }

                hotel.setImages(images);
            }
        }

        // 处理酒店映射关系
        JSONArray mappingHotels = data.getJSONArray("MappingHotels");
        if (mappingHotels != null && !mappingHotels.isEmpty()) {
            List<MappingDTO> mappings = new ArrayList<>();

            for (int i = 0; i < mappingHotels.size(); i++) {
                JSONObject mappingJson = mappingHotels.getJSONObject(i);

                MappingDTO mapping = new MappingDTO();
                mapping.setMappingCode(mappingJson.getString("MappingHotelCode"));
                mapping.setMappingType(mappingJson.getInteger("MappingType"));

                mappings.add(mapping);
            }

            hotel.setMappings(mappings);
        }
        return hotel;
    }

    /**
     * 保存酒店信息到现有表结构
     * @param hotelDetail 酒店详情
     */
    public ProcessResult saveHotelInfo(HotelDetailDTO hotelDetail) {
        if (hotelDetail == null) {
            return new ProcessResult(new AtomicInteger(1), new AtomicInteger(0), new AtomicInteger(0),new AtomicInteger(1));
        }

        try {
            // 酒店概况
            HotelGnClgj hotelInfo = new HotelGnClgj();
            hotelInfo.setId(hotelDetail.getHotelId()); // 差旅管家酒店ID作为主键
            hotelInfo.setName(hotelDetail.getHotelName());
            hotelInfo.setNameEn(hotelDetail.getHotelNameEn());
            hotelInfo.setTypeId(HotelGnTypeEnum.getClgjDefaultType().getId());
            hotelInfo.setOpenDate(hotelDetail.getOpeningYear());
            hotelInfo.setDecorationDate(hotelDetail.getRenovationYear());
            hotelInfo.setPhone(hotelDetail.getTelephone());
            hotelInfo.setStatus(1);
            // 位置信息
            HotelCity city =  hotelCityService.getByCityIdAndPlatIdForCache(PlatEnum.PLAT_CLGJ, hotelDetail.getCityId());
            if (city != null) {
                hotelInfo.setCountryId(city.getCountryId());
                hotelInfo.setCountryName(city.getCountryName());
                hotelInfo.setProvinceId(city.getProvinceId());
                hotelInfo.setProvinceName(city.getProvinceName());
                hotelInfo.setCityId(city.getCityId());
                hotelInfo.setCityName(city.getCityName());
            } else {
                logger.info("hotel_city中城市数据为空：{}", hotelDetail.getCityId());
                hotelInfo.setCityId(hotelDetail.getCityId());
                hotelInfo.setCityName(hotelDetail.getCityName());
            }

            if (StringUtils.isNotBlank(hotelDetail.getCityId()) && !hotelDetail.getCityId().equals(hotelDetail.getAreaId())) {
                hotelInfo.setAreaId(hotelDetail.getAreaId());
                hotelInfo.setAreaName(hotelDetail.getAreaName());
            }

            hotelInfo.setBusinessDistrictName(hotelDetail.getBusinessArea());
            hotelInfo.setAddress(hotelDetail.getAddress());
            hotelInfo.setLonGaode(hotelDetail.getLongitude()); // 使用高德坐标
            hotelInfo.setLatGaode(hotelDetail.getLatitude());
            String currentUserName = StringUtils.isEmpty(hotelDetail.getCurrentUserName()) ? "system" : hotelDetail.getCurrentUserName();
            // 酒店详情
            hotelInfo.setBrandId(hotelDetail.getBrandCode());
            hotelInfo.setBrandName(hotelDetail.getBrandName());
            hotelInfo.setStar(hotelDetail.getStarRating() != null ? String.valueOf(hotelDetail.getStarRating()) : "");
            hotelInfo.setImage(hotelDetail.getLogUrl());
            hotelInfo.setScore(hotelDetail.getReviewScore() != null ? hotelDetail.getReviewScore().toString() : "");
            hotelInfo.setUpdateBy(currentUserName);
            hotelInfo.setUpdateTime(new Date());
            hotelInfo.setReserve0(hotelDetail.getOriginalText());

            PolicyInfo policyInfo = new PolicyInfo();

            try {
                PolicyInfo.CheckInOutPolicy checkInOutPolicy = new PolicyInfo.CheckInOutPolicy();
                String[] times = CheckInOutUtil.getCheckInAndOutTime(hotelDetail.getInOutDesc());
                if (times != null) {
                    checkInOutPolicy.setCheckIn(times[0]);
                    if (times.length == 2) {
                        checkInOutPolicy.setCheckOut(times[1]);
                    }
                }
                policyInfo.setCheckInOutPolicy(checkInOutPolicy);
            } catch (Exception e) {
                logger.error("差旅管家-处理 inOutDesc 异常:{}, error:{}", hotelDetail.getInOutDesc(), e);
            }

            try {
                PolicyInfo.CustomerPolicy customerPolicy = new PolicyInfo.CustomerPolicy();
                if (hotelDetail.getGuestType().intValue() == 3) {
                    customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_GUEST.getCode()));
                    customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.MAINLAND_GUEST.getDescription());
                } else if (hotelDetail.getGuestType().intValue() == 4) {
                    customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getCode()));
                    customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.MAINLAND_HKMO_TW_GUEST.getDescription());
                } else if (hotelDetail.getGuestType().intValue() == 5) {
                    customerPolicy.setCustomerType(String.valueOf(HotelGnCustomerTypeEnum.GLOBAL_GUEST.getCode()));
                    customerPolicy.setCustomerDesc(HotelGnCustomerTypeEnum.GLOBAL_GUEST.getDescription());
                }
                policyInfo.setCustomerPolicy(customerPolicy);
            } catch (Exception e) {
                logger.error("差旅管家-处理 guestType 异常:{}, error:{}", hotelDetail.getGuestType(), e);
            }

            hotelInfo.setPolicyInfo(JSON.toJSONString(policyInfo));

            try {
                /**
                 * 101	wifi
                 * 201	宽带
                 * 301	停车场
                 * 401	接机服务
                 * 501	游泳池
                 * 601	会议室
                 * 701	餐厅
                 * 801	健身房
                 */
                FacilitiesInfo facilitiesInfo = new FacilitiesInfo();
                for (Integer facility : hotelDetail.getFacilityList()) {
                    if (facility.intValue() == 301) {
                        FacilitiesInfo.Parking parking = new FacilitiesInfo.Parking();
                        parking.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setParking(parking);
                    } else if (facility.intValue() == 701) {
                        FacilitiesInfo.Restaurant restaurant = new FacilitiesInfo.Restaurant();
                        restaurant.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setRestaurant(restaurant);
                    } else if (facility.intValue() == 601) {
                        FacilitiesInfo.MeetingRoom meetingRoom = new FacilitiesInfo.MeetingRoom();
                        meetingRoom.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setMeetingRoom(meetingRoom);
                    } else if (facility.intValue() == 101) {
                        FacilitiesInfo.PublicWifi publicWifi = new FacilitiesInfo.PublicWifi();
                        publicWifi.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setPublicWifi(publicWifi);
                    } else if (facility.intValue() == 801) {
                        FacilitiesInfo.Gym gym = new FacilitiesInfo.Gym();
                        gym.setIsHave(Boolean.TRUE);
                        facilitiesInfo.setGym(gym);
                    }
                }
                hotelInfo.setFacilitiesInfo(JSON.toJSONString(facilitiesInfo));
            } catch (Exception e) {
                logger.error("差旅管家-处理 设施 异常:{}, error:{}", hotelDetail.getFacilityList(), e);
            }


            HotelGnIdMapping idMapping = null;
            List<MappingDTO> mappings = hotelDetail.getMappings();
            if (CollectionUtils.isNotEmpty(mappings)) {
                MappingDTO mappingDTO = mappings.stream().filter(mapping -> mapping.getMappingType() == 65).findFirst().orElse(null);
                if (mappingDTO != null) {
                    idMapping = new HotelGnIdMapping();
                    idMapping.setPlatformId(PlatEnum.PLAT_CLGJ.getValue());
                    idMapping.setPlatformHotelId(hotelInfo.getId());
                    idMapping.setMappingPlatformId(PlatEnum.PLAT_EL.getValue());
                    idMapping.setMappingHotelId(mappingDTO.getMappingCode());
                    idMapping.setRemark(JSON.toJSONString(mappings));
                }
            }

            // 如果有图片，使用第一张图片作为主图
//            if (CollectionUtils.isNotEmpty(hotelDetail.getImages())) {
//                hotelInfo.setImage(hotelDetail.getImages().get(0).getUrl());
//            }

            // 存储特殊属性
//            if (hotelDetail.getIsScarce()) {
//                hotelInfo.setReserve5("1"); // 稀缺资源
//            }
//            if (hotelDetail.getIsAdvantage()) {
//                hotelInfo.setReserve6("1"); // 优势资源
//            }
//            if (hotelDetail.getIsHighDemand()) {
//                hotelInfo.setReserve7("1"); // 高需求资源
//            }

            // 保存或更新酒店信息
            HotelGnBase existingHotel = hotelGnBaseService.getById(PlatEnum.PLAT_CLGJ, hotelInfo.getId());
            if (existingHotel != null) {
                saveData(hotelInfo,  idMapping);
                logger.info("更新酒店[{}]信息成功", hotelDetail.getHotelId());
                return new ProcessResult(new AtomicInteger(1), new AtomicInteger(0), new AtomicInteger(1),new AtomicInteger(1));
            } else {
                hotelInfo.setCreateBy(currentUserName);
                hotelInfo.setCreateTime(new Date());
                hotelInfo.setIsDelete(0);
                hotelInfo.setStatus(1);
                saveData(hotelInfo,  idMapping);
                logger.info("保存酒店[{}]信息成功", hotelDetail.getHotelId());
                return new ProcessResult(new AtomicInteger(1), new AtomicInteger(1), new AtomicInteger(0),new AtomicInteger(1));
            }
        } catch (Exception e) {
            logger.error("处理酒店[{}]信息异常", hotelDetail.getHotelId(), e);
        }
        return new ProcessResult(new AtomicInteger(1), new AtomicInteger(0), new AtomicInteger(0),new AtomicInteger(1));
    }

    @Transactional
    public void saveData(HotelGnClgj hotelGnClgj, HotelGnIdMapping idMapping) {
        hotelGnBaseService.addOrUpdate(PlatEnum.PLAT_CLGJ, Arrays.asList(hotelGnClgj));
        hotelGnIdMappingService.addOrUpdateBatch(Arrays.asList(idMapping));
    }

    public void resetStatus() {
        HotelUpdateStatus.expStatusCLGJ = 0;
        HotelUpdateStatus.clgjDetailCountGet = 0;
        HotelUpdateStatus.clgjDetailCountUpdate = 0;
        HotelUpdateStatus.clgjDetailCountDelete = 0;
        HotelUpdateStatus.status = 0;
    }

    /**
     * 全量更新差旅管家酒店数据 - 重构版本
     */
    public void updateAll(String userName) {
        log.info("开始全量更新差旅管家酒店数据,userName:{}", userName);
        // 创建酒店数据更新策略
        HotelDataUpdateContext updateContext = new HotelDataUpdateContext(
                new DirectChailvgjUpdateStrategy(this, hotelUpdateRecodeService)
        );

        // 执行数据更新
        updateContext.executeUpdate(userName);
        log.info("全量更新差旅管家酒店数据结束,userName:{}", userName);
    };

    /**
     * 查询差旅管家数据
     *
     * @param id 差旅管家数据主键
     * @return 差旅管家数据
     */
    @Override
    public HotelInfoChailvgj selectHotelInfoChailvgjById(String id)
    {
        return hotelInfoChailvgjMapper.selectHotelInfoChailvgjById(id);
    }

    // ==================== 导出相关方法实现 ====================

    @Override
    public List<?> exportData(HotelGnBase entity, HotelGnClgj searchParams) {
        log.info("开始获取差旅管家酒店导出数据，实体：{}，查询参数：{}", entity, searchParams);
        
        // 使用现有的查询方法获取数据，这里不进行分页
        List<HotelGnBase> result = this.selectListWithParams(entity, null);
        
        log.info("获取差旅管家酒店导出数据完成，数据量：{}", result.size());
        return result;
    }

    @Override
    public Class<?> getExportEntityClass() {
        return HotelGnClgj.class;
    }

    @Override
    public String getExportFileName(HotelGnClgj searchParams) {
        return "差旅管家酒店信息数据";
    }

    @Override
    public HotelGnBase selectHotelGnBaseById(String id) {
        return mapper.selectHotelGnBaseCommonById(id,  getEntityClass());
    }

    @Override
    public void updateHotelGnBase(HotelGnBase hotelGnBase) {
        this.update(hotelGnBase);
    }

}
