package com.ltgj.supplier.common.dto;

import lombok.Data;
import java.util.List;

/**
 * 科坦城市数据传输对象
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class KetanCityDTO {
    
    /**
     * 国家编码
     */
    private String countryCode;
    
    /**
     * 城市列表
     */
    private List<City> cities;
    
    @Data
    public static class City {
        /**
         * 城市编码
         */
        private String cityCode;
        
        /**
         * 国家编码
         */
        private String countryCode;
        
        /**
         * 城市名称
         */
        private String name;
        
        /**
         * 城市中文名称
         */
        private String nameCN;
        
        /**
         * 省份编码
         */
        private String provinceCode;
        
        /**
         * 省份英文名称
         */
        private String provinceName;
        
        /**
         * 省份中文名称
         */
        private String provinceNameCN;
        
        /**
         * 区县列表
         */
        private List<District> districts;
        
        /**
         * 时区
         */
        private String timeZone;
    }
    
    @Data
    public static class District {
        /**
         * 区县名称
         */
        private String districtName;
        
        /**
         * 区县中文名称
         */
        private String districtNameCN;
    }
} 