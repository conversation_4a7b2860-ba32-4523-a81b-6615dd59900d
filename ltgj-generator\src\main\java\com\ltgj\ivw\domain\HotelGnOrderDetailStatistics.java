package com.ltgj.ivw.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table hotel_gn_order_detail_statistics
 */
@Data
public class HotelGnOrderDetailStatistics implements Serializable {
    /**
     * Database Column Remarks:
     *   主键
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.id
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   类型：1-订单
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.type
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private Integer type;


    private String interfaceName;

    /**
     * Database Column Remarks:
     *   平台酒店标识
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.interface_plat
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private Long interfacePlat;

    /**
     * Database Column Remarks:
     *   本地酒店名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.hotel_name
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private String hotelName;

    /**
     * Database Column Remarks:
     *   子类型：1-验单、2-下单
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.subtype
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private String subtype;

    /**
     * Database Column Remarks:
     *   本地酒店ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.local_id
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private String localId;

    /**
     * Database Column Remarks:
     *   平台酒店ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.plat_id
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private String platId;

    /**
     * Database Column Remarks:
     *   原因
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.reason
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private String reason;

    /**
     * Database Column Remarks:
     *   是否失败：0-否，1-是
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.is_failure
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private Integer isFailure;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.create_time
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   原始请求
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.original_request
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private String originalRequest;

    /**
     * Database Column Remarks:
     *   原始响应
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column hotel_gn_order_detail_statistics.original_response
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private String originalResponse;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table hotel_gn_order_detail_statistics
     *
     * @mbg.generated 2025/06/11 16:16
     */
    private static final long serialVersionUID = 1L;
}
