package com.ltgj.ivw.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ltgj.ivw.domain.SupplierElongCityMapping;

/**
 * 供应商与艺龙城市映射关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface SupplierElongCityMappingMapper 
{
    /**
     * 查询供应商与艺龙城市映射关系
     * 
     * @param id 供应商与艺龙城市映射关系主键
     * @return 供应商与艺龙城市映射关系
     */
    public SupplierElongCityMapping selectSupplierElongCityMappingById(Long id);

    /**
     * 查询供应商与艺龙城市映射关系列表
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 供应商与艺龙城市映射关系集合
     */
    public List<SupplierElongCityMapping> selectSupplierElongCityMappingList(SupplierElongCityMapping supplierElongCityMapping);

    /**
     * 根据供应商编码和供应商城市编码查询映射关系
     * 
     * @param supplierCode 供应商编码
     * @param supplierCityCode 供应商城市编码
     * @return 供应商与艺龙城市映射关系
     */
    public SupplierElongCityMapping selectBySupplierAndCityCode(@Param("supplierCode") String supplierCode, @Param("supplierCityCode") String supplierCityCode);

    /**
     * 根据供应商编码和艺龙城市编码查询映射关系
     * 
     * @param supplierCode 供应商编码
     * @param elongCityCode 艺龙城市编码
     * @return 供应商与艺龙城市映射关系列表
     */
    public List<SupplierElongCityMapping> selectBySupplierAndElongCityCode(@Param("supplierCode") String supplierCode, @Param("elongCityCode") String elongCityCode);

    /**
     * 新增供应商与艺龙城市映射关系
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 结果
     */
    public int insertSupplierElongCityMapping(SupplierElongCityMapping supplierElongCityMapping);

    /**
     * 修改供应商与艺龙城市映射关系
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 结果
     */
    public int updateSupplierElongCityMapping(SupplierElongCityMapping supplierElongCityMapping);

    /**
     * 批量更新供应商与艺龙城市映射关系
     * 
     * @param list 供应商与艺龙城市映射关系列表
     * @return 结果
     */
    public int batchUpdateSupplierElongCityMapping(List<SupplierElongCityMapping> list);

    /**
     * 批量插入供应商与艺龙城市映射关系
     * 
     * @param list 供应商与艺龙城市映射关系列表
     * @return 结果
     */
    public int batchInsertSupplierElongCityMapping(List<SupplierElongCityMapping> list);

    /**
     * 逻辑删除供应商与艺龙城市映射关系
     * 
     * @param id 供应商与艺龙城市映射关系主键
     * @return 结果
     */
    public int deleteSupplierElongCityMappingById(Long id);

    /**
     * 批量逻辑删除供应商与艺龙城市映射关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSupplierElongCityMappingByIds(Long[] ids);

    /**
     * 根据供应商编码和供应商城市编码、艺龙城市编码查询是否存在
     * 
     * @param supplierElongCityMapping 供应商与艺龙城市映射关系
     * @return 供应商与艺龙城市映射关系
     */
    public SupplierElongCityMapping checkExistMapping(SupplierElongCityMapping supplierElongCityMapping);
} 