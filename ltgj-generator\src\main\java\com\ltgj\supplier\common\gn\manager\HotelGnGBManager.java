package com.ltgj.supplier.common.gn.manager;

import com.alibaba.fastjson2.JSON;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.es.domain.Result;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.supplier.common.gn.domain.HotelGnGB;
import com.ltgj.supplier.common.gn.domain.HotelGnGBMapping;
import com.ltgj.supplier.common.gn.enums.HotelGnGBTypeEnum;
import com.ltgj.supplier.common.gn.service.HotelGnGBMappingService;
import com.ltgj.supplier.common.gn.service.HotelGnGBService;
import com.ltgj.supplier.common.gn.vo.req.AddAndSaveMappingGBReq;
import com.ltgj.supplier.common.gn.vo.req.GetBrandByGroupIdReq;
import com.ltgj.supplier.common.gn.vo.req.QueryBrandListReq;
import com.ltgj.supplier.common.gn.vo.resp.HotelGnGBResp;
import com.ltgj.supplier.common.gn.vo.resp.MappingGBResp;
import com.ltgj.supplier.common.gn.vo.resp.GetAllGBResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HotelGnGBManager {

    @Autowired
    private HotelGnGBService hotelGnGBService;
    @Autowired
    private HotelGnGBMappingService hotelGnGBMappingService;

    public Result<TableDataInfo> getAllGroup(QueryBrandListReq req) {
        log.info("查询集团品牌信息:{}", JSON.toJSONString(req));
        TableDataInfo tableDataInfo = new TableDataInfo();
        int total = hotelGnGBService.countByPlatformIdAndTypeAndParam(PlatEnum.PLAT_BASE, HotelGnGBTypeEnum.GROUP, req.getGbId(), req.getGbName());
        tableDataInfo.setTotal(total);
        List<HotelGnGB> list = null;
        if (total > 0) {
            list = hotelGnGBService.getGroupByPlatformIdAndTypeAndParam(PlatEnum.PLAT_BASE, HotelGnGBTypeEnum.GROUP,
                    req.getGbId(), req.getGbName(), (req.getPageNum() -1) * req.getPageSize(), req.getPageSize());

        }
        if (CollectionUtils.isEmpty(list)) {
            log.info("没有基础集团数据");
            return Result.ok(tableDataInfo);
        }
        List<HotelGnGBResp> respList = list.stream().map(gb -> {
            HotelGnGBResp resp = new HotelGnGBResp();
            resp.setGbId(gb.getGbId());
            resp.setGbName(gb.getGbName());
            resp.setGbNameEn(gb.getGbNameEn());
            return resp;
        }).collect(Collectors.toList());
        tableDataInfo.setRows(respList);
        return Result.ok(tableDataInfo);
    }

    public Result<List<HotelGnGBResp>> getAllBrandByGroupId(GetBrandByGroupIdReq req) {
        List<HotelGnGB> list = hotelGnGBService.getByPlatformIdAndTypeAndParentId(PlatEnum.PLAT_BASE.getValue(), HotelGnGBTypeEnum.BRAND.getType(), req.getGroupId());
        if (CollectionUtils.isEmpty(list)) {
            log.info("没有基础品牌数据,:{}", req.getGroupId());
            return Result.ok();
        }
        List<HotelGnGBResp> respList = list.stream().map(gb -> {
            HotelGnGBResp resp = new HotelGnGBResp();
            resp.setGbId(gb.getGbId());
            resp.setGbName(gb.getGbName());
            resp.setGbNameEn(gb.getGbNameEn());
            return resp;
        }).collect(Collectors.toList());
        return Result.ok(respList);
    }
    
    public Result getAllGBInfo() {
        List<HotelGnGB> list = hotelGnGBService.getAllByPlatformId(PlatEnum.PLAT_BASE.getValue());
        if (CollectionUtils.isEmpty(list)) {
            log.info("没有基础集团品牌数据");
            return Result.ok();
        }
        List<HotelGnGB> groupList = list.stream().filter(gb -> HotelGnGBTypeEnum.GROUP.getType().equals(gb.getType())).collect(Collectors.toList());
        List<HotelGnGB> brandList = list.stream().filter(gb -> HotelGnGBTypeEnum.BRAND.getType().equals(gb.getType())).collect(Collectors.toList());
        Map<String, List<HotelGnGB>> groupMap = brandList.stream().collect(Collectors.groupingBy(HotelGnGB::getParentGbId));
        List<GetAllGBResp> respList = new ArrayList<>(groupList.size());
        groupList.stream().forEach(group -> {
            try {
                GetAllGBResp resp = new GetAllGBResp();
                resp.setGroupId(group.getGbId());
                resp.setGroupName(group.getGbName());
                resp.setGroupNameEn(group.getGbNameEn());
                List<HotelGnGB> groupBrandList = groupMap.get(group.getGbId());
                if (!CollectionUtils.isEmpty(groupBrandList)) {
                    List<GetAllGBResp.BrandResp> brandRespList = new ArrayList<>(groupBrandList.size());
                    groupBrandList.stream().forEach(brand -> {
                        GetAllGBResp.BrandResp brandResp = new GetAllGBResp.BrandResp();
                        brandResp.setBrandId(brand.getGbId());
                        brandResp.setBrandName(brand.getGbName());
                        brandResp.setBrandNameEn(brand.getGbNameEn());
                        brandRespList.add(brandResp);
                    });
                    resp.setBrandList(brandRespList);
                }
                respList.add(resp);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("获取集团品牌异常:{}, error:{}", JSON.toJSONString(group), e.getStackTrace());
            }
        });
        return Result.ok(respList);
    }

    public Result<MappingGBResp> saveBaseGBInfo(AddAndSaveMappingGBReq req) {
        MappingGBResp resp = null;
        try {
            log.info("保存集团品牌：{}", JSON.toJSONString(req));
            Result<HotelGnGBMapping> saveGroupMapping = null;
            if (StringUtils.isNotBlank(req.getGroupId()) && StringUtils.isNotBlank(req.getGroupName())) {
                saveGroupMapping = addAndSaveMapping(req, HotelGnGBTypeEnum.GROUP, null);
                if (!saveGroupMapping.isSuccess()) {
                    log.error("保存集团品牌映射失败, message:{}", JSON.toJSONString(saveGroupMapping));
                    return Result.fail(saveGroupMapping.getMsg());
                }
            }
            Result<HotelGnGBMapping> saveBrandMapping = null;
            if (StringUtils.isNotBlank(req.getBrandId()) && StringUtils.isNotBlank(req.getBrandName()) && saveGroupMapping != null) {
                saveBrandMapping = addAndSaveMapping(req, HotelGnGBTypeEnum.BRAND, saveGroupMapping.getData().getMappingGbId());
                if (!saveBrandMapping.isSuccess()) {
                    log.error("保存品牌映射失败, message:{}", JSON.toJSONString(saveBrandMapping));
                    return Result.fail(saveBrandMapping.getMsg());
                }
            }

            resp = new MappingGBResp();
            if (saveGroupMapping != null) {
                resp.setGroupId(saveGroupMapping.getData().getMappingGbId());
                resp.setGroupName(saveGroupMapping.getData().getMappingGbName());
            }
            if (saveBrandMapping != null) {
                resp.setBrandId(saveBrandMapping.getData().getMappingGbId());
                resp.setBrandName(saveBrandMapping.getData().getMappingGbName());
            }
        } catch (Exception e) {
            log.info("保存集团品牌异常， req：{}, error:{}", JSON.toJSONString(req), e);
        }
        return Result.ok(resp);
    }
    
    /**
     * 新增平台|基础集团品牌 并且 完成映射
     * @param
     * @return
     */
    private Result<HotelGnGBMapping> addAndSaveMapping(AddAndSaveMappingGBReq req, HotelGnGBTypeEnum gnGBTypeEnum, String baseParentGbId) {
        String gbId = null;
        String gbName = null;
        String gbNameEn = null;
        PlatEnum platform = req.getPlatform();
        String type = gnGBTypeEnum.getType();
        if (HotelGnGBTypeEnum.GROUP.getType().equals(type)) {
            gbId = req.getGroupId();
            gbName = req.getGroupName();
            gbNameEn = req.getGroupNameEn();
        } else if (HotelGnGBTypeEnum.BRAND.getType().equals(type)) {
            gbId =  req.getBrandId();
            gbName = req.getBrandName();
            gbNameEn = req.getBrandNameEn();
        }

        // 是否已有映射
        try {
            log.info("保存集团品牌映射，req:{}", platform.getValue(), gbId, gbName, type);
            HotelGnGBMapping mapping = hotelGnGBMappingService.getByPlatformIdAndTypeAndGBId(platform.getValue(), type, gbId);
            if (mapping != null) {
                return Result.ok(mapping);
            }
            List<HotelGnGB> addList = new ArrayList<>();
            // 平台数据是否有该集团品牌
            HotelGnGB platformGB = hotelGnGBService.getByPlatformIdAndTypeAndGBId(platform.getValue(), type, gbId);
            if (platformGB == null) {// 不存在，新增
                platformGB = new HotelGnGB();
                platformGB.setPlatformId(platform.getValue());
                platformGB.setType(type);
                platformGB.setGbId(gbId);
                platformGB.setGbName(gbName);
                platformGB.setGbNameEn(gbNameEn);
                platformGB.setMappingStatus(0);
                if (HotelGnGBTypeEnum.BRAND.getType().equals(type)) {
                    platformGB.setParentGbId(req.getGroupId());
                }
                addList.add(platformGB);
            }

            // 基础数据是否有该集团品牌
            HotelGnGB mappingGB = hotelGnGBService.getByPlatformIdAndTypeAndGBName(PlatEnum.PLAT_BASE.getValue(), type, gbName);
            if (mappingGB == null) {
                String mappingGBId = hotelGnGBService.getMaxGbId(PlatEnum.PLAT_BASE.getValue(), type);
                mappingGB = new HotelGnGB();
                mappingGB.setPlatformId(PlatEnum.PLAT_BASE.getValue());
                mappingGB.setType(type);
                mappingGB.setGbId(mappingGBId);
                mappingGB.setGbName(gbName);
                mappingGB.setMappingStatus(1);
                if (HotelGnGBTypeEnum.BRAND.getType().equals(type)) {
                    mappingGB.setParentGbId(baseParentGbId);
                }
                mappingGB.setRemark("数据来源:" + platform.getValue() + ",gbId=" + gbId);
                addList.add(mappingGB);
            }

            HotelGnGBMapping mappingGBMapping = new HotelGnGBMapping();
            mappingGBMapping.setPlatformId(platform.getValue());
            mappingGBMapping.setPlatformGbId(platformGB.getGbId());
            mappingGBMapping.setPlatformGbName(platformGB.getGbName());
            mappingGBMapping.setMappingPlatfromId(PlatEnum.PLAT_BASE.getValue());
            mappingGBMapping.setMappingGbId(mappingGB.getGbId());
            mappingGBMapping.setMappingGbName(mappingGB.getGbName());
            mappingGBMapping.setType(platformGB.getType());
            saveAndMapping(mappingGBMapping, platformGB, addList);
            return Result.ok(mappingGBMapping);
        } catch (Exception e) {
            log.info("保存集团品牌映射异常，平台:{}, gbId:{}, gbName:{}, type:{}, error:{}", platform.getValue(), gbId, gbName, type, e.getStackTrace());
            e.printStackTrace();
        }
        return Result.fail("保存失败");
    }

    @Transactional
    public void saveAndMapping(HotelGnGBMapping mappingGBMapping, HotelGnGB hotelGnGB, List<HotelGnGB> addList) {
        if (!CollectionUtils.isEmpty(addList)) {
            addList.stream().forEach(gb ->{
                // 非基础数据，映射状态为1
                if (!PlatEnum.PLAT_BASE.getValue().equals(gb.getPlatformId())) {
                    gb.setMappingStatus(1);
                }
            });
            hotelGnGBService.addBatch(addList);
        }
        hotelGnGBMappingService.add(mappingGBMapping);
    }

}
