package com.ltgj.ivw.service.idempotent;

import lombok.Data;
import lombok.Builder;
import java.util.List;
import java.util.ArrayList;

/**
 * 幂等性处理结果
 * 
 * @param <T> 实体类型
 */
@Data
@Builder
public class IdempotentResult<T> {
    
    /**
     * 总处理数量
     */
    private int totalCount;
    
    /**
     * 新增数量
     */
    private int insertCount;
    
    /**
     * 更新数量
     */
    private int updateCount;
    
    /**
     * 跳过数量（无变化）
     */
    private int skipCount;
    
    /**
     * 错误数量
     */
    private int errorCount;
    
    /**
     * 处理耗时（毫秒）
     */
    private long processingTimeMs;
    
    /**
     * 新增的数据列表
     */
    @Builder.Default
    private List<T> insertedData = new ArrayList<>();
    
    /**
     * 更新的数据列表
     */
    @Builder.Default
    private List<T> updatedData = new ArrayList<>();
    
    /**
     * 跳过的数据列表
     */
    @Builder.Default
    private List<T> skippedData = new ArrayList<>();
    
    /**
     * 错误信息列表
     */
    @Builder.Default
    private List<String> errorMessages = new ArrayList<>();
    
    /**
     * 获取处理成功率
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) (insertCount + updateCount + skipCount) / totalCount * 100;
    }
    
    /**
     * 获取处理摘要信息
     */
    public String getSummary() {
        return String.format(
            "处理完成 - 总数:%d, 新增:%d, 更新:%d, 跳过:%d, 错误:%d, 成功率:%.2f%%, 耗时:%dms",
            totalCount, insertCount, updateCount, skipCount, errorCount, getSuccessRate(), processingTimeMs
        );
    }
} 