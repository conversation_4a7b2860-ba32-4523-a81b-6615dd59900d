package com.ltgj.supplier.common.gn.enums;

public enum HotelGnGBTypeEnum {
    GROUP("group", "集团"),
    BRAND("brand", "品牌"),
    ;

    private String type;
    private String name;

    HotelGnGBTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }


    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static HotelGnGBTypeEnum getByType(String type) {
        for (HotelGnGBTypeEnum hotelGnGBTypeEnum : HotelGnGBTypeEnum.values()) {
            if (hotelGnGBTypeEnum.getType().equals(type)) {
                return hotelGnGBTypeEnum;
            }
        }
        return null;
    }
}
