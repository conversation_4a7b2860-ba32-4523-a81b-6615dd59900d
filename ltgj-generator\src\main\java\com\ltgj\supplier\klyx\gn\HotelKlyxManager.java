package com.ltgj.supplier.klyx.gn;

import com.alibaba.fastjson2.JSON;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.*;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.*;
import com.ltgj.supplier.common.AbstractHotelService;
import com.ltgj.supplier.common.aspect.SupplierTask;
import com.ltgj.supplier.common.domain.PolicyInfo;
import com.ltgj.supplier.common.dto.HotelBaseRequest;
import com.ltgj.supplier.common.dto.HotelResult;
import com.ltgj.supplier.common.enums.HotelTaskOpTypeEnum;
import com.ltgj.supplier.common.enums.HotelTypeEnum;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.gn.domain.HotelGnIdMapping;
import com.ltgj.supplier.common.gn.domain.HotelGnKlyx;
import com.ltgj.supplier.common.gn.enums.HotelGnTypeEnum;
import com.ltgj.supplier.common.gn.service.HotelGnBaseService;
import com.ltgj.supplier.common.gn.service.HotelGnIdMappingService;
import com.ltgj.supplier.common.utils.CheckInOutUtil;
import com.ltgj.supplier.klyx.gn.req.KlyxGetHotelIdListReq;
import com.ltgj.supplier.klyx.gn.req.KlyxGetHotelInfoReq;
import com.ltgj.supplier.klyx.gn.req.KlyxGetHotelLowestPriceReq;
import com.ltgj.supplier.klyx.gn.resp.KlyxGetHotelIdListResp;
import com.ltgj.supplier.klyx.gn.resp.KlyxGetHotelInfoResp;
import com.ltgj.supplier.klyx.gn.resp.KlyxGetHotelLowestPriceResp;
import com.ltgj.supplier.klyx.gn.resp.KlyxPushHotelIdInfoResp;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HotelKlyxManager extends AbstractHotelService {

    @Autowired
    private HotelInfoService hotelInfoService;
    @Autowired
    private IJdJdbService jdJdbService;
    @Autowired
    private ZhJdJdbMappingService zhJdJdbMappingService;
    @Autowired
    private IZhJdJdbMinPriceService zhJdJdbMinPriceService;
    @Autowired
    private DataSourceTransactionManager dataSourceTransactionManager;
    @Autowired
    private TransactionDefinition transactionDefinition;
    @Autowired
    private HotelKlyxManager hotelKlyxManager;
    @Autowired
    private HotelGnBaseService hotelGnBaseService;
    @Autowired
    private IHotelCityService hotelCityService;
    @Autowired
    private HotelGnIdMappingService  hotelGnIdMappingService;

    private static Executor tp = new ThreadPoolExecutor(20, 50,
            30L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>());
    public static Executor klyxThreadPool = TtlExecutors.getTtlExecutor(tp);

    private static int apiBatchSize = 10;


    /**
     * 初始化酒店数据
     * @throws Exception
     */
    @SupplierTask(opType = HotelTaskOpTypeEnum.INIT_HOTEL, hotelType = HotelTypeEnum.GN)
    public HotelResult initHotelDate(HotelBaseRequest request) throws Exception {
        List<Long> apiIdList = getAPIHotelIdList();
        List<Long> dbIdList = getDBHotelIdList();
        List<Long> sameIdList = new ArrayList<>(dbIdList);// 更新的酒店id
        sameIdList.retainAll(apiIdList);
        if (!sameIdList.isEmpty()) {
            apiIdList.removeAll(sameIdList);
        }
        // 新增酒店
        hotelKlyxManager.batchAddHotelInfo(request, apiIdList);
        // 更新酒店
        hotelKlyxManager.batchUpdateHotelInfo(request, sameIdList);
        return HotelResult.createBySuccess();
    }

    /**
     * 批量更新最低价
     */
    @SupplierTask(opType = HotelTaskOpTypeEnum.UPDATE_MIN_PRICE, hotelType = HotelTypeEnum.GN)
    public void batchUpdateLowestPrice(HotelBaseRequest request) {
        log.info("康旅严选更新最低价开始");
        long start = System.currentTimeMillis();
        List<Long> idList = getDBHotelIdList();
        List<Long> ids = new ArrayList<>(apiBatchSize);
        List<CompletableFuture<List<HotelInfoKlyx>>> taskList = new ArrayList<>(10);
        for (int i = 0; i < idList.size(); i++) {
            ids.add(idList.get(i));
            if (i == (idList.size() -1) || ids.size() % apiBatchSize == 0) {
                final List<Long> finalIdList = new ArrayList<>(ids.size());
                finalIdList.addAll(ids);
                CompletableFuture<List<HotelInfoKlyx>> hotelFuture = CompletableFuture.supplyAsync(new SupplierMDC(() -> {
                    try {
                        updateMinPrice(finalIdList);
                    } catch (Exception e) {
                        log.error("获取酒店信息失败", e);
                    }
                    return null;
                }), klyxThreadPool);
                taskList.add(hotelFuture);
                ids.clear();
            }
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(taskList.toArray(new CompletableFuture[taskList.size()]));
        try {
            allOf.get();
        } catch (Exception e) {
            e.getStackTrace();
            log.error("康旅严选更新最低价完成,error:{}", e.getStackTrace());
        }

        long end = System.currentTimeMillis();
        log.info("康旅严选更新最低价完成,耗时:{}ms", end - start);
    }

    /**
     * 单个更新康旅严选酒店最低价
     * @param klyxHotelId
     */
    public void updateMinPrice(String klyxHotelId) {
        updateMinPrice(Arrays.asList(Long.valueOf(klyxHotelId)));
    }

    public void handlerHotelInfoCallback(String body) {
        try {
            log.info("handlerHotelInfoCallback:{}", body);
            List<KlyxPushHotelIdInfoResp> resp = JSON.parseArray(body, KlyxPushHotelIdInfoResp.class);
            List<Long> addIdList = resp.stream().filter(r -> r.getType().equals(1)).map(r -> r.getHotelId()).collect(Collectors.toList());
            List<Long> updateIdList = resp.stream().filter(r -> r.getType().equals(2)).map(r -> r.getHotelId()).collect(Collectors.toList());
            CompletableFuture<List<HotelInfoKlyx>> hotelFuture = CompletableFuture.supplyAsync(new SupplierMDC(() -> {
                try {
                    getAndSaveHotelInfo(addIdList, true);
                    getAndSaveHotelInfo(updateIdList, false);
                } catch (Exception e) {
                    e.getStackTrace();
                    log.error("处理康旅严选酒店回调异常：{}", e);
                }
                return null;
            }), klyxThreadPool);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理康旅严选酒店回调异常：{}", e);
        }
    }

    /**
     * api获取可售酒店id列表
     * @return
     */
    private List<Long> getAPIHotelIdList() {
        int pageNum = 1;
        int pageSize = apiBatchSize;
        boolean flag = true;
        List<Long> hotelIdList = new ArrayList<>();
        KlyxGetHotelIdListReq req = new KlyxGetHotelIdListReq();
        while (flag) {
            try {
                req.setPageNum(pageNum);
                req.setPageSize(pageSize);
                KlyxGetHotelIdListResp resp = KlyxApi.getHotelIdList(req);
                if (resp == null) {
                    return hotelIdList;
                }
                hotelIdList.addAll(resp.getHotelIdList());
                pageNum++;
            } catch (Exception e) {
                flag = false;
            }

        }
        return hotelIdList;
    }

    @SupplierTask(opType = HotelTaskOpTypeEnum.SUB, hotelType = HotelTypeEnum.GN, subOpType = "addHotelInfo", order = 1)
    public HotelResult batchAddHotelInfo(HotelBaseRequest request, List<Long> hotelIdList) {
        log.info("批量新增酒店数量:{}", hotelIdList.size());
        return getAndSaveHotelInfo(hotelIdList,true);
    }

    @SupplierTask(opType = HotelTaskOpTypeEnum.SUB, hotelType = HotelTypeEnum.GN, subOpType = "updateHotelInfo", order = 2)
    public HotelResult batchUpdateHotelInfo(HotelBaseRequest request, List<Long> hotelIdList) {
        log.info("批量更新康旅严选酒店数量:{}", hotelIdList.size());
        return getAndSaveHotelInfo(hotelIdList,false);
    }

    public HotelResult getAndSaveHotelInfo(List<Long> hotelIdList, boolean isAdd) {
        if (hotelIdList == null || hotelIdList.isEmpty()) {
            log.error("getAndSaveHotelInfo hotelIdList is empty");
            return HotelResult.createBySuccess();
        }
        // 获取酒店信息
        List<CompletableFuture<List<HotelInfoKlyx>>> taskList = new ArrayList<>(10);
        List<Long> ids = new ArrayList<>(apiBatchSize);
        for (int i = 0; i < hotelIdList.size(); i++) {
            ids.add(hotelIdList.get(i));
            if (i == (hotelIdList.size() -1) || ids.size() % apiBatchSize == 0) {
                final List<Long> finalIdList = new ArrayList<>(ids.size());
                finalIdList.addAll(ids);
                CompletableFuture<List<HotelInfoKlyx>> hotelFuture = CompletableFuture.supplyAsync(new SupplierMDC(() -> {
                    try {
                        hotelKlyxManager.queryApiAndSaveDBHotelInfo(finalIdList, isAdd);
                    } catch (Exception e) {
                        log.error("获取酒店信息失败", e);
                    }
                    return null;
                }), klyxThreadPool);
                taskList.add(hotelFuture);
                ids.clear();
            }
        }
        CompletableFuture<Void> allOf = CompletableFuture.allOf(taskList.toArray(new CompletableFuture[taskList.size()]));
        try {
            allOf.get();
        } catch (Exception e) {
            e.getStackTrace();
            log.error("{}康旅严选酒店完成,error:{}", isAdd?"新增":"更新", e.getStackTrace());
        }
        log.info("{}康旅严选酒店完成", isAdd?"新增":"更新");
        return HotelResult.createBySuccess();
    }

    public int queryApiAndSaveDBHotelInfo(List<Long> hotelIdList, boolean isAdd) {
        if (CollectionUtils.isEmpty(hotelIdList)) {
            return 0;
        }
        try {
            KlyxGetHotelInfoReq req = new KlyxGetHotelInfoReq();
            req.setHotelIds(hotelIdList);
            log.info("请求参数:{}", req);
            List<KlyxGetHotelInfoResp> respList = KlyxApi.getHotelInfo(req);
            if (CollectionUtils.isEmpty(respList)) {
                log.error("批量查询酒店信息为空,idList:{}", hotelIdList);
                return 0;
            }
            List<HotelInfo> klyxList = new ArrayList<>(respList.size());
            List<ZhJdJdbMapping> mapList = new ArrayList<>(respList.size());
            for (KlyxGetHotelInfoResp resp : respList) {
                saveHotelInfo(resp, isAdd);
            }
            return respList.size();
        } catch (Exception e) {
            log.error("获取酒店信息失败:{}", e.getStackTrace());
        }
        return 0;
    }


    /**
     * 获取数据库所有酒店Id
     * @return
     */
    private List<Long> getDBHotelIdList() {
        List<Long> hotelIdList = new ArrayList<>();
        List<HotelGnBase> hotelList = hotelGnBaseService.getAllIdList(PlatEnum.PLAT_KLYX);
        if (hotelList == null || hotelList.isEmpty()) {
            return hotelIdList;
        }
        List<String> idList = hotelList.stream().map(HotelGnBase::getId).collect(Collectors.toList());
        hotelIdList.addAll(idList.stream().map(Long::valueOf).collect(Collectors.toList()));
        return hotelIdList;
    }

    /**
     * 保存酒店
     * @param resp
     * @param isAdd
     */
    private void saveHotelInfo(KlyxGetHotelInfoResp resp, boolean isAdd) {
        try {
            HotelGnKlyx hotelInfoKlyx = new HotelGnKlyx();
            hotelInfoKlyx.setId(String.valueOf(resp.getJdid()));
            hotelInfoKlyx.setName(resp.getJdmc());
            hotelInfoKlyx.setNameEn(resp.getJden());
            hotelInfoKlyx.setTypeId(HotelGnTypeEnum.getKlyxDefaultType().getId());
            hotelInfoKlyx.setOpenDate(getYYMMDD(resp.getEstablishmentDate()));
            hotelInfoKlyx.setDecorationDate(getYYMMDD(resp.getRenovationDate()));
            hotelInfoKlyx.setPhone(resp.getPhone());
            hotelInfoKlyx.setCreateTime(new Date());
            hotelInfoKlyx.setUpdateTime(new Date());
            HotelCity city = hotelCityService.getByCityIdAndPlatIdForCache(PlatEnum.PLAT_KLYX, resp.getCityId());
            if (city != null) {
                hotelInfoKlyx.setCountryId(city.getCountryId());
                hotelInfoKlyx.setCountryName(city.getCountryName());
                hotelInfoKlyx.setProvinceId(city.getProvinceId());
                hotelInfoKlyx.setProvinceName(city.getProvinceName());
                hotelInfoKlyx.setCityId(city.getCityId());
                hotelInfoKlyx.setCityName(city.getCityName());
            } else {
                log.info("hotel_city中城市数据为空：{}", resp.getCityId());
                hotelInfoKlyx.setCityId(resp.getCityId());
                hotelInfoKlyx.setCityName(resp.getCityName());
            }
            hotelInfoKlyx.setAreaId(resp.getDistrict());
            hotelInfoKlyx.setAreaName(resp.getDistrictname());
            hotelInfoKlyx.setAddress(resp.getAddress());
            hotelInfoKlyx.setLonBaidu(resp.getBaiduLon());
            hotelInfoKlyx.setLatBaidu(resp.getBaiduLat());

            hotelInfoKlyx.setStar(resp.getStartRate() != null ? resp.getStartRate().toString() : "");
            hotelInfoKlyx.setImage(resp.getImg());
            hotelInfoKlyx.setScore(resp.getScore());
            hotelInfoKlyx.setSynopsis(resp.getIntroEditor());

            //设施
            hotelInfoKlyx.setFacilitiesInfo("{}");
            hotelInfoKlyx.setPolicyInfo("{}");

            PolicyInfo policyInfo = new PolicyInfo();

            try {
                PolicyInfo.CheckInOutPolicy checkInOutPolicy = new PolicyInfo.CheckInOutPolicy();
                String[] times = CheckInOutUtil.getCheckInAndOutTime(resp.getCheckInOutDesc());
                if (times != null) {
                    checkInOutPolicy.setCheckIn(times[0]);
                    if (times.length == 2) {
                        checkInOutPolicy.setCheckOut(times[1]);
                    }
                }
                policyInfo.setCheckInOutPolicy(checkInOutPolicy);
            } catch (Exception e) {
                log.error("康旅严选-处理 inOutDesc 异常:{}, error:{}", resp.getCheckInOutDesc(), e);
            }
            hotelInfoKlyx.setPolicyInfo(JSON.toJSONString(policyInfo));

            hotelInfoKlyx.setStatus(1);
            hotelInfoKlyx.setIsDelete(0);
            hotelInfoKlyx.setReserve0(JSON.toJSONString(resp));
            String localHotelId = resp.getCollectHotelId();
            hotelInfoKlyx.setReserve1(localHotelId);// 推荐分

            HotelGnIdMapping idMapping = null;
            if (StringUtils.isNotBlank(localHotelId)) {
                idMapping = new HotelGnIdMapping();
                idMapping.setPlatformId(PlatEnum.PLAT_KLYX.getValue());
                idMapping.setPlatformHotelId(hotelInfoKlyx.getId());
                idMapping.setMappingPlatformId("-1");
                idMapping.setMappingHotelId(localHotelId);
            }


            ZhJdJdbMapping mapping = null;
            if (isAdd){// 新增
                hotelInfoKlyx.setCreateTime(new Date());
                //hotelInfoKlyx.setRecommendationLevel(100);
            } else {
                HotelGnBase oldHotelInfo = hotelGnBaseService.getById(PlatEnum.PLAT_KLYX, String.valueOf(resp.getJdid()));
                if (oldHotelInfo != null) {
                    hotelInfoKlyx.setStatus(oldHotelInfo.getStatus());
                    // 编辑，返回的供应商id和数据库不匹配 或者目前是下架状态
                    if ((oldHotelInfo != null && StringUtils.isNotBlank(oldHotelInfo.getReserve1()) && !oldHotelInfo.getReserve1().equalsIgnoreCase(localHotelId))) {
                        List<ZhJdJdbMapping> dbMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(String.valueOf(resp.getJdid()), Long.valueOf(PlatEnum.PLAT_KLYX.getValue()));
                        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(dbMappings)){
                            zhJdJdbMappingService.deleteById(dbMappings.get(0).getId());
                            log.info("本地酒店id变更删除映射：{}", JSON.toJSONString(dbMappings.get(0)));
                            hotelInfoKlyx.setStatus(1);
                        }
                    }
                }
            }

            //查映射
            List<ZhJdJdbMapping> dbMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlat(String.valueOf(resp.getJdid()), Long.valueOf(PlatEnum.PLAT_KLYX.getValue()));

            if (resp.getStatus() != null && resp.getStatus().intValue() == 0) {// 下架，删除映射（严选标记)
                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(dbMappings)){
                    zhJdJdbMappingService.deleteById(dbMappings.get(0).getId());
                    log.info("下架删除映射：{}", JSON.toJSONString(dbMappings.get(0)));
                    hotelInfoKlyx.setStatus(1);
                }
            }

            // 使用优化的映射处理方法，替代原来的349-354行逻辑及事务处理
            processHotelMappingWithTransaction(resp, hotelInfoKlyx, dbMappings);

        } catch (Exception e) {
            log.error("康旅酒店保存异常:{}, error:{}", JSON.toJSONString(resp), e.getStackTrace());
            e.getStackTrace();
        }
    }

    /**
     * 映射操作类型枚举
     */
    private enum MappingOperation {
        NONE,           // 无操作
        INSERT,         // 新增映射
        UPDATE,         // 更新映射（删除旧的，插入新的）
        MAINTAIN        // 维持现有映射状态
    }

    /**
     * 映射操作信息
     */
    @Data
    private static class MappingOperationInfo {
        private MappingOperation operation;
        private ZhJdJdbMapping mappingToDelete;
        private ZhJdJdbMapping mappingToInsert;
        private String description;

        public MappingOperationInfo(MappingOperation operation, String description) {
            this.operation = operation;
            this.description = description;
        }
    }

    /**
     * 优化的映射处理方法，集成事务管理
     * 替代第349-354行的逻辑并完善原有的getZhJdJdbMapping逻辑
     */
    private void processHotelMappingWithTransaction(KlyxGetHotelInfoResp resp, HotelGnKlyx hotelInfoKlyx, List<ZhJdJdbMapping> dbMappings) {

        // 分析映射操作类型
        MappingOperationInfo operationInfo = analyzeMappingOperation(resp, hotelInfoKlyx, dbMappings);

        // 使用编程式事务统一处理酒店信息保存和映射操作
        TransactionStatus transactionStatus = dataSourceTransactionManager.getTransaction(transactionDefinition);
        try {
            // 保存酒店信息
            hotelGnBaseService.addOrUpdateBatchNoFunction(PlatEnum.PLAT_KLYX, Arrays.asList(hotelInfoKlyx));

            // 根据操作类型执行相应的映射操作
            switch (operationInfo.getOperation()) {
                case INSERT:
                    if (operationInfo.getMappingToInsert() != null) {
                        zhJdJdbMappingService.insert(operationInfo.getMappingToInsert());
                        log.info("成功新增康旅严选映射：localId={}, platId={}",
                                operationInfo.getMappingToInsert().getLocalId(),
                                operationInfo.getMappingToInsert().getPlatId());
                    }
                    break;

                case UPDATE:
                    // 先删除旧映射
                    if (operationInfo.getMappingToDelete() != null) {
                        zhJdJdbMappingService.deleteById(operationInfo.getMappingToDelete().getId());
                        log.info("成功删除旧映射：ID={}, localId={}",
                                operationInfo.getMappingToDelete().getId(),
                                operationInfo.getMappingToDelete().getLocalId());
                    }
                    // 再插入新映射
                    if (operationInfo.getMappingToInsert() != null) {
                        zhJdJdbMappingService.insert(operationInfo.getMappingToInsert());
                        log.info("成功插入新映射：localId={}, platId={}",
                                operationInfo.getMappingToInsert().getLocalId(),
                                operationInfo.getMappingToInsert().getPlatId());
                    }
                    break;

                case MAINTAIN:
                    log.info("维持现有映射状态：{}", operationInfo.getDescription());
                    break;

                case NONE:
                default:
                    log.info("无需处理映射：{}", operationInfo.getDescription());
                    break;
            }

            // 提交事务
            dataSourceTransactionManager.commit(transactionStatus);
            log.info("康旅严选酒店映射事务处理完成：操作类型={}, 描述={}", operationInfo.getOperation(), operationInfo.getDescription());

        } catch (Exception e) {
            // 回滚事务
            dataSourceTransactionManager.rollback(transactionStatus);
            e.printStackTrace();
            log.error("康旅严选酒店映射事务执行异常, data:{}, error:{}", JSON.toJSONString(resp), e.getMessage());
            throw e; // 重新抛出异常，保持原有的异常处理逻辑
        }
    }

    /**
     * 分析映射操作类型
     */
    private MappingOperationInfo analyzeMappingOperation(KlyxGetHotelInfoResp resp, HotelGnKlyx hotelInfoKlyx, List<ZhJdJdbMapping> dbMappings) {
        String localHotelId = resp.getCollectHotelId();

        // 处理严选供应商的映射逻辑（原第349-354行的逻辑）
        if ("0".equalsIgnoreCase(resp.getSupplierType()) && StringUtils.isNotBlank(localHotelId)) {
            hotelInfoKlyx.setReserve1(localHotelId);

            if (resp.getStatus() != null && resp.getStatus().intValue() == 1) {// 上架，处理映射
                // 整合原getZhJdJdbMapping方法的逻辑
                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(dbMappings)) {
                    String existingLocalId = dbMappings.get(0).getLocalId();
                    if (!StringUtils.equals(resp.getCollectHotelId(), existingLocalId)) {
                        // localId不匹配，需要删除旧映射并构建新映射
                        ZhJdJdbMapping newMapping = buildZhJdJdbMapping(hotelInfoKlyx);
                        if (newMapping != null) {
                            hotelInfoKlyx.setStatus(8);
                            MappingOperationInfo operationInfo = new MappingOperationInfo(MappingOperation.UPDATE,
                                    String.format("localId变更：旧localId=%s, 新localId=%s", existingLocalId, resp.getCollectHotelId()));
                            operationInfo.setMappingToDelete(dbMappings.get(0));
                            operationInfo.setMappingToInsert(newMapping);
                            return operationInfo;
                        }
                    } else {
                        // localId匹配，映射已存在，只需设置状态
                        hotelInfoKlyx.setStatus(8);
                        return new MappingOperationInfo(MappingOperation.MAINTAIN,
                                String.format("映射已存在且匹配，localId=%s", existingLocalId));
                    }
                } else {
                    // 不存在映射，创建新映射
                    ZhJdJdbMapping newMapping = buildZhJdJdbMapping(hotelInfoKlyx);
                    if (newMapping != null) {
                        hotelInfoKlyx.setStatus(8);
                        MappingOperationInfo operationInfo = new MappingOperationInfo(MappingOperation.INSERT,
                                String.format("创建新映射，localId=%s", resp.getCollectHotelId()));
                        operationInfo.setMappingToInsert(newMapping);
                        return operationInfo;
                    }
                }
            }
        }

        // 默认情况：无需处理映射
        return new MappingOperationInfo(MappingOperation.NONE, "非严选供应商或非上架状态，无需处理映射");
    }


    /**
     * 构建映射
     * @param klyx
     * @return
     */
    private ZhJdJdbMapping buildZhJdJdbMapping(HotelGnKlyx klyx) {
        String localId = klyx.getReserve1();
        if (StringUtils.isBlank(localId)) {
            return null;
        }
        JdJdb jdJdb = jdJdbService.selectJdJdbById(localId);
        if (jdJdb == null) {
            log.error("康旅酒店保存异常，未找到jdJdb打底数据，InterfacePlat:{}，酒店id:{}", PlatEnum.PLAT_KLYX.getValue(), localId);
            return null;
        }
        List<ZhJdJdbMapping> dbMappings = zhJdJdbMappingService.findByPlateIdAndInterfacePlatAndLocalId(String.valueOf(klyx.getId()), Long.valueOf(PlatEnum.PLAT_KLYX.getValue()), localId);
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(dbMappings)){
            klyx.setStatus(8);
            log.info("映射已存在，无需新增：{}", JSON.toJSONString(dbMappings.get(0)));
            return null;
        }
        ZhJdJdbMapping map = new ZhJdJdbMapping();
        map.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_KLYX.getValue()));
        map.setLocalId(localId);
        map.setJdName(jdJdb.getJdmc());
        map.setPlatId(klyx.getId());
        map.setIsGnGj(1);
        map.setStatus(0);
        map.setPlatJdName(klyx.getName());
        map.setSaveDate(new Date());
        return map;
    }

    /**
     * 更新最低价
     * @param idList
     */
    public void updateMinPrice(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            log.info("康旅严选，查询最低价id是空:{}", idList);
            return;
        }
        KlyxGetHotelLowestPriceReq req = new KlyxGetHotelLowestPriceReq();
        req.setHotelIdList(idList);
        req.setCheckInDate(LocalDate.now());
        req.setCheckOutDate(LocalDate.now().plusDays(30));
        List<KlyxGetHotelLowestPriceResp> respList = KlyxApi.getHotelLowestPrice(req);
        if (CollectionUtils.isEmpty(respList)) {
            log.info("康旅严选，查询最低价结果是空{}", respList);
            return;
        }
        List<ZhJdJdbMinPrice> zhJdJdbMinPriceListInsert = Lists.newArrayList();
        List<ZhJdJdbMinPrice> zhJdJdbMinPriceListUpdate = Lists.newArrayList();
        respList.stream().forEach(priceItems->{
            // 获取映射
            ZhJdJdbMapping mapping = new ZhJdJdbMapping();
            mapping.setInterfacePlat(Long.valueOf(PlatEnum.PLAT_KLYX.getValue()));
            mapping.setPlatId(String.valueOf(priceItems.getHotelId()));
            List<ZhJdJdbMapping> zhJdJdbMappingList = zhJdJdbMappingService.selectZhJdJdbMappingList(mapping);
            ZhJdJdbMapping jdJdbMapping =  null;
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(zhJdJdbMappingList)) {
                jdJdbMapping = zhJdJdbMappingList.get(0);
            }
            String localId = null;
            if (jdJdbMapping != null) {
                localId = jdJdbMapping.getLocalId();
            } else {
                localId = PlatEnum.PLAT_HSJL.getValue() + priceItems.getHotelId();
                log.info("康旅酒店无映射，localHotelId:{}", localId);
            }
            List<KlyxGetHotelLowestPriceResp.DateLowestPrice> priceItemsInner = priceItems.getDateLowestPriceList();
            for (KlyxGetHotelLowestPriceResp.DateLowestPrice priceItem : priceItemsInner) {
                BigDecimal salePrice = priceItem.getPrice();
                Date date = DateUtils.dateTime(DateUtils.YYYY_MM_DD, priceItem.getDate());
                ZhJdJdbMinPrice zhJdJdbMinPrice = new ZhJdJdbMinPrice();
                zhJdJdbMinPrice.setJdid(localId);
                zhJdJdbMinPrice.setSxsj(date);
                zhJdJdbMinPrice.setInterfacePlat(PlatEnum.PLAT_KLYX.getValue());
                List<ZhJdJdbMinPrice> prices = zhJdJdbMinPriceService.selectZhJdJdbMinPriceList(zhJdJdbMinPrice);
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(prices)) {
                    zhJdJdbMinPrice.setMinPrice(salePrice);
                    zhJdJdbMinPriceListInsert.add(zhJdJdbMinPrice);
                } else {
                    log.info("### 康旅严选最低价查询成功 localId：{} sxsj：{},size:{}", localId, date, prices.size());
                    zhJdJdbMinPrice = prices.get(0);
                    if (zhJdJdbMinPrice.getMinPrice().intValue() - salePrice.intValue() == 0) {
                        //不需要更新最低价
                        log.info("### 康旅严选最低价查询成功不需要更新最低价 sxsj：{},酒店ID：{},最低价：{},拉取最低价：{}", zhJdJdbMinPrice.getSxsj(), zhJdJdbMinPrice.getJdid(), zhJdJdbMinPrice.getMinPrice(), salePrice);
                    } else {
                        zhJdJdbMinPrice.setJdid(localId);
                        zhJdJdbMinPrice.setSxsj(date);
                        zhJdJdbMinPrice.setMinPrice(salePrice);
//                            zhJdJdbMinPriceService.updateZhJdJdbMinPrice(zhJdJdbMinPrice);
                        zhJdJdbMinPriceListUpdate.add(zhJdJdbMinPrice);
                        log.info("### 康旅严选最低价查询成功最低价更新成功 localId：{} sxsj：{}", localId, date);
                    }
                }
            }
        });
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(zhJdJdbMinPriceListInsert)) {
            int zhJdJdbMinPriceResult = zhJdJdbMinPriceService.batchInsertZhJdJdbMinPrice(zhJdJdbMinPriceListInsert);
            if (zhJdJdbMinPriceResult!=0)   log.info("### 康旅严选最低价查询成功最低价插入成功");
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(zhJdJdbMinPriceListUpdate)) {
            int updateZhJdJdbMinPrice = zhJdJdbMinPriceService.batchUpdateZhJdJdbMinPrice(zhJdJdbMinPriceListUpdate);
            if (updateZhJdJdbMinPrice!=0)   log.info("### 康旅严选最低价查询成功最低价修改成功");
        }


    }


    private String getYYMMDD(String date) {
        if (StringUtils.isBlank(date)) {
            return "";
        }
        String[] split = date.split(" ");
        return split[0];
    }
}
