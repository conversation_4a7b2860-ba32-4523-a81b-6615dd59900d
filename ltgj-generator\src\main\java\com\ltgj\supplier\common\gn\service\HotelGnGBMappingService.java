package com.ltgj.supplier.common.gn.service;

import com.ltgj.common.utils.uuid.IdUtils;
import com.ltgj.supplier.common.gn.domain.HotelGnGB;
import com.ltgj.supplier.common.gn.domain.HotelGnGBMapping;
import com.ltgj.supplier.common.gn.mapper.HotelGnGBMapper;
import com.ltgj.supplier.common.gn.mapper.HotelGnGBMappingMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class HotelGnGBMappingService {

    @Autowired
    private HotelGnGBMappingMapper hotelGnGBMappingMapper;

    public int add(HotelGnGBMapping hotelGnGBMapping) {
        if (hotelGnGBMapping == null) {
            return 0;
        }
        hotelGnGBMapping.setId(IdUtils.fastSimpleUUID());
        hotelGnGBMapping.setCreateTime(new Date());
        return hotelGnGBMappingMapper.add(hotelGnGBMapping);
    }

    public HotelGnGBMapping getByPlatformIdAndTypeAndGBId(String platformId, String type, String gbId) {
        return hotelGnGBMappingMapper.selectByPlatformIdAndTypeAndGBId(platformId, type, gbId);
    }

    public List<HotelGnGBMapping> getByPlatformIdAndType(String platformId, String type) {
        return hotelGnGBMappingMapper.selectByPlatformIdAndType(platformId, type);
    }
}
