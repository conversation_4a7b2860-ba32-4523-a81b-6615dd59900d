package com.ltgj.web.job.handler;

import com.ltgj.ivw.controller.HotelGnKetanController;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 科坦拉取全量酒店信息任务处理器
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@Component
@JobHandler(value = "ketanAllHotelGnHandler")
public class KetanAllHotelGnHandler extends IJobHandler {
    @Autowired
    private HotelGnKetanController hotelGnKetanController;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("开始执行科坦拉取全量酒店信息时任务，参数：{}", param);

        try {
            this.hotelGnKetanController.updateDataNew();
            log.info("科坦拉取全量酒店信息时任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("科坦拉取全量酒店信息时任务执行异常: {}", e.getMessage(), e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "执行异常：" + e.getMessage());
        }
    }


} 