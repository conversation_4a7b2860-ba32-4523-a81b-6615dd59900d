package com.ltgj.ivw.service.impl;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.service.BaseHotelService;
import com.ltgj.ivw.service.IHotelGnBaseService;
import com.ltgj.ivw.utils.ReflectForServiceUtils;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.SecurityUtils;
import com.ltgj.common.utils.spring.SpringUtils;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.service.HotelGnPreMappingService;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.supplier.common.gn.domain.HotelGnBase;
import com.ltgj.supplier.common.utils.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.ParameterizedType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 基础酒店服务抽象实现
 * 提供通用的业务逻辑和注解驱动的元数据管理
 * 使用模板方法模式，数据访问由子类具体实现
 *
 * @param <T> 实体类型
 * @param <S> 查询条件类型
 */
public abstract class BaseHotelServiceImpl<T, S> implements BaseHotelService<T, S> {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Autowired
    protected HotelGnPreMappingService hotelGnPreMappingService;
    
    protected IdUtil sequence = new IdUtil();

    /**
     * 获取实体类的Class对象
     */
    protected abstract Class<S> getEntityClass();
    
    /**
     * 获取平台枚举值
     * 子类必须实现此方法返回对应的PlatEnum
     */
    protected abstract PlatEnum getPlatEnum();
    
    /**
     * 获取平台名称
     * 默认从PlatEnum获取，子类可以重写自定义
     */
    protected String getPlatformName() {
        PlatEnum platEnum = getPlatEnum();
        return platEnum != null ? platEnum.getName() : "未知平台";
    }

    // ==================== 抽象数据访问方法（由子类实现具体的Mapper调用） ====================

    /**
     * 执行参数化查询（子类实现具体的Mapper调用）
     */
    protected abstract List<T> doSelectListWithParams(T entity, Map<String, Object> queryParams);

    /**
     * 根据ID查询（子类实现具体的Mapper调用）
     */
    protected abstract T doSelectById(String id);

    /**
     * 执行插入操作（子类实现具体的Mapper调用）
     */
    protected abstract int doInsert(T entity);

    /**
     * 执行更新操作（子类实现具体的Mapper调用）
     */
    protected abstract int doUpdate(T entity);

    /**
     * 执行删除操作（子类实现具体的Mapper调用）
     */
    protected abstract int doDeleteByIds(String[] ids);

    // ==================== 公共业务方法实现 ====================

    @Override
    public List<T> selectListWithParams(T entity, Map<String, Object> queryParams) {
        return doSelectListWithParams(entity, queryParams);
    }

    @Override
    public T selectById(String id) {
        return doSelectById(id);
    }

    @Override
    public int insert(T entity) {
        return doInsert(entity);
    }

    @Override
    public int update(T entity) {
        return doUpdate(entity);
    }

    @Override
    public int deleteByIds(String[] ids) {
        return doDeleteByIds(ids);
    }

    @Override
    public Map<String, Object> buildDefaultQueryParams(T entity, S searchParams) {
        Map<String, Object> params = new HashMap<>();
        
        // 自动添加表名
        Class<S> entityClass = getEntityClass();
        String tableName = ReflectForServiceUtils.getTableNameSafely(entityClass);
        params.put("tableName", tableName);
        
        // 子类可以重写此方法来添加特定的查询参数
        return params;
    }

    @Override
    public String validateEntity(T entity, boolean isInsert) {
        // 基础验证逻辑，子类可以重写
        if (entity == null) {
            return "实体对象不能为空";
        }
        return null; // 验证通过
    }

    @Override
    public String getTableName() {
        Class<S> entityClass = getEntityClass();
        return ReflectForServiceUtils.getTableNameSafely(entityClass);
    }

    @Override
    public String getBusinessTitle() {
        Class<S> entityClass = getEntityClass();
        return ReflectForServiceUtils.getLogDynamicInfoSafely(entityClass, "数据管理");
    }

    @Override
    public T preprocessEntity(T entity, S searchParams, boolean isInsert) {
        // 默认不做任何预处理，子类可以重写
        return entity;
    }

    @Override
    public String validateBeforeDelete(String[] ids, S searchParams) {
        if (ids == null || ids.length == 0) {
            return "请选择要删除的数据";
        }
        // 基础删除验证，子类可以重写添加特定验证
        return null;
    }

    @Override
    public void afterInsert(T entity, int result) {
        // 默认不做任何后处理，子类可以重写
    }

    @Override
    public void afterUpdate(T entity, int result) {
        // 默认不做任何后处理，子类可以重写
    }

    @Override
    public void afterDelete(String[] ids, int result) {
        // 默认不做任何后处理，子类可以重写
    }

    /**
     * 获取泛型参数的实际类型
     * 这是一个辅助方法，子类可以使用它来获取泛型类型
     */
    @SuppressWarnings("unchecked")
    protected Class<T> getActualEntityClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    @SuppressWarnings("unchecked")
    protected Class<S> getActualSearchClass() {
        return (Class<S>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }


    protected ExecutorService newExecutorService(int core, int max) {
        return new ThreadPoolExecutor(core, max,
                30L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>());
    }

    /**
     * 根据ID数组将平台数据同步到本地
     *
     * @param ids
     * @return
     * @throws Exception
     */
    @Override
    public AjaxResult platToLocal(String[] ids) throws Exception {
        String platformName = getPlatformName();
        logger.info("{}数据转换开始，数量：{}", platformName, ids.length);
        
        Integer successCount = 0;
        PlatEnum platEnum = getPlatEnum();
        
        if (platEnum == null) {
            logger.error("平台枚举为空，无法继续处理");
            return AjaxResult.fail("平台配置错误!");
        }
        
        String platformValue = platEnum.getValue();
        String serviceName = platEnum.getService();
        
        for (String id : ids) {
            try {
                // 获取对应的service
                IHotelGnBaseService hotelGnBaseService = SpringUtils.getBean(serviceName);
                HotelGnBase hotelGnBase = hotelGnBaseService.selectHotelGnBaseById(id);
                
                if (Objects.isNull(hotelGnBase)) {
                    throw new RuntimeException("酒店不存在!");
                }
                
                // 创建预映射对象
                HotelGnPreMapping hotelGnPreMapping = new HotelGnPreMapping();
                hotelGnPreMapping.setId(String.valueOf(sequence.nextId()));
                hotelGnPreMapping.setSourceHotelId(id);
                hotelGnPreMapping.setSourceHotelName(hotelGnBase.getName());
                hotelGnPreMapping.setSourceHotelAddress(hotelGnBase.getAddress());
                hotelGnPreMapping.setSourceHotelPhone(hotelGnBase.getPhone());
                hotelGnPreMapping.setSourceHotelCityName(hotelGnBase.getCityName());
                hotelGnPreMapping.setSourePlatformId(platformValue);
                hotelGnPreMapping.setMappingType("import");
                hotelGnPreMapping.setScore(0);
                hotelGnPreMapping.setIsDelete(Boolean.FALSE);
                hotelGnPreMapping.setStatus((byte)0);
                hotelGnPreMapping.setCreateTime(DateUtils.getNowDate());
                hotelGnPreMapping.setCreateBy(SecurityUtils.getUsername());
                
                // 保存预映射数据
                hotelGnPreMappingService.addHotelGnPreMapping(hotelGnPreMapping);
                hotelGnPreMappingService.addBasic(hotelGnPreMapping.getId());
                
                successCount++;
            } catch (RuntimeException e) {
                logger.error("处理酒店[{}]时发生错误：{}", id, e.getMessage());
                throw new RuntimeException(e.getMessage());
            }
        }
        
        logger.info("{}数据转换完成，成功数量：{}", platformName, successCount);
        return successCount > 0 ? AjaxResult.success() : AjaxResult.fail("导入平台酒店错误!");
    }
} 