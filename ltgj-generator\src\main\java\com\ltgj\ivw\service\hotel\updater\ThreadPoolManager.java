package com.ltgj.ivw.service.hotel.updater;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池管理器(单例模式)
 * 
 * <AUTHOR>
 */
public class ThreadPoolManager {
    private static final ThreadPoolManager INSTANCE = new ThreadPoolManager();
    private final ThreadPoolExecutor executor;
    
    private ThreadPoolManager() {
        // 创建线程池，核心线程数10，最大线程数20，空闲线程存活时间60秒
        executor = new ThreadPoolExecutor(
            4, 10, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(),
            r -> {
                Thread t = new Thread(r, "hotel-data-updater-");
                t.setDaemon(true);
                return t;
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
    
    public static ThreadPoolManager getInstance() {
        return INSTANCE;
    }
    
    public ThreadPoolExecutor getExecutor() {
        return executor;
    }
    
    public void shutdown() {
        executor.shutdown();
    }
} 