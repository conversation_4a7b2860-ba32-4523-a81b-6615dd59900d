package com.ltgj.sdk.qiantao.model.staticdata;

import com.ltgj.sdk.qiantao.base.QiantaoBaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 千淘查询城市信息响应
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QiantaoQueryCityResponse extends QiantaoBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 城市列表
     */
    private List<City> returnJson;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class City implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 国家编码
         */
        private String countryCode;
        /**
         * 国家名称
         */
        private String countryName;
        /**
         * 国家名称英文
         */
        private String countryNameEn;
        /**
         * 省份编码
         */
        private String provinceCode;
        /**
         * 省份名称
         */
        private String Province;
        /**
         * 城市编码
         */
        private String CityCode;
        /**
         * 城市名称
         */
        private String CityName;
        /**
         * 城市名称英文
         */
        private String CityNameEn;

    }
} 