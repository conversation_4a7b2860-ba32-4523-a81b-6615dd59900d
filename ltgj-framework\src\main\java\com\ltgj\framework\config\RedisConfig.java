package com.ltgj.framework.config;

import com.alibaba.fastjson2.JSON;
import com.ltgj.framework.config.CacheProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * redis配置
 *
 * <AUTHOR>
 */
@Configuration
//@EnableCaching
@Slf4j
public class RedisConfig extends CachingConfigurerSupport
{
    @Autowired
    private CacheProperties cacheProperties;

    /**
     * 初始化fastjson2安全配置
     */
    @PostConstruct
    public void initFastJson2Security() {
        // 设置fastjson2的autoType白名单 - 使用系统属性方式
        System.setProperty("fastjson2.parser.safeMode", "false");
        // 设置autoType白名单
        System.setProperty("fastjson2.autoTypeAccept", "com.ltgj.**,cn.hutool.json.**");
        
        // 验证配置是否生效
        String safeMode = System.getProperty("fastjson2.parser.safeMode");
        String autoTypeAccept = System.getProperty("fastjson2.autoTypeAccept");
        
        log.info("FastJson2 安全配置已初始化:");
        log.info("  - safeMode: {}", safeMode);
        log.info("  - autoTypeAccept: {}", autoTypeAccept);
        log.info("FastJson2 autoType白名单已配置: com.ltgj.**, cn.hutool.json.**");
    }

    @Bean(name = "redisTemplate")
    @ConditionalOnMissingBean(name = "redisTemplate")
    @SuppressWarnings(value = { "unchecked", "rawtypes" })
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory connectionFactory)
    {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        FastJson2JsonRedisSerializer serializer = new FastJson2JsonRedisSerializer(Object.class);

        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);

        // Hash的key也采用StringRedisSerializer的序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);

        template.afterPropertiesSet();
        return template;
    }


    @Bean
    public DefaultRedisScript<Long> limitScript()
    {
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
        redisScript.setScriptText(limitScriptText());
        redisScript.setResultType(Long.class);
        return redisScript;
    }

    /**
     * 限流脚本
     */
    private String limitScriptText()
    {
        return "local key = KEYS[1]\n" +
                "local count = tonumber(ARGV[1])\n" +
                "local time = tonumber(ARGV[2])\n" +
                "local current = redis.call('get', key);\n" +
                "if current and tonumber(current) > count then\n" +
                "    return tonumber(current);\n" +
                "end\n" +
                "current = redis.call('incr', key)\n" +
                "if tonumber(current) == 1 then\n" +
                "    redis.call('expire', key, time)\n" +
                "end\n" +
                "return tonumber(current);";
    }



    /**
     * 当有多个管理器的时候，必须使用该注解在一个管理器上注释：表示该管理器为默认的管理器
     *
     * @param connectionFactory 链接工厂
     * @return 缓存
     */
    @Bean
    @Primary
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        //初始化一个RedisCacheWriter
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(connectionFactory);
        //序列化方式2 - 使用FastJson2JsonRedisSerializer保持一致性
        FastJson2JsonRedisSerializer<Object> fastJson2RedisSerializer = new FastJson2JsonRedisSerializer<>(Object.class);
        RedisSerializationContext.SerializationPair<Object> pair = RedisSerializationContext.SerializationPair.fromSerializer(fastJson2RedisSerializer);

        // 全局默认配置
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                .serializeValuesWith(pair)
                .entryTtl(Duration.ofSeconds(cacheProperties.getTimeout()));

        // 为特定的缓存名称配置不同的过期时间
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        if (cacheProperties.getTtl() != null) {
            for (Map.Entry<String, Long> entry : cacheProperties.getTtl().entrySet()) {
                cacheConfigurations.put(entry.getKey(), defaultCacheConfig.entryTtl(Duration.ofSeconds(entry.getValue())));
            }
        }

        // 使用 RedisCacheManager.builder 来构建
        return RedisCacheManager.builder(redisCacheWriter)
                .cacheDefaults(defaultCacheConfig) // 设置默认的缓存配置
                .withInitialCacheConfigurations(cacheConfigurations) // 设置特定缓存的配置
                .transactionAware() // 支持事务
                .build();
    }

    /**
     * 自定义缓存key生成策略，默认不使用该策略
     * 数据只存在于本地 JVM 中，不同节点之间不会共享缓存,本地缓存比远程访问redis快很多
     *
     * @return
     */
    @Bean("caffeineCacheManager")
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();

        // 配置Caffeine缓存的过期策略
        caffeineCacheManager.setCaffeine(cacheBuilder());

        // 设置缓存值的序列化方式
        caffeineCacheManager.setCacheLoader(key -> {
            // 这里可以根据 key 做一些业务处理，比如从数据库中获取数据
            log.info("caffeineCacheManager_调用Redis缓存Key生成策略：Key -> [{}]", key);
            if (key == null) {
                return null; // 表示没有找到对应的缓存值
            }
            // 这里返回 null 表示没有找到对应的缓存值
            return null; // 默认加载行为
        });

        return caffeineCacheManager;
    }

    @Bean("caffeine")
    public com.github.benmanes.caffeine.cache.@NonNull Cache<Object, Object>  caffeine() {
        return cacheBuilder().build();
    }

    @Bean
    public com.github.benmanes.caffeine.cache.Caffeine<Object, Object> cacheBuilder() {
        return com.github.benmanes.caffeine.cache.Caffeine.newBuilder()
                .expireAfterWrite(cacheProperties.getTimeout(), java.util.concurrent.TimeUnit.SECONDS)
                .maximumSize(1000);
    }



    /**
     * 自定义缓存key生成策略，默认将使用该策略
     */
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            Map<String, Object> container = new HashMap<>(3);
            Class<?> targetClassClass = target.getClass();
            //类地址
            container.put("class", targetClassClass.toGenericString());
            //方法名称
            container.put("methodName", method.getName());
            //包名称
            container.put("package", targetClassClass.getPackage());
            //参数列表
            for (int i = 0; i < params.length; i++) {
                container.put(String.valueOf(i), params[i]);
            }

            //转为JSON字符串
            String jsonString = JSON.toJSONString(container);
            //做SHA256 Hash计算，得到一个SHA256摘要作为Key
            String sha256Hex = DigestUtils.sha256Hex(jsonString);
            log.info("### RedisCacheKeyGenerator_调用Redis缓存Key生成策略：Key -> [{}]", sha256Hex);
            return DigestUtils.sha256Hex(sha256Hex);
        };
    }

    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        //异常处理，当Redis发生异常时，打印日志，但是程序正常走
        log.info("初始化 -> [{}]", "Redis CacheErrorHandler");
        return new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException e, Cache cache, Object key) {
                log.error("Redis occur handleCacheGetError：key -> [{}]", key, e);
            }

            @Override
            public void handleCachePutError(RuntimeException e, Cache cache, Object key, Object value) {
                log.error("Redis occur handleCachePutError：key -> [{}]；value -> [{}]", key, value, e);
            }

            @Override
            public void handleCacheEvictError(RuntimeException e, Cache cache, Object key) {
                log.error("Redis occur handleCacheEvictError：key -> [{}]", key, e);
            }

            @Override
            public void handleCacheClearError(RuntimeException e, Cache cache) {
                log.error("Redis occur handleCacheClearError：", e);
            }
        };
    }
}
