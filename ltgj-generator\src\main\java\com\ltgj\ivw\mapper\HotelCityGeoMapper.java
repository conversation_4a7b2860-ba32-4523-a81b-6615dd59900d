package com.ltgj.ivw.mapper;

import com.ltgj.ivw.domain.HotelCityGeo;
import com.ltgj.ivw.domain.HotelCityGeoExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HotelCityGeoMapper {
    long countByExample(HotelCityGeoExample example);

    int deleteByExample(HotelCityGeoExample example);

    int deleteByPrimaryKey(String id);

    int insert(HotelCityGeo record);

    int insertSelective(HotelCityGeo record);

    List<HotelCityGeo> selectByExample(HotelCityGeoExample example);

    HotelCityGeo selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") HotelCityGeo record, @Param("example") HotelCityGeoExample example);

    int updateByExample(@Param("record") HotelCityGeo record, @Param("example") HotelCityGeoExample example);

    int updateByPrimaryKeySelective(HotelCityGeo record);

    int updateByPrimaryKey(HotelCityGeo record);

    /**
     * 批量插入酒店城市商圈数据
     *
     * @param records 商圈数据列表
     * @return 插入成功的条数
     */
    int batchInsert(@Param("records") List<HotelCityGeo> records);

    /**
     * 批量插入或更新酒店城市商圈数据
     *
     * @param records 商圈数据列表
     * @return 插入/更新成功的条数
     */
    int batchInsertOrUpdate(@Param("records") List<HotelCityGeo> records);

    int batchInsertForContidion(List<HotelCityGeo> records);

    /**
     * 批量插入或更新酒店城市商圈数据
     * 只有当新值非空且非空字符串时才更新，否则保留原值
     *
     * @param records 商圈数据列表
     * @return 插入/更新成功的条数
     */
    int batchInsertOrUpdateSmart(@Param("records") List<HotelCityGeo> records);
}