package com.ltgj.supplier.common.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/5
 * @description: 设施信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FacilitiesInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 停车信息
     */
    private Parking parking;
    /**
     * 充电设施
     */
    private Charging charging;
    /**
     * 行李寄存
     */
    private Baggage baggage;
    /**
     * 电梯
     */
    private Lift lift;
    /**
     * 餐厅
     */
    private Restaurant restaurant;
    /**
     * 会议室
     */
    private MeetingRoom meetingRoom;
    /**
     * 公共wifi
     */
    private PublicWifi publicWifi;
    /**
     * 健身房
     */
    private Gym gym;
    /**
     * 洗衣房
     */
    private Laundry laundry;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BaseIsHave implements Serializable {
        /**
         * 是否有无
         */
        private Boolean isHave;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Parking extends BaseIsHave implements Serializable {
        /**
         * 是否收费
         */
        private Boolean isCharge;
    }

    @Data
    @NoArgsConstructor
    public static class Charging extends BaseIsHave implements Serializable {
    }
    @Data
    @NoArgsConstructor
    public static class Baggage extends BaseIsHave implements Serializable {
    }

    @Data
    @NoArgsConstructor
    public static class Lift extends BaseIsHave implements Serializable {

    }

    @Data
    @NoArgsConstructor
    public static class Restaurant extends BaseIsHave implements Serializable {
    }

    @Data
    @NoArgsConstructor
    public static class MeetingRoom extends BaseIsHave implements Serializable {

    }

    @Data
    @NoArgsConstructor
    public static class PublicWifi extends BaseIsHave implements Serializable {

    }

    @Data
    @NoArgsConstructor
    public static class Gym extends BaseIsHave implements Serializable {

    }

    @Data
    @NoArgsConstructor
    public static class Laundry extends BaseIsHave implements Serializable {

    }
}
