package com.ltgj.web.job.handler;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.ivw.controller.*;
import com.ltgj.sdk.cozyTime.model.staticdata.CozyTimeHotelContentApi20CitiesResponse;
import com.ltgj.sdk.qiantao.model.staticdata.QiantaoQueryCityResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/6/6
 * @description:
 */
@Component
@Slf4j
@JobHandler(value = "hotelCitySyncHandler")
public class HotelCitySyncHandler extends IJobHandler {

    @Autowired
    private HotelGnKetanController hotelGnKetanController;

    @Autowired
    private HotelInfoHsjlController hotelInfoHsjlController;

    @Autowired
    private HotelInfoChailvgjOldController hotelInfoChailvgjOldController;

    @Autowired
    private HotelInfoQiantaoController hotelInfoQiantaoController;

    @Autowired
    private HotelInfoElongController hotelInfoElongController;

    @Autowired
    private HotelInfoMeituanController hotelInfoMeituanController;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        // elong
        AjaxResult elongCityData = hotelInfoElongController.updateCityInfo();
        if (elongCityData.get(AjaxResult.DATA_TAG) != null) {
            log.info("elong城市处理完成:{}",elongCityData);
        }
        // ketan
        AjaxResult ajaxResult = hotelGnKetanController.getCityData("CN");
        if (ajaxResult.get(AjaxResult.DATA_TAG) != null) {
            CozyTimeHotelContentApi20CitiesResponse contentApi20CitiesResponse = (CozyTimeHotelContentApi20CitiesResponse) ajaxResult.get(AjaxResult.DATA_TAG);
            Optional.ofNullable(contentApi20CitiesResponse).ifPresent(item->hotelGnKetanController.processCityData(item,"1"));
        }
        // chailvgj
        AjaxResult cityData = hotelInfoChailvgjOldController.syncCityData("1");
        if (cityData.get(AjaxResult.DATA_TAG) != null) {
            log.info("chailvgj cityData:{}",cityData);
        }
        // hsjl
        AjaxResult hsjlCityData = hotelInfoHsjlController.syncCityData("1");
        if (hsjlCityData.get(AjaxResult.DATA_TAG) != null) {
            log.info("hsjl cityData:{}",hsjlCityData);
        }
        // qiantao
        AjaxResult qiantaoResult = hotelInfoQiantaoController.getCityData();
        if (qiantaoResult.get(AjaxResult.DATA_TAG) != null) {
            QiantaoQueryCityResponse qiantaoResponse = (QiantaoQueryCityResponse) qiantaoResult.get(AjaxResult.DATA_TAG);
            Optional.ofNullable(qiantaoResponse).ifPresent(item -> hotelInfoQiantaoController.processCityData(item, "1"));
        }
        // meituan
        AjaxResult meituanResult = hotelInfoMeituanController.processCityData("1");
        if (meituanResult.get(AjaxResult.DATA_TAG) != null) {
            log.info("美团城市数据处理完成: {}", meituanResult);
        }
        return ReturnT.SUCCESS;
    }
}
