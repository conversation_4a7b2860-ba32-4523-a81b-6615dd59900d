package com.ltgj.ivw.service;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.dto.GeoParentLookupReq;
import com.ltgj.ivw.dto.GeoStructureQueryReq;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 地理位置层级服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface IGeoHierarchyService {
    
    /**
     * 获取完整的四级联动数据（国家->省份->城市->区县）
     * 
     * @return 四级联动数据
     */
    AjaxResult getGeoHierarchy();
    
    /**
     * 根据级别获取地理位置数据
     * 
     * @param request 查询请求参数
     * @return 分级数据
     */
    AjaxResult getGeoDataByLevel(GeoLevelQueryReq request);
    
    /**
     * 根据子级信息查询上级行政区划
     * 
     * @param request 上级查询参数
     * @return 上级行政区划信息
     */
    AjaxResult getParentInfo(GeoParentLookupReq request);
    
    /**
     * 根据任意层级的ID获取其完整的四级联动层次结构
     * 支持传入国家、省份、城市、区县任意层级的ID
     * 返回该ID对应节点的完整层次结构数据
     * 
     * @param request 层次结构查询请求参数
     * @return 完整的层次结构数据
     */
    AjaxResult<GeoHierarchyNode> getGeoStructureById(GeoStructureQueryReq request);
    
    /**
     * 选择性清除缓存
     * 
     * @param cacheTypes 要清除的缓存类型列表，支持的类型：
     *                   - "hierarchy": 完整四级联动数据缓存
     *                   - "level": 分级查询数据缓存  
     *                   - "parent": 上级查询缓存
     *                   - "structure": 层次结构查询缓存
     *                   - "all": 清除所有缓存
     * @return 清除结果信息
     */
    String clearCacheSelective(java.util.List<String> cacheTypes);
    
} 