package com.ltgj.ivw.service;

import com.ltgj.common.core.domain.entity.SysUser;
import com.ltgj.common.core.domain.model.LoginUser;
import com.ltgj.ivw.domain.HotelCity;
import com.ltgj.ivw.domain.HotelUpdateRecode;
import com.ltgj.ivw.domain.dto.CityDTO;
import com.ltgj.ivw.domain.dto.HotelDTO;
import com.ltgj.ivw.domain.dto.HotelDetailDTO;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.service.hotel.updater.ProcessResult;
import com.ltgj.ivw.service.impl.HotelInfoChailvgjServiceImpl;
import com.ltgj.ivw.utils.hotelApi.HotelUpdateStatus;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * HotelInfoChailvgjServiceImpl的updateAll方法单元测试
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RunWith(MockitoJUnitRunner.class)
public class HotelInfoChailvgjServiceUpdateAllTest {
    @InjectMocks
    private HotelInfoChailvgjServiceImpl hotelInfoChailvgjService;

    @Mock
    private IHotelCityService hotelCityService;

    @Mock
    private IHotelUpdateRecodeService hotelUpdateRecodeService;

    @Before
    public void setup() {
        MockitoAnnotations.openMocks(this);

        // 创建测试数据
        List<HotelCity> hotelCities = new ArrayList<>();
        HotelCity hotelCity = new HotelCity();
        hotelCity.setCityId("1");
        hotelCity.setCityName("北京");
        hotelCity.setProvinceId("11");
        hotelCity.setProvinceName("北京");
        hotelCity.setCountryId("CN");
        hotelCity.setCountryName("中国");
        hotelCity.setReserve1(PlatEnum.PLAT_CLGJ.getValue());
        hotelCity.setReserve3("0");
        hotelCities.add(hotelCity);

        // Mock hotelCityService的行为
        when(hotelCityService.selectHotelCityList(any())).thenReturn(hotelCities);
        
        // Mock 安全上下文（避免SecurityUtils.getUsername异常）
        mockSecurityUtils();
    }
    
    /**
     * 模拟SecurityUtils.getUsername()方法
     */
    private void mockSecurityUtils() {
        // 创建模拟的Authentication对象
        Authentication authentication = mock(Authentication.class);
        SecurityContext securityContext = mock(SecurityContext.class);
        
        // 创建LoginUser对象
        SysUser sysUser = new SysUser();
        sysUser.setUserName("admin");
        sysUser.setNickName("管理员");
        sysUser.setUserId(1L);
        sysUser.setPassword("$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2"); // 默认密码
        sysUser.setStatus("0"); // 正常状态
        
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(1L);
        loginUser.setUser(sysUser);
        loginUser.setPermissions(Collections.singleton("*:*:*")); // 添加管理员权限
        
        // 配置模拟行为
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn(loginUser);
        
        // 设置SecurityContextHolder
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    public void testUpdateAll() {
        // 创建部分方法的模拟实现
        HotelInfoChailvgjServiceImpl spyService = Mockito.spy(hotelInfoChailvgjService);

        // 模拟获取城市数据
        List<CityDTO> cities = new ArrayList<>();
        CityDTO cityDTO = new CityDTO();
        cityDTO.setCityId("1");
        cityDTO.setCityName("北京");
        cities.add(cityDTO);
        doReturn(cities).when(spyService).getAllCities();

        // 模拟获取酒店列表
        List<HotelDTO> hotels = new ArrayList<>();
        HotelDTO hotelDTO = new HotelDTO();
        hotelDTO.setHotelId("123");
        hotelDTO.setHotelName("测试酒店");
        hotels.add(hotelDTO);
        doReturn(hotels).when(spyService).getHotelListByCityId(anyString());

        // 模拟获取酒店详情
        HotelDetailDTO hotelDetail = new HotelDetailDTO();
        hotelDetail.setHotelId("123");
        hotelDetail.setHotelName("测试酒店");
        doReturn(hotelDetail).when(spyService).getHotelDetail(anyString());

        // 模拟保存酒店信息方法
        ProcessResult mockResult = new ProcessResult(new AtomicInteger(1), new AtomicInteger(1), new AtomicInteger(0), new AtomicInteger(1));
        Mockito.doReturn(mockResult).when(spyService).saveHotelInfo(any(HotelDetailDTO.class));

        // 初始化HotelUpdateStatus
        HotelUpdateStatus.expStatusCLGJ = 0;
        HotelUpdateStatus.clgjDetailCountGet = 0;
        HotelUpdateStatus.clgjDetailCountUpdate = 0;

        // 执行测试方法
        spyService.updateAll("1111");

        // 验证方法调用
        verify(hotelUpdateRecodeService).insertHotelUpdateRecode(any(HotelUpdateRecode.class));

        // 验证状态更新
        assert HotelUpdateStatus.expStatusCLGJ == 3;
    }
} 