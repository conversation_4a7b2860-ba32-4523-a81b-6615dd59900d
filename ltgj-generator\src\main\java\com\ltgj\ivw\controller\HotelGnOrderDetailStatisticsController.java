package com.ltgj.ivw.controller;

import com.github.pagehelper.PageHelper;
import com.ltgj.common.annotation.Log;
import com.ltgj.common.constant.HttpStatus;
import com.ltgj.common.core.controller.BaseController;
import com.ltgj.common.core.page.TableDataInfo;
import com.ltgj.common.enums.BusinessType;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.bean.BeanUtils;
import com.ltgj.common.utils.poi.ExcelUtil;
import com.ltgj.ivw.request.hotelGnStatistics.GnOrderDetailStatisticsRequest;
import com.ltgj.ivw.response.HotelGnOrderDetailStatisticsDto;
import com.ltgj.ivw.response.HotelGnOrderDetailStatisticsRes;
import com.ltgj.ivw.service.HotelGnOrderDetailStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/hotelGnOrderDetailStatistics")
public class HotelGnOrderDetailStatisticsController extends BaseController {

    @Resource
    private HotelGnOrderDetailStatisticsService statisticsService;

    /**
     * 列表查询
     *
     * @param request
     * @return
     */
//    @PreAuthorize("@ss.hasPermi('ivw:hotelGnOrderDetailStatistics:list')")
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody GnOrderDetailStatisticsRequest request) {
        log.info("订单详情查询={}", request);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        request.setIsFailure(1);
        int total = statisticsService.selectCount(request);
        List<HotelGnOrderDetailStatisticsRes> resp = statisticsService.selectList(request);
        return getDataTable(resp, total);
    }

    /**
     * 导出信息
     */
//    @PreAuthorize("@ss.hasPermi('ivw:hotelGnOrderDetailStatistics:export')")
    @Log(title = "失败明细导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GnOrderDetailStatisticsRequest request) {
        request.setIsFailure(1);
        request.setPageNum(1);
        request.setPageSize(1000000);
        List<HotelGnOrderDetailStatisticsRes> detailStatistics = statisticsService.selectList(request);
        List<HotelGnOrderDetailStatisticsDto> hotelGnStatisticsList = detailStatistics.stream().map(m -> {
            HotelGnOrderDetailStatisticsDto statisticsDto = new HotelGnOrderDetailStatisticsDto();
            BeanUtils.copyProperties(m, statisticsDto);
            statisticsDto.setDate(DateUtils.formatDate(m.getCreateTime(), DateUtils.YYYY_MM_DD));
            statisticsDto.setTime(DateUtils.formatDate(m.getCreateTime(), "HH:mm:ss"));
            statisticsDto.setReason(m.getReason());
            return statisticsDto;
        }).collect(Collectors.toList());
        ExcelUtil<HotelGnOrderDetailStatisticsDto> util = new ExcelUtil<>(HotelGnOrderDetailStatisticsDto.class);
        util.exportExcel(response, hotelGnStatisticsList, "映射数据统计");
    }

    private TableDataInfo getDataTable(List<?> list, int total) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(total);
        return rspData;
    }
}
