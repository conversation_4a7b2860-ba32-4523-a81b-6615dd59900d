package com.ltgj.ivw.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.ltgj.common.core.redis.RedisCache;
import com.ltgj.common.es.hotel.model.DatePrice;
import com.ltgj.common.es.hotel.model.EsHotelInfo;
import com.ltgj.common.es.hotel.model.Location;
import com.ltgj.common.es.util.CoordinateTransformUtils;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.domain.JdJdb;
import com.ltgj.ivw.domain.ZhJdJdbMinPrice;
import com.ltgj.ivw.enums.PlatEnum;
import com.ltgj.ivw.mapper.JdJdbMapper;
import com.ltgj.ivw.mapper.ZhJdJdbMinPriceMapper;
import com.ltgj.ivw.service.EsHotelInfoService;
import com.ltgj.ivw.utils.MyTools;
import com.tem.search.config.ElasticSearchConfiguration;
import com.tem.search.repository.ElasticsearchTemplate;
import com.tem.search.util.IndexTools;
import com.tem.search.util.MetaData;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.BinaryOperator;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ltgj.common.utils.StringUtils.isEmpty;
import static com.ltgj.common.utils.uuid.UUID.getRandomNumber;

/**
 * 酒店基础数据同步es Service
 */
@Service
public class EsHotelInfoServiceImpl implements EsHotelInfoService {
    private Logger logger = LoggerFactory.getLogger(EsHotelInfoServiceImpl.class);
    @Autowired
    private JdJdbMapper jdJdbMapper;
    @Autowired
    private ZhJdJdbMinPriceMapper zhJdJdbMinPriceMapper;

    @Autowired
    private ElasticsearchTemplate<EsHotelInfo, String> hotelIntlTemplate;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    ElasticSearchConfiguration.RestHighLevelClients clients;

    @Autowired
    private RedisCache redisCache;

    @Value("${min.price:50}")
    private Integer minPrice;

    private static final Pattern LONGITUDE_PATTERN = Pattern.compile("^-?(180(\\.0+)?|1[0-7]\\d(\\.\\d+)?|[1-9]?\\d(\\.\\d+)?)$");
    private static final Pattern LATITUDE_PATTERN = Pattern.compile("^-?(90(\\.0+)?|[1-8]?\\d(\\.\\d+)?)$");

    private static final int pageSize = 1000;


    @Override
    public void syncHotelToEs(String suffix) {
        syncHotelToEs();
//        String key = "syncHotelToEs:" + suffix;
//        RLock rLock = redissonClient.getLock(key);
//        boolean lock = false;
//        try {
//            //1.锁即使异常解锁也要自动释放,在300s内nginx调用不会出现这么久的延迟，但不代表没有延迟，防止重复执行
//            lock = rLock.tryLock(2L,1800L, TimeUnit.SECONDS);
//            if(lock){
//                int batchCnt = 1;
//                int startNumber = 0;
//                long startTime = System.currentTimeMillis();
//                logger.info("syncAllHotel-开始同步酒店打底数据zh_jd_jdb_{}表到es", suffix);
//
//                int hotelTotalCount = getHotelTotalCount(suffix);
//                if (hotelTotalCount == 0) {
//                    return;
//                }
//                int pageCount = getTotalPageCount(hotelTotalCount);
//                logger.info("hotelTotalCount-酒店打底数据表zh_jd_jdb_{},数据总量为:{},总页数:{}", suffix, hotelTotalCount, pageCount);
//
//                while (startNumber < hotelTotalCount) {
//                    try {
//                        processPage(suffix, startNumber, pageSize);
//                        if (batchCnt % 10 == 0) {
//                            logger.info("酒店打底数据:[{}]表已同步条数:[{}],总页数:{},同步页数:{}", suffix, startNumber + pageSize, pageCount, batchCnt);
//                        }
//                        batchCnt++;
//                    } catch (Exception e) {
//                        logger.error("酒店打底数据同步异常：{}", e.getMessage());
//                        retryFailedBatch(startNumber, pageSize, suffix);
//                    } finally {
//                        startNumber += pageSize;
//                    }
//                }
//
//                long endTime = System.currentTimeMillis();
//                logger.info("syncAllHotel-同步酒店打底数据zh_jd_jdb_{},已完成,已同步数据总量为:{},总耗时：{}", suffix, hotelTotalCount, (endTime - startTime) / 60000);
//            }else{
//                logger.info("同步酒店数据到ES，suffix：{} 正在执行。。。。。。。。。。。。。。。。", suffix);
//            }
//        } catch (InterruptedExceptionss e) {
//            logger.info("同步酒店数据到ES:"+suffix+"异常", e);
//        } finally {
//            rLock.unlock();
//        }
    }

    @Override
    public void syncHotelToEs() {
        String key = "syncHotelToEs:" + UUID.randomUUID();
        RLock rLock = redissonClient.getLock(key);
        boolean lock;
        try {
            lock = rLock.tryLock(2L,1800L, TimeUnit.SECONDS);
            if(lock){
                int batchCnt = 1;
                int startNumber = 0;
                long startTime = System.currentTimeMillis();
                logger.info("syncAllHotel-开始同步酒店打底数据jd_jdb表到es");

                int hotelTotalCount = getJdJdbHotelTotalCount();
                if (hotelTotalCount == 0) {
                    return;
                }
                int pageCount = getTotalPageCount(hotelTotalCount);
                logger.info("hotelTotalCount-酒店打底数据表jd_jdb,数据总量为:{},总页数:{}", hotelTotalCount, pageCount);

                while (startNumber < hotelTotalCount) {
                    try {
                        processPage(startNumber);
                        if (batchCnt % 10 == 0) {
                            logger.info("酒店打底数据:已同步条数:[{}],总页数:{},同步页数:{}", startNumber + pageSize, pageCount, batchCnt);
                        }
                        batchCnt++;
                    } catch (Exception e) {
                        logger.error("酒店打底数据同步异常：{}", e.getMessage());
                        retryFailedBatch(startNumber, pageSize);
                    } finally {
                        startNumber += pageSize;
                    }
                }

                long endTime = System.currentTimeMillis();
                logger.info("syncAllHotel-同步酒店打底数据jd_jdb,已完成,已同步数据总量为:{},总耗时：{}", hotelTotalCount, (endTime - startTime) / 60000);
            }else{
                logger.info("同步酒店数据到ES， 正在执行。。。。。。。。。。。。。。。。");
            }
        } catch (InterruptedException e) {
            logger.info("同步酒店数据到ES异常", e);
        } finally {
            rLock.unlock();
        }
    }


    private int getHotelTotalCount(String suffix) {
        return jdJdbMapper.findByBeanCount(suffix);
    }

    /**
     * 获取jd-jdb酒店总数
     *
     * @return int
     */
    private int getJdJdbHotelTotalCount() {
        return jdJdbMapper.findJdJdbCount();
    }

    private int getTotalPageCount(int totalCount) {
        return totalCount % pageSize == 0 ? totalCount / pageSize : (totalCount / pageSize) + 1;
    }

    /**
     * 处理分页
     *
     * @param startNumber 起始编号
     * @throws Exception 例外
     */
    private void processPage(int startNumber) throws Exception {
        List<JdJdb> interfacePlats = jdJdbMapper.getJdJdbByPage(startNumber, pageSize);
        if (CollectionUtils.isEmpty(interfacePlats)) {
            return;
        }
        syncHotelBaseInfoToEs(interfacePlats);
    }

    /**
     * 将酒店基本信息同步到es
     *
     * @param interfacePlats 打底数据
     * @throws Exception 例外
     */
    public void syncHotelBaseInfoToEs(List<JdJdb> interfacePlats) throws Exception {
        List<EsHotelInfo> esHotelInfos = this.assemblyData(interfacePlats);
        BulkResponse save = hotelIntlTemplate.save(esHotelInfos);
        if (save.hasFailures()) {
            logger.error("酒店打底数据同步到es失败，失败原因为：{}", save.buildFailureMessage());
            retryFailedBulks(save, esHotelInfos);
        } else {
            getFinalEsHotelInfos(esHotelInfos);
            logger.info("酒店打底数据同步到酒店id{}", interfacePlats.get(interfacePlats.size() - 1).getId());
        }
    }

    public RestHighLevelClient getClient(Class<?>  t) throws Exception {
        MetaData metaData = IndexTools.getIndexType(t);
        return clients.getClient(metaData.getClientCode());
    }

    @Override
    public void updateMinPriceToZero(double minPrice) throws IOException {
        if (minPrice <= 0) {
            logger.error("低价入参不正确：{}", minPrice);
            return;
        }

        MetaData metaData = IndexTools.getIndexType(EsHotelInfo.class);
        String indexName = metaData.getIndexname();

        // 创建UpdateByQueryRequest对象
        UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest(indexName);

        // 定义Painless脚本
        Script script = new Script(ScriptType.INLINE, "painless",
                "for (int i = 0; i < ctx._source.datePriceList.length; i++) { " +
                        "        if (ctx._source.datePriceList[i].minPrice <= "+ minPrice +") { " +
                        "          ctx._source.datePriceList[i].minPrice = 0; " +
                        "        } " +
                        "      }",
                Collections.emptyMap());

        // 设置脚本
        updateByQueryRequest.setScript(script);

        // 创建范围查询，minPrice 小于等于 minPrice
        RangeQueryBuilder rangeQueryBuilder = QueryBuilders
                .rangeQuery("datePriceList.minPrice")
                .gte(0.1)
                .lte(minPrice);

        // 创建嵌套查询
        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery("datePriceList", rangeQueryBuilder, ScoreMode.None);

        // 创建布尔查询，组合多个条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(nestedQueryBuilder);
        updateByQueryRequest.setQuery(boolQueryBuilder);

        logger.info("ES boolQuery 查询条件：{}", updateByQueryRequest.getSearchRequest().source().toString());
        // 打印脚本内容日志
        logger.info("Painless 脚本内容: {}", script.getIdOrCode());

        RestHighLevelClient client;
        try {
            client = getClient(EsHotelInfo.class);

            // 记录开始时间
            long startTime = System.currentTimeMillis();

            // 执行Update By Query操作
            BulkByScrollResponse response = client.updateByQuery(updateByQueryRequest, RequestOptions.DEFAULT);

            // 记录结束时间
            long endTime = System.currentTimeMillis();

            // 计算耗时
            long duration = endTime - startTime;
            logger.info("updateByQuery 耗时: {} ms", duration);
            logger.info("Update By Query 响应: {}", response.getStatus().toString());
            logger.info("更新的文档数量: {}", response.getUpdated());
            logger.info("处理的文档总数: {}", response.getTotal());
            logger.info("版本冲突数量: {}", response.getVersionConflicts());
            logger.info("批量操作失败数量: {}", response.getBulkFailures().size());
            logger.info("搜索操作失败数量: {}", response.getSearchFailures().size());
        } catch (IOException e) {
            logger.error("ES 更新低价为0 异常", e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void clearDatePriceListForInterfacePlat(String interfacePlat) {
        // 创建嵌套查询
        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery("datePriceList",
                QueryBuilders.termQuery("datePriceList.interfacePlat", interfacePlat),
                ScoreMode.None);

        // 创建布尔查询，组合多个条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(nestedQueryBuilder)
                .mustNot(QueryBuilders.termsQuery("hotelId", "200000491312436").boost(1));

        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            List<EsHotelInfo> esHotelInfos = hotelIntlTemplate.searchMore(boolQueryBuilder, 10000, EsHotelInfo.class);
            logger.info("clearDatePriceListForInterfacePlat 记录条数: {} ms", esHotelInfos.size());
            if (CollectionUtils.isEmpty(esHotelInfos)) {
                return;
            }

            List<List<EsHotelInfo>> lists = MyTools.listPartition(esHotelInfos, 100);

            // 获取系统可用的核心数
            int availableProcessors = Runtime.getRuntime().availableProcessors();
            // 设置线程池大小为可用核心数的1.5倍，且至少保证有2个线程
            int threadPoolSize = Math.max((int) Math.ceil(availableProcessors * 1.5), 30);
            logger.info("酒店数据线程数: {}",  threadPoolSize);
            ExecutorService executorService = Executors.newFixedThreadPool(threadPoolSize);
            try {
                List<CompletableFuture<?>> completableFutures = Lists.newCopyOnWriteArrayList();
                for (List<EsHotelInfo> hotelInfoSubList : lists) {
                    CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> {
                        for (EsHotelInfo hotelInfo : hotelInfoSubList) {
                            hotelInfo.setDatePriceList(Collections.emptyList());
                            try {
                                hotelIntlTemplate.save(hotelInfo);
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }, executorService);
                    completableFutures.add(completableFuture);
                }
                CompletableFuture<?> allOf = CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0]));
                allOf.get();
            } catch (InterruptedException | java.util.concurrent.ExecutionException e) {
                logger.error("酒店数据更新异常", e);
                Thread.currentThread().interrupt(); // 重新设置中断状态
            } finally {
                executorService.shutdownNow(); // 确保所有任务都完成后再关闭线程池
            }
            // 记录结束时间
            long endTime = System.currentTimeMillis();

            // 计算耗时
            long duration = endTime - startTime;
            logger.info("clearDatePriceListForInterfacePlat 耗时: {} ms", duration);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private void retryFailedBulks(BulkResponse save, List<EsHotelInfo> esHotelInfos) {
        if (!CollectionUtils.isEmpty(esHotelInfos)) {
            Predicate<BulkItemResponse> bulkItemResponsePredicate = n -> n.isFailed();
            List<Integer> isFailedItems = Arrays.stream(save.getItems()).filter(bulkItemResponsePredicate)
                    .collect(Collectors.mapping(BulkItemResponse::getItemId,Collectors.toList()));
            Predicate<EsHotelInfo> isSuccess = item -> !isFailedItems.contains(item.getHotelId());
            //部分成功的筛选出来,入缓存
            List<EsHotelInfo> esHotelInfosSuccess = esHotelInfos.stream().filter(isSuccess).collect(Collectors.toList());
            //幂等
            this.getFinalEsHotelInfos(esHotelInfosSuccess);

            //失败的
            List<EsHotelInfo> esHotelInfosFailed = isFailedItems.stream().map(esHotelInfos::get).collect(Collectors.toList());
            try {
                if (!CollectionUtils.isEmpty(esHotelInfosFailed)) {
                    hotelIntlTemplate.save(esHotelInfosFailed);
                    //幂等
                    this.getFinalEsHotelInfos(esHotelInfosFailed);
                }else {
                    logger.error("重试保存失败，已经存入,酒店id:{}",esHotelInfosFailed.stream().map(EsHotelInfo::getHotelId).collect(Collectors.toList()));
                }
            } catch (Exception e) {
                logger.error("重试保存失败的项时发生异常：", e);
            }
        }
    }

    /**
     * 重试失败批处理
     *
     * @param startNumber 起始编号
     * @param pageSize    页面大小
     */
    private void retryFailedBatch(int startNumber, int pageSize) {
        try {
            Thread.sleep(5000);
            List<JdJdb> interfacePlats = jdJdbMapper.getJdJdbByPage(startNumber, pageSize);
            List<EsHotelInfo> esHotelInfos = this.assemblyData(interfacePlats);
            //
            List<EsHotelInfo> finalEsHotelInfos = getFinalEsHotelInfos(esHotelInfos);
            if (!CollectionUtils.isEmpty(finalEsHotelInfos)){
                BulkResponse save = hotelIntlTemplate.save(finalEsHotelInfos);
                if (save.hasFailures()) {
                    logger.error("重试同步失败，失败原因为：{}", save.buildFailureMessage());
                }else {
                    logger.info("### retryFailedBatch_重试成功!!,startNumber:{},pageSize:{}", startNumber, pageSize);
                }
            }else {
                logger.info("### retryFailedBatch_重试跳过!!无符合存入条件的数据!!,startNumber:{},pageSize:{}", startNumber, pageSize);
            }
        } catch (Exception ex) {
            logger.error("重试同步时发生异常：", ex);
        }
    }

    @NotNull
    public List<EsHotelInfo> getFinalEsHotelInfos(List<EsHotelInfo> esHotelInfos) {
        if (!CollectionUtils.isEmpty(esHotelInfos)) {

            return esHotelInfos.stream().filter(item -> {
                final String[] key = {"extracted_"};
                Integer randomNumber = getRandomNumber(60 * 10, 60 * 12);
                try {
                    key[0] = String.format("%s%s", key[0], item.getHotelId());
                    boolean tryLock = redissonClient.getLock(key[0]).tryLock(3l, randomNumber, TimeUnit.SECONDS);
                    if (!tryLock) {
                        logger.error("### getFinalEsHotelInfos_幂等跳过([{}]秒内已经执行过!!无需执行该酒店id),key:{}",randomNumber, key[0]);
                    }
                    return tryLock;
                } catch (InterruptedException interruptedException) {
                    logger.error("### getFinalEsHotelInfos_interruptedException:{}",interruptedException);
                    return false;
                }
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public List<EsHotelInfo> assemblyData(List<JdJdb> interfacePlats) {
        List<EsHotelInfo> esHotelInfos = new ArrayList<>();
        for (JdJdb jdJdb : interfacePlats) {
            EsHotelInfo esHotelInfo = new EsHotelInfo();
            esHotelInfo.setHotelId(jdJdb.getId());
            esHotelInfo.setNationalType(1);
            esHotelInfo.setName(jdJdb.getJdmc());
            esHotelInfo.setNameEn(jdJdb.getJdmcEn());

            StringBuilder jdmc = new StringBuilder(jdJdb.getJdmc());
            appendIfNotEmpty(jdmc, jdJdb.getJdmcEn());
            appendIfNotEmpty(jdmc, jdJdb.getJddz());
            // appendIfNotEmpty(jdmc, jdJdb.getBrandName());

            esHotelInfo.setKeyWord(jdmc.toString());
            esHotelInfo.setIndexPicture(jdJdb.getImgUrl());

            esHotelInfo.setDistrictId(jdJdb.getDistrict());
            esHotelInfo.setCityCode(jdJdb.getCityId());
            esHotelInfo.setCityName(jdJdb.getCityName());
            esHotelInfo.setScore(jdJdb.getScore() != null ? jdJdb.getScore().floatValue() : 0.0f);

            esHotelInfo.setStar(StringUtils.isNotEmpty(jdJdb.getJdxj()) ?
                    Integer.parseInt(jdJdb.getJdxj()) : 0);

            esHotelInfo.setBrandId(jdJdb.getBrandId());
            esHotelInfo.setBrandName(jdJdb.getBrandName());
            String interfacePlat = jdJdb.getInterfacePlat();
            if (StringUtils.isNotEmpty(interfacePlat) && interfacePlat.contains(PlatEnum.PLAT_HSJL_XY.getValue())) {
                esHotelInfo.setAgreementCodeList(Collections.singletonList("kanglv"));
            }

            esHotelInfo.setAddress(jdJdb.getJddz());
            setLocation(jdJdb, esHotelInfo);


            Optional.ofNullable(jdJdb.getJdlx()).ifPresent(type -> {
                if (type.contains("/")) {
                    esHotelInfo.setHotelType(Arrays.asList(type.split("/")));
                } else {
                    esHotelInfo.setHotelType(Collections.singletonList(type));
                }
            });
            esHotelInfo.setAddressEn(jdJdb.getJddzEn());
            esHotelInfo.setLinkPhone(jdJdb.getJddh());
            esHotelInfo.setBusinessZone(jdJdb.getBusinessZone());
            esHotelInfo.setBusinessZoneName(jdJdb.getBusinessZoneName());
            esHotelInfo.setInterfacePlatList(Collections.singletonList(interfacePlat));
            esHotelInfo.setInterfacePlatListNew(StringUtils.isEmpty(interfacePlat)?new ArrayList<>():Arrays.asList(interfacePlat.split(",")));
            esHotelInfo.setHotelGroupId(jdJdb.getJtid());
            esHotelInfo.setHotelGroupName(jdJdb.getJtmc());
            String kysj = jdJdb.getKysj();
            if (StringUtils.isNotEmpty(kysj)) {
                esHotelInfo.setOpeningDate(convertToDateFormat(kysj));
            }
            esHotelInfo.setFinalDecorationTime(jdJdb.getZhzxsj());
            Optional.ofNullable(jdJdb.getRank())
                    .map(Long::floatValue)
                    .ifPresent(esHotelInfo::setRank);
            esHotelInfo.setNoticeInfo(jdJdb.getNoticeInfo());
            esHotelInfo.setPolicyInfo(jdJdb.getPolicyInfo());
            esHotelInfo.setFacilitiesInfo(jdJdb.getFacilitiesInfo());
            esHotelInfo.setRecommendLevel(jdJdb.getRecommendLevel());
            esHotelInfos.add(esHotelInfo);
            esHotelInfo.setStatus(jdJdb.getStatus());
        }
        updatePrice(interfacePlats, esHotelInfos);
        return esHotelInfos;
    }

    private void updatePrice(List<JdJdb> interfacePlats, List<EsHotelInfo> esHotelInfos) {
        Map<String, EsHotelInfo> idcollectEsHotelInfoMap = esHotelInfos.stream()
                .collect(Collectors.toMap(EsHotelInfo::getHotelId, esHotelInfo -> esHotelInfo));

        List<String> idList = interfacePlats.stream()
                .map(JdJdb::getId)
                .collect(Collectors.toList());

        String yesterday = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String thirtyDay = LocalDateTime.now().plusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<ZhJdJdbMinPrice> zhJdJdbMinPrices = zhJdJdbMinPriceMapper.selectListByTimeAndJdIdList(idList, yesterday, thirtyDay);
        if (CollectionUtils.isEmpty(zhJdJdbMinPrices)) {
            logger.error("更新es没有查询到最低价数据 idList:{}", idList);
            return;
        }

        //过滤价格小于指定金额的数据
        zhJdJdbMinPrices = zhJdJdbMinPrices.stream().filter(zhJdJdbMinPrice -> zhJdJdbMinPrice.getMinPrice().compareTo(BigDecimal.valueOf(minPrice)) >= 0).collect(Collectors.toList());

        Map<String, ZhJdJdbMinPrice> minPriceMap = zhJdJdbMinPrices.stream()
                .collect(Collectors.toMap(
                        zhJdJdbMinPrice -> zhJdJdbMinPrice.getSxsj() + zhJdJdbMinPrice.getJdid(),
                        zhJdJdbMinPrice -> zhJdJdbMinPrice,
                        BinaryOperator.minBy(Comparator.comparing(ZhJdJdbMinPrice::getMinPrice))
                ));

        Map<String, List<ZhJdJdbMinPrice>> hotelIdMap = minPriceMap.values().stream()
                .collect(Collectors.groupingBy(ZhJdJdbMinPrice::getJdid));

        hotelIdMap.forEach((key, value) -> {
            List<DatePrice> datePriceList = value.stream()
                    .map(zhJdJdbMinPrice -> new DatePrice(
                            DateUtils.formatDate(zhJdJdbMinPrice.getSxsj(), "yyyy-MM-dd"),
                            zhJdJdbMinPrice.getMinPrice(),
                            zhJdJdbMinPrice.getInterfacePlat()))
                    .collect(Collectors.toList());

            logger.info("当前酒店ID为:{},当前酒店最低价数据为:{}", key, JSONObject.toJSONString(datePriceList));

            EsHotelInfo esHotelInfo = idcollectEsHotelInfoMap.get(key);
            if (esHotelInfo != null) {
                esHotelInfo.setDatePriceList(datePriceList);
            }
        });
    }

    @Override
    public void appendIfNotEmpty(StringBuilder sb, String value) {
        if (StringUtils.isNotEmpty(value)) {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            sb.append(value);
        }
    }

    @Override
    public int findByBeanCount(String suffix) {
        return jdJdbMapper.findByBeanCount(suffix);
    }

    @Override
    public void batchUpdateMinPrice(EsHotelInfo hotel) {
        if (hotel == null || isEmpty(hotel.getHotelId()) ||
                CollectionUtils.isEmpty(hotel.getDatePriceList())) {
            logger.info("酒店数据不存在:{}", JSON.toJSON(hotel));
            return;
        }

        List<DatePrice> datePriceList = hotel.getDatePriceList();

        if (CollectionUtils.isEmpty(datePriceList)) {
            logger.info("酒店最低价不能为空");
            return;
        }

        try {
            EsHotelInfo esHotelInfo = hotelIntlTemplate.getById(hotel.getHotelId(), EsHotelInfo.class);
            if (esHotelInfo == null) {
                logger.info("{}:酒店不存在!", hotel.getHotelId());
                return;
            }
            esHotelInfo.setDatePriceList(hotel.getDatePriceList());
            hotelIntlTemplate.save(esHotelInfo);
            logger.info("更新酒店最低价到es成功 酒店ID:{}", hotel.getHotelId());
        } catch (Exception e) {
            logger.error("更新酒店最低价到es失败", e);
        }
    }

    @Override
    public EsHotelInfo getEsHotelInfoByHotelId(String hotelId) {
        try {
            return hotelIntlTemplate.getById(hotelId, EsHotelInfo.class);
        } catch (Exception e) {
            logger.error("查询酒店ES数据异常：", e);
        }
        return null;
    }

    @Override
    public void updateEsHotelInfo(EsHotelInfo esHotelInfo) {
        try {
            hotelIntlTemplate.save(esHotelInfo);
        } catch (Exception e) {
            logger.error("更新酒店ES数据异常：", e);
        }
    }

    @Override
    public List<JdJdb> getJdJdbByIds(List<String> idList) {
            if (isEmpty(idList)) {
                return new ArrayList<>();
            }
            return jdJdbMapper.getJdJdbByIds(idList);
    }

    public void setLocation(JdJdb jdJdb, EsHotelInfo esHotelInfo) {
        BigDecimal lon = jdJdb.getLonBaidu();
        BigDecimal lat = jdJdb.getLatBaidu();

        if (lon == null || lat == null) {
            if (jdJdb.getLonGoogle() != null && jdJdb.getLatGoogle() != null) {
                CoordinateTransformUtils.Coordinate googleCoordinate = CoordinateTransformUtils.transformFromWGSToGCJ(jdJdb.getLatGoogle().doubleValue(), jdJdb.getLonGoogle().doubleValue());
                CoordinateTransformUtils.Coordinate baiduCoordinate = CoordinateTransformUtils.transformFromGCJToBaidu(googleCoordinate.getLatitude(), googleCoordinate.getLongitude());
                lon = BigDecimal.valueOf(baiduCoordinate.getLongitude());
                lat = BigDecimal.valueOf(baiduCoordinate.getLatitude());
            } else if (jdJdb.getLonGaode() != null && jdJdb.getLatGaode() != null) {
                CoordinateTransformUtils.Coordinate gaodeCoordinate = CoordinateTransformUtils.transformFromGCJToBaidu(jdJdb.getLatGaode().doubleValue(), jdJdb.getLonGaode().doubleValue());
                lon = BigDecimal.valueOf(gaodeCoordinate.getLongitude());
                lat = BigDecimal.valueOf(gaodeCoordinate.getLatitude());
            }
        }

        if (isValidCoordinate(lon, lat)) {
            esHotelInfo.setLocation(new Location(lon, lat));
        }else{
            esHotelInfo.setLocation(new Location(BigDecimal.ZERO, BigDecimal.ZERO));
        }
    }

    private boolean isValidCoordinate(BigDecimal lon, BigDecimal lat) {
        return lon != null && lat != null &&
                LONGITUDE_PATTERN.matcher(String.valueOf(lon)).matches() &&
                LATITUDE_PATTERN.matcher(String.valueOf(lat)).matches();
    }

    @Override
    public void updateMinPrice(EsHotelInfo hotel) {
        if (hotel == null || isEmpty(hotel.getHotelId()) ||
                CollectionUtils.isEmpty(hotel.getDatePriceList())) {
            logger.info("酒店数据不存在:{}!", JSON.toJSON(hotel));
            return;
        }

        List<DatePrice> newDatePriceList = hotel.getDatePriceList();
        DatePrice newDatePrice = newDatePriceList.get(0);

        if (isEmpty(newDatePrice.getDate()) ||
                isEmpty(newDatePrice.getInterfacePlat()) ||
                newDatePrice.getMinPrice() == null) {
            logger.info("酒店价格数据不存在:{}!", JSON.toJSON(newDatePrice));
            return;
        }

        try {
            EsHotelInfo esHotelInfo = hotelIntlTemplate.getById(hotel.getHotelId(), EsHotelInfo.class);
            if (esHotelInfo == null) {
                logger.info("{}:酒店不存在!", hotel.getHotelId());
                return;
            }

            updateMinPriceInList(esHotelInfo, newDatePrice);
            hotelIntlTemplate.save(esHotelInfo);
        } catch (Exception e) {
            logger.error("更新酒店最低价到es失败", e);
        }
    }

    private void updateMinPriceInList(EsHotelInfo esHotelInfo, DatePrice newDatePrice) {
        List<DatePrice> datePriceList = esHotelInfo.getDatePriceList();

        if (CollectionUtils.isEmpty(datePriceList)) {
            datePriceList = new ArrayList<>();
            esHotelInfo.setDatePriceList(datePriceList);
        }

        boolean updated = false;
        for (DatePrice datePriceDto : datePriceList) {
            if (datePriceDto.getDate().equals(newDatePrice.getDate())) {
                BigDecimal newMinPrice = newDatePrice.getMinPrice();
                if (datePriceDto.getMinPrice().compareTo(newMinPrice) > 0) {
                    datePriceDto.setMinPrice(newMinPrice);
                    datePriceDto.setInterfacePlat(newDatePrice.getInterfacePlat());
                    updated = true;
                }
                break;
            }
        }

        if (!updated) {
            datePriceList.add(newDatePrice);
        }
    }

    public String convertToDateFormat(String kysj) {
        if (kysj == null || kysj.isEmpty()) {
            return null;
        }

        try {
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

            if (kysj.matches("^\\d{10,13}$")) {
                long timestamp = Long.parseLong(kysj);
                Date date = new Date(timestamp);
                return outputFormat.format(date);
            }
            else if (kysj.matches("^\\d{4}/\\d{2}$")) {
                SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy/MM");
                Date date = monthFormat.parse(kysj);
                return outputFormat.format(date);
            }
            else if (kysj.matches("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$")) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = inputFormat.parse(kysj);
                return outputFormat.format(date);
            }
            else if (kysj.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date date = inputFormat.parse(kysj);
                return outputFormat.format(date);
            } else {
                return null;
            }
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }


}
