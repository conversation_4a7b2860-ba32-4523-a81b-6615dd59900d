package com.ltgj.ivw.service.impl;

import com.ltgj.common.core.domain.AjaxResult;
import com.ltgj.common.utils.StringUtils;
import com.ltgj.ivw.builder.GeoHierarchyBuilder;
import com.ltgj.ivw.domain.BCity;
import com.ltgj.ivw.domain.GeoCityGeo;
import com.ltgj.ivw.domain.GeoProvince;
import com.ltgj.ivw.dto.GeoHierarchyNode;
import com.ltgj.ivw.dto.GeoLevelQueryReq;
import com.ltgj.ivw.dto.GeoParentLookupReq;
import com.ltgj.ivw.dto.GeoStructureQueryReq;
import com.ltgj.ivw.service.IGeoBaseDataService;
import com.ltgj.ivw.service.IGeoHierarchyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 地理位置层级服务实现类
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Service
@Slf4j
public class GeoHierarchyServiceImpl implements IGeoHierarchyService {
    
    @Autowired
    private GeoHierarchyBuilder hierarchyBuilder;
    
    @Autowired
    private IGeoBaseDataService geoBaseDataService;
    
    // 本地缓存，用于提高查询性能
    private final Map<String, Object> localCache = new ConcurrentHashMap<>();
    
    @Override
    @Cacheable(value = "getGeoHierarchy", keyGenerator = "keyGenerator", cacheManager = "cacheManager")
    public AjaxResult getGeoHierarchy() {
        log.info("获取完整四级联动数据");
        
        try {
            // 使用异步方式构建数据，提高性能
            CompletableFuture<List<GeoHierarchyNode>> future = CompletableFuture.supplyAsync(() -> 
                hierarchyBuilder.buildFullHierarchy()
            );
            
            List<GeoHierarchyNode> hierarchy = future.get();
            
            log.info("完整四级联动数据获取成功，数据量：{}", hierarchy.size());
            return AjaxResult.success("获取成功", hierarchy);
            
        } catch (Exception e) {
            log.error("获取完整四级联动数据异常", e);
            return AjaxResult.error("获取四级联动数据失败：" + e.getMessage());
        }
    }
    
    @Override
    @Cacheable(value = "geoDataByLevel", keyGenerator = "keyGenerator", cacheManager = "cacheManager")
    public AjaxResult getGeoDataByLevel(GeoLevelQueryReq request) {
        log.info("分级查询地理位置数据，请求：{}", request);
        
        // 参数验证
        if (request == null) {
            return AjaxResult.error("查询参数不能为空");
        }

        // 记录开始时间，用于性能监控
        long startTime = System.currentTimeMillis();
        
        List<GeoHierarchyNode> result = hierarchyBuilder.buildByLevel(request);

        long elapsedTime = System.currentTimeMillis() - startTime;
        
        // 数据量监控和警告
        if (result.size() > 1000) {
            log.warn("查询返回大量数据，数量：{}，耗时：{}ms，请求：{}", result.size(), elapsedTime, request);
        } else {
            log.info("分级查询完成，结果数量：{}，耗时：{}ms", result.size(), elapsedTime);
        }
        
        return AjaxResult.success("查询成功", result);
    }
    
    @Override
    @Cacheable(value = "getParentInfo", keyGenerator = "keyGenerator", cacheManager = "cacheManager")
    public AjaxResult getParentInfo(GeoParentLookupReq request) {
        log.info("获取上级行政区划信息，请求：{}", request);
        
        try {
            GeoHierarchyNode parentInfo = null;
            
            switch (request.getChildType().toLowerCase()) {
                case "city":
                    parentInfo = getProvinceByCity(request);
                    break;
                case "district":
                    parentInfo = getCityByDistrict(request);
                    break;
                case "province":
                    parentInfo = getCountryByProvince(request);
                    break;
                default:
                    return AjaxResult.error("不支持的子级类型：" + request.getChildType());
            }
            
            if (parentInfo == null) {
                return AjaxResult.error("未找到对应的上级行政区划信息");
            }
            
            log.info("上级行政区划信息获取成功：{}", parentInfo.getName());
            return AjaxResult.success("查询成功", parentInfo);
            
        } catch (Exception e) {
            log.error("获取上级行政区划信息异常，请求：{}", request, e);
            return AjaxResult.error("查询上级行政区划失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据城市获取省份信息
     */
    private GeoHierarchyNode getProvinceByCity(GeoParentLookupReq request) {
        BCity city = geoBaseDataService.selectBCityById(request.getChildId());
        if (city == null || StringUtils.isEmpty(city.getProvince())) {
            return null;
        }
        
        GeoProvince province = geoBaseDataService.selectGeoProvinceById(city.getProvince());
        if (province == null) {
            return null;
        }
        
        return convertProvinceToNode(province);
    }
    
    /**
     * 根据区县获取城市信息
     */
    private GeoHierarchyNode getCityByDistrict(GeoParentLookupReq request) {
        GeoCityGeo district = geoBaseDataService.selectGeoCityGeoById(request.getChildId());
        if (district == null || StringUtils.isEmpty(district.getCityId())) {
            return null;
        }
        
        BCity city = geoBaseDataService.selectBCityById(district.getCityId());
        if (city == null) {
            return null;
        }
        
        return convertCityToNode(city);
    }
    
    /**
     * 根据省份获取国家信息
     */
    private GeoHierarchyNode getCountryByProvince(GeoParentLookupReq request) {
        GeoProvince province = geoBaseDataService.selectGeoProvinceById(request.getChildId());
        if (province == null || StringUtils.isEmpty(province.getNation())) {
            return null;
        }
        
        GeoHierarchyNode countryNode = new GeoHierarchyNode();
        countryNode.setId(province.getNation());
        countryNode.setName(province.getNationName());
        countryNode.setType("country");
        countryNode.setLevel(1);
        
        return countryNode;
    }
    
    /**
     * 转换省份实体为节点
     */
    private GeoHierarchyNode convertProvinceToNode(GeoProvince province) {
        GeoHierarchyNode node = new GeoHierarchyNode();
        node.setId(province.getId());
        node.setName(province.getProvinceName());
        node.setEnName(province.getProvinceEname());
        node.setFullName(province.getProvinceFullName());
        node.setType("province");
        node.setLevel(2);
        node.setParentId(province.getNation());
        node.setParentName(province.getNationName());
        return node;
    }
    
    /**
     * 转换城市实体为节点
     */
    private GeoHierarchyNode convertCityToNode(BCity city) {
        GeoHierarchyNode node = new GeoHierarchyNode();
        node.setId(city.getId());
        node.setName(city.getCityName());
        node.setEnName(city.geteName());
        node.setFullName(city.getCityNameLong());
        node.setType("city");
        node.setLevel(3);
        node.setParentId(city.getProvince());
        node.setParentName(city.getProvinceName());
        node.setLongitude(city.getLon());
        node.setLatitude(city.getLat());
        return node;
    }
    
    @Override
    @Cacheable(value = "getGeoStructureById", keyGenerator = "keyGenerator", cacheManager = "cacheManager")
    public AjaxResult<GeoHierarchyNode> getGeoStructureById(GeoStructureQueryReq request) {
        log.info("根据ID获取地理位置层次结构，请求：{}", request);
        
        try {
            // 参数校验
            if (request == null || StringUtils.isEmpty(request.getId())) {
                return AjaxResult.errorFor("ID参数不能为空");
            }
            
            // 确定ID类型
            String idType = determineIdType(request);
            if (StringUtils.isEmpty(idType)) {
                return AjaxResult.errorFor("无法识别的ID类型或ID不存在：" + request.getId());
            }
            
            // 构建层次结构
            GeoHierarchyNode structure = buildGeoStructure(request, idType);
            if (structure == null) {
                return AjaxResult.errorFor("未找到对应的地理位置信息");
            }
            
            log.info("地理位置层次结构获取成功，ID：{}，类型：{}", request.getId(), idType);
            return AjaxResult.successWith("查询成功", structure);
            
        } catch (Exception e) {
            log.error("获取地理位置层次结构异常，请求：{}", request, e);
            throw new RuntimeException("查询地理位置层次结构失败:" + e.getMessage());
        }
    }
    
    /**
     * 确定ID的类型
     */
    private String determineIdType(GeoStructureQueryReq request) {
        // 如果已指定类型，先验证是否正确
        if (StringUtils.isNotEmpty(request.getIdType())) {
            if (verifyIdType(request.getId(), request.getIdType())) {
                return request.getIdType();
            }
        }
        
        // 自动识别ID类型
        // 1. 检查是否为区县ID
        GeoCityGeo district = geoBaseDataService.selectGeoCityGeoById(request.getId());
        if (district != null) {
            return "district";
        }
        
        // 2. 检查是否为城市ID
        BCity city = geoBaseDataService.selectBCityById(request.getId());
        if (city != null) {
            return "city";
        }
        
        // 3. 检查是否为省份ID
        GeoProvince province = geoBaseDataService.selectGeoProvinceById(request.getId());
        if (province != null) {
            return "province";
        }
        
        // 4. 检查是否为国家ID
        List<Map<String, Object>> countries = geoBaseDataService.selectDistinctCountries(true);
        for (Map<String, Object> country : countries) {
            if (request.getId().equals(String.valueOf(country.get("nation")))) {
                return "country";
            }
        }
        
        return null;
    }
    
    /**
     * 验证ID类型是否正确
     */
    private boolean verifyIdType(String id, String idType) {
        switch (idType.toLowerCase()) {
            case "district":
                return geoBaseDataService.selectGeoCityGeoById(id) != null;
            case "city":
                return geoBaseDataService.selectBCityById(id) != null;
            case "province":
                return geoBaseDataService.selectGeoProvinceById(id) != null;
            case "country":
                List<Map<String, Object>> countries = geoBaseDataService.selectDistinctCountries(true);
                return countries.stream().anyMatch(country -> 
                    id.equals(String.valueOf(country.get("nation"))));
            default:
                return false;
        }
    }
    
    /**
     * 构建地理位置层次结构
     */
    private GeoHierarchyNode buildGeoStructure(GeoStructureQueryReq request, String idType) {
        switch (idType.toLowerCase()) {
            case "district":
                return buildStructureFromDistrict(request);
            case "city":
                return buildStructureFromCity(request);
            case "province":
                return buildStructureFromProvince(request);
            case "country":
                return buildStructureFromCountry(request);
            default:
                return null;
        }
    }
    
    /**
     * 从区县开始构建层次结构
     */
    private GeoHierarchyNode buildStructureFromDistrict(GeoStructureQueryReq request) {
        GeoCityGeo district = null;
        try {
            // 使用geoBaseDataService确保数据源切换生效
            district = geoBaseDataService.selectGeoCityGeoById(request.getId());
        } catch (Exception exception){
            log.error("查询区县出错:{}", exception);
        }
        if (district == null) {
            return null;
        }
        
        GeoHierarchyNode districtNode = convertDistrictToNode(district);
        
        // 如果需要包含完整子级数据，添加当前区县下的子级（如果有的话，比如街道）
        // 目前区县是最底层，暂无子级，预留扩展
        if (Boolean.TRUE.equals(request.getIncludeFullChildren())) {
            // 预留：如果将来有街道等更细粒度的行政区划，在这里添加
        }
        
        // 如果不需要包含上级，直接返回区县节点
        if (!Boolean.TRUE.equals(request.getIncludeParents())) {
            return districtNode;
        }
        
        // 获取上级城市
        BCity city = geoBaseDataService.selectBCityById(district.getCityId());
        if (city == null) {
            return districtNode;
        }
        
        GeoHierarchyNode cityNode = convertCityToNode(city);
        cityNode.addChild(districtNode);
        
        // 获取上级省份
        GeoProvince province = geoBaseDataService.selectGeoProvinceById(city.getProvince());
        if (province == null) {
            return cityNode;
        }
        
        GeoHierarchyNode provinceNode = convertProvinceToNode(province);
        
        // 对于区县查询，只在当前区县节点添加子级，不为上级节点添加其他子级
        provinceNode.addChild(cityNode);
        
        // 获取国家
        GeoHierarchyNode countryNode = new GeoHierarchyNode();
        countryNode.setId(province.getNation());
        countryNode.setName(province.getNationName());
        countryNode.setType("country");
        countryNode.setLevel(1);
        countryNode.addChild(provinceNode);
        
        return countryNode;
    }
    
    /**
     * 从城市开始构建层次结构
     */
    private GeoHierarchyNode buildStructureFromCity(GeoStructureQueryReq request) {
        BCity city = null;
        try {
            // 使用geoBaseDataService确保数据源切换生效
            city = geoBaseDataService.selectBCityById(request.getId());
        } catch (Exception exception){
            log.error("查询城市出错:{}", exception);
        }

        if (city == null) {
            return null;
        }
        
        GeoHierarchyNode cityNode = convertCityToNode(city);
        
        // 如果需要包含完整子级数据，添加当前城市下的所有区县
        if (Boolean.TRUE.equals(request.getIncludeFullChildren())) {
            addDistrictsToCity(cityNode, request.getId(), request.getGeoType());
        }
        
        // 如果不需要包含上级，直接返回城市节点
        if (!Boolean.TRUE.equals(request.getIncludeParents())) {
            return cityNode;
        }
        
        // 获取上级省份
        GeoProvince province = geoBaseDataService.selectGeoProvinceById(city.getProvince());
        if (province == null) {
            return cityNode;
        }
        
        GeoHierarchyNode provinceNode = convertProvinceToNode(province);
        
        // 对于城市查询，只在当前城市节点的路径上构建层次，不添加其他城市
        provinceNode.addChild(cityNode);
        
        // 获取国家
        GeoHierarchyNode countryNode = new GeoHierarchyNode();
        countryNode.setId(province.getNation());
        countryNode.setName(province.getNationName());
        countryNode.setType("country");
        countryNode.setLevel(1);
        countryNode.addChild(provinceNode);
        
        return countryNode;
    }
    
    /**
     * 从省份开始构建层次结构
     */
    private GeoHierarchyNode buildStructureFromProvince(GeoStructureQueryReq request) {
        GeoProvince province = null;
        try {
            // 使用geoBaseDataService确保数据源切换生效
            province = geoBaseDataService.selectGeoProvinceById(request.getId());
        } catch (Exception exception){
            log.error("查询省份出错", exception);
        }

        if (province == null) {
            return null;
        }
        
        GeoHierarchyNode provinceNode = convertProvinceToNode(province);
        
        // 如果需要包含完整子级数据，添加当前省份下的所有城市及其区县
        if (Boolean.TRUE.equals(request.getIncludeFullChildren())) {
            addFullChildrenToProvince(provinceNode, request.getGeoType());
        }
        
        // 如果不需要包含上级，直接返回省份节点
        if (!Boolean.TRUE.equals(request.getIncludeParents())) {
            return provinceNode;
        }
        
        // 获取国家，但国家下只包含当前省份，不包含其他省份
        GeoHierarchyNode countryNode = new GeoHierarchyNode();
        countryNode.setId(province.getNation());
        countryNode.setName(province.getNationName());
        countryNode.setType("country");
        countryNode.setLevel(1);
        countryNode.addChild(provinceNode);
        
        return countryNode;
    }
    
    /**
     * 从国家开始构建层次结构
     */
    private GeoHierarchyNode buildStructureFromCountry(GeoStructureQueryReq request) {
        // 验证国家是否存在
        List<Map<String, Object>> countries = null;
        try {
            // 使用geoBaseDataService确保数据源切换生效
            countries = geoBaseDataService.selectDistinctCountries(true);
        } catch (Exception exception){
            log.error("查询国家出错:{}", exception);
        }

        Map<String, Object> targetCountry = countries.stream()
            .filter(country -> request.getId().equals(String.valueOf(country.get("nation"))))
            .findFirst()
            .orElse(null);
        
        if (targetCountry == null) {
            return null;
        }
        
        GeoHierarchyNode countryNode = new GeoHierarchyNode();
        countryNode.setId(String.valueOf(targetCountry.get("nation")));
        countryNode.setName(String.valueOf(targetCountry.get("nationName")));
        countryNode.setType("country");
        countryNode.setLevel(1);
        
        // 如果需要包含子级数据，添加当前国家下的所有省份及其完整层级
        if (Boolean.TRUE.equals(request.getIncludeFullChildren())) {
            addFullChildrenToCountry(countryNode, request.getGeoType());
        }
        
        return countryNode;
    }
    
    /**
     * 为国家添加完整的下级数据
     */
    private void addFullChildrenToCountry(GeoHierarchyNode countryNode, String geoType) {
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("province");
        request.setParentId(countryNode.getId());
        
        List<GeoHierarchyNode> provinces = hierarchyBuilder.buildByLevel(request);
        for (GeoHierarchyNode province : provinces) {
            addFullChildrenToProvince(province, geoType);
            countryNode.addChild(province);
        }
    }
    
    /**
     * 为省份添加完整的下级数据
     */
    private void addFullChildrenToProvince(GeoHierarchyNode provinceNode, String geoType) {
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("city");
        request.setParentId(provinceNode.getId());
        
        List<GeoHierarchyNode> cities = hierarchyBuilder.buildByLevel(request);
        for (GeoHierarchyNode city : cities) {
            addDistrictsToCity(city, city.getId(), geoType);
            provinceNode.addChild(city);
        }
    }
    
    /**
     * 为城市添加区县数据
     */
    private void addDistrictsToCity(GeoHierarchyNode cityNode, String cityId, String geoType) {
        GeoLevelQueryReq request = new GeoLevelQueryReq();
        request.setLevel("district");
        request.setParentId(cityId);
        request.setGeoType(geoType);
        
        List<GeoHierarchyNode> districts = hierarchyBuilder.buildByLevel(request);
        for (GeoHierarchyNode district : districts) {
            cityNode.addChild(district);
        }
    }
    
    /**
     * 转换区县实体为节点
     */
    private GeoHierarchyNode convertDistrictToNode(GeoCityGeo district) {
        GeoHierarchyNode node = new GeoHierarchyNode();
        node.setId(district.getGeoId());
        node.setName(district.getGeoName());
        node.setType("district");
        node.setLevel(4);
        node.setParentId(district.getCityId());
        node.setParentName(district.getCityName());
        node.setGeoType(district.getGeoType());
        
        // 转换坐标
        if (district.getLonBaidu() != null) {
            node.setLongitude(district.getLonBaidu().toString());
        }
        if (district.getLatBaidu() != null) {
            node.setLatitude(district.getLatBaidu().toString());
        }
        
        return node;
    }

    @Override
    public String clearCacheSelective(List<String> cacheTypes) {
        if (cacheTypes == null || cacheTypes.isEmpty()) {
            return "缓存类型参数为空，未执行任何清除操作";
        }
        
        List<String> clearedCaches = new ArrayList<>();
        List<String> invalidTypes = new ArrayList<>();
        
        for (String cacheType : cacheTypes) {
            switch (cacheType.toLowerCase()) {
                case "hierarchy":
                    clearHierarchyCache();
                    clearedCaches.add("完整四级联动数据缓存(getGeoHierarchy)");
                    break;
                case "level":
                    clearLevelCache();
                    clearedCaches.add("分级查询数据缓存(geoDataByLevel)");
                    break;
                case "parent":
                    clearParentCache();
                    clearedCaches.add("上级查询缓存(getParentInfo)");
                    break;
                case "structure":
                    clearStructureCache();
                    clearedCaches.add("层次结构查询缓存(getGeoStructureById)");
                    break;
                case "all":
                    clearAllCaches();
                    clearedCaches.add("所有缓存(包含本地缓存)");
                    break;
                default:
                    invalidTypes.add(cacheType);
                    break;
            }
        }
        
        StringBuilder result = new StringBuilder();
        if (!clearedCaches.isEmpty()) {
            result.append("成功清除的缓存: ").append(String.join(", ", clearedCaches));
        }
        if (!invalidTypes.isEmpty()) {
            if (result.length() > 0) {
                result.append("; ");
            }
            result.append("无效的缓存类型: ").append(String.join(", ", invalidTypes));
        }
        
        String resultMessage = result.toString();
        log.info("选择性缓存清除完成: {}", resultMessage);
        return resultMessage;
    }
    
    /**
     * 清除完整四级联动数据缓存
     */
    @CacheEvict(value = "getGeoHierarchy", allEntries = true)
    public void clearHierarchyCache() {
        log.info("清除完整四级联动数据缓存");
    }
    
    /**
     * 清除分级查询数据缓存
     */
    @CacheEvict(value = "geoDataByLevel", allEntries = true)
    public void clearLevelCache() {
        log.info("清除分级查询数据缓存");
    }
    
    /**
     * 清除上级查询缓存
     */
    @CacheEvict(value = "getParentInfo", allEntries = true)
    public void clearParentCache() {
        log.info("清除上级查询缓存");
    }
    
    /**
     * 清除层次结构查询缓存
     */
    @CacheEvict(value = "getGeoStructureById", allEntries = true)
    public void clearStructureCache() {
        log.info("清除层次结构查询缓存");
    }
    
    /**
     * 清除所有缓存
     */
    @CacheEvict(value = {"getGeoStructureById", "geoDataByLevel", "getGeoHierarchy", "getParentInfo"}, allEntries = true)
    public void clearAllCaches() {
        localCache.clear();
        log.info("清除所有缓存包含本地缓存");
    }
} 