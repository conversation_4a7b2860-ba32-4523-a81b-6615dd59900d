package com.ltgj.sdk.cozyTime.model.staticdata;

import com.ltgj.sdk.cozyTime.base.CozyTimeRequestInterface;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据国家编码查询城市信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CozyTimeHotelContentApi20FacilitiesRequest implements CozyTimeRequestInterface<CozyTimeHotelContentApi20FacilitiesResponse> {
    @Override
    public String getRequestMethod() {
        return "GET";
    }

    @Override
    public String getEndpoint() {
        return "/hotel_content_api/2.0/facilities";
    }

    @Override
    public Class<CozyTimeHotelContentApi20FacilitiesResponse> getResponseClass() {
        return CozyTimeHotelContentApi20FacilitiesResponse.class;
    }

    /**
     * 国内：1；国际：2
     * 必填: true
     */
    private String region;
}
