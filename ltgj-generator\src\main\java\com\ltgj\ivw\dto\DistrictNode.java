package com.ltgj.ivw.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 区县节点DTO
 * 
 * <AUTHOR>
 * @date 2024-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistrictNode {
    
    /**
     * 区县ID
     */
    private String locationId;
    
    /**
     * 区县名称(中文)
     */
    private String locationName;
    
    /**
     * 区县名称(英文)
     */
    private String locationNameEn;
    
    /**
     * 平台ID
     */
    private String platformId;
    
    /**
     * 排序字段
     */
    private Integer sortOrder;
} 