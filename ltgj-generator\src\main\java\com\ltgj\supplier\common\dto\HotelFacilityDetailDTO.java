package com.ltgj.supplier.common.dto;

/**
 * 酒店设施详细信息 DTO
 * 支持设施的完整属性配置，包括是否有无、是否收费等
 */
public class HotelFacilityDetailDTO {
    /**
     * 设施ID
     */
    private Integer id;
    
    /**
     * 设施名称
     */
    private String name;
    
    /**
     * 设施key（英文标识）
     */
    private String key;
    
    /**
     * 是否支持"是否有无"属性
     */
    private Boolean supportIsHave;
    
    /**
     * 是否支持"是否收费"属性
     */
    private Boolean supportIsCharge;
    
    /**
     * 是否有无（实际值，供前端设置和获取）
     */
    private Boolean isHave;
    
    /**
     * 是否收费（实际值，供前端设置和获取）
     */
    private Boolean isCharge;
    
    /**
     * 备注信息
     */
    private String remark;

    public HotelFacilityDetailDTO() {
    }

    public HotelFacilityDetailDTO(Integer id, String name, String key, Boolean supportIsHave, Boolean supportIsCharge) {
        this.id = id;
        this.name = name;
        this.key = key;
        this.supportIsHave = supportIsHave;
        this.supportIsCharge = supportIsCharge;
    }

    public HotelFacilityDetailDTO(Integer id, String name, String key, Boolean supportIsHave, Boolean supportIsCharge, 
                                 Boolean isHave, Boolean isCharge, String remark) {
        this.id = id;
        this.name = name;
        this.key = key;
        this.supportIsHave = supportIsHave;
        this.supportIsCharge = supportIsCharge;
        this.isHave = isHave;
        this.isCharge = isCharge;
        this.remark = remark;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Boolean getSupportIsHave() {
        return supportIsHave;
    }

    public void setSupportIsHave(Boolean supportIsHave) {
        this.supportIsHave = supportIsHave;
    }

    public Boolean getSupportIsCharge() {
        return supportIsCharge;
    }

    public void setSupportIsCharge(Boolean supportIsCharge) {
        this.supportIsCharge = supportIsCharge;
    }

    public Boolean getIsHave() {
        return isHave;
    }

    public void setIsHave(Boolean isHave) {
        this.isHave = isHave;
    }

    public Boolean getIsCharge() {
        return isCharge;
    }

    public void setIsCharge(Boolean isCharge) {
        this.isCharge = isCharge;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "HotelFacilityDetailDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", key='" + key + '\'' +
                ", supportIsHave=" + supportIsHave +
                ", supportIsCharge=" + supportIsCharge +
                ", isHave=" + isHave +
                ", isCharge=" + isCharge +
                ", remark='" + remark + '\'' +
                '}';
    }
} 