package com.ltgj.sdk.cozyTime.model.hotel;

import com.ltgj.sdk.cozyTime.base.CozyTimeBaseResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CozyTimeHotelContentApi20HotelInfoResponse extends CozyTimeBaseResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 酒店列表
     */
    private List<Hotel> hotels;

    @Data
    public class Hotel {
        private String brandCode;
        private String brandName;
        private String checkIn;
        private String checkInCN;
        private String checkOut;
        private String checkOutCN;
        private String cityCode;
        private String commentScore;
        private Coordinates coordinates;
        private String countryCode;
        private String customerType;
        private String districtName; // 新增
        private String districtNameCN; // 新增
        private String groupCode;
        private String groupName;
        private String hotelAddress;
        private String hotelAddressCN;
        private String hotelDesc;
        private String hotelDescCN;
        private List<String> hotelFacilities;
        private String hotelFee;
        private String hotelFeeCN;
        private Integer hotelId;
        private List<HotelImage> hotelImages;
        private String hotelName;
        private String hotelNameCN;
        private String hotelPolicy;
        private String hotelPolicyCN;
        private String hotelStatus; // 新增
        private String hotelType; // 新增
        private String helpfulTip; // 新增
        private String phone;
        private String poi;
        private String poiCN;
        private Integer roomCount; // 新增
        private Integer star;
        private String establishmentDate;
        private String renovationDate;
        private String zipCode; // 新增
    }

    @Data
    public class Coordinates {
        private String latitude;
        private String longitude;
    }

    @Data
    public class HotelImage {
        private String imageType;
        private String imageUrl;
        private String size;
    }
}