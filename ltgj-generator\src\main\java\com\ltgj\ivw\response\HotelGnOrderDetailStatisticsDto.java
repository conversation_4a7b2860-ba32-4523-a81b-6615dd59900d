package com.ltgj.ivw.response;

import com.ltgj.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

@Data
public class HotelGnOrderDetailStatisticsDto implements Serializable {

    @Excel(name = "日期")
    private String date;

    @Excel(name = "失败类型")
    private String subtypeName;

    @Excel(name = "时间")
    private String time;

    @Excel(name = "酒店名称")
    private String hotelName;

    @Excel(name = "酒店ID")
    private String localId;

    @Excel(name = "供应商")
    private String interfaceName;

    @Excel(name = "供应商酒店ID")
    private String platId;

    @Excel(name = "失败原因")
    private String reason;
}
