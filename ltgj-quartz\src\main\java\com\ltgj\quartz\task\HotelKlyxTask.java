package com.ltgj.quartz.task;

import com.ltgj.ivw.enums.InterfacePlatEnum;
import com.ltgj.supplier.common.dto.HotelBaseRequest;
import com.ltgj.supplier.klyx.gn.HotelKlyxManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("hotelKlyxTask")
@Slf4j
public class HotelKlyxTask {

    @Autowired
    private HotelKlyxManager hotelKlyxManager;

    /**
     * 初始化酒店数据
     */
    public void initHotelData() {
        HotelBaseRequest request = new HotelBaseRequest();
        request.setInterfacePlat(InterfacePlatEnum.INTERFACE_KLYX);
        try {
            hotelKlyxManager.initHotelDate(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 初始化酒店最低价
     */
    public void batchUpdateLowestPrice() {
        HotelBaseRequest request = new HotelBaseRequest();
        request.setInterfacePlat(InterfacePlatEnum.INTERFACE_KLYX);
        try {
            hotelKlyxManager.batchUpdateLowestPrice(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
