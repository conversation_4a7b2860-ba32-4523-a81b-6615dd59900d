package com.ltgj.ivw.mapper;

import java.util.List;
import java.util.Map;

import com.ltgj.common.annotation.DataSource;
import com.ltgj.common.enums.DataSourceType;
import com.ltgj.ivw.domain.GeoProvince;
import org.apache.ibatis.annotations.Param;

/**
 * 省份信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface GeoProvinceMapper
{
    /**
     * 查询省份信息
     *
     * @param id 省份信息主键
     * @return 省份信息
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    public GeoProvince selectGeoProvinceById(String id);

    /**
     * 查询省份信息列表
     *
     * @param geoProvince 省份信息
     * @return 省份信息集合
     */
    public List<GeoProvince> selectGeoProvinceList(GeoProvince geoProvince);

    /**
     * 查询去重的国家信息
     *
     * @param validOnly 是否只查询有效数据
     * @return 国家信息集合
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    public List<Map<String, Object>> selectDistinctCountries(@Param("validOnly") Boolean validOnly);

    /**
     * 根据国家ID查询省份列表
     *
     * @param countryId 国家ID
     * @param validOnly 是否只查询有效数据
     * @return 省份信息集合
     */
    @DataSource(DataSourceType.DEVYIXBASE)
    public List<GeoProvince> selectProvincesByCountry(@Param("countryId") String countryId, @Param("validOnly") Boolean validOnly);

    /**
     * 新增省份信息
     *
     * @param geoProvince 省份信息
     * @return 结果
     */
    public int insertGeoProvince(GeoProvince geoProvince);

    /**
     * 修改省份信息
     *
     * @param geoProvince 省份信息
     * @return 结果
     */
    public int updateGeoProvince(GeoProvince geoProvince);

    /**
     * 删除省份信息
     *
     * @param id 省份信息主键
     * @return 结果
     */
    public int deleteGeoProvinceById(String id);

    /**
     * 批量删除省份信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGeoProvinceByIds(String[] ids);
}
