package com.ltgj.web.job.handler;

import com.alibaba.fastjson.JSON;
import com.ltgj.common.utils.DateUtils;
import com.ltgj.ivw.domain.MinPriceReq;
import com.ltgj.quartz.task.MinPriceTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/5/26
 * @Description： hotel-科坦最低价增量同步任务
 */
@Slf4j
@Component
@JobHandler(value = "cozyTimeMinPriceIncrementSyncHandler")
public class CozyTimeMinPriceIncrementSyncHandler extends IJobHandler {

    @Resource
    private MinPriceTask minPriceTask;

    private static final String COZY_TIME_TIME_FORMATTER = "yyyy-MM-dd'T'HH:mm:ss";

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        long startTime = System.currentTimeMillis();
        XxlJobLogger.log("----hotel-科坦最低价增量同步任务----开始");

        Boolean result = true;
        try {
            long buffer = 60 * 1000;// 缓冲时间
            long tenMinutes = 10 * 60 * 1000;//  10分钟

            long checkInTime = System.currentTimeMillis() - tenMinutes - buffer;
            String checkInDate = DateUtils.formatDate(new Date(checkInTime), COZY_TIME_TIME_FORMATTER);
            String checkOutDate = DateUtils.formatDate(new Date(), COZY_TIME_TIME_FORMATTER);
            MinPriceReq minPriceReq = new MinPriceReq();
            minPriceReq.setCheckInDate(checkInDate);
            minPriceReq.setCheckOutDate(checkOutDate);
            minPriceReq.setUserName("admin");
            log.info("----hotel-科坦最低价增量同步任务, req:{}", JSON.toJSONString(minPriceReq));
            this.minPriceTask.updateLowPriceOfCozyTime(minPriceReq);
        } catch (Exception e) {
            log.error("科坦最低价增量同步任务异常：" + e);
            result = false;
        }

        log.info("---hotel-科坦最低价增量同步任务---结束 执行结果：" + (result ? "成功" : "失败"));
        XxlJobLogger.log("-----hotel-科坦最低价增量同步任务-----务结束 执行结果：" + (result ? "成功" : "失败") + " 执行用时：" + (System.currentTimeMillis() - startTime) / 1000 + "秒");

        return result ? ReturnT.SUCCESS : ReturnT.FAIL;
    }
}
