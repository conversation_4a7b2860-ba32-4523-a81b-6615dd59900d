package com.ltgj.ivw.service;

import com.ltgj.ivw.domain.HotelGnOrderDetailStatistics;
import com.ltgj.ivw.domain.HotelGnPreMapping;
import com.ltgj.ivw.request.hotelGnPreMapping.ListRequest;
import com.ltgj.ivw.request.hotelGnStatistics.GnOrderDetailStatisticsRequest;
import com.ltgj.ivw.response.HotelGnOrderDetailStatisticsRes;

import java.util.List;

public interface HotelGnOrderDetailStatisticsService {
    public int insertHotelGnOrderDetailStatistics();

    List<HotelGnOrderDetailStatisticsRes> selectList(GnOrderDetailStatisticsRequest request);

    int selectCount(GnOrderDetailStatisticsRequest request);
}
