package com.ltgj.supplier.common.dto;

/**
 * 客户类型 DTO
 * 专门用于 HotelGnCustomerTypeEnum 数据的前端展示
 */
public class CustomerTypeDTO {
    /**
     * 客户类型编码
     */
    private Integer customerType;
    
    /**
     * 客户类型描述
     */
    private String customerDesc;
    
    /**
     * 枚举常量名称
     */
    private String key;

    public CustomerTypeDTO() {
    }

    public CustomerTypeDTO(Integer customerType, String customerDesc, String key) {
        this.customerType = customerType;
        this.customerDesc = customerDesc;
        this.key = key;
    }

    public Integer getCustomerType() {
        return customerType;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public String getCustomerDesc() {
        return customerDesc;
    }

    public void setCustomerDesc(String customerDesc) {
        this.customerDesc = customerDesc;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public String toString() {
        return "CustomerTypeDTO{" +
                "customerType=" + customerType +
                ", customerDesc='" + customerDesc + '\'' +
                ", key='" + key + '\'' +
                '}';
    }
} 